/**
 * أنواع تحكم YouTube
 * YouTube controller types
 * 
 * هذا الملف يحتوي على تعريفات أنواع تحكم YouTube
 * This file contains YouTube controller type definitions
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * تكوين تحكم YouTube
 * YouTube controller configuration
 */
export interface YouTubeControllerConfig {
    readonly enableAutoInitialization: boolean;
    readonly initializationTimeout: number;
    readonly retryAttempts: number;
    readonly retryDelay: number;
    readonly enablePerformanceMonitoring: boolean;
    readonly enableErrorReporting: boolean;
    readonly enableDebugLogging: boolean;
    readonly autoApplySettings: boolean;
    readonly settingsApplyDelay: number;
    readonly enableSettingsValidation: boolean;
}

/**
 * حالة تحكم YouTube
 * YouTube controller state
 */
export interface YouTubeControllerState {
    readonly isInitialized: boolean;
    readonly isReady: boolean;
    readonly isLoading: boolean;
    readonly hasError: boolean;
    readonly errorMessage?: string;
    readonly lastUpdateTime: Date;
    readonly operationCount: number;
    readonly errorCount: number;
}

/**
 * نوع خطأ تحكم YouTube
 * YouTube controller error type
 */
export type YouTubeControllerErrorType = 
    | 'INITIALIZATION_ERROR'
    | 'SETTINGS_APPLICATION_ERROR'
    | 'VIDEO_QUALITY_ERROR'
    | 'DARK_MODE_ERROR'
    | 'AD_BLOCKER_ERROR'
    | 'PERFORMANCE_ERROR'
    | 'NETWORK_ERROR'
    | 'TIMEOUT_ERROR'
    | 'VALIDATION_ERROR'
    | 'UNKNOWN_ERROR';

/**
 * تقرير خطأ تحكم YouTube
 * YouTube controller error report
 */
export interface YouTubeControllerErrorReport {
    readonly id: string;
    readonly type: YouTubeControllerErrorType;
    readonly message: string;
    readonly timestamp: Date;
    readonly stack?: string;
    readonly context?: Record<string, any>;
    readonly severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    readonly isRecoverable: boolean;
}

/**
 * تقرير أداء تحكم YouTube
 * YouTube controller performance report
 */
export interface YouTubeControllerPerformanceReport {
    readonly timestamp: Date;
    readonly uptime: number;
    readonly memoryUsage: number;
    readonly operationCount: number;
    readonly errorCount: number;
    readonly warningCount: number;
    readonly averageResponseTime?: number;
    readonly cpuUsage?: number;
    readonly networkLatency?: number;
    readonly activeConnections?: number;
    readonly cacheHitRate?: number;
    readonly throughput?: number;
}

/**
 * معلومات العملية
 * Operation information
 */
export interface OperationInfo {
    readonly id: string;
    readonly type: string;
    readonly priority: number;
    readonly status: 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
    readonly startTime: Date;
    readonly endTime?: Date;
    readonly duration?: number;
    readonly progress: number;
    readonly metadata: Record<string, any>;
}

/**
 * واجهة مدير العمليات
 * Operations manager interface
 */
export interface OperationsManager {
    startOperation(type: string, priority?: number): string;
    completeOperation(operationId: string, success: boolean, errorMessage?: string): void;
    retryOperation(operationId: string): boolean;
    cancelOperation(operationId: string): boolean;
    getActiveOperations(): OperationInfo[];
    getOperationHistory(): OperationInfo[];
    getOperationStatistics(): Record<string, number>;
    cleanup(): void;
}

/**
 * واجهة مراقب الأداء
 * Performance monitor interface
 */
export interface PerformanceMonitor {
    startMonitoring(): void;
    stopMonitoring(): void;
    getCurrentPerformanceReport(): YouTubeControllerPerformanceReport | null;
    getPerformanceHistory(): YouTubeControllerPerformanceReport[];
    recordOperation(): void;
    recordError(): void;
    recordWarning(): void;
    resetStatistics(): void;
    cleanup(): void;
}

/**
 * واجهة مُبلغ الأخطاء
 * Error reporter interface
 */
export interface ErrorReporter {
    reportError(error: YouTubeControllerErrorReport): void;
    getErrorHistory(): YouTubeControllerErrorReport[];
    getErrorStatistics(): Record<string, number>;
    getErrorsByType(errorType: YouTubeControllerErrorType): YouTubeControllerErrorReport[];
    getRecentErrors(count?: number): YouTubeControllerErrorReport[];
    getCriticalErrors(): YouTubeControllerErrorReport[];
    clearErrorHistory(): void;
    exportErrorReport(): string;
    cleanup(): void;
}

/**
 * معلومات التهيئة
 * Initialization information
 */
export interface InitializationInfo {
    readonly startTime: Date;
    readonly endTime?: Date;
    readonly duration?: number;
    readonly success: boolean;
    readonly errorMessage?: string;
    readonly retryCount: number;
    readonly steps: string[];
    readonly completedSteps: string[];
    readonly failedSteps: string[];
}

/**
 * معلومات تطبيق الإعدادات
 * Settings application information
 */
export interface SettingsApplicationInfo {
    readonly settingsType: string;
    readonly oldValue: any;
    readonly newValue: any;
    readonly timestamp: Date;
    readonly success: boolean;
    readonly errorMessage?: string;
    readonly validationResult?: any;
}

/**
 * معلومات تغيير جودة الفيديو
 * Video quality change information
 */
export interface VideoQualityChangeInfo {
    readonly oldQuality: string;
    readonly newQuality: string;
    readonly timestamp: Date;
    readonly success: boolean;
    readonly errorMessage?: string;
    readonly detectionMethod: string;
    readonly applicationMethod: string;
}

/**
 * معلومات تبديل الوضع المظلم
 * Dark mode toggle information
 */
export interface DarkModeToggleInfo {
    readonly oldState: boolean;
    readonly newState: boolean;
    readonly timestamp: Date;
    readonly success: boolean;
    readonly errorMessage?: string;
    readonly styleMethod: string;
}

/**
 * معلومات تبديل مانع الإعلانات
 * Ad blocker toggle information
 */
export interface AdBlockerToggleInfo {
    readonly oldState: boolean;
    readonly newState: boolean;
    readonly timestamp: Date;
    readonly success: boolean;
    readonly errorMessage?: string;
    readonly blockedDomains: number;
    readonly allowedDomains: number;
}

/**
 * نتيجة التحقق
 * Validation result
 */
export interface ValidationResult {
    readonly isValid: boolean;
    readonly errors: string[];
    readonly warnings: string[];
    readonly details?: Record<string, any>;
}

/**
 * معلومات الأداء السريعة
 * Quick performance information
 */
export interface QuickPerformanceInfo {
    readonly memoryUsage: string;
    readonly cpuUsage: string;
    readonly responseTime: string;
    readonly operations: number;
    readonly errors: number;
    readonly warnings: number;
    readonly uptime: string;
    readonly status: string;
}
