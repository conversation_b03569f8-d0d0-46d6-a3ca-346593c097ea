/**
 * ثوابت التطبيق الأساسية
 * Core application constants
 *
 * هذا الملف يحتوي على جميع الثوابت المستخدمة في التطبيق
 * This file contains all constants used in the application
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ApplicationConfig } from '@shared/types';

// إعادة تصدير الثوابت من الملفات المتخصصة
// Re-export constants from specialized files
export * from './application-info';
export * from './application-timeouts';
export * from './video-quality-constants';

/**
 * إعدادات التطبيق الافتراضية
 * Default application configuration
 */
export const DEFAULT_APPLICATION_CONFIG: ApplicationConfig = {
    videoQuality: '480p',
    darkMode: false,
    adBlocker: true
} as const;

/**
 * أحداث IPC الرئيسية
 * Main IPC events
 */
export const IPC_EVENTS = {
    // أحداث الإعدادات / Settings events
    SETTINGS_GET: 'settings:get',
    SETTINGS_SET: 'settings:set',
    SETTINGS_RESET: 'settings:reset',
    SETTINGS_CHANGED: 'settings:changed',

    // أحداث النافذة / Window events
    WINDOW_MINIMIZE: 'window:minimize',
    WINDOW_MAXIMIZE: 'window:maximize',
    WINDOW_CLOSE: 'window:close',
    WINDOW_SHOW_SETTINGS: 'window:show-settings',

    // أحداث YouTube / YouTube events
    YOUTUBE_QUALITY_SET: 'youtube:quality-set',
    YOUTUBE_DARK_MODE_TOGGLE: 'youtube:dark-mode-toggle',
    YOUTUBE_AD_BLOCKER_TOGGLE: 'youtube:ad-blocker-toggle',

    // أحداث النظام / System events
    SYSTEM_INFO: 'system:info',
    SYSTEM_PERFORMANCE: 'system:performance',
    SYSTEM_LOGS: 'system:logs'
} as const;

/**
 * مسارات الملفات
 * File paths
 */
export const FILE_PATHS = {
    SETTINGS: 'settings.json',
    LOGS: 'logs',
    CACHE: 'cache',
    TEMP: 'temp',
    BACKUPS: 'backups',
    USER_DATA: 'userData'
} as const;

/**
 * محددات CSS لمانع الإعلانات
 * Ad blocker CSS selectors
 */
export const AD_BLOCKER_SELECTORS = [
    '.ytd-display-ad-renderer',
    '.ytd-promoted-sparkles-web-renderer',
    '.ytd-ad-slot-renderer',
    '.ytd-in-feed-ad-layout-renderer',
    '.ytd-banner-promo-renderer',
    '.ytd-video-masthead-ad-v3-renderer',
    '.ytd-primetime-promo-renderer',
    '.ytp-ad-module',
    '.ytp-ad-overlay-container',
    '.ytp-ad-text-overlay',
    '.ytp-ad-player-overlay',
    '.video-ads',
    '.masthead-ad',
    '.promoted-videos',
    '.ad-container',
    '.advertisement',
    '[class*="ad-"]',
    '[id*="ad-"]',
    '[class*="ads-"]',
    '[id*="ads-"]'
] as const;

/**
 * نطاقات مانع الإعلانات
 * Ad blocker domains
 */
export const AD_BLOCKER_DOMAINS = [
    'doubleclick.net',
    'googleadservices.com',
    'googlesyndication.com',
    'googletagmanager.com',
    'googletagservices.com',
    'google-analytics.com',
    'adsystem.com',
    'amazon-adsystem.com',
    'facebook.com/tr',
    'connect.facebook.net',
    'scorecardresearch.com',
    'outbrain.com',
    'taboola.com',
    'adsafeprotected.com',
    'moatads.com',
    'adsymptotic.com'
] as const;
