/**
 * معالجة أزرار النماذج في الوضع المظلم
 * Form button processing for dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeConfig } from './dark-mode-config';

/**
 * فئة معالجة أزرار النماذج
 * Form button processing class
 */
export class DarkModeObserverOperationsAdvancedElementsGenericFormsButtons {

    /** معالجة أزرار النموذج / Process form buttons */
    public static processFormButtons(element: HTMLFormElement, config: DarkModeConfig): void {
        const buttons = element.querySelectorAll('button, input[type="submit"], input[type="button"], input[type="reset"]');
        
        buttons.forEach(button => {
            this.styleFormButton(button as HTMLButtonElement);
        });
    }

    /** تنسيق زر النموذج / Style form button */
    public static styleFormButton(button: HTMLButtonElement): void {
        const buttonType = this.getButtonType(button);
        
        // الأنماط الأساسية
        const baseStyles = {
            backgroundColor: '#4da6ff',
            color: '#ffffff',
            border: 'none',
            borderRadius: '6px',
            padding: '10px 20px',
            fontSize: '14px',
            fontWeight: 'bold',
            cursor: 'pointer',
            transition: 'all 0.3s ease'
        };

        Object.assign(button.style, baseStyles);

        // أنماط خاصة حسب نوع الزر
        this.applyButtonTypeStyles(button, buttonType);

        // إضافة تأثيرات التفاعل
        this.addButtonInteractionEffects(button, buttonType);
    }

    /** تحديد نوع الزر / Get button type */
    public static getButtonType(button: HTMLButtonElement): string {
        const type = button.type || 'button';
        const className = button.className.toLowerCase();
        const text = button.textContent?.toLowerCase() || '';

        if (type === 'submit' || className.includes('submit') || text.includes('إرسال') || text.includes('submit')) {
            return 'submit';
        }
        
        if (type === 'reset' || className.includes('reset') || text.includes('إعادة تعيين') || text.includes('reset')) {
            return 'reset';
        }
        
        if (className.includes('cancel') || text.includes('إلغاء') || text.includes('cancel')) {
            return 'cancel';
        }
        
        if (className.includes('danger') || className.includes('delete') || text.includes('حذف') || text.includes('delete')) {
            return 'danger';
        }

        return 'default';
    }

    /** تطبيق أنماط حسب نوع الزر / Apply button type styles */
    public static applyButtonTypeStyles(button: HTMLButtonElement, type: string): void {
        switch (type) {
            case 'submit':
                button.style.backgroundColor = '#28a745';
                break;
            case 'reset':
                button.style.backgroundColor = '#6c757d';
                break;
            case 'cancel':
                button.style.backgroundColor = '#6c757d';
                break;
            case 'danger':
                button.style.backgroundColor = '#dc3545';
                break;
            default:
                button.style.backgroundColor = '#4da6ff';
                break;
        }
    }

    /** إضافة تأثيرات التفاعل للزر / Add button interaction effects */
    public static addButtonInteractionEffects(button: HTMLButtonElement, type: string): void {
        const originalColor = button.style.backgroundColor;

        button.addEventListener('mouseenter', () => {
            button.style.transform = 'translateY(-2px)';
            button.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
            button.style.filter = 'brightness(1.1)';
        });

        button.addEventListener('mouseleave', () => {
            button.style.transform = 'translateY(0)';
            button.style.boxShadow = 'none';
            button.style.filter = 'brightness(1)';
        });

        button.addEventListener('mousedown', () => {
            button.style.transform = 'translateY(1px)';
        });

        button.addEventListener('mouseup', () => {
            button.style.transform = 'translateY(-2px)';
        });
    }

    /** إضافة أيقونات للأزرار / Add icons to buttons */
    public static addButtonIcons(button: HTMLButtonElement, type: string): void {
        const iconMap: { [key: string]: string } = {
            'submit': '✓',
            'reset': '↻',
            'cancel': '✕',
            'danger': '⚠',
            'default': '→'
        };

        const icon = iconMap[type] || iconMap['default'];
        
        // إضافة الأيقونة قبل النص
        const iconSpan = document.createElement('span');
        iconSpan.textContent = icon + ' ';
        iconSpan.style.marginRight = '4px';
        
        button.insertBefore(iconSpan, button.firstChild);
    }

    /** إضافة تأثيرات تحميل للأزرار / Add loading effects to buttons */
    public static addLoadingEffect(button: HTMLButtonElement): void {
        const originalText = button.textContent || '';
        const originalDisabled = button.disabled;

        // إضافة حالة التحميل
        button.textContent = 'جاري التحميل...';
        button.disabled = true;
        button.style.opacity = '0.7';
        button.style.cursor = 'not-allowed';

        // إضافة أنيميشن دوران
        const spinner = document.createElement('span');
        spinner.textContent = '⟳';
        spinner.style.animation = 'spin 1s linear infinite';
        spinner.style.marginRight = '8px';
        
        // إضافة CSS للأنيميشن
        if (!document.querySelector('#button-spinner-style')) {
            const style = document.createElement('style');
            style.id = 'button-spinner-style';
            style.textContent = `
                @keyframes spin {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }

        button.textContent = '';
        button.appendChild(spinner);
        button.appendChild(document.createTextNode('جاري التحميل...'));

        // إرجاع دالة لإزالة التأثير
        return () => {
            button.textContent = originalText;
            button.disabled = originalDisabled;
            button.style.opacity = '1';
            button.style.cursor = 'pointer';
        };
    }

    /** إضافة تأثيرات نجاح للأزرار / Add success effects to buttons */
    public static addSuccessEffect(button: HTMLButtonElement, duration: number = 2000): void {
        const originalBackground = button.style.backgroundColor;
        const originalText = button.textContent || '';

        // تطبيق تأثير النجاح
        button.style.backgroundColor = '#28a745';
        button.textContent = '✓ تم بنجاح';
        button.style.transform = 'scale(1.05)';

        // إرجاع الحالة الأصلية بعد المدة المحددة
        setTimeout(() => {
            button.style.backgroundColor = originalBackground;
            button.textContent = originalText;
            button.style.transform = 'scale(1)';
        }, duration);
    }

    /** إضافة تأثيرات خطأ للأزرار / Add error effects to buttons */
    public static addErrorEffect(button: HTMLButtonElement, duration: number = 2000): void {
        const originalBackground = button.style.backgroundColor;
        const originalText = button.textContent || '';

        // تطبيق تأثير الخطأ
        button.style.backgroundColor = '#dc3545';
        button.textContent = '✕ حدث خطأ';
        button.style.animation = 'shake 0.5s ease-in-out';

        // إضافة CSS للاهتزاز
        if (!document.querySelector('#button-shake-style')) {
            const style = document.createElement('style');
            style.id = 'button-shake-style';
            style.textContent = `
                @keyframes shake {
                    0%, 100% { transform: translateX(0); }
                    25% { transform: translateX(-5px); }
                    75% { transform: translateX(5px); }
                }
            `;
            document.head.appendChild(style);
        }

        // إرجاع الحالة الأصلية بعد المدة المحددة
        setTimeout(() => {
            button.style.backgroundColor = originalBackground;
            button.textContent = originalText;
            button.style.animation = 'none';
        }, duration);
    }
}
