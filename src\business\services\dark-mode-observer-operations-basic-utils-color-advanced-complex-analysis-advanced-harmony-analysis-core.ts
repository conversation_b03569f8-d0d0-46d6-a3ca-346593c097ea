/**
 * تحليل توافق الألوان الأساسي الأساسي المتقدم المعقد المتقدم
 * Advanced complex advanced basic core color harmony analysis
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeObserverOperationsBasicUtilsColorCore } from './dark-mode-observer-operations-basic-utils-color-core';
import { DarkModeObserverOperationsBasicUtilsColorAdvancedBasic } from './dark-mode-observer-operations-basic-utils-color-advanced-basic';
import { DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic } from './dark-mode-observer-operations-basic-utils-color-advanced-complex-analysis-basic';

/**
 * فئة تحليل توافق الألوان الأساسي الأساسي المتقدم المعقد المتقدم
 * Advanced complex advanced basic core color harmony analysis class
 */
export class DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisAdvancedHarmonyAnalysisCore {

    /** تحليل توافق الألوان الأساسي / Basic color harmony analysis */
    public static analyzeColorHarmony(colors: string[]): {
        harmonyType: 'monochromatic' | 'analogous' | 'complementary' | 'triadic' | 'tetradic' | 'mixed';
        score: number;
        recommendation: string;
    } {
        if (colors.length < 2) {
            return {
                harmonyType: 'monochromatic',
                score: 100,
                recommendation: 'لون واحد فقط'
            };
        }

        // تحليل الألوان وتحويلها إلى HSL
        const hslColors = colors.map(color => {
            const analysis = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(color);
            return analysis.hsl;
        }).filter(hsl => hsl !== null);

        if (hslColors.length < 2) {
            return {
                harmonyType: 'mixed',
                score: 50,
                recommendation: 'لا يمكن تحليل الألوان'
            };
        }

        // حساب الفروق في الصبغة
        const hues = hslColors.map(hsl => hsl!.h);
        const hueDifferences = [];
        
        for (let i = 0; i < hues.length - 1; i++) {
            for (let j = i + 1; j < hues.length; j++) {
                let diff = Math.abs(hues[i] - hues[j]);
                if (diff > 180) diff = 360 - diff; // أقصر مسافة على دائرة الألوان
                hueDifferences.push(diff);
            }
        }

        // تحديد نوع التوافق
        let harmonyType: 'monochromatic' | 'analogous' | 'complementary' | 'triadic' | 'tetradic' | 'mixed';
        let score = 0;
        let recommendation = '';

        const maxDiff = Math.max(...hueDifferences);
        const minDiff = Math.min(...hueDifferences);
        const avgDiff = hueDifferences.reduce((sum, diff) => sum + diff, 0) / hueDifferences.length;

        if (maxDiff <= 30) {
            // أحادي اللون
            harmonyType = 'monochromatic';
            score = 90;
            recommendation = 'توافق أحادي ممتاز';
        } else if (maxDiff <= 60) {
            // متشابه
            harmonyType = 'analogous';
            score = 85;
            recommendation = 'توافق متشابه جيد';
        } else if (Math.abs(maxDiff - 180) <= 30) {
            // مكمل
            harmonyType = 'complementary';
            score = 80;
            recommendation = 'توافق مكمل قوي';
        } else if (Math.abs(maxDiff - 120) <= 30 || colors.length === 3) {
            // ثلاثي
            harmonyType = 'triadic';
            score = 75;
            recommendation = 'توافق ثلاثي متوازن';
        } else if (colors.length === 4 && avgDiff >= 80 && avgDiff <= 100) {
            // رباعي
            harmonyType = 'tetradic';
            score = 70;
            recommendation = 'توافق رباعي معقد';
        } else {
            // مختلط
            harmonyType = 'mixed';
            score = Math.max(30, 80 - (avgDiff - 60) / 2);
            recommendation = 'توافق مختلط يحتاج تحسين';
        }

        // تعديل النقاط بناءً على التشبع والسطوع
        const saturations = hslColors.map(hsl => hsl!.s);
        const lightnesses = hslColors.map(hsl => hsl!.l);
        
        const saturationVariance = this.calculateVariance(saturations);
        const lightnessVariance = this.calculateVariance(lightnesses);

        // تحسين النقاط إذا كان هناك تنوع جيد في التشبع والسطوع
        if (saturationVariance > 200) score += 5;
        if (lightnessVariance > 300) score += 5;

        // تقليل النقاط إذا كان التنوع قليل جداً
        if (saturationVariance < 50) score -= 10;
        if (lightnessVariance < 100) score -= 10;

        return {
            harmonyType,
            score: Math.max(0, Math.min(100, Math.round(score))),
            recommendation
        };
    }

    /** حساب التباين / Calculate variance */
    public static calculateVariance(values: number[]): number {
        if (values.length === 0) return 0;
        
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
        return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
    }

    /** تحليل التوزيع اللوني / Analyze color distribution */
    public static analyzeColorDistribution(colors: string[]): {
        hueDistribution: number[];
        saturationDistribution: number[];
        lightnessDistribution: number[];
        balance: {
            hue: number;
            saturation: number;
            lightness: number;
        };
    } {
        if (colors.length === 0) {
            return {
                hueDistribution: [],
                saturationDistribution: [],
                lightnessDistribution: [],
                balance: { hue: 0, saturation: 0, lightness: 0 }
            };
        }

        const analyses = colors.map(color => 
            DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(color)
        );

        const validAnalyses = analyses.filter(a => a.hsl !== null);
        
        if (validAnalyses.length === 0) {
            return {
                hueDistribution: [],
                saturationDistribution: [],
                lightnessDistribution: [],
                balance: { hue: 0, saturation: 0, lightness: 0 }
            };
        }

        const hues = validAnalyses.map(a => a.hsl!.h);
        const saturations = validAnalyses.map(a => a.hsl!.s);
        const lightnesses = validAnalyses.map(a => a.hsl!.l);

        // حساب التوزيع
        const hueDistribution = this.calculateDistribution(hues, 360, 12); // 12 قطاع
        const saturationDistribution = this.calculateDistribution(saturations, 100, 10); // 10 مستويات
        const lightnessDistribution = this.calculateDistribution(lightnesses, 100, 10); // 10 مستويات

        // حساب التوازن
        const hueBalance = this.calculateBalance(hueDistribution);
        const saturationBalance = this.calculateBalance(saturationDistribution);
        const lightnessBalance = this.calculateBalance(lightnessDistribution);

        return {
            hueDistribution,
            saturationDistribution,
            lightnessDistribution,
            balance: {
                hue: hueBalance,
                saturation: saturationBalance,
                lightness: lightnessBalance
            }
        };
    }

    /** حساب التوزيع / Calculate distribution */
    private static calculateDistribution(values: number[], max: number, bins: number): number[] {
        const distribution = new Array(bins).fill(0);
        const binSize = max / bins;

        values.forEach(value => {
            const binIndex = Math.min(Math.floor(value / binSize), bins - 1);
            distribution[binIndex]++;
        });

        return distribution;
    }

    /** حساب التوازن / Calculate balance */
    private static calculateBalance(distribution: number[]): number {
        if (distribution.length === 0) return 0;

        const total = distribution.reduce((sum, count) => sum + count, 0);
        if (total === 0) return 0;

        // حساب الانتروبيا كمقياس للتوازن
        let entropy = 0;
        distribution.forEach(count => {
            if (count > 0) {
                const probability = count / total;
                entropy -= probability * Math.log2(probability);
            }
        });

        // تحويل الانتروبيا إلى نقاط من 0 إلى 100
        const maxEntropy = Math.log2(distribution.length);
        return Math.round((entropy / maxEntropy) * 100);
    }
}
