/**
 * أنواع تكوين الوضع المظلم
 * Dark mode configuration types
 * 
 * هذا الملف يحتوي على تعريفات أنواع التكوين للوضع المظلم
 * This file contains configuration type definitions for dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * واجهة تكوين الوضع المظلم
 * Dark mode configuration interface
 */
export interface DarkModeConfig {
    readonly enableAutoDetection: boolean;
    readonly enableCustomStyles: boolean;
    readonly enableTransitions: boolean;
    readonly transitionDuration: number;
    readonly observeChanges: boolean;
    readonly applyToIframes: boolean;
    readonly preserveImages: boolean;
    readonly customThemeColors: DarkModeThemeColors;
    readonly performanceMode: boolean;
    readonly debugMode: boolean;
}

/**
 * واجهة ألوان الثيم المظلم
 * Dark mode theme colors interface
 */
export interface DarkModeThemeColors {
    readonly backgroundColor: string;
    readonly textColor: string;
    readonly linkColor: string;
    readonly borderColor: string;
    readonly buttonColor: string;
    readonly inputColor: string;
    readonly headerColor: string;
    readonly sidebarColor: string;
    readonly accentColor: string;
    readonly surfaceColor: string;
}

/**
 * أنواع الثيمات المدعومة
 * Supported theme types
 */
export enum ThemeType {
    LIGHT = 'light',
    DARK = 'dark',
    AUTO = 'auto',
    CUSTOM = 'custom',
    HIGH_CONTRAST = 'high_contrast'
}

/**
 * مستويات الأداء
 * Performance levels
 */
export enum PerformanceLevel {
    LOW = 'low',
    MEDIUM = 'medium',
    HIGH = 'high',
    ULTRA = 'ultra'
}

/**
 * واجهة معلومات الثيم
 * Theme information interface
 */
export interface ThemeInfo {
    readonly type: ThemeType;
    readonly name: string;
    readonly description: string;
    readonly colors: DarkModeThemeColors;
    readonly isCustom: boolean;
    readonly version: string;
}

/**
 * واجهة إعدادات الأداء
 * Performance settings interface
 */
export interface PerformanceSettings {
    readonly level: PerformanceLevel;
    readonly enableTransitions: boolean;
    readonly enableAnimations: boolean;
    readonly enableShadows: boolean;
    readonly enableGradients: boolean;
    readonly maxStyleRules: number;
    readonly debounceDelay: number;
}

/**
 * واجهة إعدادات المراقبة
 * Monitoring settings interface
 */
export interface DarkModeMonitoringSettings {
    readonly enabled: boolean;
    readonly watchSelectors: string[];
    readonly debounceDelay: number;
    readonly maxMutationsPerBatch: number;
    readonly enablePerformanceMonitoring: boolean;
    readonly logChanges: boolean;
}

/**
 * واجهة معلومات التوافق
 * Compatibility information interface
 */
export interface DarkModeCompatibility {
    readonly isSupported: boolean;
    readonly supportedFeatures: string[];
    readonly missingFeatures: string[];
    readonly browserVersion: string;
    readonly recommendedFallback: ThemeType;
}

/**
 * واجهة إعدادات التصدير/الاستيراد
 * Export/Import settings interface
 */
export interface DarkModeExportSettings {
    readonly includeCustomColors: boolean;
    readonly includePerformanceSettings: boolean;
    readonly includeCompatibilityInfo: boolean;
    readonly format: 'json' | 'css' | 'scss';
    readonly minify: boolean;
}

/**
 * واجهة خيارات الاستيراد
 * Import options interface
 */
export interface DarkModeImportOptions {
    readonly validateConfig: boolean;
    readonly mergeWithExisting: boolean;
    readonly applyImmediately: boolean;
    readonly backupCurrent: boolean;
}

/**
 * نوع خيارات التكوين
 * Configuration options type
 */
export type DarkModeConfigOptions = Partial<DarkModeConfig>;

/**
 * نوع محدد الألوان
 * Color selector type
 */
export type ColorSelector = keyof DarkModeThemeColors;

/**
 * نوع قيمة اللون
 * Color value type
 */
export type ColorValue = string;

/**
 * نوع خريطة الألوان
 * Color map type
 */
export type ColorMap = Record<ColorSelector, ColorValue>;
