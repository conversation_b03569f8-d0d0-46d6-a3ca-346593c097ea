/**
 * العمليات المتقدمة لمراقب الوضع المظلم
 * Advanced operations for dark mode observer
 *
 * هذا الملف يجمع جميع العمليات المتقدمة من الملفات المتخصصة
 * This file aggregates all advanced operations from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeConfig } from './dark-mode-config';

// استيراد الفئات المتخصصة / Import specialized classes
import { DarkModeObserverOperationsAdvancedDom } from './dark-mode-observer-operations-advanced-dom';
import { DarkModeObserverOperationsAdvancedElements } from './dark-mode-observer-operations-advanced-elements';

/**
 * فئة العمليات المتقدمة لمراقب الوضع المظلم
 * Advanced dark mode observer operations class
 */
export class DarkModeObserverOperationsAdvanced {
    private readonly config: DarkModeConfig;
    private readonly domOperations: DarkModeObserverOperationsAdvancedDom;
    private readonly elementOperations: DarkModeObserverOperationsAdvancedElements;

    constructor(config: DarkModeConfig) {
        this.config = config;
        this.domOperations = new DarkModeObserverOperationsAdvancedDom(config);
        this.elementOperations = new DarkModeObserverOperationsAdvancedElements(config);
    }

    /** معالجة العقد المضافة / Process added nodes */
    public processAddedNodes(nodes: NodeList): void {
        this.domOperations.processAddedNodes(nodes);
    }

    /** معالجة العقد المحذوفة / Process removed nodes */
    public processRemovedNodes(nodes: NodeList): void {
        this.domOperations.processRemovedNodes(nodes);
    }

    /** معالجة التغييرات في الخصائص / Process attribute changes */
    public processAttributeChanges(target: Element, attributeName: string): void {
        this.domOperations.processAttributeChanges(target, attributeName);
    }


}
