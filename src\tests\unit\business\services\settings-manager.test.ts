/**
 * اختبارات وحدة مدير الإعدادات
 * Settings manager unit tests
 * 
 * هذا الملف يحتوي على اختبارات شاملة لمدير الإعدادات
 * This file contains comprehensive tests for settings manager
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { SettingsManager } from '@business/services/settings-manager';
import { ApplicationConfig, ValidationResult } from '@shared/types';
import * as fs from 'fs';
import * as path from 'path';

// Mock fs module
jest.mock('fs');
const mockFs = fs as jest.Mocked<typeof fs>;

describe('SettingsManager', () => {
    let settingsManager: SettingsManager;
    const mockConfigPath = path.join(process.cwd(), 'config', 'settings.json');

    beforeEach(() => {
        settingsManager = new SettingsManager();
        jest.clearAllMocks();
    });

    describe('Initialization', () => {
        it('should create default settings if file does not exist', () => {
            // Arrange
            mockFs.existsSync.mockReturnValue(false);
            mockFs.mkdirSync.mockImplementation();
            mockFs.writeFileSync.mockImplementation();

            // Act
            const settings = settingsManager.getAllSettings();

            // Assert
            expect(settings).toBeDefined();
            expect(settings.videoQuality).toBe('auto');
            expect(settings.darkModeEnabled).toBe(true);
            expect(settings.adBlockEnabled).toBe(true);
            expect(mockFs.mkdirSync).toHaveBeenCalled();
            expect(mockFs.writeFileSync).toHaveBeenCalled();
        });

        it('should load existing settings from file', () => {
            // Arrange
            const existingSettings: ApplicationConfig = {
                videoQuality: '720p',
                darkModeEnabled: false,
                adBlockEnabled: false,
                autoApplySettings: true,
                windowBounds: { width: 1200, height: 800 }
            };

            mockFs.existsSync.mockReturnValue(true);
            mockFs.readFileSync.mockReturnValue(JSON.stringify(existingSettings));

            // Act
            const settings = settingsManager.getAllSettings();

            // Assert
            expect(settings.videoQuality).toBe('720p');
            expect(settings.darkModeEnabled).toBe(false);
            expect(settings.adBlockEnabled).toBe(false);
        });

        it('should handle corrupted settings file', () => {
            // Arrange
            mockFs.existsSync.mockReturnValue(true);
            mockFs.readFileSync.mockReturnValue('invalid json');
            mockFs.writeFileSync.mockImplementation();

            // Act
            const settings = settingsManager.getAllSettings();

            // Assert
            expect(settings.videoQuality).toBe('auto'); // Default values
            expect(mockFs.writeFileSync).toHaveBeenCalled(); // Should recreate file
        });
    });

    describe('Settings Validation', () => {
        it('should validate video quality setting', () => {
            // Act
            const validResult = settingsManager.updateSetting('videoQuality', '720p');
            const invalidResult = settingsManager.updateSetting('videoQuality', 'invalid');

            // Assert
            expect(validResult.isValid).toBe(true);
            expect(invalidResult.isValid).toBe(false);
            expect(invalidResult.errors).toHaveLength(1);
            expect(invalidResult.errors[0].field).toBe('videoQuality');
        });

        it('should validate boolean settings', () => {
            // Act
            const validResult = settingsManager.updateSetting('darkModeEnabled', true);
            const invalidResult = settingsManager.updateSetting('darkModeEnabled', 'invalid' as any);

            // Assert
            expect(validResult.isValid).toBe(true);
            expect(invalidResult.isValid).toBe(false);
            expect(invalidResult.errors[0].field).toBe('darkModeEnabled');
        });

        it('should validate window bounds', () => {
            // Act
            const validBounds = { width: 1200, height: 800 };
            const invalidBounds = { width: -100, height: 50 };

            const validResult = settingsManager.updateSetting('windowBounds', validBounds);
            const invalidResult = settingsManager.updateSetting('windowBounds', invalidBounds);

            // Assert
            expect(validResult.isValid).toBe(true);
            expect(invalidResult.isValid).toBe(false);
        });
    });

    describe('Settings Updates', () => {
        beforeEach(() => {
            mockFs.existsSync.mockReturnValue(false);
            mockFs.mkdirSync.mockImplementation();
            mockFs.writeFileSync.mockImplementation();
        });

        it('should update single setting', () => {
            // Act
            const result = settingsManager.updateSetting('videoQuality', '1080p');
            const settings = settingsManager.getAllSettings();

            // Assert
            expect(result.isValid).toBe(true);
            expect(settings.videoQuality).toBe('1080p');
            expect(mockFs.writeFileSync).toHaveBeenCalled();
        });

        it('should update multiple settings', () => {
            // Arrange
            const updates: Partial<ApplicationConfig> = {
                videoQuality: '720p',
                darkModeEnabled: false,
                adBlockEnabled: false
            };

            // Act
            const result = settingsManager.updateSettings(updates);
            const settings = settingsManager.getAllSettings();

            // Assert
            expect(result.isValid).toBe(true);
            expect(settings.videoQuality).toBe('720p');
            expect(settings.darkModeEnabled).toBe(false);
            expect(settings.adBlockEnabled).toBe(false);
        });

        it('should reject invalid multiple settings', () => {
            // Arrange
            const invalidUpdates = {
                videoQuality: 'invalid',
                darkModeEnabled: 'not-boolean' as any
            };

            // Act
            const result = settingsManager.updateSettings(invalidUpdates);

            // Assert
            expect(result.isValid).toBe(false);
            expect(result.errors).toHaveLength(2);
        });

        it('should not save invalid settings', () => {
            // Arrange
            const originalCallCount = mockFs.writeFileSync.mock.calls.length;

            // Act
            settingsManager.updateSetting('videoQuality', 'invalid');

            // Assert
            expect(mockFs.writeFileSync.mock.calls.length).toBe(originalCallCount);
        });
    });

    describe('Settings Reset', () => {
        it('should reset to default settings', () => {
            // Arrange
            mockFs.writeFileSync.mockImplementation();
            settingsManager.updateSetting('videoQuality', '1080p');
            settingsManager.updateSetting('darkModeEnabled', false);

            // Act
            const result = settingsManager.resetToDefaults();
            const settings = settingsManager.getAllSettings();

            // Assert
            expect(result.isValid).toBe(true);
            expect(settings.videoQuality).toBe('auto');
            expect(settings.darkModeEnabled).toBe(true);
            expect(mockFs.writeFileSync).toHaveBeenCalled();
        });
    });

    describe('Import/Export', () => {
        it('should export settings', () => {
            // Arrange
            settingsManager.updateSetting('videoQuality', '720p');

            // Act
            const exported = settingsManager.exportSettings();

            // Assert
            expect(exported).toContain('"videoQuality":"720p"');
            expect(() => JSON.parse(exported)).not.toThrow();
        });

        it('should import valid settings', () => {
            // Arrange
            const importData = JSON.stringify({
                videoQuality: '1080p',
                darkModeEnabled: false,
                adBlockEnabled: true,
                autoApplySettings: false,
                windowBounds: { width: 1400, height: 900 }
            });

            mockFs.writeFileSync.mockImplementation();

            // Act
            const result = settingsManager.importSettings(importData);
            const settings = settingsManager.getAllSettings();

            // Assert
            expect(result.isValid).toBe(true);
            expect(settings.videoQuality).toBe('1080p');
            expect(settings.darkModeEnabled).toBe(false);
        });

        it('should reject invalid import data', () => {
            // Act
            const invalidJsonResult = settingsManager.importSettings('invalid json');
            const invalidSettingsResult = settingsManager.importSettings(JSON.stringify({
                videoQuality: 'invalid',
                darkModeEnabled: 'not-boolean'
            }));

            // Assert
            expect(invalidJsonResult.isValid).toBe(false);
            expect(invalidSettingsResult.isValid).toBe(false);
        });
    });

    describe('File Operations', () => {
        it('should handle file write errors', () => {
            // Arrange
            mockFs.writeFileSync.mockImplementation(() => {
                throw new Error('Write failed');
            });

            // Act
            const result = settingsManager.updateSetting('videoQuality', '720p');

            // Assert
            expect(result.isValid).toBe(false);
            expect(result.errors[0].message).toContain('Write failed');
        });

        it('should handle directory creation errors', () => {
            // Arrange
            mockFs.existsSync.mockReturnValue(false);
            mockFs.mkdirSync.mockImplementation(() => {
                throw new Error('Directory creation failed');
            });

            // Act & Assert
            expect(() => new SettingsManager()).not.toThrow();
        });
    });

    describe('Edge Cases', () => {
        it('should handle empty settings object', () => {
            // Act
            const result = settingsManager.updateSettings({});

            // Assert
            expect(result.isValid).toBe(true);
        });

        it('should handle null/undefined values', () => {
            // Act
            const result = settingsManager.updateSetting('videoQuality', null as any);

            // Assert
            expect(result.isValid).toBe(false);
        });

        it('should maintain settings integrity after failed update', () => {
            // Arrange
            const originalSettings = settingsManager.getAllSettings();

            // Act
            settingsManager.updateSetting('videoQuality', 'invalid');
            const currentSettings = settingsManager.getAllSettings();

            // Assert
            expect(currentSettings).toEqual(originalSettings);
        });
    });
});
