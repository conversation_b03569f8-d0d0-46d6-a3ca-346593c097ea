/**
 * إحصائيات الملفات المتقدمة - ملف التفويض
 * Advanced file statistics operations - Delegation file
 * 
 * هذا الملف يفوض جميع العمليات للملفات المتخصصة
 * This file delegates all operations to specialized files
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsCore } from './simple-verification-core-utils-file-operations-basic-info-advanced-stats-core';
import { SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsGrouping } from './simple-verification-core-utils-file-operations-basic-info-advanced-stats-grouping';

/**
 * فئة إحصائيات الملفات المتقدمة - التفويض
 * Advanced file statistics class - Delegation
 */
export class SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStats {

    /**
     * الحصول على إحصائيات مجموعة ملفات - تفويض للوحدة الجوهرية
     * Get statistics for a group of files - Delegate to core module
     */
    public static getFilesStatistics(filePaths: string[]): {
        totalFiles: number;
        existingFiles: number;
        totalSize: number;
        averageSize: number;
        largestFile: string;
        largestSize: number;
        smallestFile: string;
        smallestSize: number;
        extensionCounts: Record<string, number>;
    } {
        // تفويض الحصول على الإحصائيات للوحدة الجوهرية
        // Delegate statistics retrieval to core module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsCore.getFilesStatistics(filePaths);
    }

    /**
     * فلترة الملفات حسب المعايير - تفويض للوحدة الجوهرية
     * Filter files by criteria - Delegate to core module
     */
    public static filterFilesByCriteria(
        filePaths: string[],
        criteria: {
            minSize?: number;
            maxSize?: number;
            extensions?: string[];
            modifiedAfter?: Date;
            modifiedBefore?: Date;
        }
    ): string[] {
        // تفويض فلترة الملفات للوحدة الجوهرية
        // Delegate file filtering to core module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsCore.filterFilesByCriteria(filePaths, criteria);
    }

    /**
     * ترتيب الملفات حسب الحجم - تفويض للوحدة الجوهرية
     * Sort files by size - Delegate to core module
     */
    public static sortFilesBySize(filePaths: string[], ascending: boolean = true): string[] {
        // تفويض ترتيب الملفات حسب الحجم للوحدة الجوهرية
        // Delegate file sorting by size to core module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsCore.sortFilesBySize(filePaths, ascending);
    }

    /**
     * ترتيب الملفات حسب تاريخ التعديل - تفويض للوحدة الجوهرية
     * Sort files by modification date - Delegate to core module
     */
    public static sortFilesByModificationDate(filePaths: string[], ascending: boolean = true): string[] {
        // تفويض ترتيب الملفات حسب تاريخ التعديل للوحدة الجوهرية
        // Delegate file sorting by modification date to core module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsCore.sortFilesByModificationDate(filePaths, ascending);
    }

    /**
     * تجميع الملفات حسب الامتداد - تفويض لوحدة التجميع
     * Group files by extension - Delegate to grouping module
     */
    public static groupFilesByExtension(filePaths: string[]): Record<string, string[]> {
        // تفويض تجميع الملفات حسب الامتداد لوحدة التجميع
        // Delegate file grouping by extension to grouping module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsGrouping.groupFilesByExtension(filePaths);
    }

    /**
     * تجميع الملفات حسب الحجم - تفويض لوحدة التجميع
     * Group files by size range - Delegate to grouping module
     */
    public static groupFilesBySizeRange(filePaths: string[]): {
        small: string[];    // < 1KB
        medium: string[];   // 1KB - 1MB
        large: string[];    // 1MB - 10MB
        huge: string[];     // > 10MB
    } {
        // تفويض تجميع الملفات حسب الحجم لوحدة التجميع
        // Delegate file grouping by size to grouping module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsGrouping.groupFilesBySizeRange(filePaths);
    }

    /**
     * تجميع الملفات حسب تاريخ التعديل - تفويض لوحدة التجميع
     * Group files by modification date range - Delegate to grouping module
     */
    public static groupFilesByModificationDateRange(filePaths: string[]): {
        today: string[];
        thisWeek: string[];
        thisMonth: string[];
        older: string[];
    } {
        // تفويض تجميع الملفات حسب تاريخ التعديل لوحدة التجميع
        // Delegate file grouping by modification date to grouping module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsGrouping.groupFilesByModificationDateRange(filePaths);
    }

    /**
     * تجميع الملفات حسب المجلد الأب - تفويض لوحدة التجميع
     * Group files by parent directory - Delegate to grouping module
     */
    public static groupFilesByParentDirectory(filePaths: string[]): Record<string, string[]> {
        // تفويض تجميع الملفات حسب المجلد الأب لوحدة التجميع
        // Delegate file grouping by parent directory to grouping module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsGrouping.groupFilesByParentDirectory(filePaths);
    }

    /**
     * تجميع الملفات حسب الصلاحيات - تفويض لوحدة التجميع
     * Group files by permissions - Delegate to grouping module
     */
    public static groupFilesByPermissions(filePaths: string[]): {
        readOnly: string[];
        writeOnly: string[];
        readWrite: string[];
        noAccess: string[];
    } {
        // تفويض تجميع الملفات حسب الصلاحيات لوحدة التجميع
        // Delegate file grouping by permissions to grouping module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsGrouping.groupFilesByPermissions(filePaths);
    }
}
