/**
 * أنواع تكوين الإعدادات
 * Settings configuration types
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQuality, WindowBounds } from '@shared/types';
import { SettingsErrorType, SettingsState, SettingsPriority, LogLevel } from './settings-config-constants';

/**
 * تكوين مدير الإعدادات / Settings manager configuration
 */
export interface SettingsManagerConfig {
    readonly storeFileName: string;
    readonly encryptionKey?: string;
    readonly enableBackup: boolean;
    readonly backupInterval: number;
    readonly maxBackups: number;
    readonly validateOnLoad: boolean;
    readonly autoSave: boolean;
    readonly saveDelay: number;
    readonly compressionEnabled?: boolean;
    readonly syncEnabled?: boolean;
    readonly logLevel?: LogLevel;
}

/**
 * تقرير خطأ الإعدادات / Settings error report
 */
export interface SettingsErrorReport {
    readonly errorType: SettingsErrorType;
    readonly message: string;
    readonly timestamp: Date;
    readonly key?: string;
    readonly details: Record<string, unknown>;
    readonly stack?: string;
    readonly severity?: SettingsPriority;
}

/**
 * نتيجة التحقق من الإعدادات / Settings validation result
 */
export interface SettingsValidationResult {
    readonly isValid: boolean;
    readonly errors: Array<{
        readonly key: string;
        readonly message: string;
        readonly code: string;
        readonly value?: unknown;
    }>;
    readonly warnings: Array<{
        readonly key: string;
        readonly message: string;
        readonly value?: unknown;
    }>;
    readonly validatedSettings?: Record<string, unknown>;
}

/**
 * معلومات تغيير الإعداد / Setting change info
 */
export interface SettingChangeInfo {
    readonly key: string;
    readonly oldValue: unknown;
    readonly newValue: unknown;
    readonly timestamp: Date;
    readonly source?: string;
    readonly priority?: SettingsPriority;
}

/**
 * معلومات النسخة الاحتياطية / Backup info
 */
export interface BackupInfo {
    readonly id: string;
    readonly name: string;
    readonly timestamp: Date;
    readonly size: number;
    readonly checksum: string;
    readonly version: string;
    readonly description?: string;
    readonly tags?: string[];
    readonly isAutomatic: boolean;
}

/**
 * خيارات النسخ الاحتياطي / Backup options
 */
export interface BackupOptions {
    readonly name?: string;
    readonly description?: string;
    readonly tags?: string[];
    readonly compress?: boolean;
    readonly encrypt?: boolean;
    readonly includeUserData?: boolean;
    readonly excludeKeys?: string[];
}

/**
 * خيارات الاستعادة / Restore options
 */
export interface RestoreOptions {
    readonly backupId: string;
    readonly overwriteExisting?: boolean;
    readonly validateBeforeRestore?: boolean;
    readonly createBackupBeforeRestore?: boolean;
    readonly excludeKeys?: string[];
    readonly dryRun?: boolean;
}

/**
 * إحصائيات الإعدادات / Settings statistics
 */
export interface SettingsStatistics {
    readonly totalSettings: number;
    readonly totalSize: number;
    readonly lastModified: Date;
    readonly lastBackup?: Date;
    readonly backupCount: number;
    readonly validationErrors: number;
    readonly changeCount: number;
    readonly averageChangeFrequency: number;
    readonly mostChangedSettings: Array<{
        readonly key: string;
        readonly changeCount: number;
    }>;
}

/**
 * حالة الإعدادات / Settings status
 */
export interface SettingsStatus {
    readonly state: SettingsState;
    readonly isLoaded: boolean;
    readonly isValid: boolean;
    readonly hasChanges: boolean;
    readonly lastSaved?: Date;
    readonly lastValidated?: Date;
    readonly errorCount: number;
    readonly warningCount: number;
}

/**
 * خيارات المزامنة / Sync options
 */
export interface SyncOptions {
    readonly enabled: boolean;
    readonly provider: 'local' | 'cloud' | 'p2p';
    readonly endpoint?: string;
    readonly credentials?: Record<string, string>;
    readonly interval: number;
    readonly conflictResolution: 'local' | 'remote' | 'merge' | 'ask';
    readonly encryptInTransit: boolean;
}

/**
 * خيارات التصدير / Export options
 */
export interface ExportOptions {
    readonly format: 'json' | 'yaml' | 'xml' | 'ini';
    readonly includeMetadata: boolean;
    readonly includeDefaults: boolean;
    readonly excludeKeys?: string[];
    readonly compress: boolean;
    readonly encrypt: boolean;
    readonly password?: string;
}

/**
 * خيارات الاستيراد / Import options
 */
export interface ImportOptions {
    readonly format: 'json' | 'yaml' | 'xml' | 'ini';
    readonly overwriteExisting: boolean;
    readonly validateBeforeImport: boolean;
    readonly createBackupBeforeImport: boolean;
    readonly excludeKeys?: string[];
    readonly password?: string;
    readonly dryRun: boolean;
}

/**
 * قاعدة التحقق / Validation rule
 */
export interface ValidationRule {
    readonly type: string;
    readonly required?: boolean;
    readonly min?: number;
    readonly max?: number;
    readonly pattern?: string;
    readonly allowedValues?: unknown[];
    readonly customValidator?: (value: unknown) => boolean;
    readonly errorMessage?: string;
}

/**
 * مخطط الإعدادات / Settings schema
 */
export interface SettingsSchema {
    readonly version: string;
    readonly properties: Record<string, ValidationRule>;
    readonly required?: string[];
    readonly additionalProperties?: boolean;
}

/**
 * معلومات الإعداد / Setting info
 */
export interface SettingInfo {
    readonly key: string;
    readonly value: unknown;
    readonly type: string;
    readonly isDefault: boolean;
    readonly lastModified: Date;
    readonly changeCount: number;
    readonly description?: string;
    readonly category?: string;
    readonly tags?: string[];
}

/**
 * فئة الإعدادات / Settings category
 */
export interface SettingsCategory {
    readonly id: string;
    readonly name: string;
    readonly description?: string;
    readonly icon?: string;
    readonly order: number;
    readonly settings: string[];
}

/**
 * ملف تعريف الإعدادات / Settings profile
 */
export interface SettingsProfile {
    readonly id: string;
    readonly name: string;
    readonly description?: string;
    readonly settings: Record<string, unknown>;
    readonly isDefault: boolean;
    readonly createdAt: Date;
    readonly updatedAt: Date;
}

/**
 * خيارات البحث / Search options
 */
export interface SearchOptions {
    readonly query: string;
    readonly searchInKeys: boolean;
    readonly searchInValues: boolean;
    readonly searchInDescriptions: boolean;
    readonly caseSensitive: boolean;
    readonly useRegex: boolean;
    readonly categories?: string[];
    readonly tags?: string[];
}

/**
 * نتيجة البحث / Search result
 */
export interface SearchResult {
    readonly key: string;
    readonly value: unknown;
    readonly matchType: 'key' | 'value' | 'description';
    readonly matchText: string;
    readonly category?: string;
    readonly relevanceScore: number;
}

/**
 * خيارات المراقبة / Monitoring options
 */
export interface MonitoringOptions {
    readonly enabled: boolean;
    readonly trackChanges: boolean;
    readonly trackAccess: boolean;
    readonly trackPerformance: boolean;
    readonly logLevel: LogLevel;
    readonly maxLogSize: number;
    readonly retentionDays: number;
}

/**
 * حدث الإعدادات / Settings event
 */
export interface SettingsEvent {
    readonly type: string;
    readonly timestamp: Date;
    readonly data: Record<string, unknown>;
    readonly source?: string;
    readonly priority: SettingsPriority;
}

/**
 * مستمع الأحداث / Event listener
 */
export type SettingsEventListener = (event: SettingsEvent) => void;

/**
 * مدير الأحداث / Event manager
 */
export interface SettingsEventManager {
    addEventListener(type: string, listener: SettingsEventListener): void;
    removeEventListener(type: string, listener: SettingsEventListener): void;
    dispatchEvent(event: SettingsEvent): void;
    clearEventListeners(type?: string): void;
}
