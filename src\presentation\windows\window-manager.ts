/**
 * مدير النوافذ للتطبيق
 * Application window manager
 *
 * هذا الملف يحتوي على منطق إدارة النوافذ في التطبيق
 * This file contains window management logic for the application
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 *
 * @requires electron للواجهة الرسومية
 * @requires path للتعامل مع المسارات
 * @requires @shared/types لتعريفات الأنواع
 * @requires @shared/constants للثوابت
 */

// إعادة تصدير المكونات المتخصصة
// Re-export specialized components
export * from './window-configuration';
export * from './window-operations';

import { BASE_URLS, FILE_PATHS } from '@shared/constants';
import { ApplicationConfig, WindowBounds, WindowInfo } from '@shared/types';
import { BrowserWindow } from 'electron';
import * as path from 'path';
import { WindowOperations } from './window-operations';

/**
 * مدير النوافذ
 * Window manager class
 */
export class WindowManager {
    private mainWindow: BrowserWindow | null = null;
    private settingsWindow: BrowserWindow | null = null;
    private readonly windowOperations: WindowOperations;

    /**
     * منشئ مدير النوافذ
     * Window manager constructor
     */
    constructor() {
        this.windowOperations = new WindowOperations();
    }

    /**
     * إنشاء النافذة الرئيسية للتطبيق
     * Creates the main application window
     */
    public async createMainWindow(config: ApplicationConfig): Promise<BrowserWindow> {
        try {
            const windowConfig = WindowConfiguration.getMainWindowConfig(config);
            this.mainWindow = this.windowOperations.createWindow(windowConfig);
            await this.mainWindow.loadURL(BASE_URLS.YOUTUBE);

            this.mainWindow.once('ready-to-show', () => {
                if (this.mainWindow) {
                    this.mainWindow.show();
                }
            });

            return this.mainWindow;
        } catch (error) {
            throw new Error(`فشل في إنشاء النافذة الرئيسية: ${error}`);
        }
    }

    /**
     * إنشاء نافذة الإعدادات
     * Creates the settings window
     */
    public async createSettingsWindow(): Promise<BrowserWindow> {
        if (this.settingsWindow) {
            this.windowOperations.focusWindow(this.settingsWindow.id);
            return this.settingsWindow;
        }

        if (!this.mainWindow) {
            throw new Error('النافذة الرئيسية غير موجودة');
        }

        try {
            const windowConfig = WindowConfiguration.getSettingsWindowConfig();
            windowConfig.parent = this.mainWindow;
            this.settingsWindow = this.windowOperations.createWindow(windowConfig);
            await this.settingsWindow.loadFile(path.join(__dirname, FILE_PATHS.SETTINGS_HTML));

            this.settingsWindow.on('closed', () => {
                this.settingsWindow = null;
            });

            return this.settingsWindow;
        } catch (error) {
            throw new Error(`فشل في إنشاء نافذة الإعدادات: ${error}`);
        }
    }

    /**
     * الحصول على النافذة الرئيسية / Gets the main window
     */
    public getMainWindow(): BrowserWindow | null {
        return this.mainWindow;
    }

    /**
     * الحصول على نافذة الإعدادات / Gets the settings window
     */
    public getSettingsWindow(): BrowserWindow | null {
        return this.settingsWindow;
    }

    /**
     * الحصول على جميع النوافذ / Gets all windows
     */
    public getAllWindows(): WindowInfo[] {
        return this.windowOperations.getAllWindows();
    }

    /**
     * إغلاق جميع النوافذ / Closes all windows
     */
    public async closeAllWindows(): Promise<void> {
        try {
            if (this.settingsWindow) {
                this.windowOperations.closeWindow(this.settingsWindow.id);
                this.settingsWindow = null;
            }

            if (this.mainWindow) {
                this.windowOperations.closeWindow(this.mainWindow.id);
                this.mainWindow = null;
            }

            this.windowOperations.cleanup();
        } catch (error) {
            console.error('خطأ في إغلاق النوافذ:', error);
        }
    }

    /**
     * حفظ حدود النافذة / Saves window bounds
     */
    public saveWindowBounds(window: BrowserWindow): WindowBounds {
        if (!window) {
            throw new Error('النافذة غير موجودة');
        }

        const bounds = window.getBounds();
        return {
            width: bounds.width,
            height: bounds.height,
            x: bounds.x,
            y: bounds.y
        };
    }

    /**
     * تركيز نافذة / Focus window
     */
    public focusWindow(windowId: number): boolean {
        return this.windowOperations.focusWindow(windowId);
    }

    /**
     * تصغير نافذة / Minimize window
     */
    public minimizeWindow(windowId: number): boolean {
        return this.windowOperations.minimizeWindow(windowId);
    }

    /**
     * تكبير نافذة / Maximize window
     */
    public maximizeWindow(windowId: number): boolean {
        return this.windowOperations.maximizeWindow(windowId);
    }

    /**
     * تغيير حجم النافذة / Resize window
     */
    public resizeWindow(windowId: number, bounds: WindowBounds): boolean {
        return this.windowOperations.resizeWindow(windowId, bounds);
    }

    /**
     * تنظيف الموارد / Cleanup resources
     */
    public cleanup(): void {
        this.windowOperations.cleanup();
    }
}
