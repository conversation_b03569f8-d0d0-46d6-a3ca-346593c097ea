/**
 * معالجة الملفات المجمعة المتقدمة
 * Advanced batch file processing operations
 * 
 * هذا الملف يحتوي على عمليات معالجة الملفات المجمعة المتقدمة
 * This file contains advanced batch file processing operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import * as fs from 'fs';
import * as path from 'path';
import { SimpleVerificationConfig } from './simple-verification-types';
import { SimpleVerificationCoreUtilsFileOperationsAdvancedSingle } from './simple-verification-core-utils-file-operations-advanced-single';

/**
 * فئة معالجة الملفات المجمعة المتقدمة
 * Advanced batch file processing class
 */
export class SimpleVerificationCoreUtilsFileOperationsAdvancedBatch {

    /**
     * فحص ملفات متعددة
     * Check multiple files
     */
    public static checkMultipleFiles(filePaths: string[], config: SimpleVerificationConfig): {
        totalFiles: number;
        validFiles: number;
        invalidFiles: number;
        totalIssues: number;
        averageScore: number;
        results: Array<{
            filePath: string;
            isValid: boolean;
            issues: string[];
            score: number;
            lineCount: number;
        }>;
    } {
        const results: Array<{
            filePath: string;
            isValid: boolean;
            issues: string[];
            score: number;
            lineCount: number;
        }> = [];

        let validFiles = 0;
        let totalIssues = 0;
        let totalScore = 0;

        for (const filePath of filePaths) {
            try {
                const result = SimpleVerificationCoreUtilsFileOperationsAdvancedSingle.checkSingleFile(filePath, config);
                
                results.push({
                    filePath,
                    isValid: result.isValid,
                    issues: result.issues,
                    score: result.score,
                    lineCount: result.lineCount
                });

                if (result.isValid) {
                    validFiles++;
                }

                totalIssues += result.issues.length;
                totalScore += result.score;

            } catch (error) {
                results.push({
                    filePath,
                    isValid: false,
                    issues: [`Error processing file: ${error}`],
                    score: 0,
                    lineCount: 0
                });
                totalIssues++;
            }
        }

        return {
            totalFiles: filePaths.length,
            validFiles,
            invalidFiles: filePaths.length - validFiles,
            totalIssues,
            averageScore: filePaths.length > 0 ? Math.round(totalScore / filePaths.length) : 0,
            results
        };
    }

    /**
     * تحليل أنماط الملفات
     * Analyze file patterns
     */
    public static analyzeFilePatterns(filePaths: string[]): {
        extensionCounts: Record<string, number>;
        sizeCounts: {
            small: number;    // < 50 lines
            medium: number;   // 50-200 lines
            large: number;    // > 200 lines
        };
        namingPatterns: {
            kebabCase: number;
            camelCase: number;
            pascalCase: number;
            snakeCase: number;
            other: number;
        };
    } {
        const extensionCounts: Record<string, number> = {};
        const sizeCounts = { small: 0, medium: 0, large: 0 };
        const namingPatterns = { kebabCase: 0, camelCase: 0, pascalCase: 0, snakeCase: 0, other: 0 };

        for (const filePath of filePaths) {
            try {
                // تحليل الامتداد
                const extension = path.extname(filePath).toLowerCase();
                extensionCounts[extension] = (extensionCounts[extension] || 0) + 1;

                // تحليل الحجم
                const content = fs.readFileSync(filePath, 'utf-8');
                const lineCount = content.split('\n').length;
                
                if (lineCount < 50) {
                    sizeCounts.small++;
                } else if (lineCount <= 200) {
                    sizeCounts.medium++;
                } else {
                    sizeCounts.large++;
                }

                // تحليل نمط التسمية
                const fileName = path.basename(filePath, path.extname(filePath));
                
                if (/^[a-z0-9]+(-[a-z0-9]+)*$/.test(fileName)) {
                    namingPatterns.kebabCase++;
                } else if (/^[a-z][a-zA-Z0-9]*$/.test(fileName)) {
                    namingPatterns.camelCase++;
                } else if (/^[A-Z][a-zA-Z0-9]*$/.test(fileName)) {
                    namingPatterns.pascalCase++;
                } else if (/^[a-z0-9]+(_[a-z0-9]+)*$/.test(fileName)) {
                    namingPatterns.snakeCase++;
                } else {
                    namingPatterns.other++;
                }

            } catch (error) {
                // تجاهل الأخطاء في قراءة الملفات
                extensionCounts['unknown'] = (extensionCounts['unknown'] || 0) + 1;
                namingPatterns.other++;
            }
        }

        return {
            extensionCounts,
            sizeCounts,
            namingPatterns
        };
    }

    /**
     * إنشاء تقرير مفصل
     * Generate detailed report
     */
    public static generateDetailedReport(filePaths: string[], config: SimpleVerificationConfig): {
        summary: {
            totalFiles: number;
            validFiles: number;
            averageScore: number;
            totalIssues: number;
        };
        patterns: {
            extensionCounts: Record<string, number>;
            sizeCounts: { small: number; medium: number; large: number };
            namingPatterns: { kebabCase: number; camelCase: number; pascalCase: number; snakeCase: number; other: number };
        };
        topIssues: Array<{ issue: string; count: number }>;
        recommendations: string[];
    } {
        // فحص الملفات
        const checkResult = this.checkMultipleFiles(filePaths, config);
        
        // تحليل الأنماط
        const patterns = this.analyzeFilePatterns(filePaths);

        // تحليل المشاكل الأكثر شيوعاً
        const issueMap: Record<string, number> = {};
        for (const result of checkResult.results) {
            for (const issue of result.issues) {
                issueMap[issue] = (issueMap[issue] || 0) + 1;
            }
        }

        const topIssues = Object.entries(issueMap)
            .map(([issue, count]) => ({ issue, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);

        // إنشاء التوصيات
        const recommendations: string[] = [];
        
        if (patterns.sizeCounts.large > 0) {
            recommendations.push(`Break down ${patterns.sizeCounts.large} large files into smaller modules`);
        }
        
        if (patterns.namingPatterns.kebabCase < filePaths.length * 0.8) {
            recommendations.push('Standardize file naming to kebab-case convention');
        }
        
        if (checkResult.averageScore < 80) {
            recommendations.push('Improve overall code quality to achieve higher scores');
        }

        if (topIssues.length > 0) {
            recommendations.push(`Address the most common issue: ${topIssues[0].issue}`);
        }

        return {
            summary: {
                totalFiles: checkResult.totalFiles,
                validFiles: checkResult.validFiles,
                averageScore: checkResult.averageScore,
                totalIssues: checkResult.totalIssues
            },
            patterns,
            topIssues,
            recommendations
        };
    }
}
