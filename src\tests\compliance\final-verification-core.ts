/**
 * الوظائف الأساسية للتحقق النهائي
 * Core final verification functions
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import * as fs from 'fs';
import * as path from 'path';
import { TestRunner } from '../run-tests.js';
import { ConstitutionChecker } from './constitution-checker.js';
import { FunctionalityTester } from './functionality-tester.js';
import { VerificationResult, VerificationConfig, TestResult, GradingSettings } from './final-verification-types';

/**
 * فئة الوظائف الأساسية للتحقق النهائي
 * Core final verification functions class
 */
export class FinalVerificationCore {

    /** تشغيل التحقق من الدستور / Run constitution verification */
    public static async runConstitutionVerification(projectRoot: string): Promise<any> {
        try {
            console.log('🔍 بدء التحقق من الدستور...');
            const checker = new ConstitutionChecker(projectRoot);
            const result = await checker.checkAll();
            
            console.log(`✅ اكتمل التحقق من الدستور - النتيجة: ${result.score}%`);
            return result;
        } catch (error) {
            console.error('❌ خطأ في التحقق من الدستور:', error);
            return {
                score: 0,
                issues: 999,
                criticalIssues: 999,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    /** تشغيل اختبارات الوظائف / Run functionality tests */
    public static async runFunctionalityTests(projectRoot: string): Promise<any> {
        try {
            console.log('🧪 بدء اختبارات الوظائف...');
            const tester = new FunctionalityTester(projectRoot);
            const result = await tester.runAllTests();
            
            console.log(`✅ اكتملت اختبارات الوظائف - النتيجة: ${result.score}%`);
            return result;
        } catch (error) {
            console.error('❌ خطأ في اختبارات الوظائف:', error);
            return {
                score: 0,
                passedTests: 0,
                totalTests: 0,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    /** تشغيل اختبارات الوحدة / Run unit tests */
    public static async runUnitTests(projectRoot: string): Promise<any> {
        try {
            console.log('🔬 بدء اختبارات الوحدة...');
            const runner = new TestRunner();
            const result = await runner.runAllTests();
            
            console.log(`✅ اكتملت اختبارات الوحدة - التغطية: ${result.coverage}%`);
            return result;
        } catch (error) {
            console.error('❌ خطأ في اختبارات الوحدة:', error);
            return {
                coverage: 0,
                passedTests: 0,
                totalTests: 0,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    /** حساب النتيجة الإجمالية / Calculate overall score */
    public static calculateOverallScore(
        constitutionScore: number,
        functionalityScore: number,
        unitTestScore: number,
        weights: GradingSettings['weights'] = {
            constitution: 0.4,
            functionality: 0.3,
            unitTests: 0.2,
            performance: 0.05,
            security: 0.05
        }
    ): number {
        const weightedScore = (
            constitutionScore * weights.constitution +
            functionalityScore * weights.functionality +
            unitTestScore * weights.unitTests
        );
        
        return Math.round(weightedScore * 100) / 100;
    }

    /** تحديد الدرجة / Determine grade */
    public static determineGrade(
        score: number,
        thresholds: GradingSettings['thresholds'] = {
            aPlus: 95,
            a: 90,
            bPlus: 85,
            b: 80,
            cPlus: 75,
            c: 70,
            d: 60
        }
    ): VerificationResult['grade'] {
        if (score >= thresholds.aPlus) return 'A+';
        if (score >= thresholds.a) return 'A';
        if (score >= thresholds.bPlus) return 'B+';
        if (score >= thresholds.b) return 'B';
        if (score >= thresholds.cPlus) return 'C+';
        if (score >= thresholds.c) return 'C';
        if (score >= thresholds.d) return 'D';
        return 'F';
    }

    /** توليد التوصيات / Generate recommendations */
    public static generateRecommendations(
        constitutionResult: any,
        functionalityResult: any,
        unitTestResult: any
    ): string[] {
        const recommendations: string[] = [];

        // توصيات الدستور
        if (constitutionResult.score < 80) {
            recommendations.push('تحسين الالتزام بقواعد الدستور البرمجي');
        }
        if (constitutionResult.criticalIssues > 0) {
            recommendations.push('إصلاح المشاكل الحرجة في الدستور فوراً');
        }

        // توصيات الوظائف
        if (functionalityResult.score < 85) {
            recommendations.push('تحسين اختبارات الوظائف الأساسية');
        }

        // توصيات اختبارات الوحدة
        if (unitTestResult.coverage < 80) {
            recommendations.push('زيادة تغطية اختبارات الوحدة إلى 80% على الأقل');
        }

        if (recommendations.length === 0) {
            recommendations.push('ممتاز! جميع المعايير مستوفاة');
        }

        return recommendations;
    }

    /** تحديد المشاكل الحرجة / Identify critical issues */
    public static identifyCriticalIssues(
        constitutionResult: any,
        functionalityResult: any,
        unitTestResult: any
    ): string[] {
        const criticalIssues: string[] = [];

        if (constitutionResult.criticalIssues > 0) {
            criticalIssues.push(`${constitutionResult.criticalIssues} مشكلة حرجة في الدستور`);
        }

        if (functionalityResult.score < 50) {
            criticalIssues.push('فشل في اختبارات الوظائف الأساسية');
        }

        if (unitTestResult.coverage < 50) {
            criticalIssues.push('تغطية اختبارات الوحدة منخفضة جداً');
        }

        return criticalIssues;
    }

    /** التحقق من صحة التكوين / Validate configuration */
    public static validateConfig(config: VerificationConfig): boolean {
        if (config.minCoverageThreshold < 0 || config.minCoverageThreshold > 100) {
            throw new Error('حد التغطية يجب أن يكون بين 0 و 100');
        }

        if (config.minScoreThreshold < 0 || config.minScoreThreshold > 100) {
            throw new Error('حد النتيجة يجب أن يكون بين 0 و 100');
        }

        if (!['json', 'html', 'text'].includes(config.outputFormat)) {
            throw new Error('تنسيق الإخراج يجب أن يكون json أو html أو text');
        }

        return true;
    }
}
