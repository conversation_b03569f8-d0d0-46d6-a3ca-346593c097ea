/**
 * فاحص وظائف الأمان - ملف التفويض الرئيسي
 * Security functionality tester - Main delegation file
 *
 * هذا الملف يفوض جميع العمليات للملفات المتخصصة
 * This file delegates all operations to specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { AdBlockerService } from '@infrastructure/security/ad-blocker';
import { SecurityLayer } from '@infrastructure/security/security-layer';
import { ValidationResult } from '@shared/types';
import { SecurityFunctionalityTesterAdvanced } from './security-functionality-tester-advanced';
import { SecurityFunctionalityTesterCore } from './security-functionality-tester-core';

/**
 * فاحص وظائف الأمان - التفويض
 * Security functionality tester - Delegation
 */
export class SecurityFunctionalityTester {
    private securityLayer?: SecurityLayer;
    private adBlockerService?: AdBlockerService;

    /**
     * تهيئة المكونات
     * Initialize components
     */
    public async initializeComponents(): Promise<void> {
        try {
            this.securityLayer = new SecurityLayer();
            this.adBlockerService = new AdBlockerService();

            await this.securityLayer.initialize();
            await this.adBlockerService.initialize();
        } catch (error) {
            console.error('Failed to initialize security components:', error);
            throw error;
        }
    }

    /**
     * اختبار حجب الإعلانات - تفويض للوحدة المتخصصة
     * Test ad blocking - Delegate to specialized module
     */
    public async testAdBlocking(): Promise<ValidationResult> {
        if (!this.adBlockerService) {
            throw new Error('AdBlocker service not initialized');
        }

        // تفويض العملية للوحدة المتخصصة
        // Delegate operation to specialized module
        return await SecurityFunctionalityTesterCore.testAdBlocking(this.adBlockerService);
    }

    /**
     * اختبار التحقق من الأمان - تفويض للوحدة المتخصصة
     * Test security validation - Delegate to specialized module
     */
    public async testSecurityValidation(): Promise<ValidationResult> {
        if (!this.securityLayer) {
            throw new Error('Security layer not initialized');
        }

        // تفويض العملية للوحدة المتخصصة
        // Delegate operation to specialized module
        return await SecurityFunctionalityTesterCore.testSecurityValidation(this.securityLayer);
    }

    /**
     * اختبار كشف التهديدات - تفويض للوحدة المتخصصة
     * Test threat detection - Delegate to specialized module
     */
    public async testThreatDetection(): Promise<ValidationResult> {
        if (!this.securityLayer) {
            throw new Error('Security layer not initialized');
        }

        // تفويض العملية للوحدة المتخصصة
        // Delegate operation to specialized module
        return await SecurityFunctionalityTesterCore.testThreatDetection(this.securityLayer);
    }

    /**
     * اختبار حماية الذاكرة - تفويض للوحدة المتخصصة
     * Test memory protection - Delegate to specialized module
     */
    public async testMemoryProtection(): Promise<ValidationResult> {
        if (!this.securityLayer) {
            throw new Error('Security layer not initialized');
        }

        // تفويض العملية للوحدة المتخصصة
        // Delegate operation to specialized module
        return await SecurityFunctionalityTesterCore.testMemoryProtection(this.securityLayer);
    }

    /**
     * اختبار الأداء الأمني - تفويض للوحدة المتخصصة
     * Test security performance - Delegate to specialized module
     */
    public async testSecurityPerformance(): Promise<ValidationResult> {
        if (!this.securityLayer) {
            throw new Error('Security layer not initialized');
        }

        // تفويض العملية للوحدة المتخصصة
        // Delegate operation to specialized module
        return await SecurityFunctionalityTesterAdvanced.testSecurityPerformance(this.securityLayer);
    }

    /**
     * اختبار مقاومة الهجمات - تفويض للوحدة المتخصصة
     * Test attack resistance - Delegate to specialized module
     */
    public async testAttackResistance(): Promise<ValidationResult> {
        if (!this.securityLayer) {
            throw new Error('Security layer not initialized');
        }

        // تفويض العملية للوحدة المتخصصة
        // Delegate operation to specialized module
        return await SecurityFunctionalityTesterAdvanced.testAttackResistance(this.securityLayer);
    }

    /**
     * اختبار التشفير والحماية - تفويض للوحدة المتخصصة
     * Test encryption and protection - Delegate to specialized module
     */
    public async testEncryptionProtection(): Promise<ValidationResult> {
        if (!this.securityLayer) {
            throw new Error('Security layer not initialized');
        }

        // تفويض العملية للوحدة المتخصصة
        // Delegate operation to specialized module
        return await SecurityFunctionalityTesterAdvanced.testEncryptionProtection(this.securityLayer);
    }

    /**
     * اختبار مراقبة الأمان - تفويض للوحدة المتخصصة
     * Test security monitoring - Delegate to specialized module
     */
    public async testSecurityMonitoring(): Promise<ValidationResult> {
        if (!this.securityLayer) {
            throw new Error('Security layer not initialized');
        }

        // تفويض العملية للوحدة المتخصصة
        // Delegate operation to specialized module
        return await SecurityFunctionalityTesterAdvanced.testSecurityMonitoring(this.securityLayer);
    }

    /**
     * تشغيل جميع اختبارات الأمان
     * Run all security tests
     */
    public async runAllSecurityTests(): Promise<{
        adBlocking: ValidationResult;
        securityValidation: ValidationResult;
        threatDetection: ValidationResult;
        memoryProtection: ValidationResult;
        securityPerformance: ValidationResult;
        attackResistance: ValidationResult;
        encryptionProtection: ValidationResult;
        securityMonitoring: ValidationResult;
        overallScore: number;
    }> {
        const results = {
            adBlocking: await this.testAdBlocking(),
            securityValidation: await this.testSecurityValidation(),
            threatDetection: await this.testThreatDetection(),
            memoryProtection: await this.testMemoryProtection(),
            securityPerformance: await this.testSecurityPerformance(),
            attackResistance: await this.testAttackResistance(),
            encryptionProtection: await this.testEncryptionProtection(),
            securityMonitoring: await this.testSecurityMonitoring(),
            overallScore: 0
        };

        // حساب النتيجة الإجمالية
        const scores = [
            results.adBlocking.score,
            results.securityValidation.score,
            results.threatDetection.score,
            results.memoryProtection.score,
            results.securityPerformance.score,
            results.attackResistance.score,
            results.encryptionProtection.score,
            results.securityMonitoring.score
        ];

        results.overallScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;

        return results;
    }

    /**
     * تنظيف الموارد
     * Cleanup resources
     */
    public async cleanup(): Promise<void> {
        try {
            if (this.securityLayer) {
                await this.securityLayer.cleanup();
            }
            if (this.adBlockerService) {
                await this.adBlockerService.cleanup();
            }
        } catch (error) {
            console.error('Failed to cleanup security components:', error);
        }
    }
}