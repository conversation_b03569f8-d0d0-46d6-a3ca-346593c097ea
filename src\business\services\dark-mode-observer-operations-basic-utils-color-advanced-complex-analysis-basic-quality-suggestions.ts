/**
 * اقتراحات تحسين جودة الألوان الأساسي المعقد المتقدم
 * Basic complex advanced color quality improvement suggestions
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeObserverOperationsBasicUtilsColorAdvancedBasic } from './dark-mode-observer-operations-basic-utils-color-advanced-basic';
import { DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicCore } from './dark-mode-observer-operations-basic-utils-color-advanced-complex-analysis-basic-core';
import { DarkModeObserverOperationsBasicUtilsColorCore } from './dark-mode-observer-operations-basic-utils-color-core';

/**
 * فئة اقتراحات تحسين جودة الألوان الأساسي المعقد المتقدم
 * Basic complex advanced color quality improvement suggestions class
 */
export class DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicQualitySuggestions {

    /** اقتراح تحسينات للون / Suggest color improvements */
    public static suggestColorImprovements(color: string): {
        improvedColor: string;
        improvements: string[];
        score: number;
        changes: {
            hue: number;
            saturation: number;
            lightness: number;
        };
    } {
        const analysis = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicCore.analyzeColor(color);
        const improvements: string[] = [];
        let score = 50;

        if (!analysis.hsl) {
            return {
                improvedColor: color,
                improvements: ['لون غير صالح - لا يمكن تحسينه'],
                score: 0,
                changes: { hue: 0, saturation: 0, lightness: 0 }
            };
        }

        let { h, s, l } = analysis.hsl;
        const originalH = h;
        const originalS = s;
        const originalL = l;

        // تحسين التشبع
        if (s < 20) {
            s = Math.min(40, s + 20);
            improvements.push('زيادة التشبع للحيوية');
            score += 15;
        } else if (s > 80) {
            s = Math.max(60, s - 20);
            improvements.push('تقليل التشبع للراحة');
            score += 10;
        } else {
            score += 10; // تشبع جيد
        }

        // تحسين السطوع
        if (l < 15) {
            l = Math.min(25, l + 10);
            improvements.push('زيادة السطوع للوضوح');
            score += 20;
        } else if (l > 85) {
            l = Math.max(75, l - 10);
            improvements.push('تقليل السطوع للراحة');
            score += 15;
        } else {
            score += 10; // سطوع جيد
        }

        // تحسين التباين
        const newRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(h, s, l);
        const improvedColor = DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(newRgb.r, newRgb.g, newRgb.b);

        // فحص التباين المحسن
        const contrastWithWhite = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicCore.analyzeContrast(improvedColor, '#ffffff');
        const contrastWithBlack = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicCore.analyzeContrast(improvedColor, '#000000');

        if (contrastWithWhite.isAccessible || contrastWithBlack.isAccessible) {
            score += 15;
            improvements.push('تحسين التباين للوصولية');
        }

        if (improvements.length === 0) {
            improvements.push('اللون لا يحتاج تحسين');
            score = 100;
        }

        return {
            improvedColor,
            improvements,
            score: Math.min(100, score),
            changes: {
                hue: h - originalH,
                saturation: s - originalS,
                lightness: l - originalL
            }
        };
    }

    /** اقتراح بدائل للون / Suggest color alternatives */
    public static suggestColorAlternatives(color: string, count: number = 3): {
        alternatives: string[];
        descriptions: string[];
        scores: number[];
    } {
        const analysis = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicCore.analyzeColor(color);
        const alternatives: string[] = [];
        const descriptions: string[] = [];
        const scores: number[] = [];

        if (!analysis.hsl) {
            return {
                alternatives: ['#3498db', '#e74c3c', '#2ecc71'],
                descriptions: ['لون غير صالح - بدائل افتراضية'],
                scores: [70, 70, 70]
            };
        }

        const { h, s, l } = analysis.hsl;

        for (let i = 0; i < count; i++) {
            let newH = h;
            let newS = s;
            let newL = l;
            let description = '';

            switch (i) {
                case 0:
                    // بديل بتشبع أقل
                    newS = Math.max(10, s - 30);
                    description = 'نسخة أقل تشبعاً';
                    break;
                case 1:
                    // بديل بسطوع مختلف
                    newL = l > 50 ? Math.max(20, l - 40) : Math.min(80, l + 40);
                    description = l > 50 ? 'نسخة أغمق' : 'نسخة أفتح';
                    break;
                case 2:
                    // بديل بصبغة مكملة
                    newH = (h + 180) % 360;
                    description = 'لون مكمل';
                    break;
                default:
                    // بدائل إضافية بصبغات متنوعة
                    newH = (h + (i * 60)) % 360;
                    description = 'بديل متنوع';
                    break;
            }

            const newRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(newH, newS, newL);
            const alternative = DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(newRgb.r, newRgb.g, newRgb.b);
            
            // تقييم البديل
            const evaluation = this.evaluateAlternative(alternative);
            
            alternatives.push(alternative);
            descriptions.push(description);
            scores.push(evaluation.score);
        }

        return {
            alternatives,
            descriptions,
            scores
        };
    }

    /** تقييم البديل / Evaluate alternative */
    private static evaluateAlternative(color: string): { score: number } {
        const analysis = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicCore.analyzeColor(color);
        let score = 50;

        if (!analysis.hsl) {
            return { score: 0 };
        }

        const { s, l } = analysis.hsl;

        // تقييم التشبع
        if (s >= 20 && s <= 70) {
            score += 15;
        } else if (s < 10 || s > 90) {
            score -= 10;
        }

        // تقييم السطوع
        if (l >= 20 && l <= 80) {
            score += 15;
        } else if (l < 10 || l > 90) {
            score -= 10;
        }

        // تقييم التباين
        const contrastWithWhite = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicCore.analyzeContrast(color, '#ffffff');
        const contrastWithBlack = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicCore.analyzeContrast(color, '#000000');

        if (contrastWithWhite.isAccessible || contrastWithBlack.isAccessible) {
            score += 20;
        }

        return { score: Math.max(0, Math.min(100, score)) };
    }

    /** اقتراح تحسينات للمجموعة / Suggest set improvements */
    public static suggestSetImprovements(colors: string[]): {
        improvedColors: string[];
        improvements: string[];
        overallScore: number;
        individualChanges: Array<{
            original: string;
            improved: string;
            changes: string[];
        }>;
    } {
        if (colors.length === 0) {
            return {
                improvedColors: [],
                improvements: ['لا توجد ألوان للتحسين'],
                overallScore: 0,
                individualChanges: []
            };
        }

        const improvedColors: string[] = [];
        const improvements: string[] = [];
        const individualChanges: Array<{
            original: string;
            improved: string;
            changes: string[];
        }> = [];
        let totalScore = 0;

        // تحسين كل لون على حدة
        colors.forEach((color, index) => {
            const improvement = this.suggestColorImprovements(color);
            improvedColors.push(improvement.improvedColor);
            totalScore += improvement.score;

            individualChanges.push({
                original: color,
                improved: improvement.improvedColor,
                changes: improvement.improvements
            });
        });

        // تحسينات على مستوى المجموعة
        const analyses = improvedColors.map(color => 
            DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicCore.analyzeColor(color)
        );

        // فحص التوازن الحراري
        const temperatures = analyses.map(a => a.temperature);
        const warmCount = temperatures.filter(t => t === 'warm').length;
        const coolCount = temperatures.filter(t => t === 'cool').length;

        if (colors.length > 2 && Math.abs(warmCount - coolCount) > colors.length * 0.6) {
            improvements.push('تحسين التوازن الحراري بين الألوان');
            
            // تحسين التوازن
            const needsWarm = warmCount < coolCount;
            for (let i = 0; i < improvedColors.length; i++) {
                const analysis = analyses[i];
                if (analysis.hsl && 
                    ((needsWarm && analysis.temperature === 'cool') || 
                     (!needsWarm && analysis.temperature === 'warm'))) {
                    
                    let { h, s, l } = analysis.hsl;
                    
                    if (needsWarm) {
                        // تحويل إلى دافئ
                        h = h > 180 ? h - 60 : h + 60;
                        h = h % 360;
                    } else {
                        // تحويل إلى بارد
                        h = h < 180 ? h + 60 : h - 60;
                        h = (h + 360) % 360;
                    }
                    
                    const newRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(h, s, l);
                    const newColor = DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(newRgb.r, newRgb.g, newRgb.b);
                    improvedColors[i] = newColor;
                    
                    individualChanges[i].improved = newColor;
                    individualChanges[i].changes.push('تحسين التوازن الحراري');
                    break; // تحسين لون واحد فقط
                }
            }
        }

        // فحص التباين بين الألوان
        let contrastIssues = 0;
        for (let i = 0; i < improvedColors.length - 1; i++) {
            for (let j = i + 1; j < improvedColors.length; j++) {
                const contrast = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicCore.analyzeContrast(improvedColors[i], improvedColors[j]);
                if (!contrast.isAccessible) {
                    contrastIssues++;
                }
            }
        }

        if (contrastIssues > 0) {
            improvements.push('تحسين التباين بين الألوان');
        }

        // حساب النقاط الإجمالية
        const overallScore = Math.round(totalScore / colors.length);

        if (improvements.length === 0) {
            improvements.push('المجموعة محسنة ومتوازنة');
        }

        return {
            improvedColors,
            improvements,
            overallScore,
            individualChanges
        };
    }

    /** اقتراح ألوان مناسبة للسياق / Suggest context-appropriate colors */
    public static suggestContextColors(context: 'dark-mode' | 'light-mode' | 'high-contrast' | 'colorblind-friendly', count: number = 5): {
        colors: string[];
        descriptions: string[];
        reasons: string[];
    } {
        const colors: string[] = [];
        const descriptions: string[] = [];
        const reasons: string[] = [];

        switch (context) {
            case 'dark-mode':
                colors.push('#2c3e50', '#34495e', '#3498db', '#e74c3c', '#f39c12');
                descriptions.push('خلفية غامقة', 'خلفية ثانوية', 'أزرق هادئ', 'أحمر مميز', 'برتقالي دافئ');
                reasons.push('مناسب للعيون في الإضاءة المنخفضة', 'تباين جيد مع النصوص', 'لون هادئ للعيون', 'لون تحذيري واضح', 'لون دافئ للتمييز');
                break;

            case 'light-mode':
                colors.push('#ecf0f1', '#bdc3c7', '#3498db', '#e74c3c', '#27ae60');
                descriptions.push('خلفية فاتحة', 'خلفية ثانوية', 'أزرق مشرق', 'أحمر واضح', 'أخضر طبيعي');
                reasons.push('مناسب للإضاءة العالية', 'تباين مناسب', 'لون مريح ومألوف', 'لون تحذيري بارز', 'لون إيجابي ومريح');
                break;

            case 'high-contrast':
                colors.push('#000000', '#ffffff', '#ff0000', '#00ff00', '#0000ff');
                descriptions.push('أسود خالص', 'أبيض خالص', 'أحمر خالص', 'أخضر خالص', 'أزرق خالص');
                reasons.push('تباين أقصى', 'تباين أقصى', 'وضوح تام', 'وضوح تام', 'وضوح تام');
                break;

            case 'colorblind-friendly':
                colors.push('#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd');
                descriptions.push('أزرق آمن', 'برتقالي آمن', 'أخضر آمن', 'أحمر آمن', 'بنفسجي آمن');
                reasons.push('مميز لعمى الألوان', 'بديل آمن للأحمر', 'واضح لجميع الأنواع', 'مميز ومختلف', 'لون فريد وواضح');
                break;
        }

        return {
            colors: colors.slice(0, count),
            descriptions: descriptions.slice(0, count),
            reasons: reasons.slice(0, count)
        };
    }
}
