/**
 * تكوين مانع الإعلانات
 * Ad blocker configuration
 * 
 * هذا الملف يحتوي على تكوين وثوابت مانع الإعلانات
 * This file contains ad blocker configuration and constants
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * إحصائيات مانع الإعلانات
 * Ad blocker statistics
 */
export interface AdBlockerStats {
    readonly blockedAds: number;
    readonly blockedDomains: number;
    readonly allowedRequests: number;
    readonly startTime: number;
}

/**
 * تكوين مانع الإعلانات
 * Ad blocker configuration
 */
export interface AdBlockerConfig {
    readonly enabled: boolean;
    readonly strictMode: boolean;
    readonly allowAnalytics: boolean;
    readonly customRules: string[];
}

/**
 * الأنماط الأساسية المحمية
 * Essential protected patterns
 */
export const ESSENTIAL_PATTERNS: readonly string[] = [
    '/videoplayback',
    '/api/timedtext',
    '/youtubei/v1/player/heartbeat',
    'googlevideo.com',
    'gstatic.com/fonts',
    'youtube.com/api/stats',
    'youtube.com/ptracking',
    'youtube.com/generate_204'
] as const;

/**
 * النطاقات المحظورة للإعلانات
 * Blocked advertising domains
 */
export const BLOCKED_AD_DOMAINS: readonly string[] = [
    'doubleclick.net',
    'googleadservices.com',
    'googlesyndication.com',
    'googletagmanager.com',
    'googletagservices.com',
    'google-analytics.com',
    'adsystem.com',
    'adsense.com',
    'adnxs.com',
    'amazon-adsystem.com',
    'facebook.com/tr',
    'connect.facebook.net',
    'scorecardresearch.com',
    'outbrain.com',
    'taboola.com',
    'criteo.com',
    'adsafeprotected.com',
    'moatads.com',
    'quantserve.com',
    'rubiconproject.com',
    'pubmatic.com',
    'openx.net',
    'adsystem.com',
    'advertising.com',
    'adsystem.com',
    'ads.yahoo.com',
    'adsystem.com'
] as const;

/**
 * محددات CSS للإعلانات
 * CSS selectors for ads
 */
export const AD_SELECTORS: readonly string[] = [
    '.video-ads',
    '.ytp-ad-module',
    '.ytp-ad-overlay-container',
    '.ytp-ad-text-overlay',
    '.ytp-ad-player-overlay',
    '.ytp-ad-skip-button-container',
    '.ytp-ad-overlay-close-button',
    '.masthead-ad-control',
    '.ytd-promoted-sparkles-web-renderer',
    '.ytd-ad-slot-renderer',
    '.ytd-banner-promo-renderer',
    '.ytd-video-masthead-ad-v3-renderer',
    '.ytd-promoted-video-renderer',
    '.ytd-compact-promoted-video-renderer',
    '.ytd-display-ad-renderer',
    '.ytd-promoted-sparkles-text-search-renderer',
    '.ytd-search-pyv-renderer',
    '.ytd-carousel-ad-renderer',
    '.ytd-video-masthead-ad-advertiser-info-renderer',
    '.ytd-video-masthead-ad-primary-video-renderer',
    '.ad-container',
    '.advertisement',
    '.sponsored',
    '.promoted-content'
] as const;

/**
 * الثوابت الافتراضية
 * Default constants
 */
export const AD_BLOCKER_DEFAULTS = {
    CLEANUP_INTERVAL: 1000,
    STATS_UPDATE_INTERVAL: 5000,
    MAX_BLOCKED_DOMAINS: 1000,
    OBSERVER_CONFIG: {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'id', 'src']
    }
} as const;

/**
 * رسائل السجل
 * Log messages
 */
export const AD_BLOCKER_MESSAGES = {
    ENABLED: 'تم تفعيل مانع الإعلانات / Ad blocker enabled',
    DISABLED: 'تم إيقاف مانع الإعلانات / Ad blocker disabled',
    BLOCKED_REQUEST: 'تم حظر طلب إعلان / Blocked ad request',
    ALLOWED_REQUEST: 'تم السماح بطلب أساسي / Allowed essential request',
    CLEANED_DOM: 'تم تنظيف DOM من الإعلانات / Cleaned DOM from ads',
    ERROR_INIT: 'خطأ في تهيئة مانع الإعلانات / Error initializing ad blocker',
    ERROR_CLEANUP: 'خطأ في تنظيف الإعلانات / Error cleaning ads'
} as const;

export { AdBlockerConfig, AdBlockerStats };
