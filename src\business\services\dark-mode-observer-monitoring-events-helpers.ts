/**
 * مراقبة الوضع المظلم - مساعدات الأحداث
 * Dark mode monitoring - Events helpers
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * فئة مساعدات الأحداث لمراقبة الوضع المظلم
 * Events helpers for dark mode monitoring
 */
export class DarkModeObserverMonitoringEventsHelpers {

    /**
     * الحصول على عدد المستمعين النشطين
     * Get active listeners count
     */
    public static getActiveListenersCount(eventListeners: Map<string, EventListener[]>): number {
        let count = 0;
        eventListeners.forEach(listeners => {
            count += listeners.length;
        });
        return count;
    }

    /**
     * الحصول على أنواع الأحداث النشطة
     * Get active event types
     */
    public static getActiveEventTypes(eventListeners: Map<string, EventListener[]>): string[] {
        return Array.from(eventListeners.keys());
    }
}
