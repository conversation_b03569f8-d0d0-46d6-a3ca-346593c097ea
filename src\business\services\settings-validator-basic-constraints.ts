/**
 * مدقق الإعدادات الأساسي - القيود
 * Basic settings validator - Constraints
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ApplicationConfig, ValidationResult } from '@shared/types';

/**
 * فئة قيود مدقق الإعدادات الأساسي
 */
export class SettingsValidatorBasicConstraints {
    constructor() {
        // منشئ بسيط
    }

    /**
     * التحقق من القيود / Validate constraints
     */
    public validateConstraints<K extends keyof ApplicationConfig>(
        key: K,
        value: ApplicationConfig[K]
    ): ValidationResult {
        const errors: string[] = [];

        try {
            switch (key) {
                case 'videoQuality':
                    const validQualities = ['144p', '240p', '360p', '480p', '720p', '1080p', '1440p', '2160p', 'auto'];
                    if (typeof value === 'string' && !validQualities.includes(value)) {
                        errors.push(`جودة فيديو غير صالحة: ${value}. القيم المسموحة: ${validQualities.join(', ')}`);
                    }
                    break;

                case 'volume':
                    if (typeof value === 'number' && (value < 0 || value > 100)) {
                        errors.push('مستوى الصوت يجب أن يكون بين 0 و 100');
                    }
                    break;

                case 'playbackSpeed':
                    const validSpeeds = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2];
                    if (typeof value === 'number' && !validSpeeds.includes(value)) {
                        errors.push(`سرعة تشغيل غير صالحة: ${value}. القيم المسموحة: ${validSpeeds.join(', ')}`);
                    }
                    break;

                case 'language':
                    const validLanguages = ['ar', 'en', 'fr', 'es', 'de'];
                    if (typeof value === 'string' && !validLanguages.includes(value)) {
                        errors.push(`لغة غير صالحة: ${value}. اللغات المسموحة: ${validLanguages.join(', ')}`);
                    }
                    break;

                case 'theme':
                    const validThemes = ['light', 'dark', 'auto'];
                    if (typeof value === 'string' && !validThemes.includes(value)) {
                        errors.push(`مظهر غير صالح: ${value}. المظاهر المسموحة: ${validThemes.join(', ')}`);
                    }
                    break;
            }

        } catch (error) {
            errors.push(`خطأ في التحقق من قيود الإعداد ${String(key)}: ${error}`);
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * التحقق من قيود الأرقام / Validate numeric constraints
     */
    public validateNumericConstraints(
        value: number,
        min?: number,
        max?: number,
        fieldName?: string
    ): ValidationResult {
        const errors: string[] = [];

        try {
            if (min !== undefined && value < min) {
                errors.push(`${fieldName || 'القيمة'} أقل من الحد الأدنى: ${value} < ${min}`);
            }

            if (max !== undefined && value > max) {
                errors.push(`${fieldName || 'القيمة'} أكبر من الحد الأقصى: ${value} > ${max}`);
            }

            if (!Number.isFinite(value)) {
                errors.push(`${fieldName || 'القيمة'} يجب أن تكون رقم صالح`);
            }

        } catch (error) {
            errors.push(`خطأ في التحقق من قيود الأرقام: ${error}`);
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * التحقق من قيود النصوص / Validate string constraints
     */
    public validateStringConstraints(
        value: string,
        minLength?: number,
        maxLength?: number,
        pattern?: RegExp,
        fieldName?: string
    ): ValidationResult {
        const errors: string[] = [];

        try {
            if (minLength !== undefined && value.length < minLength) {
                errors.push(`${fieldName || 'النص'} قصير جداً. الحد الأدنى: ${minLength} أحرف`);
            }

            if (maxLength !== undefined && value.length > maxLength) {
                errors.push(`${fieldName || 'النص'} طويل جداً. الحد الأقصى: ${maxLength} أحرف`);
            }

            if (pattern && !pattern.test(value)) {
                errors.push(`${fieldName || 'النص'} لا يطابق النمط المطلوب`);
            }

        } catch (error) {
            errors.push(`خطأ في التحقق من قيود النصوص: ${error}`);
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * التحقق من قيود المصفوفات / Validate array constraints
     */
    public validateArrayConstraints(
        value: unknown[],
        minLength?: number,
        maxLength?: number,
        uniqueItems?: boolean,
        fieldName?: string
    ): ValidationResult {
        const errors: string[] = [];

        try {
            if (minLength !== undefined && value.length < minLength) {
                errors.push(`${fieldName || 'المصفوفة'} تحتوي على عناصر قليلة. الحد الأدنى: ${minLength}`);
            }

            if (maxLength !== undefined && value.length > maxLength) {
                errors.push(`${fieldName || 'المصفوفة'} تحتوي على عناصر كثيرة. الحد الأقصى: ${maxLength}`);
            }

            if (uniqueItems) {
                const uniqueValues = new Set(value.map(item => JSON.stringify(item)));
                if (uniqueValues.size !== value.length) {
                    errors.push(`${fieldName || 'المصفوفة'} تحتوي على عناصر مكررة`);
                }
            }

        } catch (error) {
            errors.push(`خطأ في التحقق من قيود المصفوفات: ${error}`);
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * التحقق من القيم المسموحة / Validate allowed values
     */
    public validateAllowedValues<T>(
        value: T,
        allowedValues: T[],
        fieldName?: string
    ): ValidationResult {
        const errors: string[] = [];

        try {
            if (!allowedValues.includes(value)) {
                errors.push(`${fieldName || 'القيمة'} غير مسموحة: ${value}. القيم المسموحة: ${allowedValues.join(', ')}`);
            }

        } catch (error) {
            errors.push(`خطأ في التحقق من القيم المسموحة: ${error}`);
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * التحقق من القيم المحظورة / Validate forbidden values
     */
    public validateForbiddenValues<T>(
        value: T,
        forbiddenValues: T[],
        fieldName?: string
    ): ValidationResult {
        const errors: string[] = [];

        try {
            if (forbiddenValues.includes(value)) {
                errors.push(`${fieldName || 'القيمة'} محظورة: ${value}`);
            }

        } catch (error) {
            errors.push(`خطأ في التحقق من القيم المحظورة: ${error}`);
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }
}
