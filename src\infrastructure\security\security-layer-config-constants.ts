/**
 * ثوابت تكوين طبقة الأمان
 * Security layer configuration constants
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * ثوابت طبقة الأمان / Security layer constants
 */
export const SECURITY_LAYER_CONSTANTS = {
    // حدود الأداء / Performance limits
    MAX_CONCURRENT_SCANS: 10,
    MIN_CONCURRENT_SCANS: 1,
    DEFAULT_CONCURRENT_SCANS: 5,
    
    MAX_SCAN_TIMEOUT: 30000,
    MIN_SCAN_TIMEOUT: 1000,
    DEFAULT_SCAN_TIMEOUT: 5000,
    
    MAX_REPORTING_INTERVAL: 300000,
    MIN_REPORTING_INTERVAL: 1000,
    DEFAULT_REPORTING_INTERVAL: 30000,
    
    MAX_CACHE_SIZE: 10000,
    MIN_CACHE_SIZE: 100,
    DEFAULT_CACHE_SIZE: 1000,
    
    // حدود المحتوى / Content limits
    MAX_URL_LENGTH: 2048,
    MAX_CONTENT_SIZE: 1024 * 1024, // 1MB
    MAX_HEADER_SIZE: 8192,
    MAX_QUERY_PARAMS: 100,
    
    // حدود التهديدات / Threat limits
    MAX_THREAT_HISTORY: 1000,
    MAX_BLOCKED_DOMAINS: 10000,
    MAX_WHITELIST_DOMAINS: 1000,
    MAX_THREAT_REPORTS: 100,
    
    // فترات زمنية / Time intervals
    THREAT_CLEANUP_INTERVAL: 3600000, // 1 hour
    CACHE_CLEANUP_INTERVAL: 1800000, // 30 minutes
    STATS_UPDATE_INTERVAL: 60000, // 1 minute
    HEALTH_CHECK_INTERVAL: 300000, // 5 minutes
    
    // أولويات / Priorities
    PRIORITY_CRITICAL: 1,
    PRIORITY_HIGH: 2,
    PRIORITY_MEDIUM: 3,
    PRIORITY_LOW: 4,
    
    // أحجام البيانات / Data sizes
    SMALL_DATA_SIZE: 1024, // 1KB
    MEDIUM_DATA_SIZE: 10240, // 10KB
    LARGE_DATA_SIZE: 102400, // 100KB
    
    // عدادات / Counters
    MAX_RETRY_ATTEMPTS: 3,
    MAX_ERROR_COUNT: 100,
    MAX_WARNING_COUNT: 500,
    
    // معرفات / Identifiers
    SECURITY_LAYER_ID: 'security-layer',
    AD_BLOCKER_ID: 'ad-blocker',
    THREAT_DETECTOR_ID: 'threat-detector',
    CONTENT_SANITIZER_ID: 'content-sanitizer'
} as const;

/**
 * رسائل طبقة الأمان / Security layer messages
 */
export const SECURITY_LAYER_MESSAGES = {
    // رسائل النجاح / Success messages
    INITIALIZED: 'تم تهيئة طبقة الأمان بنجاح / Security layer initialized successfully',
    STARTED: 'تم بدء طبقة الأمان / Security layer started',
    STOPPED: 'تم إيقاف طبقة الأمان / Security layer stopped',
    THREAT_BLOCKED: 'تم حجب التهديد / Threat blocked',
    CONTENT_SANITIZED: 'تم تنظيف المحتوى / Content sanitized',
    REQUEST_VALIDATED: 'تم التحقق من الطلب / Request validated',
    
    // رسائل الخطأ / Error messages
    INITIALIZATION_FAILED: 'فشل في تهيئة طبقة الأمان / Failed to initialize security layer',
    START_FAILED: 'فشل في بدء طبقة الأمان / Failed to start security layer',
    STOP_FAILED: 'فشل في إيقاف طبقة الأمان / Failed to stop security layer',
    SCAN_FAILED: 'فشل في فحص التهديدات / Failed to scan for threats',
    BLOCK_FAILED: 'فشل في حجب التهديد / Failed to block threat',
    SANITIZATION_FAILED: 'فشل في تنظيف المحتوى / Failed to sanitize content',
    VALIDATION_FAILED: 'فشل في التحقق من الطلب / Failed to validate request',
    
    // رسائل التحذير / Warning messages
    HIGH_THREAT_LEVEL: 'مستوى تهديد عالي / High threat level detected',
    SUSPICIOUS_ACTIVITY: 'نشاط مشبوه / Suspicious activity detected',
    PERFORMANCE_DEGRADED: 'تدهور في الأداء / Performance degraded',
    CACHE_FULL: 'ذاكرة التخزين المؤقت ممتلئة / Cache is full',
    
    // رسائل المعلومات / Info messages
    THREAT_DETECTED: 'تم اكتشاف تهديد / Threat detected',
    SCAN_COMPLETED: 'تم إكمال الفحص / Scan completed',
    REPORT_GENERATED: 'تم إنشاء التقرير / Report generated',
    CACHE_CLEARED: 'تم مسح ذاكرة التخزين المؤقت / Cache cleared'
} as const;

/**
 * أنواع التهديدات / Threat types
 */
export enum SecurityThreatType {
    MALWARE = 'MALWARE',
    PHISHING = 'PHISHING',
    SPAM = 'SPAM',
    TRACKING = 'TRACKING',
    ADVERTISEMENT = 'ADVERTISEMENT',
    SUSPICIOUS_SCRIPT = 'SUSPICIOUS_SCRIPT',
    UNSAFE_REDIRECT = 'UNSAFE_REDIRECT',
    DATA_LEAK = 'DATA_LEAK',
    INJECTION_ATTACK = 'INJECTION_ATTACK',
    UNKNOWN = 'UNKNOWN'
}

/**
 * حالات طبقة الأمان / Security layer states
 */
export enum SecurityLayerState {
    UNINITIALIZED = 'UNINITIALIZED',
    INITIALIZING = 'INITIALIZING',
    INITIALIZED = 'INITIALIZED',
    STARTING = 'STARTING',
    RUNNING = 'RUNNING',
    STOPPING = 'STOPPING',
    STOPPED = 'STOPPED',
    ERROR = 'ERROR'
}

/**
 * مستويات السجل / Log levels
 */
export enum SecurityLogLevel {
    DEBUG = 'debug',
    INFO = 'info',
    WARN = 'warn',
    ERROR = 'error'
}

/**
 * أنواع الإجراءات / Action types
 */
export enum SecurityActionType {
    BLOCK = 'BLOCK',
    ALLOW = 'ALLOW',
    SANITIZE = 'SANITIZE',
    WARN = 'WARN',
    LOG = 'LOG',
    REDIRECT = 'REDIRECT'
}

/**
 * أنماط الأمان / Security patterns
 */
export const SECURITY_PATTERNS = {
    // أنماط URL المشبوهة / Suspicious URL patterns
    SUSPICIOUS_URLS: [
        /.*\.exe$/i,
        /.*\.scr$/i,
        /.*\.bat$/i,
        /.*\.cmd$/i,
        /.*\.com$/i,
        /.*\.pif$/i,
        /.*\.vbs$/i,
        /.*\.js$/i,
        /data:.*base64/i,
        /javascript:/i,
        /vbscript:/i
    ],
    
    // أنماط المحتوى المشبوه / Suspicious content patterns
    SUSPICIOUS_CONTENT: [
        /<script[^>]*>.*<\/script>/gi,
        /<iframe[^>]*>.*<\/iframe>/gi,
        /<object[^>]*>.*<\/object>/gi,
        /<embed[^>]*>/gi,
        /on\w+\s*=/gi,
        /javascript:/gi,
        /vbscript:/gi,
        /data:.*base64/gi
    ],
    
    // أنماط الإعلانات / Advertisement patterns
    ADVERTISEMENT_PATTERNS: [
        /doubleclick\.net/i,
        /googleadservices\.com/i,
        /googlesyndication\.com/i,
        /amazon-adsystem\.com/i,
        /facebook\.com\/tr/i,
        /google-analytics\.com/i,
        /googletagmanager\.com/i,
        /adsystem\.amazon/i
    ],
    
    // أنماط التتبع / Tracking patterns
    TRACKING_PATTERNS: [
        /google-analytics/i,
        /googletagmanager/i,
        /facebook\.com\/tr/i,
        /twitter\.com\/i\/adsct/i,
        /linkedin\.com\/px/i,
        /pinterest\.com\/ct/i,
        /snapchat\.com\/tr/i,
        /tiktok\.com\/i18n\/pixel/i
    ]
} as const;

/**
 * قوائم النطاقات / Domain lists
 */
export const SECURITY_DOMAIN_LISTS = {
    // نطاقات محظورة / Blocked domains
    BLOCKED_DOMAINS: [
        'doubleclick.net',
        'googleadservices.com',
        'googlesyndication.com',
        'amazon-adsystem.com',
        'facebook.com',
        'google-analytics.com',
        'googletagmanager.com'
    ],
    
    // نطاقات موثوقة / Trusted domains
    TRUSTED_DOMAINS: [
        'youtube.com',
        'googlevideo.com',
        'ytimg.com',
        'gstatic.com',
        'googleapis.com'
    ],
    
    // نطاقات التتبع / Tracking domains
    TRACKING_DOMAINS: [
        'google-analytics.com',
        'googletagmanager.com',
        'facebook.com',
        'twitter.com',
        'linkedin.com',
        'pinterest.com'
    ]
} as const;

/**
 * إعدادات الأداء / Performance settings
 */
export const SECURITY_PERFORMANCE_SETTINGS = {
    // حدود الذاكرة / Memory limits
    MAX_MEMORY_USAGE: 100 * 1024 * 1024, // 100MB
    MEMORY_WARNING_THRESHOLD: 80 * 1024 * 1024, // 80MB
    MEMORY_CLEANUP_THRESHOLD: 90 * 1024 * 1024, // 90MB
    
    // حدود المعالجة / Processing limits
    MAX_PROCESSING_TIME: 10000, // 10 seconds
    PROCESSING_WARNING_THRESHOLD: 5000, // 5 seconds
    
    // حدود الشبكة / Network limits
    MAX_CONCURRENT_REQUESTS: 50,
    REQUEST_TIMEOUT: 30000, // 30 seconds
    RETRY_DELAY: 1000, // 1 second
    
    // حدود التخزين المؤقت / Cache limits
    CACHE_TTL: 3600000, // 1 hour
    CACHE_MAX_AGE: 86400000, // 24 hours
    CACHE_CLEANUP_INTERVAL: 1800000 // 30 minutes
} as const;
