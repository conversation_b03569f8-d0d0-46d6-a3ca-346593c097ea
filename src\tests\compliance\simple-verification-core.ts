/**
 * العمليات الأساسية لأداة التحقق المبسطة - ملف التفويض
 * Simple verification core operations - Delegation file
 *
 * هذا الملف يفوض جميع العمليات للملفات المتخصصة
 * This file delegates all operations to specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { SimpleVerificationCoreOperations } from './simple-verification-core-operations';
import { SimpleVerificationCoreUtils } from './simple-verification-core-utils';
import {
    CodeQualityCheckResult,
    ConstitutionCheckResult,
    DEFAULT_SIMPLE_VERIFICATION_CONFIG,
    ProjectStructureCheckResult,
    SimpleVerificationConfig
} from './simple-verification-types';

/**
 * فئة العمليات الأساسية للتحقق المبسط - التفويض
 * Simple verification core operations class - Delegation
 */
export class SimpleVerificationCore {

    /**
     * فحص الامتثال للدستور - تفويض للوحدة المتخصصة
     * Check constitution compliance - Delegate to specialized module
     */
    public static async checkConstitutionCompliance(
        projectRoot: string,
        config: SimpleVerificationConfig = DEFAULT_SIMPLE_VERIFICATION_CONFIG
    ): Promise<ConstitutionCheckResult> {
        try {
            // الحصول على جميع الملفات من الوحدة المساعدة
            // Get all files from utility module
            const files = await SimpleVerificationCoreUtils.getAllFiles(projectRoot, config);

            // تفويض فحص الدستور للوحدة المتخصصة
            // Delegate constitution check to specialized module
            return await SimpleVerificationCoreOperations.checkConstitutionCompliance(
                projectRoot,
                config,
                files
            );

        } catch (error) {
            return {
                score: 0,
                issues: [`Constitution check failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
                recommendations: ['Fix constitution check errors and retry'],
                fileSizeViolations: [],
                namingViolations: [],
                documentationIssues: [],
                structureIssues: []
            };
        }
    }

    /**
     * فحص جودة الكود - تفويض للوحدة المتخصصة
     * Check code quality - Delegate to specialized module
     */
    public static async checkCodeQuality(
        projectRoot: string,
        config: SimpleVerificationConfig = DEFAULT_SIMPLE_VERIFICATION_CONFIG
    ): Promise<CodeQualityCheckResult> {
        try {
            // الحصول على جميع الملفات من الوحدة المساعدة
            // Get all files from utility module
            const files = await SimpleVerificationCoreUtils.getAllFiles(projectRoot, config);

            // تفويض فحص جودة الكود للوحدة المتخصصة
            // Delegate code quality check to specialized module
            return await SimpleVerificationCoreOperations.checkCodeQuality(
                projectRoot,
                config,
                files
            );

        } catch (error) {
            return {
                score: 0,
                issues: [`Code quality check failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
                recommendations: ['Fix code quality check errors and retry'],
                typeScriptCoverage: 0,
                documentationCoverage: 0,
                testCoverage: 0,
                complexityIssues: []
            };
        }
    }

    /**
     * فحص بنية المشروع - تفويض للوحدة المتخصصة
     * Check project structure - Delegate to specialized module
     */
    public static async checkProjectStructure(
        projectRoot: string,
        config: SimpleVerificationConfig = DEFAULT_SIMPLE_VERIFICATION_CONFIG
    ): Promise<ProjectStructureCheckResult> {
        try {
            // الحصول على جميع الملفات من الوحدة المساعدة
            // Get all files from utility module
            const files = await SimpleVerificationCoreUtils.getAllFiles(projectRoot, config);

            // تفويض فحص بنية المشروع للوحدة المتخصصة
            // Delegate project structure check to specialized module
            return await SimpleVerificationCoreOperations.checkProjectStructure(
                projectRoot,
                config,
                files
            );

        } catch (error) {
            return {
                score: 0,
                issues: [`Project structure check failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
                recommendations: ['Fix project structure check errors and retry'],
                missingDirectories: [],
                extraDirectories: [],
                incorrectFileLocations: [],
                organizationScore: 0
            };
        }
    }

    /**
     * فحص ملف واحد - تفويض للوحدة المساعدة
     * Check single file - Delegate to utility module
     */
    public static checkSingleFile(
        filePath: string,
        config: SimpleVerificationConfig = DEFAULT_SIMPLE_VERIFICATION_CONFIG
    ): {
        isValid: boolean;
        issues: string[];
        score: number;
        lineCount: number;
        hasDocumentation: boolean;
        followsNamingConvention: boolean;
    } {
        // تفويض فحص الملف الواحد للوحدة المساعدة
        // Delegate single file check to utility module
        return SimpleVerificationCoreUtils.checkSingleFile(filePath, config);
    }

    /**
     * فحص مجلد واحد - تفويض للوحدة المساعدة
     * Check single directory - Delegate to utility module
     */
    public static checkSingleDirectory(
        dirPath: string,
        config: SimpleVerificationConfig = DEFAULT_SIMPLE_VERIFICATION_CONFIG
    ): {
        isValid: boolean;
        issues: string[];
        score: number;
        fileCount: number;
        subdirectoryCount: number;
        followsStructure: boolean;
    } {
        // تفويض فحص المجلد الواحد للوحدة المساعدة
        // Delegate single directory check to utility module
        return SimpleVerificationCoreUtils.checkSingleDirectory(dirPath, config);
    }
}