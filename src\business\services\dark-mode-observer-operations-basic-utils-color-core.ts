/**
 * الوظائف الأساسية لأدوات الألوان
 * Core color utility functions
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * فئة الوظائف الأساسية لأدوات الألوان
 * Core color utility functions class
 */
export class DarkModeObserverOperationsBasicUtilsColorCore {

    /** حساب التباين بين لونين / Calculate contrast between two colors */
    public static calculateContrast(color1: string, color2: string): number {
        const rgb1 = this.parseColorToRgb(color1);
        const rgb2 = this.parseColorToRgb(color2);

        if (!rgb1 || !rgb2) {
            return 0;
        }

        const luminance1 = this.calculateLuminance(rgb1);
        const luminance2 = this.calculateLuminance(rgb2);

        const lighter = Math.max(luminance1, luminance2);
        const darker = Math.min(luminance1, luminance2);

        return (lighter + 0.05) / (darker + 0.05);
    }

    /** حساب السطوع النسبي / Calculate relative luminance */
    public static calculateLuminance(rgb: { r: number; g: number; b: number }): number {
        const rsRGB = rgb.r / 255;
        const gsRGB = rgb.g / 255;
        const bsRGB = rgb.b / 255;

        const r = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);
        const g = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);
        const b = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);

        return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    }

    /** التحقق من التباين الجيد / Check if contrast is good */
    public static hasGoodContrast(color1: string, color2: string, level: 'AA' | 'AAA' = 'AA'): boolean {
        const contrast = this.calculateContrast(color1, color2);
        
        switch (level) {
            case 'AA':
                return contrast >= 4.5;
            case 'AAA':
                return contrast >= 7;
            default:
                return contrast >= 4.5;
        }
    }

    /** الحصول على لون متباين / Get contrasting color */
    public static getContrastingColor(backgroundColor: string): string {
        if (this.isLightColor(backgroundColor)) {
            return '#000000'; // نص أسود للخلفيات الفاتحة
        } else {
            return '#ffffff'; // نص أبيض للخلفيات الداكنة
        }
    }

    /** تحليل اللون إلى قيم RGB / Parse color to RGB values */
    public static parseColorToRgb(color: string): { r: number; g: number; b: number } | null {
        if (!color) {
            return null;
        }

        // تحليل RGB
        const rgbMatch = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
        if (rgbMatch) {
            return {
                r: parseInt(rgbMatch[1], 10),
                g: parseInt(rgbMatch[2], 10),
                b: parseInt(rgbMatch[3], 10)
            };
        }

        // تحليل RGBA
        const rgbaMatch = color.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d.]+\)/);
        if (rgbaMatch) {
            return {
                r: parseInt(rgbaMatch[1], 10),
                g: parseInt(rgbaMatch[2], 10),
                b: parseInt(rgbaMatch[3], 10)
            };
        }

        // تحليل HEX
        const hexMatch = color.match(/^#([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i);
        if (hexMatch) {
            return {
                r: parseInt(hexMatch[1], 16),
                g: parseInt(hexMatch[2], 16),
                b: parseInt(hexMatch[3], 16)
            };
        }

        // تحليل HEX مختصر
        const shortHexMatch = color.match(/^#([a-f\d])([a-f\d])([a-f\d])$/i);
        if (shortHexMatch) {
            return {
                r: parseInt(shortHexMatch[1] + shortHexMatch[1], 16),
                g: parseInt(shortHexMatch[2] + shortHexMatch[2], 16),
                b: parseInt(shortHexMatch[3] + shortHexMatch[3], 16)
            };
        }

        return null;
    }

    /** التحقق من كون اللون فاتح / Check if color is light */
    public static isLightColor(color: string): boolean {
        if (!color || color === 'transparent' || color === 'rgba(0, 0, 0, 0)') {
            return false;
        }

        const rgb = this.parseColorToRgb(color);
        if (!rgb) {
            return false;
        }

        // حساب السطوع باستخدام معادلة luminance
        const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
        return brightness > 128;
    }

    /** التحقق من كون اللون داكن / Check if color is dark */
    public static isDarkColor(color: string): boolean {
        return !this.isLightColor(color);
    }

    /** تحويل RGB إلى HEX / Convert RGB to HEX */
    public static rgbToHex(r: number, g: number, b: number): string {
        const toHex = (n: number): string => {
            const hex = Math.round(n).toString(16);
            return hex.length === 1 ? '0' + hex : hex;
        };

        return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
    }

    /** تحويل HEX إلى RGB / Convert HEX to RGB */
    public static hexToRgb(hex: string): { r: number; g: number; b: number } | null {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }
}
