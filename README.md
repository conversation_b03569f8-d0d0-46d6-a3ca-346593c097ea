# 🚀 YouTube Dark CyberX

> **م<PERSON>غ<PERSON> YouTube مخصص مع ميزات متقدمة**  
> Custom YouTube Player with Advanced Features

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Electron](https://img.shields.io/badge/Electron-27.0.0-blue.svg)](https://electronjs.org/)
[![Node.js](https://img.shields.io/badge/Node.js-16%2B-green.svg)](https://nodejs.org/)

## 📋 الوصف / Description

**YouTube Dark CyberX** هو تطبيق سطح مكتب مبني بـ Electron يوفر تجربة مشاهدة محسنة لـ YouTube مع ميزات متقدمة مثل مانع الإعلانات القوي، الوضع المظلم التلقائي، والتحكم في جودة الفيديو.

**YouTube Dark CyberX** is an Electron-based desktop application that provides an enhanced YouTube viewing experience with advanced features like powerful ad blocking, automatic dark mode, and video quality control.

## ✨ الميزات الرئيسية / Key Features

### 🛡️ مانع الإعلانات القوي
- حجب جميع أنواع الإعلانات على YouTube
- فلترة متقدمة للطلبات الإعلانية
- تحسين سرعة التحميل

### 🌙 الوضع المظلم التلقائي
- تفعيل الوضع المظلم تلقائياً
- تخصيص ألوان الواجهة
- حماية العينين أثناء المشاهدة الليلية

### 🎚️ التحكم في جودة الفيديو
- تعيين جودة افتراضية لجميع الفيديوهات
- دعم جميع الجودات من 240p إلى 4K
- إخفاء أزرار تغيير الجودة (اختياري)

### 🔧 إعدادات متقدمة
- واجهة إعدادات سهلة الاستخدام
- حفظ الإعدادات تلقائياً
- إمكانية إعادة تعيين الإعدادات

### 💻 تطبيق محمول
- ملف EXE واحد قابل للتشغيل
- لا يحتاج تثبيت
- يعمل على Windows 10/11

## 🏗️ بنية المشروع / Project Structure

```
youtube-dark-cyber-x/
├── src/                          # الكود المصدري
│   ├── main/                     # العملية الرئيسية
│   │   └── main.js              # ملف التطبيق الرئيسي
│   ├── preload/                 # سكريبتات التحميل المسبق
│   │   ├── youtube-preload.js   # سكريبت YouTube
│   │   └── settings-preload.js  # سكريبت الإعدادات
│   ├── renderer/                # واجهة المستخدم
│   │   ├── settings.html        # صفحة الإعدادات
│   │   ├── settings.css         # أنماط الإعدادات
│   │   └── settings.js          # منطق الإعدادات
│   └── tests/                   # الاختبارات
├── assets/                      # الموارد
│   └── icons/                   # الأيقونات
├── config/                      # ملفات التكوين
│   └── settings.json           # إعدادات التطبيق
├── scripts/                     # سكريبتات البناء
│   └── build.js                # سكريبت البناء
├── docs/                        # التوثيق
├── dist/                        # ملفات البناء النهائية
├── package.json                 # تبعيات المشروع
└── README.md                    # هذا الملف
```

## 🚀 التثبيت والتشغيل / Installation & Usage

### المتطلبات الأساسية / Prerequisites

- **Node.js** 16.0.0 أو أحدث
- **npm** 7.0.0 أو أحدث
- **Windows** 10/11 (64-bit)

### 1. تحميل المشروع / Download Project

```bash
git clone https://github.com/user/youtube-dark-cyber-x.git
cd youtube-dark-cyber-x
```

### 2. تثبيت التبعيات / Install Dependencies

```bash
npm install
```

### 3. تشغيل التطبيق في وضع التطوير / Run in Development Mode

```bash
npm start
# أو
npm run dev
```

### 4. بناء التطبيق للإنتاج / Build for Production

```bash
# بناء ملف محمول
npm run build-portable

# بناء مثبت Windows
npm run build-win

# أو استخدام سكريبت البناء المخصص
node scripts/build.js portable
```

## ⚙️ الإعدادات / Settings

### الوصول للإعدادات / Accessing Settings

1. افتح التطبيق
2. اضغط على `Ctrl+,` أو من القائمة: **ملف > الإعدادات**
3. قم بتخصيص الإعدادات حسب تفضيلاتك
4. اضغط **حفظ الإعدادات**

### الإعدادات المتاحة / Available Settings

| الإعداد | الوصف | القيم المتاحة |
|---------|--------|-------------|
| مانع الإعلانات | تفعيل/إلغاء مانع الإعلانات | تفعيل/إلغاء |
| الوضع المظلم | تفعيل الوضع المظلم تلقائياً | تفعيل/إلغاء |
| جودة الفيديو | الجودة الافتراضية للفيديوهات | 240p - 4K |
| إخفاء زر الجودة | منع تغيير الجودة يدوياً | تفعيل/إلغاء |
| التطبيق التلقائي | تطبيق الإعدادات على كل فيديو | تفعيل/إلغاء |

## 🔧 التطوير / Development

### تشغيل الاختبارات / Running Tests

```bash
npm test
```

### فحص الكود / Code Linting

```bash
npm run lint
```

### تنسيق الكود / Code Formatting

```bash
npm run format
```

### بناء مخصص / Custom Build

```bash
# بناء للويندوز فقط
npm run build-win

# بناء محمول
npm run build-portable

# بناء شامل
npm run build
```

## 📁 ملفات التكوين / Configuration Files

### package.json
يحتوي على تبعيات المشروع وسكريبتات البناء.

### config/settings.json
يحتوي على الإعدادات الافتراضية وتكوين التطبيق.

### src/main/main.js
الملف الرئيسي للتطبيق الذي يدير النوافذ والأحداث.

## 🛡️ الأمان / Security

- **لا توجد بيانات خارجية**: جميع العمليات تتم محلياً
- **مانع إعلانات آمن**: يحجب الطلبات الضارة فقط
- **عدم تتبع**: لا يرسل أي بيانات للخوادم الخارجية
- **كود مفتوح**: يمكن مراجعة الكود بالكامل

## 🐛 الإبلاغ عن الأخطاء / Bug Reports

إذا واجهت أي مشاكل، يرجى:

1. التأكد من استخدام أحدث إصدار
2. التحقق من متطلبات النظام
3. إنشاء تقرير مفصل عن المشكلة

## 📄 الترخيص / License

هذا المشروع مرخص تحت [رخصة MIT](LICENSE) - راجع ملف LICENSE للتفاصيل.

## 👨‍💻 المطور / Developer

- **المطور**: AI Assistant
- **الإصدار**: 1.0.0
- **تاريخ الإنشاء**: 2024-01-01

## ⚠️ إخلاء المسؤولية / Disclaimer

هذا التطبيق مصمم للاستخدام الشخصي فقط ويلتزم بشروط خدمة YouTube. المطور غير مسؤول عن أي استخدام غير مناسب للتطبيق.

---

**ملاحظة**: هذا التطبيق لا يحتوي على أي ميزات ربحية مخفية أو ضارة. جميع الميزات شفافة ومصممة لتحسين تجربة المستخدم فقط.
