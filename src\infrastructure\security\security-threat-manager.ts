/**
 * مدير التهديدات الأمنية
 * Security threat manager
 * 
 * هذا الملف يحتوي على منطق إدارة وتحليل التهديدات الأمنية
 * This file contains threat management and analysis logic
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ThreatLevel } from '@shared/types';
import { 
    SecurityThreatType, 
    SecurityThreatReport, 
    SecurityStats,
    SecurityManagerConfig,
    SECURITY_THREAT_PATTERNS,
    SECURITY_MESSAGES,
    SECURITY_MANAGER_LIMITS
} from './security-manager-config';

/**
 * فئة مدير التهديدات الأمنية
 * Security threat manager class
 */
export class SecurityThreatManager {
    private readonly threatReports: SecurityThreatReport[] = [];
    private readonly config: SecurityManagerConfig;
    private readonly stats: SecurityStats;

    /**
     * منشئ مدير التهديدات
     * Threat manager constructor
     * 
     * @param config - تكوين مدير الأمان
     */
    constructor(config: SecurityManagerConfig) {
        this.config = config;
        this.stats = {
            totalThreats: 0,
            blockedThreats: 0,
            allowedRequests: 0,
            highRiskThreats: 0,
            mediumRiskThreats: 0,
            lowRiskThreats: 0,
            startTime: new Date()
        };
    }

    /**
     * تحليل النص للبحث عن تهديدات XSS
     * Analyze text for XSS threats
     * 
     * @param input - النص المراد تحليله
     * @param source - مصدر النص
     * @returns تقرير التهديد أو null
     */
    public analyzeXSSThreats(input: string, source: string): SecurityThreatReport | null {
        if (!this.config.enableXSSProtection) {
            return null;
        }

        if (input.length > SECURITY_MANAGER_LIMITS.MAX_INPUT_LENGTH) {
            return this.createThreatReport(
                SecurityThreatType.XSS_ATTACK,
                ThreatLevel.HIGH,
                'Input length exceeds maximum allowed',
                source,
                true
            );
        }

        for (const pattern of SECURITY_THREAT_PATTERNS.XSS_PATTERNS) {
            if (pattern.test(input)) {
                return this.createThreatReport(
                    SecurityThreatType.XSS_ATTACK,
                    ThreatLevel.HIGH,
                    SECURITY_MESSAGES.XSS_DETECTED,
                    source,
                    this.config.autoBlockHighRiskThreats
                );
            }
        }

        return null;
    }

    /**
     * تحليل النص للبحث عن تهديدات SQL Injection
     * Analyze text for SQL injection threats
     * 
     * @param input - النص المراد تحليله
     * @param source - مصدر النص
     * @returns تقرير التهديد أو null
     */
    public analyzeSQLInjectionThreats(input: string, source: string): SecurityThreatReport | null {
        if (!this.config.enableSQLInjectionProtection) {
            return null;
        }

        for (const pattern of SECURITY_THREAT_PATTERNS.SQL_INJECTION_PATTERNS) {
            if (pattern.test(input)) {
                return this.createThreatReport(
                    SecurityThreatType.SQL_INJECTION,
                    ThreatLevel.HIGH,
                    SECURITY_MESSAGES.SQL_INJECTION_DETECTED,
                    source,
                    this.config.autoBlockHighRiskThreats
                );
            }
        }

        return null;
    }

    /**
     * تحليل الرابط للبحث عن تهديدات
     * Analyze URL for threats
     * 
     * @param url - الرابط المراد تحليله
     * @param source - مصدر الرابط
     * @returns تقرير التهديد أو null
     */
    public analyzeURLThreats(url: string, source: string): SecurityThreatReport | null {
        if (!this.config.enableURLValidation) {
            return null;
        }

        if (url.length > SECURITY_MANAGER_LIMITS.MAX_URL_LENGTH) {
            return this.createThreatReport(
                SecurityThreatType.MALICIOUS_URL,
                ThreatLevel.MEDIUM,
                'URL length exceeds maximum allowed',
                source,
                true
            );
        }

        for (const pattern of SECURITY_THREAT_PATTERNS.MALICIOUS_URL_PATTERNS) {
            if (pattern.test(url)) {
                return this.createThreatReport(
                    SecurityThreatType.MALICIOUS_URL,
                    ThreatLevel.HIGH,
                    SECURITY_MESSAGES.MALICIOUS_URL_DETECTED,
                    source,
                    this.config.autoBlockHighRiskThreats
                );
            }
        }

        return null;
    }

    /**
     * تحليل السكريبت للبحث عن تهديدات
     * Analyze script for threats
     * 
     * @param script - السكريبت المراد تحليله
     * @param source - مصدر السكريبت
     * @returns تقرير التهديد أو null
     */
    public analyzeScriptThreats(script: string, source: string): SecurityThreatReport | null {
        if (!this.config.enableScriptValidation) {
            return null;
        }

        if (script.length > SECURITY_MANAGER_LIMITS.MAX_SCRIPT_LENGTH) {
            return this.createThreatReport(
                SecurityThreatType.SUSPICIOUS_SCRIPT,
                ThreatLevel.MEDIUM,
                'Script length exceeds maximum allowed',
                source,
                true
            );
        }

        for (const pattern of SECURITY_THREAT_PATTERNS.SUSPICIOUS_SCRIPT_PATTERNS) {
            if (pattern.test(script)) {
                return this.createThreatReport(
                    SecurityThreatType.SUSPICIOUS_SCRIPT,
                    ThreatLevel.MEDIUM,
                    SECURITY_MESSAGES.SUSPICIOUS_SCRIPT_DETECTED,
                    source,
                    false
                );
            }
        }

        return null;
    }

    /**
     * إضافة تقرير تهديد
     * Add threat report
     * 
     * @param report - تقرير التهديد
     */
    public addThreatReport(report: SecurityThreatReport): void {
        this.threatReports.push(report);
        this.updateStats(report);
        this.cleanupOldReports();

        if (this.config.logSecurityEvents) {
            console.warn('تهديد أمني / Security threat:', report);
        }
    }

    /**
     * الحصول على جميع تقارير التهديدات
     * Get all threat reports
     * 
     * @returns قائمة تقارير التهديدات
     */
    public getThreatReports(): readonly SecurityThreatReport[] {
        return [...this.threatReports];
    }

    /**
     * الحصول على الإحصائيات
     * Get statistics
     * 
     * @returns إحصائيات الأمان
     */
    public getStats(): SecurityStats {
        return { ...this.stats };
    }

    /**
     * إنشاء تقرير تهديد
     * Create threat report
     * 
     * @param threatType - نوع التهديد
     * @param threatLevel - مستوى التهديد
     * @param description - وصف التهديد
     * @param source - مصدر التهديد
     * @param blocked - هل تم حظره
     * @returns تقرير التهديد
     */
    private createThreatReport(
        threatType: SecurityThreatType,
        threatLevel: ThreatLevel,
        description: string,
        source: string,
        blocked: boolean
    ): SecurityThreatReport {
        const report: SecurityThreatReport = {
            threatType,
            threatLevel,
            description,
            timestamp: new Date(),
            source,
            blocked
        };

        this.addThreatReport(report);
        return report;
    }

    /**
     * تحديث الإحصائيات
     * Update statistics
     * 
     * @param report - تقرير التهديد
     */
    private updateStats(report: SecurityThreatReport): void {
        (this.stats as any).totalThreats++;
        
        if (report.blocked) {
            (this.stats as any).blockedThreats++;
        } else {
            (this.stats as any).allowedRequests++;
        }

        switch (report.threatLevel) {
            case ThreatLevel.HIGH:
                (this.stats as any).highRiskThreats++;
                break;
            case ThreatLevel.MEDIUM:
                (this.stats as any).mediumRiskThreats++;
                break;
            case ThreatLevel.LOW:
                (this.stats as any).lowRiskThreats++;
                break;
        }
    }

    /**
     * تنظيف التقارير القديمة
     * Cleanup old reports
     */
    private cleanupOldReports(): void {
        const maxReports = this.config.maxThreatReports;
        if (this.threatReports.length > maxReports) {
            this.threatReports.splice(0, this.threatReports.length - maxReports);
        }

        const retentionDate = new Date();
        retentionDate.setDate(retentionDate.getDate() - this.config.threatReportRetentionDays);

        const validReports = this.threatReports.filter(
            report => report.timestamp >= retentionDate
        );

        this.threatReports.length = 0;
        this.threatReports.push(...validReports);
    }
}
