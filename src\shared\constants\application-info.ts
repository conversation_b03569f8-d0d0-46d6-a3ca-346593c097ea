/**
 * معلومات التطبيق الشاملة
 * Comprehensive application information
 *
 * هذا الملف يجمع جميع معلومات التطبيق من الملفات المتخصصة
 * This file aggregates all application information from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// استيراد معلومات التطبيق الأساسية
// Import basic application information
export {
    APPLICATION_INFO, BUILD_INFO, COMBINED_APPLICATION_INFO, DEVELOPER_INFO, getApplicationInfo, getBuildInfo, getLicenseText, getSystemInfo, getVersionInfo, isDevelopment,
    isProduction, isSystemCompatible, LICENSE_INFO, PERFORMANCE_INFO, SECURITY_INFO, SYSTEM_INFO, VERSION_INFO
} from './application-basic-info';

// استيراد مسارات التطبيق
// Import application paths
export {
    APPLICATION_PATHS, BACKUP_PATHS, CONFIG_PATHS, DATABASE_PATHS, ensureDirectoryExists, FILE_PATHS, getApplicationRootPath, getBackupsPath, getCachePath, getDownloadsPath, getFileModificationDate, getFileSize, getLogsPath, getPluginsPath, getScreenshotsPath, getSettingsPath, getThemesPath, getUserDataPath, ICON_PATHS, LOG_PATHS, pathExists, RESOURCE_PATHS, SCRIPT_PATHS, STYLE_PATHS
} from './application-paths';

/**
 * إعدادات النافذة الافتراضية
 * Default window settings
 */
export const DEFAULT_WINDOW_BOUNDS = {
    WIDTH: 1200,
    HEIGHT: 800,
    MIN_WIDTH: 800,
    MIN_HEIGHT: 600,
    MAX_WIDTH: 2560,
    MAX_HEIGHT: 1440
} as const;
