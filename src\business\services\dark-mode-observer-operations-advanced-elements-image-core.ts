/**
 * الوظائف الأساسية لمعالجة الصور في الوضع المظلم
 * Core image processing functions for dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeConfig } from './dark-mode-config';

/**
 * فئة الوظائف الأساسية لمعالجة الصور
 * Core image processing functions class
 */
export class DarkModeObserverOperationsAdvancedElementsImageCore {

    /** معالجة عنصر الصورة الأساسي / Basic image element processing */
    public static processImageElement(element: HTMLImageElement, config: DarkModeConfig): void {
        try {
            // إضافة فئة الوضع المظلم
            element.classList.add('dark-mode-image');
            
            // تطبيق الأنماط الأساسية
            this.applyBaseStyles(element);
            
            // كشف نوع الصورة وتطبيق الأنماط المناسبة
            const imageType = this.detectImageType(element);
            this.applyTypeSpecificStyles(element, imageType, config);
            
        } catch (error) {
            console.error('خطأ في معالجة عنصر الصورة:', error);
        }
    }

    /** تطبيق الأنماط الأساسية / Apply base styles */
    private static applyBaseStyles(element: HTMLImageElement): void {
        const baseStyles = {
            borderRadius: '4px',
            transition: 'all 0.3s ease',
            maxWidth: '100%',
            height: 'auto'
        };

        Object.assign(element.style, baseStyles);
    }

    /** كشف نوع الصورة / Detect image type */
    public static detectImageType(element: HTMLImageElement): string {
        const src = element.src.toLowerCase();
        const alt = element.alt.toLowerCase();
        const className = element.className.toLowerCase();
        
        // فحص الصور الرمزية
        if (className.includes('avatar') || className.includes('profile') || 
            alt.includes('avatar') || alt.includes('profile')) {
            return 'avatar';
        }
        
        // فحص الصور المصغرة
        if (className.includes('thumb') || className.includes('preview') ||
            alt.includes('thumb') || alt.includes('preview')) {
            return 'thumbnail';
        }
        
        // فحص البانرات
        if (className.includes('banner') || className.includes('header') ||
            alt.includes('banner') || alt.includes('header')) {
            return 'banner';
        }
        
        // فحص الأيقونات
        if (className.includes('icon') || src.includes('icon') ||
            alt.includes('icon') || element.width < 64 || element.height < 64) {
            return 'icon';
        }
        
        return 'default';
    }

    /** تطبيق أنماط حسب النوع / Apply type-specific styles */
    public static applyTypeSpecificStyles(element: HTMLImageElement, type: string, config: DarkModeConfig): void {
        switch (type) {
            case 'avatar':
                this.applyAvatarStyles(element);
                break;
            case 'thumbnail':
                this.applyThumbnailStyles(element);
                break;
            case 'banner':
                this.applyBannerStyles(element);
                break;
            case 'icon':
                this.applyIconStyles(element);
                break;
            default:
                this.applyDefaultStyles(element);
                break;
        }
    }

    /** تطبيق أنماط الصور الرمزية / Apply avatar styles */
    private static applyAvatarStyles(element: HTMLImageElement): void {
        const styles = {
            borderRadius: '50%',
            border: '2px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)'
        };

        Object.assign(element.style, styles);
    }

    /** تطبيق أنماط الصور المصغرة / Apply thumbnail styles */
    private static applyThumbnailStyles(element: HTMLImageElement): void {
        const styles = {
            borderRadius: '8px',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.4)',
            cursor: 'pointer'
        };

        Object.assign(element.style, styles);
    }

    /** تطبيق أنماط البانرات / Apply banner styles */
    private static applyBannerStyles(element: HTMLImageElement): void {
        const styles = {
            borderRadius: '12px',
            boxShadow: '0 8px 24px rgba(0, 0, 0, 0.3)',
            width: '100%',
            objectFit: 'cover' as const
        };

        Object.assign(element.style, styles);
    }

    /** تطبيق أنماط الأيقونات / Apply icon styles */
    private static applyIconStyles(element: HTMLImageElement): void {
        const styles = {
            borderRadius: '4px',
            filter: 'brightness(0.9) contrast(1.1)'
        };

        Object.assign(element.style, styles);

        // تطبيق فلتر خاص للأيقونات الفاتحة
        if (this.isLightIcon(element)) {
            element.style.filter += ' invert(1)';
        }
    }

    /** تطبيق أنماط افتراضية / Apply default styles */
    private static applyDefaultStyles(element: HTMLImageElement): void {
        const styles = {
            borderRadius: '6px',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)'
        };

        Object.assign(element.style, styles);
    }

    /** فحص إذا كانت الأيقونة فاتحة / Check if icon is light */
    private static isLightIcon(element: HTMLImageElement): boolean {
        const src = element.src.toLowerCase();
        const lightIndicators = ['light', 'white', 'bright'];
        
        return lightIndicators.some(indicator => src.includes(indicator));
    }

    /** إضافة صورة بديلة / Add placeholder image */
    public static addPlaceholderImage(element: HTMLImageElement, config: DarkModeConfig): void {
        const placeholder = document.createElement('div');
        placeholder.className = 'dark-mode-image-placeholder';
        
        const styles = {
            width: element.style.width || '100px',
            height: element.style.height || '100px',
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            border: '2px dashed rgba(255, 255, 255, 0.3)',
            borderRadius: element.style.borderRadius || '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'rgba(255, 255, 255, 0.6)',
            fontSize: '14px',
            fontFamily: 'Arial, sans-serif'
        };

        Object.assign(placeholder.style, styles);
        placeholder.textContent = 'صورة غير متاحة';

        // استبدال العنصر
        if (element.parentElement) {
            element.parentElement.replaceChild(placeholder, element);
        }
    }
}
