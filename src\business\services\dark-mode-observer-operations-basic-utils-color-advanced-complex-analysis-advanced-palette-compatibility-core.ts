/**
 * تحليل توافق لوحة الألوان الأساسي المتقدم المعقد المتقدم
 * Advanced complex advanced core palette compatibility analysis
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic } from './dark-mode-observer-operations-basic-utils-color-advanced-complex-analysis-basic';

/**
 * فئة تحليل توافق لوحة الألوان الأساسي المتقدم المعقد المتقدم
 * Advanced complex advanced core palette compatibility analysis class
 */
export class DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisAdvancedPaletteCompatibilityCore {

    /** تحليل التوافق بين الألوان / Analyze color compatibility */
    public static analyzeColorCompatibility(colors: string[]): {
        compatibilityMatrix: number[][];
        averageCompatibility: number;
        problematicPairs: Array<{
            color1Index: number;
            color2Index: number;
            issue: string;
            suggestion: string;
        }>;
        recommendations: string[];
    } {
        const compatibilityMatrix: number[][] = [];
        const problematicPairs: Array<{
            color1Index: number;
            color2Index: number;
            issue: string;
            suggestion: string;
        }> = [];

        // إنشاء مصفوفة التوافق
        for (let i = 0; i < colors.length; i++) {
            compatibilityMatrix[i] = [];
            for (let j = 0; j < colors.length; j++) {
                if (i === j) {
                    compatibilityMatrix[i][j] = 100; // نفس اللون
                } else {
                    const contrast = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeContrast(colors[i], colors[j]);
                    const analysis1 = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(colors[i]);
                    const analysis2 = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(colors[j]);

                    let compatibility = 50; // نقطة البداية

                    // فحص التباين
                    if (contrast.isAccessible) {
                        compatibility += 20;
                    } else {
                        compatibility -= 15;
                        problematicPairs.push({
                            color1Index: i,
                            color2Index: j,
                            issue: 'تباين ضعيف',
                            suggestion: 'تحسين التباين بتعديل السطوع'
                        });
                    }

                    // فحص التوافق الحراري
                    if (analysis1.temperature === analysis2.temperature) {
                        compatibility += 10; // نفس درجة الحرارة
                    } else if (
                        (analysis1.temperature === 'warm' && analysis2.temperature === 'cool') ||
                        (analysis1.temperature === 'cool' && analysis2.temperature === 'warm')
                    ) {
                        compatibility += 15; // تباين حراري جيد
                    }

                    // فحص التشبع
                    if (analysis1.hsl && analysis2.hsl) {
                        const saturationDiff = Math.abs(analysis1.hsl.s - analysis2.hsl.s);
                        if (saturationDiff < 20) {
                            compatibility += 10; // تشبع متشابه
                        } else if (saturationDiff > 60) {
                            compatibility -= 5; // تشبع متباين جداً
                        }

                        // فحص السطوع
                        const lightnessDiff = Math.abs(analysis1.hsl.l - analysis2.hsl.l);
                        if (lightnessDiff > 30) {
                            compatibility += 15; // تباين جيد في السطوع
                        } else if (lightnessDiff < 10) {
                            compatibility -= 10; // سطوع متشابه جداً
                            problematicPairs.push({
                                color1Index: i,
                                color2Index: j,
                                issue: 'سطوع متشابه',
                                suggestion: 'زيادة التباين في السطوع'
                            });
                        }
                    }

                    compatibilityMatrix[i][j] = Math.max(0, Math.min(100, compatibility));
                }
            }
        }

        // حساب متوسط التوافق
        let totalCompatibility = 0;
        let pairCount = 0;
        for (let i = 0; i < colors.length; i++) {
            for (let j = i + 1; j < colors.length; j++) {
                totalCompatibility += compatibilityMatrix[i][j];
                pairCount++;
            }
        }
        const averageCompatibility = pairCount > 0 ? totalCompatibility / pairCount : 0;

        // إنشاء التوصيات
        const recommendations: string[] = [];
        if (averageCompatibility < 50) {
            recommendations.push('اللوحة تحتاج تحسين شامل');
        } else if (averageCompatibility < 70) {
            recommendations.push('اللوحة تحتاج بعض التحسينات');
        } else {
            recommendations.push('اللوحة متوافقة بشكل جيد');
        }

        if (problematicPairs.length > colors.length / 2) {
            recommendations.push('كثرة المشاكل في التوافق - يُنصح بإعادة تصميم اللوحة');
        }

        return {
            compatibilityMatrix,
            averageCompatibility: Math.round(averageCompatibility),
            problematicPairs,
            recommendations
        };
    }

    /** فحص التوافق بين لونين / Check compatibility between two colors */
    public static checkPairCompatibility(color1: string, color2: string): {
        score: number;
        issues: string[];
        strengths: string[];
    } {
        const issues: string[] = [];
        const strengths: string[] = [];
        let score = 50;

        const contrast = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeContrast(color1, color2);
        const analysis1 = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(color1);
        const analysis2 = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(color2);

        // فحص التباين
        if (contrast.isAccessible) {
            strengths.push('تباين جيد للوصولية');
            score += 20;
        } else {
            issues.push('تباين ضعيف');
            score -= 15;
        }

        // فحص التوافق الحراري
        if (analysis1.temperature === analysis2.temperature) {
            if (analysis1.temperature !== 'neutral') {
                strengths.push('توافق حراري متناسق');
                score += 10;
            }
        } else if (
            (analysis1.temperature === 'warm' && analysis2.temperature === 'cool') ||
            (analysis1.temperature === 'cool' && analysis2.temperature === 'warm')
        ) {
            strengths.push('تباين حراري متوازن');
            score += 15;
        }

        // فحص التشبع والسطوع
        if (analysis1.hsl && analysis2.hsl) {
            const saturationDiff = Math.abs(analysis1.hsl.s - analysis2.hsl.s);
            const lightnessDiff = Math.abs(analysis1.hsl.l - analysis2.hsl.l);

            if (saturationDiff < 20) {
                strengths.push('تشبع متناسق');
                score += 10;
            } else if (saturationDiff > 60) {
                issues.push('تشبع متباين جداً');
                score -= 5;
            }

            if (lightnessDiff > 30) {
                strengths.push('تباين جيد في السطوع');
                score += 15;
            } else if (lightnessDiff < 10) {
                issues.push('سطوع متشابه جداً');
                score -= 10;
            }
        }

        return {
            score: Math.max(0, Math.min(100, score)),
            issues,
            strengths
        };
    }

    /** تحليل التوزيع اللوني في اللوحة / Analyze color distribution in palette */
    public static analyzeColorDistribution(colors: string[]): {
        hueDistribution: { [key: string]: number };
        temperatureBalance: { warm: number; cool: number; neutral: number };
        saturationRange: { min: number; max: number; average: number };
        lightnessRange: { min: number; max: number; average: number };
        diversity: number;
    } {
        const hueDistribution: { [key: string]: number } = {};
        const temperatureBalance = { warm: 0, cool: 0, neutral: 0 };
        const saturations: number[] = [];
        const lightnesses: number[] = [];

        colors.forEach(color => {
            const analysis = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(color);
            
            // توزيع درجة الحرارة
            temperatureBalance[analysis.temperature]++;

            if (analysis.hsl) {
                // توزيع الصبغة
                const hueRange = Math.floor(analysis.hsl.h / 30) * 30; // تجميع كل 30 درجة
                const hueKey = `${hueRange}-${hueRange + 30}`;
                hueDistribution[hueKey] = (hueDistribution[hueKey] || 0) + 1;

                saturations.push(analysis.hsl.s);
                lightnesses.push(analysis.hsl.l);
            }
        });

        // حساب التنوع
        const hueRanges = Object.keys(hueDistribution).length;
        const maxPossibleRanges = 12; // 360 / 30
        const diversity = (hueRanges / maxPossibleRanges) * 100;

        return {
            hueDistribution,
            temperatureBalance,
            saturationRange: {
                min: Math.min(...saturations),
                max: Math.max(...saturations),
                average: saturations.reduce((sum, s) => sum + s, 0) / saturations.length
            },
            lightnessRange: {
                min: Math.min(...lightnesses),
                max: Math.max(...lightnesses),
                average: lightnesses.reduce((sum, l) => sum + l, 0) / lightnesses.length
            },
            diversity: Math.round(diversity)
        };
    }
}
