/**
 * دوال القراءة المتقدمة لخدمة الإعدادات
 * Advanced getter functions for settings service
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DEFAULT_APPLICATION_CONFIG } from '@shared/constants';
import { ApplicationConfig } from '@shared/types';
import { ResourceManager } from '@shared/utils/resource-manager';
import Store from 'electron-store';
import { SettingsManagerConfig } from './settings-config';
import { SettingsServiceBasicGettersSimple } from './settings-service-basic-getters-simple';
import { SettingsValidator } from './settings-validator';

/**
 * فئة دوال القراءة المتقدمة لخدمة الإعدادات
 */
export class SettingsServiceBasicGettersAdvanced {
    private readonly simpleGetters: SettingsServiceBasicGettersSimple;
    private readonly validator: SettingsValidator;

    constructor(
        config: SettingsManagerConfig,
        resourceManager: ResourceManager,
        store: Store<ApplicationConfig>,
        validator: SettingsValidator
    ) {
        this.simpleGetters = new SettingsServiceBasicGettersSimple(config, resourceManager, store, validator);
        this.validator = validator;
    }

    /**
     * الحصول على قيم متعددة / Get multiple values
     */
    public getMultipleSettings<K extends keyof ApplicationConfig>(
        keys: K[]
    ): Pick<ApplicationConfig, K> {
        try {
            const result = {} as Pick<ApplicationConfig, K>;
            
            for (const key of keys) {
                result[key] = this.simpleGetters.getSetting(key);
            }
            
            return result;
        } catch (error) {
            console.error('خطأ في الحصول على إعدادات متعددة:', error);
            return {} as Pick<ApplicationConfig, K>;
        }
    }

    /**
     * الحصول على معلومات الإعداد / Get setting info
     */
    public getSettingInfo<K extends keyof ApplicationConfig>(key: K): {
        exists: boolean;
        value: ApplicationConfig[K];
        isDefault: boolean;
        isValid: boolean;
    } {
        try {
            const exists = this.simpleGetters.hasSetting(key);
            const value = this.simpleGetters.getSetting(key);
            const defaultValue = DEFAULT_APPLICATION_CONFIG[key];
            const isDefault = JSON.stringify(value) === JSON.stringify(defaultValue);
            const isValid = this.simpleGetters.isValidSetting(key, value);

            return { exists, value, isDefault, isValid };
        } catch (error) {
            console.error(`خطأ في الحصول على معلومات الإعداد ${String(key)}:`, error);
            return {
                exists: false,
                value: DEFAULT_APPLICATION_CONFIG[key],
                isDefault: true,
                isValid: false
            };
        }
    }

    /**
     * البحث في الإعدادات / Search in settings
     */
    public searchSettings(searchTerm: string): Array<{
        key: string;
        value: any;
        matchType: 'key' | 'value';
    }> {
        try {
            const settings = this.simpleGetters.getAllSettings();
            const results: Array<{ key: string; value: any; matchType: 'key' | 'value' }> = [];
            const lowerSearchTerm = searchTerm.toLowerCase();

            for (const [key, value] of Object.entries(settings)) {
                const keyMatch = key.toLowerCase().includes(lowerSearchTerm);
                const valueMatch = String(value).toLowerCase().includes(lowerSearchTerm);

                if (keyMatch) {
                    results.push({ key, value, matchType: 'key' });
                } else if (valueMatch) {
                    results.push({ key, value, matchType: 'value' });
                }
            }

            return results;
        } catch (error) {
            console.error('خطأ في البحث في الإعدادات:', error);
            return [];
        }
    }

    /**
     * تصفية الإعدادات / Filter settings
     */
    public filterSettings(
        predicate: (key: string, value: any) => boolean
    ): Partial<ApplicationConfig> {
        try {
            const settings = this.simpleGetters.getAllSettings();
            const filtered: Partial<ApplicationConfig> = {};

            for (const [key, value] of Object.entries(settings)) {
                if (predicate(key, value)) {
                    filtered[key as keyof ApplicationConfig] = value;
                }
            }

            return filtered;
        } catch (error) {
            console.error('خطأ في تصفية الإعدادات:', error);
            return {};
        }
    }

    /**
     * الحصول على إحصائيات الإعدادات / Get settings statistics
     */
    public getSettingsStatistics(): {
        totalSettings: number;
        validSettings: number;
        invalidSettings: number;
        defaultSettings: number;
        customSettings: number;
    } {
        try {
            const settings = this.simpleGetters.getAllSettings();
            const keys = Object.keys(settings) as Array<keyof ApplicationConfig>;
            
            let validSettings = 0;
            let defaultSettings = 0;

            for (const key of keys) {
                const info = this.getSettingInfo(key);
                if (info.isValid) validSettings++;
                if (info.isDefault) defaultSettings++;
            }

            return {
                totalSettings: keys.length,
                validSettings,
                invalidSettings: keys.length - validSettings,
                defaultSettings,
                customSettings: keys.length - defaultSettings
            };
        } catch (error) {
            console.error('خطأ في الحصول على إحصائيات الإعدادات:', error);
            return {
                totalSettings: 0,
                validSettings: 0,
                invalidSettings: 0,
                defaultSettings: 0,
                customSettings: 0
            };
        }
    }

    /**
     * تنظيف الموارد / Cleanup resources
     */
    public cleanup(): void {
        this.simpleGetters.cleanup();
    }
}
