/**
 * خدمة جودة الفيديو الرئيسية
 * Main video quality service
 *
 * هذا الملف يحتوي على الخدمة الرئيسية لإدارة جودة الفيديو
 * This file contains the main service for video quality management
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// إعادة تصدير المكونات المتخصصة
// Re-export specialized components
export * from './video-quality-service-monitoring';
export * from './video-quality-service-operations';

import { ValidationResult, VideoQuality } from '@shared/types';
import { ResourceManager } from '@shared/utils/resource-manager';
import { VideoQualityApplicator } from './video-quality-applicator';
import {
    DEFAULT_VIDEO_QUALITY_CONFIG,
    VideoQualityConfig,
    VideoQualityState
} from './video-quality-config';
import { VideoQualityDetector } from './video-quality-detector';
import { VideoQualityServiceMonitoring } from './video-quality-service-monitoring';
import { VideoQualityServiceOperations } from './video-quality-service-operations';

/**
 * فئة خدمة جودة الفيديو الرئيسية
 * Main video quality service class
 */
export class VideoQualityService {
    private readonly resourceManager: ResourceManager;
    private readonly config: VideoQualityConfig;
    private readonly detector: VideoQualityDetector;
    private readonly applicator: VideoQualityApplicator;
    private readonly operations: VideoQualityServiceOperations;
    private readonly monitoring: VideoQualityServiceMonitoring;

    /**
     * منشئ خدمة جودة الفيديو
     * Video quality service constructor
     *
     * @param resourceManager - مدير الموارد
     * @param config - تكوين جودة الفيديو (اختياري)
     */
    constructor(resourceManager: ResourceManager, config?: Partial<VideoQualityConfig>) {
        this.resourceManager = resourceManager;
        this.config = { ...DEFAULT_VIDEO_QUALITY_CONFIG, ...config };
        this.detector = new VideoQualityDetector(this.config);
        this.applicator = new VideoQualityApplicator(this.config);
        this.operations = new VideoQualityServiceOperations(
            this.resourceManager,
            this.config,
            this.detector,
            this.applicator
        );
        this.monitoring = new VideoQualityServiceMonitoring(
            this.resourceManager,
            this.config
        );
    }

    /**
     * تهيئة خدمة جودة الفيديو
     * Initialize video quality service
     *
     * @returns Promise<ValidationResult> - نتيجة التهيئة
     */
    public async initialize(): Promise<ValidationResult> {
        try {
            // تطبيق الجودة الافتراضية
            if (this.config.defaultQuality !== 'auto') {
                const result = await this.operations.applyQuality(this.config.defaultQuality);
                if (!result.isValid) {
                    console.warn('فشل في تطبيق الجودة الافتراضية:', result.errors);
                }
            }

            // إخفاء محدد الجودة إذا كان مطلوباً
            if (this.config.hideQualitySelector) {
                const hideResult = await this.operations.hideQualityControls();
                if (!hideResult.isValid) {
                    console.warn('فشل في إخفاء محدد الجودة:', hideResult.errors);
                }
            }

            // بدء مراقبة التغييرات
            if (this.config.observeChanges) {
                await this.monitoring.startMonitoring();
            }

            console.log('تم تهيئة خدمة جودة الفيديو بنجاح');
            return { isValid: true, errors: [] };

        } catch (error) {
            return {
                isValid: false,
                errors: [{
                    field: 'initialization',
                    message: `خطأ في تهيئة خدمة جودة الفيديو: ${error}`,
                    code: 'INITIALIZATION_ERROR'
                }]
            };
        }
    }

    /**
     * تطبيق جودة الفيديو
     * Apply video quality
     *
     * @param quality - جودة الفيديو المطلوبة
     * @returns نتيجة التحقق من صحة العملية
     */
    public async setVideoQuality(quality: VideoQuality): Promise<ValidationResult> {
        return this.operations.applyQuality(quality);
    }

    /**
     * كشف الجودة الحالية
     * Detect current quality
     *
     * @returns الجودة المكتشفة أو null
     */
    public async getCurrentQuality(): Promise<VideoQuality | null> {
        return this.operations.detectCurrentQuality();
    }

    /**
     * إخفاء عناصر التحكم في الجودة
     * Hide quality controls
     *
     * @returns نتيجة التحقق من صحة العملية
     */
    public async hideQualitySelector(): Promise<ValidationResult> {
        return this.operations.hideQualityControls();
    }

    /**
     * إظهار عناصر التحكم في الجودة
     * Show quality controls
     *
     * @returns نتيجة التحقق من صحة العملية
     */
    public async showQualitySelector(): Promise<ValidationResult> {
        return this.operations.showQualityControls();
    }

    /**
     * بدء مراقبة تغييرات الجودة
     * Start monitoring quality changes
     *
     * @returns نتيجة التحقق من صحة العملية
     */
    public async startMonitoring(): Promise<ValidationResult> {
        return this.monitoring.startMonitoring();
    }

    /**
     * إيقاف مراقبة تغييرات الجودة
     * Stop monitoring quality changes
     *
     * @returns نتيجة التحقق من صحة العملية
     */
    public stopMonitoring(): ValidationResult {
        return this.monitoring.stopMonitoring();
    }

    /**
     * الحصول على الحالة الحالية
     * Get current state
     *
     * @returns الحالة الحالية
     */
    public getState(): VideoQualityState {
        return this.operations.getState();
    }

    /**
     * تنظيف الموارد
     * Cleanup resources
     */
    public cleanup(): void {
        this.operations.cleanup();
        this.monitoring.cleanup();
    }
