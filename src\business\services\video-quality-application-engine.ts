/**
 * محرك تطبيق جودة الفيديو
 * Video quality application engine
 *
 * هذا الملف يحتوي على منطق تطبيق جودة الفيديو الأساسي
 * This file contains core video quality application logic
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// إعادة تصدير المكونات المتخصصة
// Re-export specialized components
export * from './video-quality-application-methods';

import { ValidationResult, VideoQuality } from '@shared/types';
import {
    VIDEO_QUALITY_MESSAGES,
    VideoQualityConfig
} from './video-quality-config';
import { VideoQualityDomHelper } from './video-quality-dom-helper';

/**
 * فئة محرك تطبيق جودة الفيديو
 * Video quality application engine class
 */
export class VideoQualityApplicationEngine {
    private readonly config: VideoQualityConfig;
    private retryCount: number = 0;

    /**
     * منشئ محرك التطبيق
     * Application engine constructor
     *
     * @param config - تكوين جودة الفيديو
     */
    constructor(config: VideoQualityConfig) {
        this.config = config;
    }

    /**
     * محاولة تطبيق الجودة
     * Attempt quality application
     * 
     * @param quality - الجودة المطلوبة
     * @returns Promise<ValidationResult>
     */
    public async attemptQualityApplication(quality: VideoQuality): Promise<ValidationResult> {
        if (typeof document === 'undefined') {
            return {
                isValid: false,
                errors: [{
                    field: 'environment',
                    message: 'لا يمكن تطبيق الجودة في بيئة Node.js / Cannot apply quality in Node.js environment',
                    code: 'INVALID_ENVIRONMENT'
                }]
            };
        }

        // انتظار تحميل المشغل
        const player = await VideoQualityDomHelper.waitForPlayer();
        if (!player) {
            return {
                isValid: false,
                errors: [{
                    field: 'player',
                    message: VIDEO_QUALITY_MESSAGES.PLAYER_NOT_FOUND,
                    code: 'PLAYER_NOT_FOUND'
                }]
            };
        }

        // محاولة تطبيق الجودة بطرق مختلفة
        const methods = [
            () => VideoQualityApplicationMethods.applyQualityViaAPI(quality),
            () => VideoQualityApplicationMethods.applyQualityViaMenu(quality),
            () => VideoQualityApplicationMethods.applyQualityViaDirectSelection(quality),
            () => VideoQualityApplicationMethods.applyQualityViaDirectManipulation(quality, player)
        ];

        for (const method of methods) {
            try {
                const result = await method();
                if (result.isValid) {
                    return result;
                }
            } catch (error) {
                console.warn(`طريقة تطبيق الجودة فشلت: ${error}`);
            }
        }

        return {
            isValid: false,
            errors: [{
                field: 'quality',
                message: VIDEO_QUALITY_MESSAGES.ERROR_SET_QUALITY,
                code: 'QUALITY_APPLICATION_FAILED'
            }]
        };
    }

    /**
     * إعادة تعيين عداد المحاولات
     * Reset retry count
     */
    public resetRetryCount(): void {
        this.retryCount = 0;
    }

    /**
     * زيادة عداد المحاولات
     * Increment retry count
     */
    public incrementRetryCount(): void {
        this.retryCount++;
    }

    /**
     * الحصول على عداد المحاولات
     * Get retry count
     *
     * @returns number
     */
    public getRetryCount(): number {
        return this.retryCount;
    }

