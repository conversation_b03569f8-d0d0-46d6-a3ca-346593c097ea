# ملفات Node.js / Node.js files
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# ملفات البناء / Build files
dist/
build/
out/

# ملفات التطوير / Development files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# ملفات IDE / IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# ملفات نظام التشغيل / OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ملفات Windows / Windows files
*.exe
*.msi
*.msm
*.msp
*.lnk

# ملفات الاختبار / Test files
coverage/
.nyc_output/
*.lcov

# ملفات مؤقتة / Temporary files
*.tmp
*.temp
*.log
*.pid
*.seed
*.pid.lock

# ملفات Electron / Electron files
app/node_modules/
app/dist/
app/build/

# ملفات التبعيات / Dependency files
.pnp
.pnp.js

# ملفات TypeScript / TypeScript files
*.tsbuildinfo

# ملفات ESLint / ESLint files
.eslintcache

# ملفات Parcel / Parcel files
.cache
.parcel-cache

# ملفات Next.js / Next.js files
.next

# ملفات Nuxt.js / Nuxt.js files
.nuxt

# ملفات Gatsby / Gatsby files
.cache/
public

# ملفات Storybook / Storybook files
.out
.storybook-out

# ملفات Rush / Rush files
common/deploy/
common/temp/
common/autoinstallers/*/.npmrc
**/.rush/temp/

# ملفات خاصة بالمشروع / Project specific files
settings-backup.json
user-data/
logs/
crash-reports/

# ملفات الأيقونات المؤقتة / Temporary icon files
*.ico.tmp
*.png.tmp

# ملفات التوقيع / Signing files
*.p12
*.pfx
certificates/

# ملفات التحديث / Update files
updates/
releases/

# ملفات قاعدة البيانات / Database files
*.db
*.sqlite
*.sqlite3

# ملفات التكوين المحلية / Local configuration files
config/local.json
config/development.json
config/production.json

# ملفات النسخ الاحتياطية / Backup files
*.bak
*.backup
*.old

# ملفات الأرشيف / Archive files
*.zip
*.rar
*.7z
*.tar.gz

# ملفات الوسائط الكبيرة / Large media files
*.mp4
*.avi
*.mov
*.wmv
*.flv

# ملفات التوثيق المؤقتة / Temporary documentation files
docs/temp/
docs/build/

# ملفات الاختبار المحلية / Local test files
test-results/
screenshots/
videos/

# ملفات الأداء / Performance files
*.prof
*.heapsnapshot

# ملفات التشفير / Encryption files
*.key
*.pem
*.crt
*.csr

# ملفات التطوير المحلية / Local development files
.local/
.cache/
.tmp/
