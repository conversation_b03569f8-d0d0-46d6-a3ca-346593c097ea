/**
 * مدير حالة طبقة الأمان
 * Security layer state manager
 * 
 * هذا الملف يحتوي على إدارة حالة وإحصائيات طبقة الأمان
 * This file contains security layer state and statistics management
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import {
    SecurityLayerState,
    SecurityLayerStatus,
    SecurityStatistics,
    SecurityScanResult,
    ThreatInfo
} from './security-layer-config';

/**
 * مدير حالة طبقة الأمان
 * Security layer state manager class
 */
export class SecurityLayerStateManager {
    private state: SecurityLayerState;
    private statistics: SecurityStatistics;
    private readonly activeScans: Map<string, Promise<SecurityScanResult>>;
    private readonly threatCache: Map<string, ThreatInfo>;

    /**
     * منشئ مدير الحالة
     * State manager constructor
     */
    constructor() {
        this.activeScans = new Map();
        this.threatCache = new Map();
        
        this.state = {
            isActive: false,
            isScanning: false,
            lastScanTime: null,
            threatsDetected: 0,
            requestsProcessed: 0,
            uptime: 0,
            status: SecurityLayerStatus.INITIALIZING
        };

        this.statistics = {
            totalScans: 0,
            threatsBlocked: 0,
            requestsFiltered: 0,
            contentSanitized: 0,
            uptime: 0,
            lastUpdate: new Date(),
            averageResponseTime: 0,
            successRate: 100
        };
    }

    /**
     * تحديث الحالة
     * Update state
     */
    public updateState(updates: Partial<SecurityLayerState>): void {
        Object.assign(this.state, updates);
        this.state.uptime = Date.now() - (this.state.lastScanTime?.getTime() || Date.now());
    }

    /**
     * تحديث الإحصائيات
     * Update statistics
     */
    public updateStatistics(result: SecurityScanResult, duration: number): void {
        this.statistics.totalScans++;
        this.statistics.threatsBlocked += result.threatsFound;
        this.statistics.requestsFiltered++;
        this.statistics.lastUpdate = new Date();
        this.statistics.averageResponseTime = 
            (this.statistics.averageResponseTime + duration) / 2;
        this.statistics.successRate = 
            (this.statistics.successRate + (result.success ? 100 : 0)) / 2;
        this.statistics.uptime = Date.now() - this.statistics.lastUpdate.getTime();
    }

    /**
     * إضافة فحص نشط
     * Add active scan
     */
    public addActiveScan(url: string, scanPromise: Promise<SecurityScanResult>): void {
        this.activeScans.set(url, scanPromise);
        this.updateState({
            isScanning: true
        });
    }

    /**
     * إزالة فحص نشط
     * Remove active scan
     */
    public removeActiveScan(url: string): void {
        this.activeScans.delete(url);
        this.updateState({
            isScanning: this.activeScans.size > 0,
            lastScanTime: new Date()
        });
    }

    /**
     * الحصول على فحص نشط
     * Get active scan
     */
    public getActiveScan(url: string): Promise<SecurityScanResult> | undefined {
        return this.activeScans.get(url);
    }

    /**
     * التحقق من وجود فحص نشط
     * Check if scan is active
     */
    public hasActiveScan(url: string): boolean {
        return this.activeScans.has(url);
    }

    /**
     * إضافة تهديد إلى التخزين المؤقت
     * Add threat to cache
     */
    public addThreatToCache(threatInfo: ThreatInfo): void {
        this.threatCache.set(threatInfo.id, threatInfo);
        this.updateState({
            threatsDetected: this.state.threatsDetected + 1
        });
    }

    /**
     * الحصول على تهديد من التخزين المؤقت
     * Get threat from cache
     */
    public getThreatFromCache(threatId: string): ThreatInfo | undefined {
        return this.threatCache.get(threatId);
    }

    /**
     * إزالة تهديد من التخزين المؤقت
     * Remove threat from cache
     */
    public removeThreatFromCache(threatId: string): boolean {
        const removed = this.threatCache.delete(threatId);
        if (removed) {
            this.updateState({
                threatsDetected: Math.max(0, this.state.threatsDetected - 1)
            });
        }
        return removed;
    }

    /**
     * مسح جميع التهديدات من التخزين المؤقت
     * Clear all threats from cache
     */
    public clearThreatCache(): void {
        this.threatCache.clear();
        this.updateState({
            threatsDetected: 0
        });
    }

    /**
     * الحصول على جميع التهديدات المخزنة
     * Get all cached threats
     */
    public getAllCachedThreats(): ThreatInfo[] {
        return Array.from(this.threatCache.values());
    }

    /**
     * الحصول على عدد التهديدات المخزنة
     * Get cached threats count
     */
    public getCachedThreatsCount(): number {
        return this.threatCache.size;
    }

    /**
     * الحصول على عدد الفحوصات النشطة
     * Get active scans count
     */
    public getActiveScansCount(): number {
        return this.activeScans.size;
    }

    /**
     * الحصول على جميع الفحوصات النشطة
     * Get all active scans
     */
    public getAllActiveScans(): Map<string, Promise<SecurityScanResult>> {
        return new Map(this.activeScans);
    }

    /**
     * مسح جميع الفحوصات النشطة
     * Clear all active scans
     */
    public clearActiveScans(): void {
        this.activeScans.clear();
        this.updateState({
            isScanning: false
        });
    }

    /**
     * تحديث عداد الطلبات المعالجة
     * Update processed requests counter
     */
    public incrementProcessedRequests(): void {
        this.updateState({
            requestsProcessed: this.state.requestsProcessed + 1
        });
    }

    /**
     * تحديث عداد المحتوى المنظف
     * Update sanitized content counter
     */
    public incrementSanitizedContent(): void {
        this.statistics.contentSanitized++;
        this.statistics.lastUpdate = new Date();
    }

    /**
     * إعادة تعيين الإحصائيات
     * Reset statistics
     */
    public resetStatistics(): void {
        this.statistics = {
            totalScans: 0,
            threatsBlocked: 0,
            requestsFiltered: 0,
            contentSanitized: 0,
            uptime: 0,
            lastUpdate: new Date(),
            averageResponseTime: 0,
            successRate: 100
        };
    }

    /**
     * إعادة تعيين الحالة
     * Reset state
     */
    public resetState(): void {
        this.state = {
            isActive: false,
            isScanning: false,
            lastScanTime: null,
            threatsDetected: 0,
            requestsProcessed: 0,
            uptime: 0,
            status: SecurityLayerStatus.INITIALIZING
        };
    }

    /**
     * الحصول على الحالة الحالية
     * Get current state
     */
    public getState(): SecurityLayerState {
        return { ...this.state };
    }

    /**
     * الحصول على الإحصائيات الحالية
     * Get current statistics
     */
    public getStatistics(): SecurityStatistics {
        return { ...this.statistics };
    }

    /**
     * الحصول على ملخص الحالة
     * Get state summary
     */
    public getStateSummary(): {
        isActive: boolean;
        isScanning: boolean;
        activeScansCount: number;
        cachedThreatsCount: number;
        totalScans: number;
        threatsBlocked: number;
        successRate: number;
    } {
        return {
            isActive: this.state.isActive,
            isScanning: this.state.isScanning,
            activeScansCount: this.activeScans.size,
            cachedThreatsCount: this.threatCache.size,
            totalScans: this.statistics.totalScans,
            threatsBlocked: this.statistics.threatsBlocked,
            successRate: this.statistics.successRate
        };
    }

    /**
     * تنظيف جميع البيانات
     * Cleanup all data
     */
    public cleanup(): void {
        this.clearActiveScans();
        this.clearThreatCache();
        this.resetState();
        this.resetStatistics();
    }
}
