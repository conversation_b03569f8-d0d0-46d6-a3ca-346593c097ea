/**
 * جامع بيانات أداء YouTube
 * YouTube performance data collector
 * 
 * هذا الملف يحتوي على منطق جمع بيانات أداء YouTube
 * This file contains YouTube performance data collection logic
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ResourceManager } from '@shared/utils/resource-manager';
import { 
    YouTubeControllerPerformanceReport,
    YOUTUBE_CONTROLLER_CONSTANTS
} from './youtube-controller-config';

/**
 * فئة جامع بيانات أداء YouTube
 * YouTube performance data collector class
 */
export class YouTubePerformanceCollector {
    private readonly resourceManager: ResourceManager;
    private performanceHistory: YouTubeControllerPerformanceReport[] = [];
    private startTime: Date = new Date();
    private operationCount: number = 0;
    private errorCount: number = 0;
    private warningCount: number = 0;
    private lastCollectionTime: Date = new Date();

    /**
     * منشئ جامع بيانات الأداء
     * Performance data collector constructor
     * 
     * @param resourceManager - مدير الموارد
     */
    constructor(resourceManager: ResourceManager) {
        this.resourceManager = resourceManager;
    }

    /**
     * جمع بيانات الأداء الحالية
     * Collect current performance data
     * 
     * @returns تقرير أداء YouTube
     */
    public collectPerformanceData(): YouTubeControllerPerformanceReport {
        const now = new Date();
        const uptime = now.getTime() - this.startTime.getTime();
        const memoryUsage = this.getMemoryUsage();
        
        const report: YouTubeControllerPerformanceReport = {
            timestamp: now,
            uptime,
            memoryUsage,
            operationCount: this.operationCount,
            errorCount: this.errorCount,
            warningCount: this.warningCount,
            averageResponseTime: this.calculateAverageResponseTime(),
            cpuUsage: this.getCpuUsage(),
            networkLatency: this.getNetworkLatency(),
            activeConnections: this.getActiveConnections(),
            cacheHitRate: this.getCacheHitRate(),
            throughput: this.calculateThroughput()
        };

        // إضافة التقرير للتاريخ
        this.performanceHistory.push(report);
        
        // الحفاظ على حد أقصى للتاريخ
        const maxHistory = YOUTUBE_CONTROLLER_CONSTANTS.MAX_PERFORMANCE_HISTORY || 100;
        if (this.performanceHistory.length > maxHistory) {
            this.performanceHistory = this.performanceHistory.slice(-maxHistory);
        }

        this.lastCollectionTime = now;
        return report;
    }

    /**
     * الحصول على استخدام الذاكرة
     * Get memory usage
     * 
     * @returns استخدام الذاكرة بالميجابايت
     */
    private getMemoryUsage(): number {
        try {
            if (typeof process !== 'undefined' && process.memoryUsage) {
                const memUsage = process.memoryUsage();
                return Math.round(memUsage.heapUsed / 1024 / 1024); // MB
            }
            return 0;
        } catch (error) {
            console.warn('فشل في الحصول على استخدام الذاكرة / Failed to get memory usage:', error);
            return 0;
        }
    }

    /**
     * الحصول على استخدام المعالج
     * Get CPU usage
     * 
     * @returns استخدام المعالج كنسبة مئوية
     */
    private getCpuUsage(): number {
        try {
            // محاكاة قياس استخدام المعالج
            // في التطبيق الحقيقي، يمكن استخدام مكتبات مثل pidusage
            return Math.random() * 100;
        } catch (error) {
            console.warn('فشل في الحصول على استخدام المعالج / Failed to get CPU usage:', error);
            return 0;
        }
    }

    /**
     * الحصول على زمن استجابة الشبكة
     * Get network latency
     * 
     * @returns زمن الاستجابة بالميلي ثانية
     */
    private getNetworkLatency(): number {
        try {
            // محاكاة قياس زمن استجابة الشبكة
            return Math.random() * 200 + 10; // 10-210ms
        } catch (error) {
            console.warn('فشل في الحصول على زمن استجابة الشبكة / Failed to get network latency:', error);
            return 0;
        }
    }

    /**
     * الحصول على عدد الاتصالات النشطة
     * Get active connections count
     * 
     * @returns عدد الاتصالات النشطة
     */
    private getActiveConnections(): number {
        try {
            // محاكاة عدد الاتصالات النشطة
            return Math.floor(Math.random() * 10) + 1;
        } catch (error) {
            console.warn('فشل في الحصول على عدد الاتصالات النشطة / Failed to get active connections:', error);
            return 0;
        }
    }

    /**
     * الحصول على معدل إصابة التخزين المؤقت
     * Get cache hit rate
     * 
     * @returns معدل الإصابة كنسبة مئوية
     */
    private getCacheHitRate(): number {
        try {
            // محاكاة معدل إصابة التخزين المؤقت
            return Math.random() * 100;
        } catch (error) {
            console.warn('فشل في الحصول على معدل إصابة التخزين المؤقت / Failed to get cache hit rate:', error);
            return 0;
        }
    }

    /**
     * حساب متوسط وقت الاستجابة
     * Calculate average response time
     * 
     * @returns متوسط وقت الاستجابة بالميلي ثانية
     */
    private calculateAverageResponseTime(): number {
        if (this.performanceHistory.length === 0) {
            return 0;
        }

        const recentReports = this.performanceHistory.slice(-10); // آخر 10 تقارير
        const totalResponseTime = recentReports.reduce((sum, report) => {
            return sum + (report.averageResponseTime || 0);
        }, 0);

        return totalResponseTime / recentReports.length;
    }

    /**
     * حساب الإنتاجية
     * Calculate throughput
     * 
     * @returns الإنتاجية (عمليات في الثانية)
     */
    private calculateThroughput(): number {
        const now = new Date();
        const timeDiff = now.getTime() - this.startTime.getTime();
        
        if (timeDiff === 0) {
            return 0;
        }

        return (this.operationCount / timeDiff) * 1000; // عمليات في الثانية
    }

    /**
     * تسجيل عملية جديدة
     * Record new operation
     */
    public recordOperation(): void {
        this.operationCount++;
    }

    /**
     * تسجيل خطأ جديد
     * Record new error
     */
    public recordError(): void {
        this.errorCount++;
    }

    /**
     * تسجيل تحذير جديد
     * Record new warning
     */
    public recordWarning(): void {
        this.warningCount++;
    }

    /**
     * الحصول على تاريخ الأداء
     * Get performance history
     * 
     * @returns قائمة تقارير الأداء
     */
    public getPerformanceHistory(): YouTubeControllerPerformanceReport[] {
        return [...this.performanceHistory];
    }

    /**
     * الحصول على آخر تقرير أداء
     * Get latest performance report
     * 
     * @returns آخر تقرير أداء أو null
     */
    public getLatestReport(): YouTubeControllerPerformanceReport | null {
        return this.performanceHistory.length > 0 
            ? this.performanceHistory[this.performanceHistory.length - 1] 
            : null;
    }

    /**
     * إعادة تعيين الإحصائيات
     * Reset statistics
     */
    public resetStatistics(): void {
        this.operationCount = 0;
        this.errorCount = 0;
        this.warningCount = 0;
        this.startTime = new Date();
        this.performanceHistory = [];
    }

    /**
     * تنظيف البيانات القديمة
     * Cleanup old data
     * 
     * @param maxAge - العمر الأقصى بالميلي ثانية
     */
    public cleanupOldData(maxAge: number = 3600000): void { // ساعة واحدة افتراضياً
        const now = new Date();
        this.performanceHistory = this.performanceHistory.filter(report => {
            return (now.getTime() - report.timestamp.getTime()) <= maxAge;
        });
    }

    /**
     * الحصول على وقت آخر جمع
     * Get last collection time
     * 
     * @returns وقت آخر جمع
     */
    public getLastCollectionTime(): Date {
        return this.lastCollectionTime;
    }
}
