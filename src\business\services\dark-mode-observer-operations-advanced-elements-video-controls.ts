/**
 * عناصر التحكم في الفيديو للوضع المظلم
 * Video controls for dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeConfig } from './dark-mode-config';

/**
 * فئة عناصر التحكم في الفيديو
 * Video controls class
 */
export class DarkModeObserverOperationsAdvancedElementsVideoControls {

    /** معالجة عناصر التحكم في الفيديو / Process video controls */
    public static processVideoControls(element: HTMLVideoElement, config: DarkModeConfig): void {
        try {
            // البحث عن عناصر التحكم
            const controlsContainer = this.findControlsContainer(element);
            if (controlsContainer) {
                this.styleControlsContainer(controlsContainer);
                this.processControlButtons(controlsContainer);
                this.processProgressBar(controlsContainer);
                this.processVolumeControls(controlsContainer);
                this.processQualitySelector(controlsContainer);
            }
            
        } catch (error) {
            console.error('خطأ في معالجة عناصر التحكم:', error);
        }
    }

    /** البحث عن حاوي عناصر التحكم / Find controls container */
    private static findControlsContainer(element: HTMLVideoElement): Element | null {
        const parent = element.parentElement;
        if (!parent) return null;

        // البحث في العناصر الشقيقة
        const selectors = [
            '.ytp-chrome-bottom',
            '.video-controls',
            '.controls-container',
            '.player-controls'
        ];

        for (const selector of selectors) {
            const controls = parent.querySelector(selector);
            if (controls) return controls;
        }

        return null;
    }

    /** تنسيق حاوي عناصر التحكم / Style controls container */
    private static styleControlsContainer(container: Element): void {
        const styles = {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            backdropFilter: 'blur(10px)',
            borderTop: '1px solid rgba(255, 255, 255, 0.1)',
            borderRadius: '0 0 12px 12px'
        };

        Object.assign((container as HTMLElement).style, styles);
    }

    /** معالجة أزرار التحكم / Process control buttons */
    private static processControlButtons(container: Element): void {
        const buttons = container.querySelectorAll('button, .ytp-button');
        
        buttons.forEach(button => {
            this.styleControlButton(button as HTMLElement);
        });
    }

    /** تنسيق زر التحكم / Style control button */
    private static styleControlButton(button: HTMLElement): void {
        const styles = {
            backgroundColor: 'transparent',
            border: 'none',
            borderRadius: '6px',
            padding: '8px',
            margin: '2px',
            transition: 'all 0.2s ease',
            cursor: 'pointer'
        };

        Object.assign(button.style, styles);

        // إضافة تأثيرات التفاعل
        button.addEventListener('mouseenter', () => {
            button.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
            button.style.transform = 'scale(1.05)';
        });

        button.addEventListener('mouseleave', () => {
            button.style.backgroundColor = 'transparent';
            button.style.transform = 'scale(1)';
        });
    }

    /** معالجة شريط التقدم / Process progress bar */
    private static processProgressBar(container: Element): void {
        const progressBars = container.querySelectorAll('.ytp-progress-bar, .progress-bar');
        
        progressBars.forEach(bar => {
            this.styleProgressBar(bar as HTMLElement);
        });
    }

    /** تنسيق شريط التقدم / Style progress bar */
    private static styleProgressBar(bar: HTMLElement): void {
        const styles = {
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            borderRadius: '4px',
            height: '6px',
            overflow: 'hidden'
        };

        Object.assign(bar.style, styles);

        // تنسيق شريط التقدم المملوء
        const progressFill = bar.querySelector('.ytp-play-progress, .progress-fill');
        if (progressFill) {
            Object.assign((progressFill as HTMLElement).style, {
                backgroundColor: '#ff0000',
                borderRadius: '4px',
                transition: 'width 0.1s ease'
            });
        }

        // تنسيق شريط التحميل
        const loadProgress = bar.querySelector('.ytp-load-progress, .load-progress');
        if (loadProgress) {
            Object.assign((loadProgress as HTMLElement).style, {
                backgroundColor: 'rgba(255, 255, 255, 0.4)',
                borderRadius: '4px'
            });
        }
    }

    /** معالجة عناصر التحكم في الصوت / Process volume controls */
    private static processVolumeControls(container: Element): void {
        const volumeControls = container.querySelectorAll('.ytp-volume-panel, .volume-control');
        
        volumeControls.forEach(control => {
            this.styleVolumeControl(control as HTMLElement);
        });
    }

    /** تنسيق عنصر التحكم في الصوت / Style volume control */
    private static styleVolumeControl(control: HTMLElement): void {
        const styles = {
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            borderRadius: '8px',
            padding: '8px',
            border: '1px solid rgba(255, 255, 255, 0.1)'
        };

        Object.assign(control.style, styles);

        // تنسيق شريط الصوت
        const volumeSlider = control.querySelector('.ytp-volume-slider, .volume-slider');
        if (volumeSlider) {
            Object.assign((volumeSlider as HTMLElement).style, {
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                borderRadius: '4px'
            });
        }
    }

    /** معالجة محدد الجودة / Process quality selector */
    private static processQualitySelector(container: Element): void {
        const qualitySelectors = container.querySelectorAll('.ytp-settings-button, .quality-selector');
        
        qualitySelectors.forEach(selector => {
            this.styleQualitySelector(selector as HTMLElement);
        });
    }

    /** تنسيق محدد الجودة / Style quality selector */
    private static styleQualitySelector(selector: HTMLElement): void {
        const styles = {
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            borderRadius: '6px',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            color: '#ffffff'
        };

        Object.assign(selector.style, styles);

        // إضافة تأثيرات التفاعل
        selector.addEventListener('mouseenter', () => {
            selector.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
        });

        selector.addEventListener('mouseleave', () => {
            selector.style.backgroundColor = 'rgba(0, 0, 0, 0.6)';
        });
    }

    /** إضافة أزرار تحكم مخصصة / Add custom control buttons */
    public static addCustomControls(element: HTMLVideoElement, config: DarkModeConfig): void {
        const container = this.findControlsContainer(element);
        if (!container) return;

        // إضافة زر تبديل الوضع المظلم
        this.addDarkModeToggle(container, config);
        
        // إضافة زر تحسين الجودة
        this.addQualityEnhancer(container, config);
    }

    /** إضافة زر تبديل الوضع المظلم / Add dark mode toggle */
    private static addDarkModeToggle(container: Element, config: DarkModeConfig): void {
        const toggle = document.createElement('button');
        toggle.className = 'dark-mode-toggle-btn';
        toggle.innerHTML = '🌙';
        toggle.title = 'تبديل الوضع المظلم';

        const styles = {
            backgroundColor: 'transparent',
            border: 'none',
            color: '#ffffff',
            fontSize: '16px',
            padding: '8px',
            borderRadius: '6px',
            cursor: 'pointer',
            margin: '0 4px'
        };

        Object.assign(toggle.style, styles);

        toggle.addEventListener('click', () => {
            // تبديل الوضع المظلم
            config.enabled = !config.enabled;
            toggle.innerHTML = config.enabled ? '🌙' : '☀️';
        });

        container.appendChild(toggle);
    }

    /** إضافة زر تحسين الجودة / Add quality enhancer */
    private static addQualityEnhancer(container: Element, config: DarkModeConfig): void {
        const enhancer = document.createElement('button');
        enhancer.className = 'quality-enhancer-btn';
        enhancer.innerHTML = 'HD';
        enhancer.title = 'تحسين الجودة';

        const styles = {
            backgroundColor: 'rgba(255, 0, 0, 0.8)',
            border: 'none',
            color: '#ffffff',
            fontSize: '12px',
            fontWeight: 'bold',
            padding: '4px 8px',
            borderRadius: '4px',
            cursor: 'pointer',
            margin: '0 4px'
        };

        Object.assign(enhancer.style, styles);

        enhancer.addEventListener('click', () => {
            // تحسين جودة الفيديو
            this.enhanceVideoQuality(container);
        });

        container.appendChild(enhancer);
    }

    /** تحسين جودة الفيديو / Enhance video quality */
    private static enhanceVideoQuality(container: Element): void {
        try {
            // محاولة تعيين أعلى جودة متاحة
            const player = (window as any).ytPlayer;
            if (player && player.setPlaybackQuality) {
                const qualities = ['hd1080', 'hd720', 'large', 'medium'];
                for (const quality of qualities) {
                    try {
                        player.setPlaybackQuality(quality);
                        break;
                    } catch (error) {
                        continue;
                    }
                }
            }
        } catch (error) {
            console.warn('تعذر تحسين جودة الفيديو:', error);
        }
    }
}
