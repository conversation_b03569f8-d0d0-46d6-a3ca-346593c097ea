/**
 * اختبارات الوظائف الأساسية
 * Basic functionality tests
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

const { app, BrowserWindow } = require('electron');
const path = require('path');

describe('YouTube Dark CyberX Basic Tests', () => {
    let mainWindow;

    beforeAll(async () => {
        // تهيئة التطبيق للاختبار
        await app.whenReady();
    });

    afterAll(async () => {
        // إغلاق التطبيق بعد الاختبار
        if (app) {
            app.quit();
        }
    });

    test('should create main window', () => {
        mainWindow = new BrowserWindow({
            width: 800,
            height: 600,
            show: false,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true
            }
        });

        expect(mainWindow).toBeDefined();
        expect(mainWindow.isDestroyed()).toBe(false);
    });

    test('should load YouTube URL', async () => {
        if (mainWindow) {
            await mainWindow.loadURL('https://www.youtube.com');
            const url = mainWindow.webContents.getURL();
            expect(url).toContain('youtube.com');
        }
    });

    test('should have secure webPreferences', () => {
        if (mainWindow) {
            const webPreferences = mainWindow.webContents.getWebPreferences();
            expect(webPreferences.nodeIntegration).toBe(false);
            expect(webPreferences.contextIsolation).toBe(true);
        }
    });
});

// اختبار تحميل الملفات المطلوبة
describe('Required Files Test', () => {
    const fs = require('fs');

    test('main.js should exist', () => {
        const mainPath = path.join(__dirname, '../src/main/main.js');
        expect(fs.existsSync(mainPath)).toBe(true);
    });

    test('preload scripts should exist', () => {
        const youtubePreloadPath = path.join(__dirname, '../src/preload/youtube-preload.js');
        const settingsPreloadPath = path.join(__dirname, '../src/preload/settings-preload.js');
        
        expect(fs.existsSync(youtubePreloadPath)).toBe(true);
        expect(fs.existsSync(settingsPreloadPath)).toBe(true);
    });

    test('settings files should exist', () => {
        const settingsHtmlPath = path.join(__dirname, '../src/renderer/settings.html');
        const settingsCssPath = path.join(__dirname, '../src/renderer/settings.css');
        const settingsJsPath = path.join(__dirname, '../src/renderer/settings.js');
        
        expect(fs.existsSync(settingsHtmlPath)).toBe(true);
        expect(fs.existsSync(settingsCssPath)).toBe(true);
        expect(fs.existsSync(settingsJsPath)).toBe(true);
    });

    test('config file should exist and be valid JSON', () => {
        const configPath = path.join(__dirname, '../config/settings.json');
        expect(fs.existsSync(configPath)).toBe(true);
        
        const configContent = fs.readFileSync(configPath, 'utf8');
        expect(() => JSON.parse(configContent)).not.toThrow();
    });
});
