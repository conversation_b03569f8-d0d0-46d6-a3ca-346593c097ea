/**
 * الأنواع الأساسية لأداة التحقق المبسطة
 * Simple verification core types
 * 
 * هذا الملف يحتوي على تعريفات الأنواع الأساسية
 * This file contains core type definitions
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * نتيجة التحقق المبسط
 * Simple verification result
 */
export interface SimpleVerificationResult {
    readonly constitutionCompliance: number;
    readonly codeQuality: number;
    readonly projectStructure: number;
    readonly overallScore: number;
    readonly grade: string;
    readonly issues: string[];
    readonly recommendations: string[];
}

/**
 * إعدادات التحقق المبسط
 * Simple verification configuration
 */
export interface SimpleVerificationConfig {
    readonly projectRoot: string;
    readonly enableDetailedLogging: boolean;
    readonly skipLargeFiles: boolean;
    readonly maxFileSize: number;
    readonly excludePatterns: string[];
    readonly includePatterns: string[];
    readonly maxFileLines: number;
}

/**
 * نتيجة فحص الملف
 * File check result
 */
export interface FileCheckResult {
    readonly filePath: string;
    readonly isValid: boolean;
    readonly issues: string[];
    readonly score: number;
    readonly lineCount: number;
    readonly hasDocumentation: boolean;
    readonly followsNamingConvention: boolean;
}

/**
 * نتيجة فحص المجلد
 * Directory check result
 */
export interface DirectoryCheckResult {
    readonly dirPath: string;
    readonly isValid: boolean;
    readonly issues: string[];
    readonly score: number;
    readonly fileCount: number;
    readonly subdirectoryCount: number;
    readonly followsStructure: boolean;
}

/**
 * معايير التقييم الافتراضية
 * Default evaluation criteria
 */
export const DEFAULT_EVALUATION_CRITERIA = {
    CONSTITUTION_WEIGHT: 0.4,
    CODE_QUALITY_WEIGHT: 0.35,
    PROJECT_STRUCTURE_WEIGHT: 0.25,
    EXCELLENT_THRESHOLD: 90,
    GOOD_THRESHOLD: 75,
    FAIR_THRESHOLD: 60,
    POOR_THRESHOLD: 40
} as const;

/**
 * درجات التقييم
 * Evaluation grades
 */
export type EvaluationGrade = 'ممتاز' | 'جيد' | 'مقبول' | 'ضعيف' | 'فاشل';

/**
 * أنواع المشاكل
 * Issue types
 */
export enum IssueType {
    FILE_SIZE = 'file-size',
    NAMING_CONVENTION = 'naming-convention',
    DOCUMENTATION = 'documentation',
    CODE_QUALITY = 'code-quality',
    SECURITY = 'security',
    PERFORMANCE = 'performance',
    STRUCTURE = 'structure'
}

/**
 * مستويات الخطورة
 * Severity levels
 */
export enum SeverityLevel {
    LOW = 'low',
    MEDIUM = 'medium',
    HIGH = 'high',
    CRITICAL = 'critical'
}

/**
 * مشكلة مع التفاصيل
 * Issue with details
 */
export interface DetailedIssue {
    readonly type: IssueType;
    readonly severity: SeverityLevel;
    readonly message: string;
    readonly filePath?: string;
    readonly lineNumber?: number;
    readonly suggestion?: string;
}

/**
 * إحصائيات التحقق
 * Verification statistics
 */
export interface VerificationStatistics {
    readonly totalFiles: number;
    readonly validFiles: number;
    readonly invalidFiles: number;
    readonly totalDirectories: number;
    readonly validDirectories: number;
    readonly invalidDirectories: number;
    readonly totalIssues: number;
    readonly issuesByType: Record<IssueType, number>;
    readonly issuesBySeverity: Record<SeverityLevel, number>;
    readonly averageScore: number;
    readonly processingTime: number;
}

/**
 * تقرير التحقق المفصل
 * Detailed verification report
 */
export interface DetailedVerificationReport {
    readonly summary: VerificationStatistics;
    readonly constitutionCompliance: ConstitutionCheckResult;
    readonly codeQuality: CodeQualityCheckResult;
    readonly projectStructure: ProjectStructureCheckResult;
    readonly fileResults: FileCheckResult[];
    readonly directoryResults: DirectoryCheckResult[];
    readonly detailedIssues: DetailedIssue[];
    readonly recommendations: string[];
    readonly timestamp: string;
}

/**
 * نتيجة فحص الامتثال للدستور
 * Constitution compliance check result
 */
export interface ConstitutionCheckResult {
    readonly score: number;
    readonly issues: string[];
    readonly recommendations: string[];
    readonly fileSizeViolations: string[];
    readonly namingViolations: string[];
    readonly documentationIssues: string[];
    readonly structureIssues: string[];
    readonly totalFiles: number;
    readonly compliantFiles: number;
}

/**
 * نتيجة فحص جودة الكود
 * Code quality check result
 */
export interface CodeQualityCheckResult {
    readonly score: number;
    readonly issues: string[];
    readonly recommendations: string[];
    readonly complexityIssues: string[];
    readonly typeIssues: string[];
    readonly securityIssues: string[];
    readonly performanceIssues: string[];
    readonly totalFiles: number;
    readonly qualityFiles: number;
}

/**
 * نتيجة فحص بنية المشروع
 * Project structure check result
 */
export interface ProjectStructureCheckResult {
    readonly score: number;
    readonly issues: string[];
    readonly recommendations: string[];
    readonly missingDirectories: string[];
    readonly extraDirectories: string[];
    readonly organizationIssues: string[];
    readonly requiredDirectories: number;
    readonly actualDirectories: number;
}
