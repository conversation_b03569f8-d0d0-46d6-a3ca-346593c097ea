/**
 * العمليات الأساسية للنسخ الاحتياطية للإعدادات
 * Settings backup core operations
 * 
 * هذا الملف يحتوي على العمليات الأساسية للنسخ الاحتياطية
 * This file contains core backup operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ApplicationConfig, ValidationResult } from '@shared/types';
import * as crypto from 'crypto';
import * as fs from 'fs';
import * as path from 'path';
import {
    BackupInfo,
    ExportOptions,
    ImportOptions,
    SETTINGS_MESSAGES,
    SettingsErrorReport,
    SettingsErrorType,
    SettingsManagerConfig
} from './settings-config';

/**
 * العمليات الأساسية للنسخ الاحتياطية
 * Core backup operations class
 */
export class SettingsBackupCoreOperations {
    private readonly config: SettingsManagerConfig;
    private readonly backupDirectory: string;

    /**
     * منشئ العمليات الأساسية / Core operations constructor
     */
    constructor(config: SettingsManagerConfig, backupDirectory: string) {
        this.config = config;
        this.backupDirectory = backupDirectory;
    }

    /**
     * إنشاء نسخة احتياطية / Create backup
     */
    public async createBackup(settings: ApplicationConfig, description?: string): Promise<BackupInfo> {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `settings-backup-${timestamp}.json`;
            const filePath = path.join(this.backupDirectory, filename);

            const backupData = {
                version: '1.0.0',
                timestamp: new Date().toISOString(),
                description: description || 'تلقائي / Automatic',
                settings: settings,
                checksum: this.calculateChecksum(JSON.stringify(settings))
            };

            await fs.promises.writeFile(filePath, JSON.stringify(backupData, null, 2), 'utf8');

            return {
                id: this.generateBackupId(),
                filename,
                filePath,
                timestamp: new Date(),
                size: (await fs.promises.stat(filePath)).size,
                description: backupData.description,
                checksum: backupData.checksum,
                isValid: true
            };
        } catch (error) {
            throw new Error(`فشل في إنشاء النسخة الاحتياطية: ${error}`);
        }
    }

    /**
     * استعادة من نسخة احتياطية / Restore from backup
     */
    public async restoreFromBackup(backupPath: string): Promise<ApplicationConfig> {
        try {
            if (!fs.existsSync(backupPath)) {
                throw new Error('ملف النسخة الاحتياطية غير موجود');
            }

            const backupContent = await fs.promises.readFile(backupPath, 'utf8');
            const backupData = JSON.parse(backupContent);

            // التحقق من صحة البيانات
            if (!this.validateBackupData(backupData)) {
                throw new Error('بيانات النسخة الاحتياطية غير صحيحة');
            }

            // التحقق من التوقيع
            const calculatedChecksum = this.calculateChecksum(JSON.stringify(backupData.settings));
            if (calculatedChecksum !== backupData.checksum) {
                throw new Error('فشل في التحقق من سلامة البيانات');
            }

            return backupData.settings;
        } catch (error) {
            throw new Error(`فشل في استعادة النسخة الاحتياطية: ${error}`);
        }
    }

    /**
     * حذف نسخة احتياطية / Delete backup
     */
    public async deleteBackup(backupPath: string): Promise<boolean> {
        try {
            if (fs.existsSync(backupPath)) {
                await fs.promises.unlink(backupPath);
                return true;
            }
            return false;
        } catch (error) {
            throw new Error(`فشل في حذف النسخة الاحتياطية: ${error}`);
        }
    }

    /**
     * تصدير الإعدادات / Export settings
     */
    public async exportSettings(
        settings: ApplicationConfig,
        exportPath: string,
        options: ExportOptions = {}
    ): Promise<boolean> {
        try {
            const exportData = {
                version: '1.0.0',
                exportDate: new Date().toISOString(),
                application: 'YouTube Dark Cyber X',
                settings: options.includeSecrets ? settings : this.sanitizeSettings(settings),
                metadata: {
                    format: options.format || 'json',
                    compressed: options.compressed || false,
                    encrypted: options.encrypted || false
                }
            };

            let content = JSON.stringify(exportData, null, 2);

            if (options.encrypted) {
                content = this.encryptData(content, options.encryptionKey || '');
            }

            await fs.promises.writeFile(exportPath, content, 'utf8');
            return true;
        } catch (error) {
            throw new Error(`فشل في تصدير الإعدادات: ${error}`);
        }
    }

    /**
     * استيراد الإعدادات / Import settings
     */
    public async importSettings(
        importPath: string,
        options: ImportOptions = {}
    ): Promise<ApplicationConfig> {
        try {
            if (!fs.existsSync(importPath)) {
                throw new Error('ملف الاستيراد غير موجود');
            }

            let content = await fs.promises.readFile(importPath, 'utf8');

            if (options.encrypted) {
                content = this.decryptData(content, options.encryptionKey || '');
            }

            const importData = JSON.parse(content);

            if (!this.validateImportData(importData)) {
                throw new Error('بيانات الاستيراد غير صحيحة');
            }

            return importData.settings;
        } catch (error) {
            throw new Error(`فشل في استيراد الإعدادات: ${error}`);
        }
    }

    /**
     * التحقق من صحة بيانات النسخة الاحتياطية / Validate backup data
     */
    private validateBackupData(data: any): boolean {
        return (
            data &&
            typeof data === 'object' &&
            data.version &&
            data.timestamp &&
            data.settings &&
            data.checksum
        );
    }

    /**
     * التحقق من صحة بيانات الاستيراد / Validate import data
     */
    private validateImportData(data: any): boolean {
        return (
            data &&
            typeof data === 'object' &&
            data.version &&
            data.settings &&
            data.application === 'YouTube Dark Cyber X'
        );
    }

    /**
     * حساب التوقيع / Calculate checksum
     */
    private calculateChecksum(data: string): string {
        return crypto.createHash('sha256').update(data).digest('hex');
    }

    /**
     * تنظيف الإعدادات من البيانات الحساسة / Sanitize settings
     */
    private sanitizeSettings(settings: ApplicationConfig): ApplicationConfig {
        const sanitized = JSON.parse(JSON.stringify(settings));
        
        // إزالة البيانات الحساسة
        if (sanitized.security) {
            delete sanitized.security.apiKeys;
            delete sanitized.security.tokens;
        }

        return sanitized;
    }

    /**
     * تشفير البيانات / Encrypt data
     */
    private encryptData(data: string, key: string): string {
        if (!key) {
            throw new Error('مفتاح التشفير مطلوب');
        }

        const cipher = crypto.createCipher('aes-256-cbc', key);
        let encrypted = cipher.update(data, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        return encrypted;
    }

    /**
     * فك تشفير البيانات / Decrypt data
     */
    private decryptData(encryptedData: string, key: string): string {
        if (!key) {
            throw new Error('مفتاح فك التشفير مطلوب');
        }

        const decipher = crypto.createDecipher('aes-256-cbc', key);
        let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    }

    /**
     * توليد معرف النسخة الاحتياطية / Generate backup ID
     */
    private generateBackupId(): string {
        return `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
