/**
 * أنواع مشغل YouTube
 * YouTube player types
 * 
 * هذا الملف يحتوي على تعريفات أنواع مشغل YouTube
 * This file contains YouTube player type definitions
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQuality } from './video-types';

/**
 * حالة مشغل الفيديو
 * Video player state
 */
export interface VideoPlayerState {
    /** هل يتم التشغيل / Is playing */
    readonly isPlaying: boolean;
    /** الوقت الحالي / Current time */
    readonly currentTime: number;
    /** المدة الإجمالية / Total duration */
    readonly duration: number;
    /** مستوى الصوت / Volume level */
    readonly volume: number;
    /** جودة الفيديو / Video quality */
    readonly quality: VideoQuality;
    /** هل في وضع ملء الشاشة / Is fullscreen */
    readonly isFullscreen: boolean;
    /** هل الصوت مكتوم / Is muted */
    readonly isMuted: boolean;
    /** سرعة التشغيل / Playback rate */
    readonly playbackRate: number;
}

/**
 * أحداث مشغل YouTube
 * YouTube player events
 */
export interface YouTubePlayerEvents {
    /** عند جاهزية المشغل / On player ready */
    onReady?: () => void;
    /** عند تغيير حالة التشغيل / On state change */
    onStateChange?: (state: number) => void;
    /** عند تغيير الجودة / On quality change */
    onPlaybackQualityChange?: (quality: string) => void;
    /** عند تغيير سرعة التشغيل / On rate change */
    onPlaybackRateChange?: (rate: number) => void;
    /** عند حدوث خطأ / On error */
    onError?: (error: number) => void;
    /** عند تحديث الوقت / On time update */
    onTimeUpdate?: (currentTime: number) => void;
}

/**
 * إعدادات مشغل YouTube
 * YouTube player settings
 */
export interface YouTubePlayerSettings {
    /** تشغيل تلقائي / Auto play */
    autoplay: boolean;
    /** إخفاء أزرار التحكم / Hide controls */
    controls: boolean;
    /** إخفاء معلومات الفيديو / Hide video info */
    showinfo: boolean;
    /** إخفاء الفيديوهات المقترحة / Hide related videos */
    rel: boolean;
    /** تمكين JavaScript API / Enable JS API */
    enablejsapi: boolean;
    /** معرف الأصل / Origin ID */
    origin?: string;
    /** لغة الواجهة / Interface language */
    hl?: string;
    /** لغة التسميات التوضيحية / Caption language */
    cc_lang_pref?: string;
}

/**
 * معلومات قناة YouTube
 * YouTube channel information
 */
export interface YouTubeChannelInfo {
    /** معرف القناة / Channel ID */
    readonly id: string;
    /** اسم القناة / Channel name */
    readonly name: string;
    /** وصف القناة / Channel description */
    readonly description: string;
    /** صورة القناة / Channel avatar */
    readonly avatarUrl: string;
    /** عدد المشتركين / Subscriber count */
    readonly subscriberCount: number;
    /** عدد الفيديوهات / Video count */
    readonly videoCount: number;
    /** عدد المشاهدات الإجمالي / Total view count */
    readonly totalViewCount: number;
    /** تاريخ إنشاء القناة / Channel creation date */
    readonly createdAt: Date;
}

/**
 * معلومات فيديو YouTube
 * YouTube video information
 */
export interface YouTubeVideoInfo {
    /** معرف الفيديو / Video ID */
    readonly id: string;
    /** عنوان الفيديو / Video title */
    readonly title: string;
    /** وصف الفيديو / Video description */
    readonly description: string;
    /** مدة الفيديو بالثواني / Video duration in seconds */
    readonly duration: number;
    /** جودة الفيديو الحالية / Current video quality */
    readonly quality: VideoQuality;
    /** رابط الفيديو / Video URL */
    readonly url: string;
    /** رابط الصورة المصغرة / Thumbnail URL */
    readonly thumbnailUrl: string;
    /** معلومات القناة / Channel information */
    readonly channel: YouTubeChannelInfo;
    /** عدد المشاهدات / View count */
    readonly viewCount: number;
    /** عدد الإعجابات / Like count */
    readonly likeCount: number;
    /** عدد عدم الإعجاب / Dislike count */
    readonly dislikeCount: number;
    /** تاريخ النشر / Published date */
    readonly publishedAt: Date;
    /** الكلمات المفتاحية / Tags */
    readonly tags: string[];
    /** الفئة / Category */
    readonly category: string;
    /** اللغة / Language */
    readonly language: string;
}

/**
 * إعدادات تشغيل YouTube
 * YouTube playback settings
 */
export interface YouTubePlaybackSettings {
    /** الجودة المفضلة / Preferred quality */
    preferredQuality: VideoQuality;
    /** التشغيل التلقائي / Auto play */
    autoPlay: boolean;
    /** كتم الصوت التلقائي / Auto mute */
    autoMute: boolean;
    /** مستوى الصوت الافتراضي / Default volume */
    defaultVolume: number;
    /** سرعة التشغيل الافتراضية / Default playback rate */
    defaultPlaybackRate: number;
    /** تمكين التسميات التوضيحية / Enable captions */
    enableCaptions: boolean;
    /** لغة التسميات التوضيحية / Caption language */
    captionLanguage: string;
    /** تمكين الإعلانات / Enable ads */
    enableAds: boolean;
    /** تخطي الإعلانات تلقائياً / Auto skip ads */
    autoSkipAds: boolean;
}

/**
 * حالة تحميل YouTube
 * YouTube loading state
 */
export interface YouTubeLoadingState {
    /** هل يتم التحميل / Is loading */
    isLoading: boolean;
    /** نسبة التحميل / Loading percentage */
    loadingPercentage: number;
    /** رسالة التحميل / Loading message */
    loadingMessage: string;
    /** هل حدث خطأ / Has error */
    hasError: boolean;
    /** رسالة الخطأ / Error message */
    errorMessage?: string;
    /** كود الخطأ / Error code */
    errorCode?: number;
}

/**
 * إحصائيات YouTube
 * YouTube statistics
 */
export interface YouTubeStats {
    /** إجمالي وقت المشاهدة / Total watch time */
    totalWatchTime: number;
    /** عدد الفيديوهات المشاهدة / Videos watched */
    videosWatched: number;
    /** متوسط وقت المشاهدة / Average watch time */
    averageWatchTime: number;
    /** الجودة الأكثر استخداماً / Most used quality */
    mostUsedQuality: VideoQuality;
    /** عدد مرات تغيير الجودة / Quality changes count */
    qualityChanges: number;
    /** عدد الإعلانات المحجوبة / Blocked ads count */
    blockedAdsCount: number;
    /** آخر تحديث / Last updated */
    lastUpdated: Date;
}

/**
 * واجهة مدير YouTube
 * YouTube manager interface
 */
export interface YouTubeManager {
    /** تحميل فيديو / Load video */
    loadVideo(videoId: string): Promise<void>;
    /** تشغيل الفيديو / Play video */
    playVideo(): Promise<void>;
    /** إيقاف الفيديو / Pause video */
    pauseVideo(): Promise<void>;
    /** إيقاف الفيديو / Stop video */
    stopVideo(): Promise<void>;
    /** تغيير الجودة / Set quality */
    setQuality(quality: VideoQuality): Promise<void>;
    /** تغيير مستوى الصوت / Set volume */
    setVolume(volume: number): Promise<void>;
    /** كتم/إلغاء كتم الصوت / Mute/unmute */
    toggleMute(): Promise<void>;
    /** تغيير سرعة التشغيل / Set playback rate */
    setPlaybackRate(rate: number): Promise<void>;
    /** الانتقال لوقت محدد / Seek to time */
    seekTo(seconds: number): Promise<void>;
    /** الحصول على حالة المشغل / Get player state */
    getPlayerState(): Promise<VideoPlayerState>;
    /** الحصول على معلومات الفيديو / Get video info */
    getVideoInfo(): Promise<YouTubeVideoInfo>;
    /** الحصول على الإحصائيات / Get statistics */
    getStats(): Promise<YouTubeStats>;
}
