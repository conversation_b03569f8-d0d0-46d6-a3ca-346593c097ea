/**
 * اختبارات وحدة مدير الموارد
 * Resource manager unit tests
 * 
 * هذا الملف يحتوي على اختبارات شاملة لمدير الموارد
 * This file contains comprehensive tests for resource manager
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ResourceManager } from '@shared/utils/resource-manager';

describe('ResourceManager', () => {
    let resourceManager: ResourceManager;

    beforeEach(() => {
        resourceManager = new ResourceManager();
    });

    afterEach(() => {
        resourceManager.cleanup();
    });

    describe('Timer Management', () => {
        it('should add and track timers', () => {
            // Arrange
            const callback = jest.fn();
            
            // Act
            const timer = setTimeout(callback, 100);
            const trackedTimer = resourceManager.addTimer(timer);
            
            // Assert
            expect(trackedTimer).toBe(timer);
            expect(resourceManager.getStats().timers).toBe(1);
        });

        it('should cleanup timers on cleanup', (done) => {
            // Arrange
            const callback = jest.fn();
            const timer = setTimeout(callback, 50);
            resourceManager.addTimer(timer);
            
            // Act
            resourceManager.cleanup();
            
            // Assert
            setTimeout(() => {
                expect(callback).not.toHaveBeenCalled();
                expect(resourceManager.getStats().timers).toBe(0);
                done();
            }, 100);
        });

        it('should remove specific timer', () => {
            // Arrange
            const timer = setTimeout(() => {}, 1000);
            resourceManager.addTimer(timer);
            
            // Act
            resourceManager.removeTimer(timer);
            
            // Assert
            expect(resourceManager.getStats().timers).toBe(0);
        });
    });

    describe('Interval Management', () => {
        it('should add and track intervals', () => {
            // Arrange
            const callback = jest.fn();
            
            // Act
            const interval = setInterval(callback, 100);
            const trackedInterval = resourceManager.addInterval(interval);
            
            // Assert
            expect(trackedInterval).toBe(interval);
            expect(resourceManager.getStats().intervals).toBe(1);
            
            // Cleanup
            clearInterval(interval);
        });

        it('should cleanup intervals on cleanup', (done) => {
            // Arrange
            const callback = jest.fn();
            const interval = setInterval(callback, 50);
            resourceManager.addInterval(interval);
            
            // Act
            resourceManager.cleanup();
            
            // Assert
            setTimeout(() => {
                expect(resourceManager.getStats().intervals).toBe(0);
                done();
            }, 150);
        });

        it('should remove specific interval', () => {
            // Arrange
            const interval = setInterval(() => {}, 1000);
            resourceManager.addInterval(interval);
            
            // Act
            resourceManager.removeInterval(interval);
            
            // Assert
            expect(resourceManager.getStats().intervals).toBe(0);
        });
    });

    describe('Observer Management', () => {
        it('should add and track observers', () => {
            // Arrange
            const observer = new MutationObserver(() => {});
            
            // Act
            const trackedObserver = resourceManager.addObserver(observer);
            
            // Assert
            expect(trackedObserver).toBe(observer);
            expect(resourceManager.getStats().observers).toBe(1);
        });

        it('should cleanup observers on cleanup', () => {
            // Arrange
            const disconnectSpy = jest.fn();
            const observer = {
                disconnect: disconnectSpy
            } as unknown as MutationObserver;
            
            resourceManager.addObserver(observer);
            
            // Act
            resourceManager.cleanup();
            
            // Assert
            expect(disconnectSpy).toHaveBeenCalled();
            expect(resourceManager.getStats().observers).toBe(0);
        });

        it('should remove specific observer', () => {
            // Arrange
            const disconnectSpy = jest.fn();
            const observer = {
                disconnect: disconnectSpy
            } as unknown as MutationObserver;
            
            resourceManager.addObserver(observer);
            
            // Act
            resourceManager.removeObserver(observer);
            
            // Assert
            expect(disconnectSpy).toHaveBeenCalled();
            expect(resourceManager.getStats().observers).toBe(0);
        });
    });

    describe('Event Listener Management', () => {
        it('should add and track event listeners', () => {
            // Arrange
            const element = document.createElement('div');
            const listener = jest.fn();
            
            // Act
            resourceManager.addEventListener(element, 'click', listener);
            
            // Assert
            expect(resourceManager.getStats().eventListeners).toBe(1);
        });

        it('should cleanup event listeners on cleanup', () => {
            // Arrange
            const element = document.createElement('div');
            const listener = jest.fn();
            const removeEventListenerSpy = jest.spyOn(element, 'removeEventListener');
            
            resourceManager.addEventListener(element, 'click', listener);
            
            // Act
            resourceManager.cleanup();
            
            // Assert
            expect(removeEventListenerSpy).toHaveBeenCalledWith('click', listener, undefined);
            expect(resourceManager.getStats().eventListeners).toBe(0);
        });

        it('should remove specific event listener', () => {
            // Arrange
            const element = document.createElement('div');
            const listener = jest.fn();
            const removeEventListenerSpy = jest.spyOn(element, 'removeEventListener');
            
            resourceManager.addEventListener(element, 'click', listener);
            
            // Act
            resourceManager.removeEventListener(element, 'click', listener);
            
            // Assert
            expect(removeEventListenerSpy).toHaveBeenCalledWith('click', listener, undefined);
            expect(resourceManager.getStats().eventListeners).toBe(0);
        });
    });

    describe('Statistics', () => {
        it('should provide accurate statistics', () => {
            // Arrange
            const timer = setTimeout(() => {}, 1000);
            const interval = setInterval(() => {}, 1000);
            const observer = new MutationObserver(() => {});
            const element = document.createElement('div');
            const listener = jest.fn();
            
            // Act
            resourceManager.addTimer(timer);
            resourceManager.addInterval(interval);
            resourceManager.addObserver(observer);
            resourceManager.addEventListener(element, 'click', listener);
            
            const stats = resourceManager.getStats();
            
            // Assert
            expect(stats.timers).toBe(1);
            expect(stats.intervals).toBe(1);
            expect(stats.observers).toBe(1);
            expect(stats.eventListeners).toBe(1);
            expect(stats.totalResources).toBe(4);
            
            // Cleanup
            clearTimeout(timer);
            clearInterval(interval);
        });

        it('should detect memory leaks', () => {
            // Arrange
            for (let i = 0; i < 15; i++) {
                const timer = setTimeout(() => {}, 1000);
                resourceManager.addTimer(timer);
            }
            
            // Act
            const hasLeaks = resourceManager.hasMemoryLeaks();
            
            // Assert
            expect(hasLeaks).toBe(true);
        });

        it('should not detect memory leaks with normal usage', () => {
            // Arrange
            const timer = setTimeout(() => {}, 1000);
            resourceManager.addTimer(timer);
            
            // Act
            const hasLeaks = resourceManager.hasMemoryLeaks();
            
            // Assert
            expect(hasLeaks).toBe(false);
            
            // Cleanup
            clearTimeout(timer);
        });
    });

    describe('Error Handling', () => {
        it('should handle cleanup errors gracefully', () => {
            // Arrange
            const observer = {
                disconnect: jest.fn(() => {
                    throw new Error('Disconnect failed');
                })
            } as unknown as MutationObserver;
            
            resourceManager.addObserver(observer);
            
            // Act & Assert
            expect(() => resourceManager.cleanup()).not.toThrow();
        });

        it('should handle invalid timer removal', () => {
            // Arrange
            const invalidTimer = setTimeout(() => {}, 1000) as any;
            clearTimeout(invalidTimer);
            
            // Act & Assert
            expect(() => resourceManager.removeTimer(invalidTimer)).not.toThrow();
        });
    });
});
