/**
 * مشغل الاختبارات الشامل - ملف التفويض الرئيسي
 * Comprehensive test runner - Main delegation file
 *
 * هذا الملف يفوض جميع عمليات تشغيل الاختبارات للملفات المتخصصة
 * This file delegates all test running operations to specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { TestRunnerCore } from './run-tests-core';
import {
    DEFAULT_EXECUTION_OPTIONS,
    DEFAULT_TEST_RUNNER_CONFIG,
    TestExecutionOptions,
    TestReport,
    TestRunnerConfig
} from './run-tests-types';

/**
 * مشغل الاختبارات الشامل - فئة التفويض
 * Comprehensive test runner - Delegation class
 */
class TestRunner {
    private readonly config: TestRunnerConfig;

    constructor(config: TestRunnerConfig = DEFAULT_TEST_RUNNER_CONFIG) {
        this.config = config;
    }

    /**
     * تشغيل جميع الاختبارات - تفويض للوحدة الأساسية
     * Run all tests - Delegate to core module
     */
    public async runAllTests(options: TestExecutionOptions = DEFAULT_EXECUTION_OPTIONS): Promise<TestReport> {
        // تفويض العملية للوحدة الأساسية
        // Delegate operation to core module
        return await TestRunnerCore.runAllTests(this.config, options);
    }

    /**
     * تشغيل اختبارات الوحدة - تفويض للوحدة الأساسية
     * Run unit tests - Delegate to core module
     */
    public async runUnitTests(options: TestExecutionOptions = DEFAULT_EXECUTION_OPTIONS): Promise<TestReport> {
        // تفويض العملية للوحدة الأساسية
        // Delegate operation to core module
        const unitResult = await TestRunnerCore.runUnitTests(this.config, options);

        return {
            totalTests: unitResult.summary.total,
            totalPassed: unitResult.summary.passed,
            totalFailed: unitResult.summary.failed,
            totalSkipped: unitResult.summary.skipped,
            overallCoverage: unitResult.summary.coverage,
            totalDuration: unitResult.summary.duration,
            results: unitResult.testResults,
            timestamp: new Date()
        };
    }

    /**
     * تشغيل اختبارات التكامل - تفويض للوحدة الأساسية
     * Run integration tests - Delegate to core module
     */
    public async runIntegrationTests(options: TestExecutionOptions = DEFAULT_EXECUTION_OPTIONS): Promise<TestReport> {
        // تفويض العملية للوحدة الأساسية
        // Delegate operation to core module
        const integrationResult = await TestRunnerCore.runIntegrationTests(this.config, options);

        return {
            totalTests: integrationResult.summary.total,
            totalPassed: integrationResult.summary.passed,
            totalFailed: integrationResult.summary.failed,
            totalSkipped: integrationResult.summary.skipped,
            overallCoverage: integrationResult.summary.coverage,
            totalDuration: integrationResult.summary.duration,
            results: integrationResult.testResults,
            timestamp: new Date()
        };
    }

    /**
     * تشغيل اختبارات النهاية إلى النهاية - تفويض للوحدة الأساسية
     * Run end-to-end tests - Delegate to core module
     */
    public async runE2ETests(options: TestExecutionOptions = DEFAULT_EXECUTION_OPTIONS): Promise<TestReport> {
        // تفويض العملية للوحدة الأساسية
        // Delegate operation to core module
        const e2eResult = await TestRunnerCore.runE2ETests(this.config, options);

        return {
            totalTests: e2eResult.summary.total,
            totalPassed: e2eResult.summary.passed,
            totalFailed: e2eResult.summary.failed,
            totalSkipped: e2eResult.summary.skipped,
            overallCoverage: e2eResult.summary.coverage,
            totalDuration: e2eResult.summary.duration,
            results: e2eResult.testResults,
            timestamp: new Date()
        };
    }
}

// تشغيل الاختبارات إذا تم استدعاء الملف مباشرة
// Run tests if file is called directly
if (require.main === module) {
    const runner = new TestRunner();
    runner.runAllTests()
        .then((report) => {
            const isSuccess = report.totalFailed === 0 && report.overallCoverage >= 80;
            process.exit(isSuccess ? 0 : 1);
        })
        .catch((error) => {
            console.error('❌ فشل في تشغيل الاختبارات / Failed to run tests:', error);
            process.exit(1);
        });
}

export { TestRunner };
