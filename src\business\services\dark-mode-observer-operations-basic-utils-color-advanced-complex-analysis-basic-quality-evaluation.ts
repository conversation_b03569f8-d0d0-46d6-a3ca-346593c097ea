/**
 * تقييم جودة الألوان الأساسي المعقد المتقدم
 * Basic complex advanced color quality evaluation
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeObserverOperationsBasicUtilsColorAdvancedBasic } from './dark-mode-observer-operations-basic-utils-color-advanced-basic';
import { DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicCore } from './dark-mode-observer-operations-basic-utils-color-advanced-complex-analysis-basic-core';
import { DarkModeObserverOperationsBasicUtilsColorCore } from './dark-mode-observer-operations-basic-utils-color-core';

/**
 * فئة تقييم جودة الألوان الأساسي المعقد المتقدم
 * Basic complex advanced color quality evaluation class
 */
export class DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicQualityEvaluation {

    /** تقييم جودة اللون / Evaluate color quality */
    public static evaluateColorQuality(color: string): {
        score: number;
        issues: string[];
        recommendations: string[];
        accessibility: 'excellent' | 'good' | 'fair' | 'poor';
    } {
        const analysis = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicCore.analyzeColor(color);
        const issues: string[] = [];
        const recommendations: string[] = [];
        let score = 100;
        let accessibility: 'excellent' | 'good' | 'fair' | 'poor' = 'excellent';

        if (!analysis.rgb || !analysis.hsl) {
            return {
                score: 0,
                issues: ['لون غير صالح'],
                recommendations: ['استخدم لون صالح'],
                accessibility: 'poor'
            };
        }

        // فحص التباين مع الأبيض والأسود
        const contrastWithWhite = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicCore.analyzeContrast(color, '#ffffff');
        const contrastWithBlack = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicCore.analyzeContrast(color, '#000000');

        if (!contrastWithWhite.isAccessible && !contrastWithBlack.isAccessible) {
            issues.push('تباين ضعيف مع الأبيض والأسود');
            recommendations.push('اختر لون بتباين أفضل');
            score -= 30;
            accessibility = 'poor';
        } else if (contrastWithWhite.isAccessible && contrastWithBlack.isAccessible) {
            score += 10; // مكافأة للتباين الممتاز
        }

        // فحص التشبع
        const { s, l } = analysis.hsl;
        if (s < 10) {
            issues.push('تشبع منخفض جداً - اللون باهت');
            recommendations.push('زيادة التشبع للحيوية');
            score -= 15;
        } else if (s > 90) {
            issues.push('تشبع عالي جداً - قد يكون مؤذي للعين');
            recommendations.push('تقليل التشبع للراحة');
            score -= 10;
        } else if (s >= 30 && s <= 70) {
            score += 5; // تشبع مثالي
        }

        // فحص السطوع
        if (l < 10) {
            issues.push('سطوع منخفض جداً - صعب الرؤية');
            recommendations.push('زيادة السطوع للوضوح');
            score -= 20;
            if (accessibility === 'excellent') accessibility = 'fair';
        } else if (l > 90) {
            issues.push('سطوع عالي جداً - قد يكون مبهر');
            recommendations.push('تقليل السطوع للراحة');
            score -= 15;
            if (accessibility === 'excellent') accessibility = 'good';
        } else if (l >= 20 && l <= 80) {
            score += 5; // سطوع مثالي
        }

        // فحص التوازن اللوني
        const { r, g, b } = analysis.rgb;
        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        const range = max - min;

        if (range < 20 && s > 20) {
            issues.push('ألوان RGB متقاربة جداً');
            recommendations.push('زيادة التباين بين مكونات RGB');
            score -= 10;
        }

        // فحص الألوان الآمنة للويب
        const webSafeColors = [0, 51, 102, 153, 204, 255];
        const isWebSafe = webSafeColors.includes(r) && webSafeColors.includes(g) && webSafeColors.includes(b);
        if (isWebSafe) {
            score += 5; // مكافأة للألوان الآمنة
        }

        // تحديد مستوى الوصولية النهائي
        if (score >= 90) {
            accessibility = 'excellent';
        } else if (score >= 70) {
            accessibility = 'good';
        } else if (score >= 50) {
            accessibility = 'fair';
        } else {
            accessibility = 'poor';
        }

        // إضافة توصيات عامة
        if (issues.length === 0) {
            recommendations.push('اللون ذو جودة ممتازة');
        }

        return {
            score: Math.max(0, Math.min(100, score)),
            issues,
            recommendations,
            accessibility
        };
    }

    /** تقييم مجموعة ألوان / Evaluate color set */
    public static evaluateColorSet(colors: string[]): {
        overallScore: number;
        individualScores: number[];
        setIssues: string[];
        setRecommendations: string[];
        accessibility: 'excellent' | 'good' | 'fair' | 'poor';
    } {
        if (colors.length === 0) {
            return {
                overallScore: 0,
                individualScores: [],
                setIssues: ['لا توجد ألوان للتقييم'],
                setRecommendations: ['إضافة ألوان للمجموعة'],
                accessibility: 'poor'
            };
        }

        const individualScores: number[] = [];
        const setIssues: string[] = [];
        const setRecommendations: string[] = [];

        // تقييم كل لون على حدة
        colors.forEach((color, index) => {
            const evaluation = this.evaluateColorQuality(color);
            individualScores.push(evaluation.score);
        });

        // حساب النقاط الإجمالية
        const averageScore = individualScores.reduce((sum, score) => sum + score, 0) / individualScores.length;
        let overallScore = averageScore;

        // فحص التنوع
        const uniqueColors = new Set(colors);
        if (uniqueColors.size < colors.length) {
            setIssues.push('ألوان مكررة في المجموعة');
            setRecommendations.push('إزالة الألوان المكررة');
            overallScore -= 10;
        }

        // فحص التباين بين الألوان
        let contrastIssues = 0;
        for (let i = 0; i < colors.length - 1; i++) {
            for (let j = i + 1; j < colors.length; j++) {
                const contrast = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicCore.analyzeContrast(colors[i], colors[j]);
                if (!contrast.isAccessible) {
                    contrastIssues++;
                }
            }
        }

        const totalPairs = (colors.length * (colors.length - 1)) / 2;
        const contrastRatio = contrastIssues / totalPairs;

        if (contrastRatio > 0.5) {
            setIssues.push('كثرة مشاكل التباين بين الألوان');
            setRecommendations.push('تحسين التباين بين الألوان');
            overallScore -= 20;
        } else if (contrastRatio > 0.3) {
            setIssues.push('بعض مشاكل التباين بين الألوان');
            setRecommendations.push('تحسين التباين للألوان المتشابهة');
            overallScore -= 10;
        }

        // فحص التوازن الحراري
        const analyses = colors.map(color => 
            DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicCore.analyzeColor(color)
        );
        const temperatures = analyses.map(a => a.temperature);
        const warmCount = temperatures.filter(t => t === 'warm').length;
        const coolCount = temperatures.filter(t => t === 'cool').length;
        const neutralCount = temperatures.filter(t => t === 'neutral').length;

        const totalColors = colors.length;
        if (totalColors > 2) {
            const warmRatio = warmCount / totalColors;
            const coolRatio = coolCount / totalColors;

            if (Math.abs(warmRatio - coolRatio) > 0.7) {
                setIssues.push('عدم توازن في درجات الحرارة اللونية');
                setRecommendations.push('إضافة ألوان متوازنة حرارياً');
                overallScore -= 15;
            } else if (Math.abs(warmRatio - coolRatio) < 0.2) {
                overallScore += 5; // مكافأة للتوازن الجيد
            }
        }

        // تحديد مستوى الوصولية للمجموعة
        let accessibility: 'excellent' | 'good' | 'fair' | 'poor';
        if (overallScore >= 85) {
            accessibility = 'excellent';
        } else if (overallScore >= 70) {
            accessibility = 'good';
        } else if (overallScore >= 50) {
            accessibility = 'fair';
        } else {
            accessibility = 'poor';
        }

        // إضافة توصيات عامة
        if (setIssues.length === 0) {
            setRecommendations.push('مجموعة الألوان ذات جودة ممتازة');
        }

        return {
            overallScore: Math.max(0, Math.min(100, Math.round(overallScore))),
            individualScores,
            setIssues,
            setRecommendations,
            accessibility
        };
    }

    /** فحص صلاحية اللون للاستخدام / Check color usability */
    public static checkColorUsability(color: string, context: 'background' | 'text' | 'accent' | 'border' = 'background'): {
        suitable: boolean;
        issues: string[];
        suggestions: string[];
        score: number;
    } {
        const analysis = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicCore.analyzeColor(color);
        const issues: string[] = [];
        const suggestions: string[] = [];
        let score = 50;
        let suitable = true;

        if (!analysis.hsl) {
            return {
                suitable: false,
                issues: ['لون غير صالح'],
                suggestions: ['استخدم لون صالح'],
                score: 0
            };
        }

        const { s, l } = analysis.hsl;

        switch (context) {
            case 'background':
                // الخلفيات يجب أن تكون هادئة
                if (s > 50) {
                    issues.push('تشبع عالي للخلفية');
                    suggestions.push('تقليل التشبع للخلفية');
                    score -= 20;
                    suitable = false;
                } else {
                    score += 10;
                }

                if (l < 20 || l > 85) {
                    score += 10; // سطوع مناسب للخلفية
                } else {
                    issues.push('سطوع متوسط قد يؤثر على قراءة النص');
                    suggestions.push('جعل الخلفية أفتح أو أغمق');
                    score -= 10;
                }
                break;

            case 'text':
                // النصوص يجب أن تكون واضحة
                if (s > 30) {
                    issues.push('تشبع عالي للنص');
                    suggestions.push('تقليل التشبع للوضوح');
                    score -= 15;
                }

                if (l > 20 && l < 80) {
                    issues.push('سطوع متوسط للنص - قد يكون غير واضح');
                    suggestions.push('جعل النص أغمق أو أفتح للوضوح');
                    score -= 20;
                    suitable = false;
                } else {
                    score += 15;
                }
                break;

            case 'accent':
                // الألوان المميزة يجب أن تكون بارزة
                if (s < 40) {
                    issues.push('تشبع منخفض للون المميز');
                    suggestions.push('زيادة التشبع للبروز');
                    score -= 15;
                } else {
                    score += 15;
                }

                if (l < 30 || l > 70) {
                    issues.push('سطوع غير مناسب للون المميز');
                    suggestions.push('تعديل السطوع ليكون متوسط');
                    score -= 10;
                } else {
                    score += 10;
                }
                break;

            case 'border':
                // الحدود يجب أن تكون واضحة لكن غير مشتتة
                if (s > 60) {
                    issues.push('تشبع عالي للحدود');
                    suggestions.push('تقليل التشبع للحدود');
                    score -= 10;
                }

                if (l > 30 && l < 70) {
                    score += 10; // سطوع مناسب للحدود
                } else {
                    issues.push('سطوع قد يجعل الحدود غير واضحة');
                    suggestions.push('تعديل السطوع للوضوح');
                    score -= 5;
                }
                break;
        }

        // فحص التباين العام
        const contrastWithWhite = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicCore.analyzeContrast(color, '#ffffff');
        const contrastWithBlack = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicCore.analyzeContrast(color, '#000000');

        if (context === 'text' && !contrastWithWhite.isAccessible && !contrastWithBlack.isAccessible) {
            issues.push('تباين ضعيف للنص');
            suggestions.push('تحسين التباين للقراءة');
            score -= 25;
            suitable = false;
        }

        if (issues.length === 0) {
            suggestions.push(`اللون مناسب للاستخدام كـ ${context}`);
        }

        return {
            suitable,
            issues,
            suggestions,
            score: Math.max(0, Math.min(100, score))
        };
    }
}
