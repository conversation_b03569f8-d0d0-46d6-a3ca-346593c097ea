/**
 * مراقبة الوضع المظلم - أدوات العمليات الأساسية البسيطة
 * Dark mode monitoring - Basic core operations utilities
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import {
    DARK_MODE_CONSTANTS,
    DarkModeConfig
} from './dark-mode-config';

/**
 * فئة أدوات العمليات الأساسية البسيطة لمراقبة الوضع المظلم
 * Basic core operations utilities for dark mode monitoring
 */
export class DarkModeObserverMonitoringCoreOperationsUtilsBasic {

    /**
     * البحث عن عناصر جديدة
     * Find new elements
     */
    public static findNewElements(): Element[] {
        const selectors = [
            'video:not([data-dark-mode-applied])',
            '.video-container:not([data-dark-mode-applied])',
            '.player-container:not([data-dark-mode-applied])'
        ];

        const newElements: Element[] = [];
        selectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                newElements.push(...Array.from(elements));
            } catch (error) {
                console.error(`Error finding elements with selector ${selector}:`, error);
            }
        });

        return newElements;
    }

    /**
     * فحص ما إذا كان العنصر ذا صلة
     * Check if element is relevant
     */
    public static isElementRelevant(element: Element): boolean {
        const relevantSelectors = [
            'video',
            '.video-container',
            '.player-container',
            '.ytp-chrome-bottom',
            '.ytp-chrome-top',
            '.ytp-gradient-bottom',
            '.ytp-gradient-top'
        ];

        return relevantSelectors.some(selector => {
            try {
                return element.matches(selector) || element.querySelector(selector) !== null;
            } catch {
                return false;
            }
        });
    }

    /**
     * فحص صحة العنصر المستهدف
     * Validate target element
     */
    public static validateTargetElement(element: Element): boolean {
        if (!element || !element.nodeType) {
            return false;
        }

        if (element.nodeType !== Node.ELEMENT_NODE) {
            return false;
        }

        // فحص ما إذا كان العنصر متصل بالـ DOM
        return element.isConnected;
    }

    /**
     * إنشاء مراقب التغييرات مع الإعدادات المحسنة
     * Create mutation observer with optimized settings
     */
    public static createOptimizedMutationObserver(callback: (mutations: MutationRecord[]) => void): MutationObserver {
        const observer = new MutationObserver(callback);
        return observer;
    }

    /**
     * الحصول على إعدادات المراقب المحسنة
     * Get optimized observer configuration
     */
    public static getOptimizedObserverConfig(): MutationObserverInit {
        return {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['class', 'style', 'data-theme'],
            characterData: false
        };
    }

    /**
     * تنظيف الموارد
     * Cleanup resources
     */
    public static cleanup(): void {
        // تنظيف أي موارد مستخدمة
        console.log('Cleaning up monitoring core operations utilities resources');
    }

    /**
     * فحص الفئات ذات الصلة
     * Check relevant classes
     */
    public static checkRelevantClasses(classList: DOMTokenList): boolean {
        const relevantClasses = ['dark', 'light', 'theme', 'player', 'video'];
        return relevantClasses.some(cls => 
            Array.from(classList).some(className => className.includes(cls))
        );
    }

    /**
     * فحص الأنماط ذات الصلة
     * Check relevant styles
     */
    public static checkRelevantStyles(style: string): boolean {
        const relevantStyles = ['background', 'color', 'theme'];
        return relevantStyles.some(styleProp => style.includes(styleProp));
    }

    /**
     * فحص الخصائص ذات الصلة
     * Check relevant attributes
     */
    public static checkRelevantAttributes(attributeName: string): boolean {
        const relevantAttributes = ['class', 'style', 'data-theme'];
        return relevantAttributes.includes(attributeName);
    }

    /**
     * إنشاء تقرير أساسي للحالة
     * Create basic status report
     */
    public static createBasicStatusReport(): {
        timestamp: number;
        bodyHasDarkClass: boolean;
        newElementsCount: number;
        relevantElementsCount: number;
    } {
        const newElements = this.findNewElements();
        const relevantElements = document.querySelectorAll('video, .video-container, .player-container');
        
        return {
            timestamp: Date.now(),
            bodyHasDarkClass: document.body.classList.contains(DARK_MODE_CONSTANTS.DARK_MODE_CLASS),
            newElementsCount: newElements.length,
            relevantElementsCount: relevantElements.length
        };
    }

    /**
     * فحص حالة DOM الأساسية
     * Check basic DOM state
     */
    public static checkBasicDOMState(): {
        hasBody: boolean;
        hasVideoElements: boolean;
        hasPlayerElements: boolean;
        darkModeClassPresent: boolean;
    } {
        return {
            hasBody: !!document.body,
            hasVideoElements: document.querySelectorAll('video').length > 0,
            hasPlayerElements: document.querySelectorAll('.video-container, .player-container').length > 0,
            darkModeClassPresent: document.body?.classList.contains(DARK_MODE_CONSTANTS.DARK_MODE_CLASS) || false
        };
    }

    /**
     * تسجيل معلومات التشخيص الأساسية
     * Log basic diagnostic information
     */
    public static logBasicDiagnostics(): void {
        const state = this.checkBasicDOMState();
        const report = this.createBasicStatusReport();
        
        console.log('Basic Dark Mode Diagnostics:', {
            domState: state,
            statusReport: report
        });
    }
}
