/**
 * الوظائف المتقدمة لأدوات الألوان
 * Advanced color utility functions
 *
 * هذا الملف يجمع جميع الوظائف المتقدمة من الملفات المتخصصة
 * This file aggregates all advanced functions from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// استيراد الفئات المتخصصة / Import specialized classes
import { DarkModeObserverOperationsBasicUtilsColorAdvancedBasic } from './dark-mode-observer-operations-basic-utils-color-advanced-basic';
import { DarkModeObserverOperationsBasicUtilsColorAdvancedComplex } from './dark-mode-observer-operations-basic-utils-color-advanced-complex';

/**
 * فئة الوظائف المتقدمة لأدوات الألوان
 * Advanced color utility functions class
 */
export class DarkModeObserverOperationsBasicUtilsColorAdvanced {

    /** تفتيح اللون / Lighten color */
    public static lightenColor(color: string, amount: number): string {
        return DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.lightenColor(color, amount);
    }

    /** تغميق اللون / Darken color */
    public static darkenColor(color: string, amount: number): string {
        return DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.darkenColor(color, amount);
    }

    /** مزج لونين / Blend two colors */
    public static blendColors(color1: string, color2: string, ratio: number): string {
        return DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.blendColors(color1, color2, ratio);
    }

    /** الحصول على لون مكمل / Get complementary color */
    public static getComplementaryColor(color: string): string {
        return DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.getComplementaryColor(color);
    }

    /** تحويل اللون إلى تدرج رمادي / Convert color to grayscale */
    public static toGrayscale(color: string): string {
        return DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.toGrayscale(color);
    }

    /** تعديل تشبع اللون / Adjust color saturation */
    public static adjustSaturation(color: string, amount: number): string {
        return DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.adjustSaturation(color, amount);
    }

    /** تعديل سطوع اللون / Adjust color brightness */
    public static adjustBrightness(color: string, amount: number): string {
        return DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.adjustBrightness(color, amount);
    }

    /** الحصول على ألوان متناسقة / Get harmonious colors */
    public static getHarmoniousColors(baseColor: string, count: number = 5): string[] {
        return DarkModeObserverOperationsBasicUtilsColorAdvancedComplex.getHarmoniousColors(baseColor, count);
    }

    /** الحصول على ألوان متدرجة / Get gradient colors */
    public static getGradientColors(startColor: string, endColor: string, steps: number = 10): string[] {
        return DarkModeObserverOperationsBasicUtilsColorAdvancedComplex.getGradientColors(startColor, endColor, steps);
    }

    /** الحصول على ألوان مكملة / Get complementary color scheme */
    public static getComplementaryScheme(baseColor: string): { primary: string; secondary: string; accent: string } {
        return DarkModeObserverOperationsBasicUtilsColorAdvancedComplex.getComplementaryScheme(baseColor);
    }


}
