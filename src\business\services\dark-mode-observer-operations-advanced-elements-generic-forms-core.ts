/**
 * الوظائف الأساسية لمعالجة النماذج في الوضع المظلم
 * Core form processing functions for dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeConfig } from './dark-mode-config';

/**
 * فئة الوظائف الأساسية لمعالجة النماذج
 * Core form processing functions class
 */
export class DarkModeObserverOperationsAdvancedElementsGenericFormsCore {

    /** معالجة عنصر النموذج / Process form element */
    public static processFormElement(element: HTMLFormElement, config: DarkModeConfig): void {
        try {
            // إضافة فئة الوضع المظلم
            element.classList.add('dark-mode-form');
            
            // تطبيق الأنماط الأساسية
            this.applyFormStyles(element);
            
        } catch (error) {
            console.error('خطأ في معالجة عنصر النموذج:', error);
        }
    }

    /** تطبيق أنماط النموذج / Apply form styles */
    public static applyFormStyles(element: HTMLFormElement): void {
        const styles = {
            backgroundColor: 'rgba(255, 255, 255, 0.05)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            borderRadius: '8px',
            padding: '16px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)'
        };

        Object.assign(element.style, styles);
    }

    /** معالجة عناصر الإدخال / Process form inputs */
    public static processFormInputs(element: HTMLFormElement, config: DarkModeConfig): void {
        const inputs = element.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            this.styleFormInput(input as HTMLInputElement, config);
        });
    }

    /** تنسيق عنصر الإدخال / Style form input */
    public static styleFormInput(input: HTMLInputElement, config: DarkModeConfig): void {
        const inputType = input.type || 'text';
        
        // الأنماط الأساسية
        const baseStyles = {
            backgroundColor: '#2a2a2a',
            color: '#ffffff',
            border: '1px solid rgba(255, 255, 255, 0.3)',
            borderRadius: '6px',
            padding: '8px 12px',
            fontSize: '14px',
            transition: 'all 0.3s ease'
        };

        Object.assign(input.style, baseStyles);

        // أنماط خاصة حسب نوع الإدخال
        this.applyInputTypeStyles(input, inputType);

        // إضافة تأثيرات التفاعل
        this.addInputInteractionEffects(input);

        // معالجة placeholder
        this.processInputPlaceholder(input);
    }

    /** تطبيق أنماط حسب نوع الإدخال / Apply input type styles */
    public static applyInputTypeStyles(input: HTMLInputElement, type: string): void {
        switch (type) {
            case 'email':
                input.style.borderColor = 'rgba(0, 150, 255, 0.5)';
                break;
            case 'password':
                input.style.borderColor = 'rgba(255, 100, 100, 0.5)';
                break;
            case 'search':
                input.style.borderColor = 'rgba(100, 255, 100, 0.5)';
                input.style.borderRadius = '20px';
                break;
            case 'number':
                input.style.borderColor = 'rgba(255, 200, 0, 0.5)';
                break;
            case 'url':
                input.style.borderColor = 'rgba(150, 0, 255, 0.5)';
                break;
            case 'tel':
                input.style.borderColor = 'rgba(255, 150, 0, 0.5)';
                break;
            default:
                break;
        }
    }

    /** إضافة تأثيرات التفاعل للإدخال / Add input interaction effects */
    public static addInputInteractionEffects(input: HTMLInputElement): void {
        // تأثير التركيز
        input.addEventListener('focus', () => {
            input.style.borderColor = '#4da6ff';
            input.style.boxShadow = '0 0 0 3px rgba(77, 166, 255, 0.2)';
            input.style.outline = 'none';
        });

        // تأثير فقدان التركيز
        input.addEventListener('blur', () => {
            input.style.borderColor = 'rgba(255, 255, 255, 0.3)';
            input.style.boxShadow = 'none';
        });

        // تأثير الكتابة
        input.addEventListener('input', () => {
            if (input.value) {
                input.style.backgroundColor = '#333333';
            } else {
                input.style.backgroundColor = '#2a2a2a';
            }
        });
    }

    /** معالجة placeholder / Process input placeholder */
    public static processInputPlaceholder(input: HTMLInputElement): void {
        if (input.placeholder) {
            // إضافة أنماط placeholder عبر CSS
            const style = document.createElement('style');
            style.textContent = `
                .dark-mode-form input::placeholder,
                .dark-mode-form textarea::placeholder {
                    color: rgba(255, 255, 255, 0.5) !important;
                    opacity: 1 !important;
                }
            `;
            
            if (!document.querySelector('#dark-mode-placeholder-style')) {
                style.id = 'dark-mode-placeholder-style';
                document.head.appendChild(style);
            }
        }
    }

    /** معالجة تسميات النموذج / Process form labels */
    public static processFormLabels(element: HTMLFormElement, config: DarkModeConfig): void {
        const labels = element.querySelectorAll('label');
        
        labels.forEach(label => {
            this.styleFormLabel(label as HTMLLabelElement);
        });
    }

    /** تنسيق تسمية النموذج / Style form label */
    public static styleFormLabel(label: HTMLLabelElement): void {
        const styles = {
            color: '#ffffff',
            fontSize: '14px',
            fontWeight: '500',
            marginBottom: '4px',
            display: 'block'
        };

        Object.assign(label.style, styles);

        // إضافة تأثير التفاعل
        label.addEventListener('click', () => {
            const associatedInput = this.getAssociatedInput(label);
            if (associatedInput) {
                associatedInput.focus();
            }
        });
    }

    /** الحصول على عنصر الإدخال المرتبط / Get associated input */
    public static getAssociatedInput(label: HTMLLabelElement): HTMLInputElement | null {
        if (label.htmlFor) {
            return document.getElementById(label.htmlFor) as HTMLInputElement;
        }
        
        return label.querySelector('input, textarea, select') as HTMLInputElement;
    }

    /** معالجة رسائل الخطأ / Process error messages */
    public static processErrorMessages(form: HTMLFormElement): void {
        const errorElements = form.querySelectorAll('.error, .invalid, [data-error]');
        
        errorElements.forEach(error => {
            const styles = {
                color: '#ff6b6b',
                fontSize: '12px',
                marginTop: '4px',
                display: 'block'
            };

            Object.assign((error as HTMLElement).style, styles);
        });
    }

    /** معالجة رسائل النجاح / Process success messages */
    public static processSuccessMessages(form: HTMLFormElement): void {
        const successElements = form.querySelectorAll('.success, .valid, [data-success]');
        
        successElements.forEach(success => {
            const styles = {
                color: '#51cf66',
                fontSize: '12px',
                marginTop: '4px',
                display: 'block'
            };

            Object.assign((success as HTMLElement).style, styles);
        });
    }
}
