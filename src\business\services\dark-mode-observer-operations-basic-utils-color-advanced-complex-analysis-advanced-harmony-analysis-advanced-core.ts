/**
 * تحليل توافق الألوان المتقدم - العمليات الأساسية
 * Advanced color harmony analysis - Core operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * فئة العمليات الأساسية لتحليل توافق الألوان المتقدم
 * Core operations for advanced color harmony analysis
 */
export class DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisAdvancedHarmonyAnalysisAdvancedCore {

    /** تحليل التوافق المتقدم / Advanced harmony analysis */
    public static analyzeAdvancedHarmony(colors: string[]): {
        primaryHarmony: string;
        secondaryHarmony: string;
        overallScore: number;
        balanceScore: number;
        contrastScore: number;
        recommendations: string[];
    } {
        if (colors.length < 2) {
            return {
                primaryHarmony: 'monochromatic',
                secondaryHarmony: 'none',
                overallScore: 50,
                balanceScore: 50,
                contrastScore: 50,
                recommendations: ['Add more colors for better harmony analysis']
            };
        }

        const primaryHarmony = this.determinePrimaryHarmony(colors);
        const secondaryHarmony = this.determineSecondaryHarmony(colors);
        const balanceScore = this.calculateBalanceScore(colors);
        const contrastScore = this.calculateContrastScore(colors);
        const overallScore = (balanceScore + contrastScore) / 2;
        const recommendations = this.generateHarmonyRecommendations(primaryHarmony, balanceScore, contrastScore);

        return {
            primaryHarmony,
            secondaryHarmony,
            overallScore,
            balanceScore,
            contrastScore,
            recommendations
        };
    }

    /** تحديد التوافق الأساسي / Determine primary harmony */
    private static determinePrimaryHarmony(colors: string[]): string {
        const hues = colors.map(color => this.extractHue(color));
        const hueDifferences = this.calculateHueDifferences(hues);

        if (hueDifferences.every(diff => diff < 30)) {
            return 'monochromatic';
        } else if (hueDifferences.some(diff => Math.abs(diff - 180) < 30)) {
            return 'complementary';
        } else if (hueDifferences.some(diff => Math.abs(diff - 120) < 30)) {
            return 'triadic';
        } else if (hueDifferences.some(diff => Math.abs(diff - 60) < 30)) {
            return 'analogous';
        } else {
            return 'custom';
        }
    }

    /** تحديد التوافق الثانوي / Determine secondary harmony */
    private static determineSecondaryHarmony(colors: string[]): string {
        const saturations = colors.map(color => this.extractSaturation(color));
        const lightnesses = colors.map(color => this.extractLightness(color));

        const saturationVariance = this.calculateVariance(saturations);
        const lightnessVariance = this.calculateVariance(lightnesses);

        if (saturationVariance < 0.1 && lightnessVariance < 0.1) {
            return 'uniform';
        } else if (saturationVariance > 0.5) {
            return 'varied-saturation';
        } else if (lightnessVariance > 0.5) {
            return 'varied-lightness';
        } else {
            return 'balanced';
        }
    }

    /** حساب نقاط التوازن / Calculate balance score */
    private static calculateBalanceScore(colors: string[]): number {
        const hues = colors.map(color => this.extractHue(color));
        const saturations = colors.map(color => this.extractSaturation(color));
        const lightnesses = colors.map(color => this.extractLightness(color));

        const hueBalance = this.calculateHueBalance(hues);
        const saturationBalance = this.calculateSaturationBalance(saturations);
        const lightnessBalance = this.calculateLightnessBalance(lightnesses);

        return (hueBalance + saturationBalance + lightnessBalance) / 3;
    }

    /** حساب نقاط التباين / Calculate contrast score */
    private static calculateContrastScore(colors: string[]): number {
        let totalContrast = 0;
        let comparisons = 0;

        for (let i = 0; i < colors.length; i++) {
            for (let j = i + 1; j < colors.length; j++) {
                totalContrast += this.calculateColorContrast(colors[i], colors[j]);
                comparisons++;
            }
        }

        return comparisons > 0 ? (totalContrast / comparisons) * 100 : 0;
    }

    /** استخراج درجة اللون / Extract hue */
    private static extractHue(color: string): number {
        const rgb = this.hexToRgb(color);
        if (!rgb) return 0;

        const { r, g, b } = rgb;
        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        const delta = max - min;

        if (delta === 0) return 0;

        let hue = 0;
        if (max === r) {
            hue = ((g - b) / delta) % 6;
        } else if (max === g) {
            hue = (b - r) / delta + 2;
        } else {
            hue = (r - g) / delta + 4;
        }

        return (hue * 60 + 360) % 360;
    }

    /** استخراج التشبع / Extract saturation */
    private static extractSaturation(color: string): number {
        const rgb = this.hexToRgb(color);
        if (!rgb) return 0;

        const { r, g, b } = rgb;
        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        const delta = max - min;

        return max === 0 ? 0 : delta / max;
    }

    /** استخراج السطوع / Extract lightness */
    private static extractLightness(color: string): number {
        const rgb = this.hexToRgb(color);
        if (!rgb) return 0;

        const { r, g, b } = rgb;
        return (Math.max(r, g, b) + Math.min(r, g, b)) / 2 / 255;
    }

    /** تحويل من hex إلى RGB / Convert hex to RGB */
    private static hexToRgb(hex: string): { r: number; g: number; b: number } | null {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    /** حساب الفروق في درجات اللون / Calculate hue differences */
    private static calculateHueDifferences(hues: number[]): number[] {
        const differences: number[] = [];
        for (let i = 0; i < hues.length; i++) {
            for (let j = i + 1; j < hues.length; j++) {
                const diff = Math.abs(hues[i] - hues[j]);
                differences.push(Math.min(diff, 360 - diff));
            }
        }
        return differences;
    }

    /** حساب التباين / Calculate variance */
    private static calculateVariance(values: number[]): number {
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
        return squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;
    }

    /** حساب توازن درجة اللون / Calculate hue balance */
    private static calculateHueBalance(hues: number[]): number {
        const sectors = new Array(12).fill(0);
        hues.forEach(hue => {
            const sector = Math.floor(hue / 30);
            sectors[sector]++;
        });

        const variance = this.calculateVariance(sectors);
        return Math.max(0, 100 - variance * 10);
    }

    /** حساب توازن التشبع / Calculate saturation balance */
    private static calculateSaturationBalance(saturations: number[]): number {
        const variance = this.calculateVariance(saturations);
        return Math.max(0, 100 - variance * 100);
    }

    /** حساب توازن السطوع / Calculate lightness balance */
    private static calculateLightnessBalance(lightnesses: number[]): number {
        const variance = this.calculateVariance(lightnesses);
        return Math.max(0, 100 - variance * 100);
    }

    /** حساب تباين الألوان / Calculate color contrast */
    private static calculateColorContrast(color1: string, color2: string): number {
        const rgb1 = this.hexToRgb(color1);
        const rgb2 = this.hexToRgb(color2);
        
        if (!rgb1 || !rgb2) return 0;

        const l1 = this.calculateRelativeLuminance(rgb1);
        const l2 = this.calculateRelativeLuminance(rgb2);

        const lighter = Math.max(l1, l2);
        const darker = Math.min(l1, l2);

        return (lighter + 0.05) / (darker + 0.05);
    }

    /** حساب السطوع النسبي / Calculate relative luminance */
    private static calculateRelativeLuminance(rgb: { r: number; g: number; b: number }): number {
        const { r, g, b } = rgb;
        const [rs, gs, bs] = [r, g, b].map(c => {
            c = c / 255;
            return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
        });

        return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
    }

    /** إنشاء توصيات التوافق / Generate harmony recommendations */
    private static generateHarmonyRecommendations(harmony: string, balanceScore: number, contrastScore: number): string[] {
        const recommendations: string[] = [];

        if (balanceScore < 70) {
            recommendations.push('Consider improving color balance distribution');
        }

        if (contrastScore < 4.5) {
            recommendations.push('Increase contrast for better accessibility');
        }

        switch (harmony) {
            case 'monochromatic':
                recommendations.push('Add complementary colors for more visual interest');
                break;
            case 'complementary':
                recommendations.push('Consider adding neutral colors for balance');
                break;
            case 'triadic':
                recommendations.push('Ensure one color dominates for hierarchy');
                break;
            case 'analogous':
                recommendations.push('Add accent color for contrast');
                break;
            default:
                recommendations.push('Review color relationships for better harmony');
        }

        return recommendations;
    }
}
