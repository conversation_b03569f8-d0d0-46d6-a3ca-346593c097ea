/**
 * مدير المؤقتات
 * Timeout manager
 * 
 * هذا الملف يحتوي على منطق إدارة المؤقتات
 * This file contains timeout management logic
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * مدير المؤقتات
 * Timeout manager class
 */
export class TimeoutManager {
    private readonly timers: Set<NodeJS.Timeout> = new Set();

    /**
     * إنشاء مؤقت مُدار
     * Creates a managed timer
     * 
     * @param callback - دالة الاستدعاء / Callback function
     * @param delay - التأخير بالميلي ثانية / Delay in milliseconds
     * @returns NodeJS.Timeout - معرف المؤقت / Timer ID
     * 
     * @example
     * ```typescript
     * const timerId = timeoutManager.createTimer(() => {
     *     console.log('Timer executed');
     * }, 1000);
     * ```
     */
    public createTimer(callback: () => void, delay: number): NodeJS.Timeout {
        const timerId = setTimeout(() => {
            callback();
            this.timers.delete(timerId);
        }, delay);
        
        this.timers.add(timerId);
        return timerId;
    }

    /**
     * إلغاء مؤقت
     * Cancels a timer
     * 
     * @param timerId - معرف المؤقت / Timer ID
     * 
     * @example
     * ```typescript
     * timeoutManager.clearTimer(timerId);
     * ```
     */
    public clearTimer(timerId: NodeJS.Timeout): void {
        clearTimeout(timerId);
        this.timers.delete(timerId);
    }

    /**
     * تنظيف جميع المؤقتات
     * Clears all timers
     * 
     * @example
     * ```typescript
     * timeoutManager.clearAll();
     * ```
     */
    public clearAll(): void {
        for (const timerId of this.timers) {
            clearTimeout(timerId);
        }
        this.timers.clear();
    }

    /**
     * الحصول على عدد المؤقتات النشطة
     * Gets the count of active timers
     * 
     * @returns number - عدد المؤقتات النشطة / Active timer count
     * 
     * @example
     * ```typescript
     * const count = timeoutManager.getTimerCount();
     * ```
     */
    public getTimerCount(): number {
        return this.timers.size;
    }

    /**
     * التحقق من وجود مؤقت
     * Checks if a timer exists
     * 
     * @param timerId - معرف المؤقت / Timer ID
     * @returns boolean - هل المؤقت موجود / Timer exists
     * 
     * @example
     * ```typescript
     * const exists = timeoutManager.hasTimer(timerId);
     * ```
     */
    public hasTimer(timerId: NodeJS.Timeout): boolean {
        return this.timers.has(timerId);
    }

    /**
     * إنشاء مؤقت مؤجل
     * Creates a delayed timer
     * 
     * @param callback - دالة الاستدعاء / Callback function
     * @param delay - التأخير بالميلي ثانية / Delay in milliseconds
     * @param condition - شرط التنفيذ / Execution condition
     * @returns NodeJS.Timeout - معرف المؤقت / Timer ID
     * 
     * @example
     * ```typescript
     * const timerId = timeoutManager.createDelayedTimer(
     *     () => console.log('Executed'),
     *     1000,
     *     () => document.readyState === 'complete'
     * );
     * ```
     */
    public createDelayedTimer(
        callback: () => void,
        delay: number,
        condition?: () => boolean
    ): NodeJS.Timeout {
        const timerId = setTimeout(() => {
            if (!condition || condition()) {
                callback();
            }
            this.timers.delete(timerId);
        }, delay);
        
        this.timers.add(timerId);
        return timerId;
    }

    /**
     * إنشاء مؤقت متكرر
     * Creates a repeating timer
     * 
     * @param callback - دالة الاستدعاء / Callback function
     * @param delay - التأخير بالميلي ثانية / Delay in milliseconds
     * @param maxRepeats - العدد الأقصى للتكرار / Maximum repeat count
     * @returns NodeJS.Timeout - معرف المؤقت / Timer ID
     * 
     * @example
     * ```typescript
     * const timerId = timeoutManager.createRepeatingTimer(
     *     () => console.log('Repeated'),
     *     1000,
     *     5
     * );
     * ```
     */
    public createRepeatingTimer(
        callback: () => void,
        delay: number,
        maxRepeats: number = Infinity
    ): NodeJS.Timeout {
        let repeatCount = 0;
        
        const executeCallback = () => {
            if (repeatCount < maxRepeats) {
                callback();
                repeatCount++;
                
                if (repeatCount < maxRepeats) {
                    const timerId = setTimeout(executeCallback, delay);
                    this.timers.add(timerId);
                    return timerId;
                }
            }
            return null;
        };
        
        const initialTimerId = setTimeout(executeCallback, delay);
        this.timers.add(initialTimerId);
        return initialTimerId;
    }

    /**
     * إنشاء مؤقت مع إعادة المحاولة
     * Creates a timer with retry logic
     * 
     * @param callback - دالة الاستدعاء / Callback function
     * @param delay - التأخير بالميلي ثانية / Delay in milliseconds
     * @param maxRetries - العدد الأقصى لإعادة المحاولة / Maximum retry count
     * @param retryDelay - تأخير إعادة المحاولة / Retry delay
     * @returns NodeJS.Timeout - معرف المؤقت / Timer ID
     * 
     * @example
     * ```typescript
     * const timerId = timeoutManager.createRetryTimer(
     *     async () => await fetchData(),
     *     1000,
     *     3,
     *     2000
     * );
     * ```
     */
    public createRetryTimer(
        callback: () => Promise<void> | void,
        delay: number,
        maxRetries: number = 3,
        retryDelay: number = 1000
    ): NodeJS.Timeout {
        let retryCount = 0;
        
        const executeWithRetry = async () => {
            try {
                await callback();
            } catch (error) {
                if (retryCount < maxRetries) {
                    retryCount++;
                    const retryTimerId = setTimeout(executeWithRetry, retryDelay);
                    this.timers.add(retryTimerId);
                } else {
                    console.error('Max retries reached:', error);
                }
            }
        };
        
        const timerId = setTimeout(executeWithRetry, delay);
        this.timers.add(timerId);
        return timerId;
    }

    /**
     * إنشاء مؤقت مع مهلة زمنية
     * Creates a timer with timeout
     * 
     * @param callback - دالة الاستدعاء / Callback function
     * @param delay - التأخير بالميلي ثانية / Delay in milliseconds
     * @param timeout - المهلة الزمنية / Timeout duration
     * @returns NodeJS.Timeout - معرف المؤقت / Timer ID
     * 
     * @example
     * ```typescript
     * const timerId = timeoutManager.createTimerWithTimeout(
     *     async () => await longRunningTask(),
     *     1000,
     *     5000
     * );
     * ```
     */
    public createTimerWithTimeout(
        callback: () => Promise<void> | void,
        delay: number,
        timeout: number
    ): NodeJS.Timeout {
        const timerId = setTimeout(async () => {
            const timeoutPromise = new Promise<void>((_, reject) => {
                setTimeout(() => reject(new Error('Timer timeout')), timeout);
            });
            
            try {
                await Promise.race([
                    Promise.resolve(callback()),
                    timeoutPromise
                ]);
            } catch (error) {
                console.error('Timer execution failed:', error);
            } finally {
                this.timers.delete(timerId);
            }
        }, delay);
        
        this.timers.add(timerId);
        return timerId;
    }
}
