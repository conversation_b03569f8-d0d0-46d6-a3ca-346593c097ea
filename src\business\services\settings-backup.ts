/**
 * مدير النسخ الاحتياطية للإعدادات
 * Settings backup manager
 *
 * هذا الملف يجمع جميع عمليات النسخ الاحتياطية من الملفات المتخصصة
 * This file aggregates all backup operations from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// استيراد العمليات الأساسية
// Import core operations
export { SettingsBackupCoreOperations } from './settings-backup-core-operations';

// استيراد مدير النسخ الاحتياطية
// Import backup manager
export { SettingsBackupManager } from './settings-backup-manager';

import { ApplicationConfig, ValidationResult } from '@shared/types';
import * as fs from 'fs';
import * as path from 'path';
import { SettingsBackupCoreOperations } from './settings-backup-core-operations';
import { SettingsBackupManager as BackupManager } from './settings-backup-manager';
import {
    BackupInfo,
    ExportOptions,
    ImportOptions,
    SettingsManagerConfig
} from './settings-config';

/**
 * فئة مدير النسخ الاحتياطية / Backup manager class
 */
export class SettingsBackup {
    private readonly coreOperations: SettingsBackupCoreOperations;
    private readonly backupManager: BackupManager;
    private readonly backupDirectory: string;

    /**
     * منشئ مدير النسخ الاحتياطية / Backup manager constructor
     */
    constructor(config: SettingsManagerConfig) {
        this.backupDirectory = path.join(process.cwd(), 'backups', 'settings');
        this.ensureBackupDirectory();

        this.coreOperations = new SettingsBackupCoreOperations(config, this.backupDirectory);
        this.backupManager = new BackupManager(config, this.backupDirectory);
    }

    /**
     * إنشاء نسخة احتياطية / Create backup
     */
    public async createBackup(settings: ApplicationConfig, description?: string): Promise<BackupInfo> {
        return await this.coreOperations.createBackup(settings, description);
    }

    /**
     * استعادة من نسخة احتياطية / Restore from backup
     */
    public async restoreFromBackup(backupPath: string): Promise<ApplicationConfig> {
        return await this.coreOperations.restoreFromBackup(backupPath);
    }

    /**
     * حذف نسخة احتياطية / Delete backup
     */
    public async deleteBackup(backupPath: string): Promise<boolean> {
        return await this.coreOperations.deleteBackup(backupPath);
    }

    /**
     * تصدير الإعدادات / Export settings
     */
    public async exportSettings(
        settings: ApplicationConfig,
        exportPath: string,
        options?: ExportOptions
    ): Promise<boolean> {
        return await this.coreOperations.exportSettings(settings, exportPath, options);
    }

    /**
     * استيراد الإعدادات / Import settings
     */
    public async importSettings(importPath: string, options?: ImportOptions): Promise<ApplicationConfig> {
        return await this.coreOperations.importSettings(importPath, options);
    }

    /**
     * الحصول على قائمة النسخ الاحتياطية / Get backup list
     */
    public async getBackupList(): Promise<BackupInfo[]> {
        return await this.backupManager.getBackupList();
    }

    /**
     * تنظيف النسخ الاحتياطية القديمة / Cleanup old backups
     */
    public async cleanupOldBackups(maxBackups?: number): Promise<number> {
        return await this.backupManager.cleanupOldBackups(maxBackups);
    }

    /**
     * التحقق من سلامة النسخ الاحتياطية / Verify backup integrity
     */
    public async verifyBackupIntegrity(): Promise<ValidationResult> {
        return await this.backupManager.verifyBackupIntegrity();
    }

    /**
     * التأكد من وجود مجلد النسخ الاحتياطية / Ensure backup directory exists
     */
    private ensureBackupDirectory(): void {
        try {
            if (!fs.existsSync(this.backupDirectory)) {
                fs.mkdirSync(this.backupDirectory, { recursive: true });
            }
        } catch (error) {
            console.error('خطأ في إنشاء مجلد النسخ الاحتياطية:', error);
        }
    }
}
