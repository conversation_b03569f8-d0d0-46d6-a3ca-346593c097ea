{"constitutionCompliance": 92, "codeQuality": 0, "projectStructure": 100, "overallScore": 64, "grade": "F", "issues": ["ملف كبير: dark-mode-config.ts (288 سطر)", "مل<PERSON>ي<PERSON>: dark-mode-observer.ts (351 سطر)", "ملف كبير: dark-mode-service.ts (324 سطر)", "ملف كبير: dark-mode-styles.ts (336 سطر)", "مل<PERSON>: settings-manager.ts (316 سطر)", "ملف كبير: video-quality-applicator.ts (539 سطر)", "ملف كبير: video-quality-config.ts (293 سطر)", "ملف كبي<PERSON>: video-quality-detector.ts (437 سطر)", "ملف كبير: video-quality-service.ts (421 سطر)", "ملف كبير: ad-blocker-domains.ts (201 سطر)", "ملف كبير: ad-blocker-service.ts (234 سطر)", "ملف كبير: security-layer.ts (353 سطر)", "مل<PERSON>: security-manager-service.ts (299 سطر)", "م<PERSON><PERSON>: security-threat-manager.ts (310 سطر)", "ملف كبير: settings-preload.ts (300 سطر)", "ملف كبير: youtube-preload.ts (212 سطر)", "م<PERSON><PERSON>: menu-manager.ts (266 سطر)", "مل<PERSON> كبير: youtube-controller.ts (401 سطر)", "ملف كبير: main-application.ts (302 سطر)", "مل<PERSON> كبير: settings-renderer.ts (465 سطر)", "م<PERSON><PERSON>: window-manager.ts (252 سطر)", "ملف كبير: application-constants.ts (290 سطر)", "ملف كبير: application-types.ts (285 سطر)", "ملف كبير: youtube-types.ts (284 سطر)", "م<PERSON><PERSON>: resource-manager.ts (308 سطر)", "مل<PERSON> كبي<PERSON>: constitution-checker.ts (390 سطر)", "ملف كبير: final-verification.ts (465 سطر)", "مل<PERSON> كبي<PERSON>: functionality-tester.ts (578 سطر)", "ملف كبير: simple-verification.ts (358 سطر)", "ملف كبير: application.e2e.test.ts (372 سطر)", "مل<PERSON> كبير: application.integration.test.ts (481 سطر)", "ملف كبير: security-layer.integration.test.ts (322 سطر)", "مل<PERSON> كبي<PERSON>: run-tests.ts (381 سطر)", "ملف كبير: setup.ts (285 سطر)", "م<PERSON><PERSON>: settings-manager.test.ts (314 سطر)", "م<PERSON><PERSON>: video-quality-manager.test.ts (413 سطر)", "م<PERSON><PERSON>: security-manager.test.ts (345 سطر)", "مل<PERSON> كبير: youtube-controller.test.ts (438 سطر)", "م<PERSON><PERSON>: resource-manager.test.ts (294 سطر)"], "recommendations": ["⚡ تحسين جودة الكود وإضافة التوثيق"]}