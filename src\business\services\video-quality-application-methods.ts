/**
 * طرق تطبيق جودة الفيديو
 * Video quality application methods
 * 
 * هذا الملف يحتوي على طرق تطبيق جودة الفيديو المختلفة
 * This file contains different video quality application methods
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQuality, ValidationResult } from '@shared/types';
import { 
    VIDEO_QUALITY_MAP,
    VIDEO_QUALITY_CONSTANTS
} from './video-quality-config';
import { VideoQualityDomHelper } from './video-quality-dom-helper';

/**
 * فئة طرق تطبيق جودة الفيديو
 * Video quality application methods class
 */
export class VideoQualityApplicationMethods {
    
    /**
     * تطبيق الجودة عبر API
     * Apply quality via API
     * 
     * @param quality - الجودة المطلوبة
     * @returns Promise<ValidationResult>
     */
    public static async applyQualityViaAPI(quality: VideoQuality): Promise<ValidationResult> {
        try {
            // البحث عن مشغل YouTube
            const player = (window as any).ytplayer?.config?.args?.player_response;
            if (player) {
                const qualityLevel = VIDEO_QUALITY_MAP[quality];
                if (qualityLevel) {
                    // محاولة تطبيق الجودة عبر API
                    const ytPlayer = (window as any).ytplayer;
                    if (ytPlayer && typeof ytPlayer.setPlaybackQuality === 'function') {
                        ytPlayer.setPlaybackQuality(qualityLevel);
                        
                        // التحقق من تطبيق الجودة
                        await VideoQualityDomHelper.delay(1000);
                        const currentQuality = ytPlayer.getPlaybackQuality?.();
                        
                        if (currentQuality === qualityLevel) {
                            return { isValid: true, errors: [] };
                        }
                    }
                }
            }

            return {
                isValid: false,
                errors: [{
                    field: 'api',
                    message: 'فشل تطبيق الجودة عبر API / Failed to apply quality via API',
                    code: 'API_APPLICATION_FAILED'
                }]
            };

        } catch (error) {
            return {
                isValid: false,
                errors: [{
                    field: 'api',
                    message: `خطأ في API: ${error}`,
                    code: 'API_ERROR'
                }]
            };
        }
    }

    /**
     * تطبيق الجودة عبر القائمة
     * Apply quality via menu
     * 
     * @param quality - الجودة المطلوبة
     * @returns Promise<ValidationResult>
     */
    public static async applyQualityViaMenu(quality: VideoQuality): Promise<ValidationResult> {
        try {
            // البحث عن زر الإعدادات
            const settingsButton = VideoQualityDomHelper.findSettingsButton();
            if (!settingsButton) {
                return {
                    isValid: false,
                    errors: [{
                        field: 'menu',
                        message: 'زر الإعدادات غير موجود / Settings button not found',
                        code: 'SETTINGS_BUTTON_NOT_FOUND'
                    }]
                };
            }

            // النقر على زر الإعدادات
            (settingsButton as HTMLElement).click();
            await VideoQualityDomHelper.delay(500);

            // البحث عن عنصر الجودة في القائمة
            const qualityMenuItem = this.findQualityMenuItem(quality);
            if (!qualityMenuItem) {
                return {
                    isValid: false,
                    errors: [{
                        field: 'menu',
                        message: `عنصر الجودة ${quality} غير موجود في القائمة / Quality item ${quality} not found in menu`,
                        code: 'QUALITY_MENU_ITEM_NOT_FOUND'
                    }]
                };
            }

            // النقر على عنصر الجودة
            (qualityMenuItem as HTMLElement).click();
            await VideoQualityDomHelper.delay(1000);

            return { isValid: true, errors: [] };

        } catch (error) {
            return {
                isValid: false,
                errors: [{
                    field: 'menu',
                    message: `خطأ في تطبيق الجودة عبر القائمة: ${error}`,
                    code: 'MENU_APPLICATION_ERROR'
                }]
            };
        }
    }

    /**
     * تطبيق الجودة عبر التحديد المباشر
     * Apply quality via direct selection
     * 
     * @param quality - الجودة المطلوبة
     * @returns Promise<ValidationResult>
     */
    public static async applyQualityViaDirectSelection(quality: VideoQuality): Promise<ValidationResult> {
        try {
            // البحث عن عنصر الفيديو
            const videoElement = document.querySelector('video') as HTMLVideoElement;
            if (!videoElement) {
                return {
                    isValid: false,
                    errors: [{
                        field: 'video',
                        message: 'عنصر الفيديو غير موجود / Video element not found',
                        code: 'VIDEO_ELEMENT_NOT_FOUND'
                    }]
                };
            }

            // محاولة تعديل خصائص الفيديو
            const qualityInfo = VIDEO_QUALITY_MAP[quality];
            if (qualityInfo) {
                videoElement.setAttribute('data-quality', quality);
                videoElement.setAttribute('data-height', qualityInfo.height.toString());
                
                // محاولة تطبيق الجودة عبر خصائص الفيديو
                if (videoElement.videoHeight !== qualityInfo.height) {
                    // إعادة تحميل الفيديو بالجودة الجديدة
                    const currentTime = videoElement.currentTime;
                    const src = videoElement.src;
                    
                    videoElement.src = src + (src.includes('?') ? '&' : '?') + `quality=${quality}`;
                    videoElement.currentTime = currentTime;
                }

                await VideoQualityDomHelper.delay(VIDEO_QUALITY_CONSTANTS.QUALITY_APPLY_DELAY);
                return { isValid: true, errors: [] };
            }

            return {
                isValid: false,
                errors: [{
                    field: 'selection',
                    message: 'معلومات الجودة غير متوفرة / Quality info not available',
                    code: 'QUALITY_INFO_NOT_AVAILABLE'
                }]
            };

        } catch (error) {
            return {
                isValid: false,
                errors: [{
                    field: 'selection',
                    message: `خطأ في التحديد المباشر: ${error}`,
                    code: 'DIRECT_SELECTION_ERROR'
                }]
            };
        }
    }

    /**
     * البحث عن عنصر الجودة في القائمة
     * Find quality menu item
     * 
     * @param quality - الجودة المطلوبة
     * @returns Element | null
     */
    private static findQualityMenuItem(quality: VideoQuality): Element | null {
        const qualityItems = document.querySelectorAll('.ytp-quality-menu .ytp-menuitem');

        for (const item of qualityItems) {
            const label = item.querySelector('.ytp-menuitem-label');
            if (label && label.textContent) {
                const text = label.textContent.toLowerCase().trim();
                if (text.includes(quality.toLowerCase()) ||
                    text.includes(quality.replace('p', ''))) {
                    return item;
                }
            }
        }

        return null;
    }

    /**
     * تطبيق الجودة عبر التلاعب المباشر
     * Apply quality via direct manipulation
     * 
     * @param quality - الجودة المطلوبة
     * @param player - عنصر المشغل
     * @returns Promise<ValidationResult>
     */
    public static async applyQualityViaDirectManipulation(quality: VideoQuality, player: any): Promise<ValidationResult> {
        try {
            // محاولة تعديل خصائص المشغل مباشرة
            const qualityInfo = VIDEO_QUALITY_MAP[quality];

            // تعديل خصائص البيانات
            player.setAttribute('data-quality', quality);
            player.setAttribute('data-yt-format', qualityInfo.ytFormat);

            // محاولة استخدام YouTube API الداخلي
            const window = globalThis as any;
            if (window.ytplayer && window.ytplayer.config) {
                window.ytplayer.config.args = window.ytplayer.config.args || {};
                window.ytplayer.config.args.quality = qualityInfo.ytFormat;
            }

            await VideoQualityDomHelper.delay(VIDEO_QUALITY_CONSTANTS.QUALITY_APPLY_DELAY);
            return { isValid: true, errors: [] };

        } catch (error) {
            return {
                isValid: false,
                errors: [{
                    field: 'manipulation',
                    message: `خطأ في التلاعب المباشر: ${error}`,
                    code: 'MANIPULATION_ERROR'
                }]
            };
        }
    }
}
