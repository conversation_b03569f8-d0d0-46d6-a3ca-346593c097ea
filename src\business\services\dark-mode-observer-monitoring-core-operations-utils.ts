/**
 * مراقبة الوضع المظلم - أدوات العمليات الأساسية
 * Dark mode monitoring - Core operations utilities
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import {
    DARK_MODE_CONSTANTS,
    DarkModeConfig
} from './dark-mode-config';

/**
 * فئة أدوات العمليات الأساسية لمراقبة الوضع المظلم
 * Core operations utilities for dark mode monitoring
 */
export class DarkModeObserverMonitoringCoreOperationsUtils {

    /**
     * البحث عن عناصر جديدة - تفويض للأدوات الأساسية
     * Find new elements - Delegate to basic utils
     */
    public static findNewElements(): Element[] {
        return DarkModeObserverMonitoringCoreOperationsUtilsBasic.findNewElements();
    }

    /**
     * التحقق من حالة الوضع المظلم الحالية
     * Validate current dark mode state
     */
    public static validateCurrentDarkModeState(config: DarkModeConfig): {
        isDarkModeExpected: boolean;
        isDarkModeApplied: boolean;
        needsReapplication: boolean;
        needsRemoval: boolean;
    } {
        const isDarkModeExpected = config.enabled;
        const isDarkModeApplied = document.body.classList.contains(DARK_MODE_CONSTANTS.DARK_MODE_CLASS);

        return {
            isDarkModeExpected,
            isDarkModeApplied,
            needsReapplication: isDarkModeExpected && !isDarkModeApplied,
            needsRemoval: !isDarkModeExpected && isDarkModeApplied
        };
    }

    /**
     * إنشاء مراقب التغييرات مع الإعدادات المحسنة - تفويض للأدوات الأساسية
     * Create mutation observer with optimized settings - Delegate to basic utils
     */
    public static createOptimizedMutationObserver(callback: (mutations: MutationRecord[]) => void): MutationObserver {
        return DarkModeObserverMonitoringCoreOperationsUtilsBasic.createOptimizedMutationObserver(callback);
    }

    /**
     * الحصول على إعدادات المراقب المحسنة - تفويض للأدوات الأساسية
     * Get optimized observer configuration - Delegate to basic utils
     */
    public static getOptimizedObserverConfig(): MutationObserverInit {
        return DarkModeObserverMonitoringCoreOperationsUtilsBasic.getOptimizedObserverConfig();
    }

    /**
     * فحص صحة العنصر المستهدف - تفويض للأدوات الأساسية
     * Validate target element - Delegate to basic utils
     */
    public static validateTargetElement(element: Element): boolean {
        return DarkModeObserverMonitoringCoreOperationsUtilsBasic.validateTargetElement(element);
    }

    /**
     * فحص ما إذا كان العنصر ذا صلة - تفويض للأدوات الأساسية
     * Check if element is relevant - Delegate to basic utils
     */
    public static isElementRelevant(element: Element): boolean {
        return DarkModeObserverMonitoringCoreOperationsUtilsBasic.isElementRelevant(element);
    }

    /**
     * فحص ما إذا كان تغيير الخاصية ذا صلة - تفويض للأدوات الأساسية
     * Check if attribute change is relevant - Delegate to basic utils
     */
    public static isAttributeChangeRelevant(element: Element, attributeName: string | null): boolean {
        if (!attributeName) return false;

        if (!DarkModeObserverMonitoringCoreOperationsUtilsBasic.checkRelevantAttributes(attributeName)) return false;

        // فحص التغييرات في الفئات المهمة
        if (attributeName === 'class') {
            return DarkModeObserverMonitoringCoreOperationsUtilsBasic.checkRelevantClasses(element.classList);
        }

        // فحص التغييرات في الأنماط المهمة
        if (attributeName === 'style') {
            const style = element.getAttribute('style') || '';
            return DarkModeObserverMonitoringCoreOperationsUtilsBasic.checkRelevantStyles(style);
        }

        return true;
    }

    /**
     * فحص ما إذا كان التغيير ذا صلة
     * Check if mutation is relevant
     */
    public static isMutationRelevant(mutation: MutationRecord): boolean {
        // فحص إضافة عقد جديدة
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            for (const node of Array.from(mutation.addedNodes)) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    const element = node as Element;
                    if (this.isElementRelevant(element)) {
                        return true;
                    }
                }
            }
        }

        // فحص تغييرات الخصائص
        if (mutation.type === 'attributes') {
            const target = mutation.target as Element;
            return this.isAttributeChangeRelevant(target, mutation.attributeName);
        }

        return false;
    }

    /**
     * تنظيف الموارد - تفويض للأدوات الأساسية
     * Cleanup resources - Delegate to basic utils
     */
    public static cleanup(): void {
        DarkModeObserverMonitoringCoreOperationsUtilsBasic.cleanup();
    }

    /**
     * إنشاء تقرير حالة المراقبة - تفويض للأدوات الأساسية
     * Create monitoring status report - Delegate to basic utils
     */
    public static createMonitoringStatusReport(config: DarkModeConfig): {
        timestamp: number;
        configEnabled: boolean;
        bodyHasDarkClass: boolean;
        newElementsCount: number;
        relevantElementsCount: number;
    } {
        const basicReport = DarkModeObserverMonitoringCoreOperationsUtilsBasic.createBasicStatusReport();

        return {
            ...basicReport,
            configEnabled: config.enabled
        };
    }
}
