/**
 * عمليات تنفيذ الاختبارات الأساسية
 * Core test execution operations
 * 
 * هذا الملف يحتوي على عمليات تنفيذ الاختبارات الأساسية
 * This file contains core test execution operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import {
    TestResult,
    TestSuiteResult,
    TestRunnerConfig,
    TestExecutionOptions,
    DEFAULT_TEST_RUNNER_CONFIG,
    DEFAULT_EXECUTION_OPTIONS,
    TEST_RUNNER_CONSTANTS,
    TEST_RUNNER_MESSAGES
} from './run-tests-types';

/**
 * فئة عمليات تنفيذ الاختبارات الأساسية
 * Core test execution operations class
 */
export class TestRunnerCoreExecution {

    /**
     * تنفيذ مجموعة اختبارات محددة
     * Execute specific test suite
     */
    public static async executeTestSuite(
        suiteName: string,
        config: TestRunnerConfig = DEFAULT_TEST_RUNNER_CONFIG,
        options: TestExecutionOptions = DEFAULT_EXECUTION_OPTIONS
    ): Promise<TestSuiteResult> {
        try {
            console.log(`${TEST_RUNNER_MESSAGES.INFO.RUNNING_SUITE} ${suiteName}`);
            
            const startTime = Date.now();
            const command = this.buildJestCommand(suiteName, config, options);
            
            const output = execSync(command, {
                encoding: 'utf8',
                cwd: config.projectRoot,
                stdio: 'pipe',
                timeout: options.timeout
            });

            const endTime = Date.now();
            const duration = endTime - startTime;

            const testResults = this.parseJestOutput(output, suiteName, duration);
            
            return {
                suiteName,
                testResults: [testResults],
                summary: {
                    total: testResults.passed + testResults.failed + testResults.skipped,
                    passed: testResults.passed,
                    failed: testResults.failed,
                    skipped: testResults.skipped,
                    coverage: testResults.coverage,
                    duration: testResults.duration
                },
                startTime: new Date(startTime),
                endTime: new Date(endTime)
            };

        } catch (error: any) {
            console.error(`${TEST_RUNNER_MESSAGES.ERROR.SUITE_FAILED} ${suiteName}:`, error.message);
            
            const endTime = Date.now();
            const duration = endTime - Date.now();

            // محاولة تحليل النتائج حتى لو فشلت الاختبارات
            // Try to parse results even if tests failed
            try {
                const testResults = this.parseJestOutput(error.stdout || '', suiteName, duration);
                testResults.errors.push(error.message);
                
                return {
                    suiteName,
                    testResults: [testResults],
                    summary: {
                        total: testResults.passed + testResults.failed + testResults.skipped,
                        passed: testResults.passed,
                        failed: testResults.failed,
                        skipped: testResults.skipped,
                        coverage: testResults.coverage,
                        duration: testResults.duration
                    },
                    startTime: new Date(),
                    endTime: new Date(endTime)
                };
            } catch {
                const failedResult: TestResult = {
                    testSuite: suiteName,
                    passed: 0,
                    failed: 1,
                    skipped: 0,
                    coverage: 0,
                    duration,
                    errors: [error.message]
                };

                return {
                    suiteName,
                    testResults: [failedResult],
                    summary: {
                        total: 1,
                        passed: 0,
                        failed: 1,
                        skipped: 0,
                        coverage: 0,
                        duration
                    },
                    startTime: new Date(),
                    endTime: new Date(endTime)
                };
            }
        }
    }

    /**
     * بناء أمر Jest
     * Build Jest command
     */
    private static buildJestCommand(
        suiteName: string,
        config: TestRunnerConfig,
        options: TestExecutionOptions
    ): string {
        const baseCommand = 'npx jest';
        const testPattern = this.getTestPattern(suiteName);
        const coverageFlag = options.collectCoverage ? '--coverage' : '';
        const verboseFlag = options.verbose ? '--verbose' : '';
        const watchFlag = options.watch ? '--watch' : '';
        const maxWorkersFlag = options.maxWorkers ? `--maxWorkers=${options.maxWorkers}` : '';

        return [
            baseCommand,
            testPattern,
            coverageFlag,
            verboseFlag,
            watchFlag,
            maxWorkersFlag,
            '--passWithNoTests'
        ].filter(Boolean).join(' ');
    }

    /**
     * الحصول على نمط الاختبار
     * Get test pattern
     */
    private static getTestPattern(suiteName: string): string {
        switch (suiteName) {
            case 'unit':
                return 'src/tests/unit/**/*.test.ts';
            case 'integration':
                return 'src/tests/integration/**/*.test.ts';
            case 'e2e':
                return 'src/tests/e2e/**/*.test.ts';
            default:
                return `src/tests/**/*${suiteName}*.test.ts`;
        }
    }

    /**
     * تحليل مخرجات Jest
     * Parse Jest output
     */
    private static parseJestOutput(output: string, suiteName: string, duration: number): TestResult {
        const lines = output.split('\n');

        let passed = 0;
        let failed = 0;
        let skipped = 0;
        let coverage = 0;
        const errors: string[] = [];

        // البحث عن نتائج الاختبارات
        // Search for test results
        for (const line of lines) {
            if (line.includes('Tests:')) {
                const passMatch = line.match(/(\d+) passed/);
                if (passMatch) passed = parseInt(passMatch[1]);

                const failMatch = line.match(/(\d+) failed/);
                if (failMatch) failed = parseInt(failMatch[1]);

                const skipMatch = line.match(/(\d+) skipped/);
                if (skipMatch) skipped = parseInt(skipMatch[1]);
            }

            if (line.includes('All files')) {
                const coverageMatch = line.match(/(\d+\.?\d*)%/);
                if (coverageMatch) coverage = parseFloat(coverageMatch[1]);
            }

            if (line.includes('FAIL') || line.includes('Error:')) {
                errors.push(line.trim());
            }
        }

        return {
            testSuite: suiteName,
            passed,
            failed,
            skipped,
            coverage,
            duration,
            errors
        };
    }

    /**
     * التحقق من صحة التكوين
     * Validate configuration
     */
    public static validateConfig(config: TestRunnerConfig): boolean {
        if (!config.projectRoot || !fs.existsSync(config.projectRoot)) {
            console.error(TEST_RUNNER_MESSAGES.ERROR.INVALID_PROJECT_ROOT);
            return false;
        }

        if (config.coverageThreshold < 0 || config.coverageThreshold > 100) {
            console.error(TEST_RUNNER_MESSAGES.ERROR.INVALID_COVERAGE_THRESHOLD);
            return false;
        }

        return true;
    }

    /**
     * التحقق من صحة خيارات التنفيذ
     * Validate execution options
     */
    public static validateOptions(options: TestExecutionOptions): boolean {
        if (options.timeout && options.timeout < 1000) {
            console.error(TEST_RUNNER_MESSAGES.ERROR.INVALID_TIMEOUT);
            return false;
        }

        if (options.maxWorkers && options.maxWorkers < 1) {
            console.error(TEST_RUNNER_MESSAGES.ERROR.INVALID_MAX_WORKERS);
            return false;
        }

        return true;
    }
}
