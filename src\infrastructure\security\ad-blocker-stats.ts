/**
 * إحصائيات مانع الإعلانات
 * Ad blocker statistics manager
 *
 * هذا الملف يحتوي على إدارة إحصائيات مانع الإعلانات
 * This file contains ad blocker statistics management
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { AdBlockerStats } from './ad-blocker-config';

/**
 * مدير إحصائيات مانع الإعلانات
 * Ad blocker statistics manager
 */
export class AdBlockerStatsManager {
    private stats: AdBlockerStats;

    /**
     * منشئ مدير الإحصائيات
     * Statistics manager constructor
     */
    constructor() {
        this.stats = {
            blockedAds: 0,
            blockedDomains: 0,
            allowedRequests: 0,
            startTime: Date.now()
        };
    }

    /**
     * الحصول على الإحصائيات الحالية
     * Get current statistics
     *
     * @returns الإحصائيات الحالية
     */
    public getStats(): AdBlockerStats {
        return { ...this.stats };
    }

    /**
     * زيادة عدد الإعلانات المحظورة
     * Increment blocked ads count
     */
    public incrementBlockedAds(): void {
        this.stats = {
            ...this.stats,
            blockedAds: this.stats.blockedAds + 1
        };
    }

    /**
     * زيادة عدد النطاقات المحظورة
     * Increment blocked domains count
     */
    public incrementBlockedDomains(): void {
        this.stats = {
            ...this.stats,
            blockedDomains: this.stats.blockedDomains + 1
        };
    }

    /**
     * زيادة عدد الطلبات المسموحة
     * Increment allowed requests count
     */
    public incrementAllowedRequests(): void {
        this.stats = {
            ...this.stats,
            allowedRequests: this.stats.allowedRequests + 1
        };
    }

    /**
     * إعادة تعيين الإحصائيات
     * Reset statistics
     */
    public resetStats(): void {
        this.stats = {
            blockedAds: 0,
            blockedDomains: 0,
            allowedRequests: 0,
            startTime: Date.now()
        };
    }

    /**
     * الحصول على معدل الحظر
     * Get blocking rate
     *
     * @returns معدل الحظر كنسبة مئوية
     */
    public getBlockingRate(): number {
        const totalRequests = this.stats.blockedAds + this.stats.allowedRequests;
        if (totalRequests === 0) return 0;
        return (this.stats.blockedAds / totalRequests) * 100;
    }

    /**
     * الحصول على مدة التشغيل
     * Get uptime in milliseconds
     *
     * @returns مدة التشغيل بالميلي ثانية
     */
    public getUptime(): number {
        return Date.now() - this.stats.startTime;
    }

    /**
     * الحصول على تقرير مفصل
     * Get detailed report
     *
     * @returns تقرير مفصل للإحصائيات
     */
    public getDetailedReport(): string {
        const uptime = this.getUptime();
        const uptimeSeconds = Math.floor(uptime / 1000);
        const blockingRate = this.getBlockingRate();

        return `
📊 تقرير مانع الإعلانات / Ad Blocker Report:
🚫 إعلانات محظورة / Blocked Ads: ${this.stats.blockedAds}
🌐 نطاقات محظورة / Blocked Domains: ${this.stats.blockedDomains}
✅ طلبات مسموحة / Allowed Requests: ${this.stats.allowedRequests}
📈 معدل الحظر / Blocking Rate: ${blockingRate.toFixed(2)}%
⏱️ مدة التشغيل / Uptime: ${uptimeSeconds} ثانية
        `.trim();
    }
}