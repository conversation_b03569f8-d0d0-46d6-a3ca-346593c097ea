/**
 * عمليات تقارير الاختبارات الأساسية
 * Core test reporting operations
 * 
 * هذا الملف يحتوي على عمليات إنشاء التقارير للاختبارات
 * This file contains test reporting operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import * as fs from 'fs';
import * as path from 'path';
import {
    TestResult,
    TestReport,
    TestRunnerConfig,
    CoverageReport,
    PerformanceReport,
    DEFAULT_TEST_RUNNER_CONFIG,
    TEST_RUNNER_CONSTANTS,
    TEST_RUNNER_MESSAGES
} from './run-tests-types';

/**
 * فئة عمليات تقارير الاختبارات الأساسية
 * Core test reporting operations class
 */
export class TestRunnerCoreReporting {

    /**
     * إنشاء التقرير النهائي
     * Generate final report
     */
    public static generateReport(
        results: TestResult[], 
        totalDuration: number,
        config: TestRunnerConfig = DEFAULT_TEST_RUNNER_CONFIG
    ): TestReport {
        const totalTests = results.reduce((sum, r) => sum + r.passed + r.failed + r.skipped, 0);
        const totalPassed = results.reduce((sum, r) => sum + r.passed, 0);
        const totalFailed = results.reduce((sum, r) => sum + r.failed, 0);
        const totalSkipped = results.reduce((sum, r) => sum + r.skipped, 0);

        // حساب متوسط التغطية
        // Calculate average coverage
        const overallCoverage = results.length > 0
            ? results.reduce((sum, r) => sum + r.coverage, 0) / results.length
            : 0;

        return {
            totalTests,
            totalPassed,
            totalFailed,
            totalSkipped,
            overallCoverage,
            totalDuration,
            results,
            timestamp: new Date()
        };
    }

    /**
     * حفظ التقرير
     * Save report
     */
    public static async saveReport(
        report: TestReport,
        config: TestRunnerConfig = DEFAULT_TEST_RUNNER_CONFIG
    ): Promise<void> {
        const reportsDir = path.join(config.projectRoot, 'coverage', 'reports');

        // إنشاء مجلد التقارير إذا لم يكن موجوداً
        // Create reports directory if it doesn't exist
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }

        // حفظ التقرير JSON
        // Save JSON report
        const jsonPath = path.join(reportsDir, `test-report-${Date.now()}.json`);
        fs.writeFileSync(jsonPath, JSON.stringify(report, null, 2));

        // حفظ التقرير HTML
        // Save HTML report
        const htmlPath = path.join(reportsDir, `test-report-${Date.now()}.html`);
        const htmlContent = this.generateHTMLReport(report, config);
        fs.writeFileSync(htmlPath, htmlContent);

        console.log(`${TEST_RUNNER_MESSAGES.INFO.REPORT_SAVED} ${jsonPath}`);
        console.log(`${TEST_RUNNER_MESSAGES.INFO.HTML_REPORT_SAVED} ${htmlPath}`);
    }

    /**
     * إنشاء تقرير HTML
     * Generate HTML report
     */
    public static generateHTMLReport(
        report: TestReport,
        config: TestRunnerConfig = DEFAULT_TEST_RUNNER_CONFIG
    ): string {
        const passRate = report.totalTests > 0 
            ? (report.totalPassed / report.totalTests * 100).toFixed(2) 
            : '0';
        const coverageStatus = report.overallCoverage >= config.coverageThreshold ? '✅' : '❌';

        return `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الاختبارات - YouTube Dark CyberX</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .header { 
            text-align: center; 
            margin-bottom: 30px; 
        }
        .summary { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 20px; 
            margin-bottom: 30px; 
        }
        .metric { 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 8px; 
            text-align: center; 
        }
        .metric h3 { 
            margin: 0 0 10px 0; 
            color: #333; 
        }
        .metric .value { 
            font-size: 2em; 
            font-weight: bold; 
        }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .coverage { 
            color: ${report.overallCoverage >= config.coverageThreshold ? '#28a745' : '#dc3545'}; 
        }
        .results { margin-top: 30px; }
        .test-suite { 
            margin-bottom: 20px; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 8px; 
        }
        .test-suite h4 { 
            margin: 0 0 10px 0; 
        }
        .errors { 
            background: #f8d7da; 
            padding: 10px; 
            border-radius: 4px; 
            margin-top: 10px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 تقرير الاختبارات الشامل</h1>
            <p>YouTube Dark CyberX - ${report.timestamp.toLocaleString('ar-SA')}</p>
        </div>
        
        <div class="summary">
            <div class="metric">
                <h3>إجمالي الاختبارات</h3>
                <div class="value">${report.totalTests}</div>
            </div>
            <div class="metric">
                <h3>نجح</h3>
                <div class="value passed">${report.totalPassed}</div>
            </div>
            <div class="metric">
                <h3>فشل</h3>
                <div class="value failed">${report.totalFailed}</div>
            </div>
            <div class="metric">
                <h3>معدل النجاح</h3>
                <div class="value passed">${passRate}%</div>
            </div>
            <div class="metric">
                <h3>التغطية ${coverageStatus}</h3>
                <div class="value coverage">${report.overallCoverage.toFixed(2)}%</div>
            </div>
            <div class="metric">
                <h3>المدة الزمنية</h3>
                <div class="value">${(report.totalDuration / 1000).toFixed(2)}s</div>
            </div>
        </div>
        
        <div class="results">
            <h2>نتائج مفصلة</h2>
            ${report.results.map(result => `
                <div class="test-suite">
                    <h4>${result.testSuite} Tests</h4>
                    <p>✅ نجح: ${result.passed} | ❌ فشل: ${result.failed} | ⏭️ تم تخطيه: ${result.skipped}</p>
                    <p>📊 التغطية: ${result.coverage.toFixed(2)}% | ⏱️ المدة: ${(result.duration / 1000).toFixed(2)}s</p>
                    ${result.errors.length > 0 ? `
                        <div class="errors">
                            <strong>أخطاء:</strong>
                            <ul>
                                ${result.errors.map(error => `<li>${error}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                </div>
            `).join('')}
        </div>
    </div>
</body>
</html>`;
    }

    /**
     * طباعة التقرير في وحدة التحكم
     * Print report to console
     */
    public static printReport(
        report: TestReport,
        config: TestRunnerConfig = DEFAULT_TEST_RUNNER_CONFIG
    ): void {
        console.log('\n' + '='.repeat(60));
        console.log(TEST_RUNNER_MESSAGES.INFO.FINAL_REPORT);
        console.log('='.repeat(60));

        console.log(`📋 ${TEST_RUNNER_MESSAGES.INFO.TOTAL_TESTS}: ${report.totalTests}`);
        console.log(`✅ ${TEST_RUNNER_MESSAGES.INFO.PASSED}: ${report.totalPassed}`);
        console.log(`❌ ${TEST_RUNNER_MESSAGES.INFO.FAILED}: ${report.totalFailed}`);
        console.log(`⏭️ ${TEST_RUNNER_MESSAGES.INFO.SKIPPED}: ${report.totalSkipped}`);
        console.log(`📊 ${TEST_RUNNER_MESSAGES.INFO.COVERAGE}: ${report.overallCoverage.toFixed(2)}%`);
        console.log(`⏱️ ${TEST_RUNNER_MESSAGES.INFO.DURATION}: ${(report.totalDuration / 1000).toFixed(2)}s`);

        const passRate = report.totalTests > 0 ? (report.totalPassed / report.totalTests * 100) : 0;
        console.log(`🎯 ${TEST_RUNNER_MESSAGES.INFO.PASS_RATE}: ${passRate.toFixed(2)}%`);

        // تحديد حالة النجاح
        // Determine success status
        const isSuccess = report.totalFailed === 0 && report.overallCoverage >= config.coverageThreshold;
        console.log(`\n${isSuccess ? TEST_RUNNER_MESSAGES.SUCCESS.ALL_PASSED : TEST_RUNNER_MESSAGES.WARNING.ISSUES_FOUND}`);

        console.log('='.repeat(60) + '\n');
    }

    /**
     * إنشاء تقرير التغطية
     * Generate coverage report
     */
    public static generateCoverageReport(results: TestResult[]): CoverageReport {
        const totalCoverage = results.length > 0
            ? results.reduce((sum, r) => sum + r.coverage, 0) / results.length
            : 0;

        const coverageByFile = results.map(result => ({
            file: result.testSuite,
            coverage: result.coverage
        }));

        return {
            totalCoverage,
            coverageByFile,
            threshold: TEST_RUNNER_CONSTANTS.DEFAULT_COVERAGE_THRESHOLD,
            passed: totalCoverage >= TEST_RUNNER_CONSTANTS.DEFAULT_COVERAGE_THRESHOLD
        };
    }

    /**
     * إنشاء تقرير الأداء
     * Generate performance report
     */
    public static generatePerformanceReport(results: TestResult[]): PerformanceReport {
        const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);
        const averageDuration = results.length > 0 ? totalDuration / results.length : 0;
        const slowestTest = results.reduce((slowest, current) => 
            current.duration > slowest.duration ? current : slowest, 
            results[0] || { testSuite: '', duration: 0 } as TestResult
        );

        return {
            totalDuration,
            averageDuration,
            slowestTest: slowestTest.testSuite,
            slowestDuration: slowestTest.duration,
            testCount: results.length
        };
    }
}
