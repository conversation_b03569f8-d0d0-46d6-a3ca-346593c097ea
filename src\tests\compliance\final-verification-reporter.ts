/**
 * مولد تقارير التحقق النهائي
 * Final verification report generator
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import * as fs from 'fs';
import * as path from 'path';
import { VerificationResult, VerificationReport, VerificationConfig } from './final-verification-types';

/**
 * فئة مولد تقارير التحقق النهائي
 * Final verification report generator class
 */
export class FinalVerificationReporter {

    /** توليد تقرير نصي / Generate text report */
    public static generateTextReport(result: VerificationResult): string {
        const lines: string[] = [];
        
        lines.push('='.repeat(60));
        lines.push('📊 تقرير التحقق النهائي الشامل');
        lines.push('📊 Comprehensive Final Verification Report');
        lines.push('='.repeat(60));
        lines.push('');
        
        // النتيجة الإجمالية
        lines.push(`🎯 النتيجة الإجمالية: ${result.overallScore}% (${result.grade})`);
        lines.push(`📅 تاريخ التحقق: ${result.timestamp.toLocaleString('ar-SA')}`);
        lines.push('');
        
        // تفاصيل النتائج
        lines.push('📋 تفاصيل النتائج:');
        lines.push('-'.repeat(40));
        lines.push(`🏛️ الالتزام بالدستور: ${result.constitutionCompliance.score}%`);
        lines.push(`   - المشاكل: ${result.constitutionCompliance.issues}`);
        lines.push(`   - المشاكل الحرجة: ${result.constitutionCompliance.criticalIssues}`);
        lines.push('');
        
        lines.push(`🧪 اختبارات الوظائف: ${result.functionalityTests.score}%`);
        lines.push(`   - الاختبارات الناجحة: ${result.functionalityTests.passedTests}/${result.functionalityTests.totalTests}`);
        lines.push('');
        
        lines.push(`🔬 اختبارات الوحدة: ${result.unitTests.coverage}%`);
        lines.push(`   - الاختبارات الناجحة: ${result.unitTests.passedTests}/${result.unitTests.totalTests}`);
        lines.push('');
        
        // التوصيات
        if (result.recommendations.length > 0) {
            lines.push('💡 التوصيات:');
            lines.push('-'.repeat(40));
            result.recommendations.forEach((rec, index) => {
                lines.push(`${index + 1}. ${rec}`);
            });
            lines.push('');
        }
        
        // المشاكل الحرجة
        if (result.criticalIssues.length > 0) {
            lines.push('🚨 المشاكل الحرجة:');
            lines.push('-'.repeat(40));
            result.criticalIssues.forEach((issue, index) => {
                lines.push(`${index + 1}. ${issue}`);
            });
            lines.push('');
        }
        
        lines.push('='.repeat(60));
        
        return lines.join('\n');
    }

    /** توليد تقرير JSON / Generate JSON report */
    public static generateJsonReport(result: VerificationResult): string {
        return JSON.stringify(result, null, 2);
    }

    /** توليد تقرير HTML / Generate HTML report */
    public static generateHtmlReport(result: VerificationResult): string {
        const gradeColor = this.getGradeColor(result.grade);
        const scoreColor = this.getScoreColor(result.overallScore);
        
        return `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير التحقق النهائي الشامل</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .score-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: ${scoreColor};
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px auto;
            font-size: 24px;
            font-weight: bold;
            color: white;
        }
        .grade {
            font-size: 48px;
            font-weight: bold;
            color: ${gradeColor};
            margin: 10px 0;
        }
        .content {
            padding: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .metric:last-child {
            border-bottom: none;
        }
        .metric-value {
            font-weight: bold;
            color: #2c3e50;
        }
        .recommendations, .critical-issues {
            list-style: none;
            padding: 0;
        }
        .recommendations li, .critical-issues li {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            background: white;
            border-left: 4px solid #3498db;
        }
        .critical-issues li {
            border-left-color: #e74c3c;
            background: #fdf2f2;
        }
        .timestamp {
            text-align: center;
            color: #7f8c8d;
            margin-top: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 تقرير التحقق النهائي الشامل</h1>
            <div class="score-circle">${result.overallScore}%</div>
            <div class="grade">${result.grade}</div>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>📋 تفاصيل النتائج</h2>
                <div class="metric">
                    <span>🏛️ الالتزام بالدستور</span>
                    <span class="metric-value">${result.constitutionCompliance.score}%</span>
                </div>
                <div class="metric">
                    <span>🧪 اختبارات الوظائف</span>
                    <span class="metric-value">${result.functionalityTests.score}%</span>
                </div>
                <div class="metric">
                    <span>🔬 تغطية اختبارات الوحدة</span>
                    <span class="metric-value">${result.unitTests.coverage}%</span>
                </div>
            </div>
            
            ${result.recommendations.length > 0 ? `
            <div class="section">
                <h2>💡 التوصيات</h2>
                <ul class="recommendations">
                    ${result.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                </ul>
            </div>
            ` : ''}
            
            ${result.criticalIssues.length > 0 ? `
            <div class="section">
                <h2>🚨 المشاكل الحرجة</h2>
                <ul class="critical-issues">
                    ${result.criticalIssues.map(issue => `<li>${issue}</li>`).join('')}
                </ul>
            </div>
            ` : ''}
            
            <div class="timestamp">
                📅 تم إنشاء التقرير في: ${result.timestamp.toLocaleString('ar-SA')}
            </div>
        </div>
    </div>
</body>
</html>`;
    }

    /** حفظ التقرير / Save report */
    public static async saveReport(
        result: VerificationResult,
        config: VerificationConfig
    ): Promise<void> {
        const outputDir = path.dirname(config.outputPath);
        
        // إنشاء المجلد إذا لم يكن موجوداً
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        
        let content: string;
        let filename: string;
        
        switch (config.outputFormat) {
            case 'json':
                content = this.generateJsonReport(result);
                filename = config.outputPath.replace(/\.[^.]+$/, '.json');
                break;
            case 'html':
                content = this.generateHtmlReport(result);
                filename = config.outputPath.replace(/\.[^.]+$/, '.html');
                break;
            default:
                content = this.generateTextReport(result);
                filename = config.outputPath.replace(/\.[^.]+$/, '.txt');
                break;
        }
        
        fs.writeFileSync(filename, content, 'utf8');
        
        if (config.verbose) {
            console.log(`📄 تم حفظ التقرير في: ${filename}`);
        }
    }

    /** الحصول على لون الدرجة / Get grade color */
    private static getGradeColor(grade: string): string {
        const colors: { [key: string]: string } = {
            'A+': '#27ae60',
            'A': '#2ecc71',
            'B+': '#f39c12',
            'B': '#e67e22',
            'C+': '#e74c3c',
            'C': '#c0392b',
            'D': '#8e44ad',
            'F': '#2c3e50'
        };
        return colors[grade] || '#95a5a6';
    }

    /** الحصول على لون النتيجة / Get score color */
    private static getScoreColor(score: number): string {
        if (score >= 90) return '#27ae60';
        if (score >= 80) return '#f39c12';
        if (score >= 70) return '#e67e22';
        if (score >= 60) return '#e74c3c';
        return '#c0392b';
    }
}
