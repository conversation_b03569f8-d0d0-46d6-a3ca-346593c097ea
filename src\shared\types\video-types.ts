/**
 * أنواع الفيديو
 * Video types
 * 
 * هذا الملف يحتوي على تعريفات أنواع الفيديو
 * This file contains video type definitions
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * جودة الفيديو المدعومة
 * Supported video quality options
 */
export type VideoQuality = '144p' | '240p' | '360p' | '480p' | '720p' | '1080p' | '1440p' | '2160p' | 'auto';

/**
 * حالة الفيديو
 * Video state
 */
export type VideoState = 
    | 'loading'     // تحميل / Loading
    | 'playing'     // تشغيل / Playing
    | 'paused'      // متوقف / Paused
    | 'ended'       // انتهى / Ended
    | 'error'       // خطأ / Error
    | 'buffering';  // تخزين مؤقت / Buffering

/**
 * معلومات الفيديو
 * Video information
 */
export interface VideoInfo {
    /** معرف الفيديو / Video ID */
    id: string;
    /** عنوان الفيديو / Video title */
    title: string;
    /** وصف الفيديو / Video description */
    description?: string;
    /** مدة الفيديو بالثواني / Video duration in seconds */
    duration: number;
    /** جودة الفيديو الحالية / Current video quality */
    currentQuality: VideoQuality;
    /** الجودات المتاحة / Available qualities */
    availableQualities: VideoQuality[];
    /** حالة الفيديو / Video state */
    state: VideoState;
    /** الوقت الحالي / Current time */
    currentTime: number;
    /** مستوى الصوت / Volume level */
    volume: number;
    /** هل الصوت مكتوم / Is muted */
    muted: boolean;
    /** سرعة التشغيل / Playback rate */
    playbackRate: number;
}

/**
 * إعدادات الفيديو
 * Video settings
 */
export interface VideoSettings {
    /** الجودة المفضلة / Preferred quality */
    preferredQuality: VideoQuality;
    /** التشغيل التلقائي / Auto play */
    autoPlay: boolean;
    /** كتم الصوت التلقائي / Auto mute */
    autoMute: boolean;
    /** مستوى الصوت الافتراضي / Default volume */
    defaultVolume: number;
    /** سرعة التشغيل الافتراضية / Default playback rate */
    defaultPlaybackRate: number;
    /** إخفاء أزرار التحكم / Hide controls */
    hideControls: boolean;
    /** ملء الشاشة تلقائياً / Auto fullscreen */
    autoFullscreen: boolean;
}

/**
 * أحداث الفيديو
 * Video events
 */
export interface VideoEvents {
    /** عند بدء التحميل / On load start */
    onLoadStart?: () => void;
    /** عند اكتمال التحميل / On load complete */
    onLoadComplete?: () => void;
    /** عند بدء التشغيل / On play */
    onPlay?: () => void;
    /** عند التوقف / On pause */
    onPause?: () => void;
    /** عند الانتهاء / On end */
    onEnd?: () => void;
    /** عند حدوث خطأ / On error */
    onError?: (error: string) => void;
    /** عند تغيير الوقت / On time update */
    onTimeUpdate?: (currentTime: number) => void;
    /** عند تغيير الجودة / On quality change */
    onQualityChange?: (quality: VideoQuality) => void;
    /** عند تغيير مستوى الصوت / On volume change */
    onVolumeChange?: (volume: number) => void;
    /** عند تغيير سرعة التشغيل / On rate change */
    onRateChange?: (rate: number) => void;
}

/**
 * إحصائيات الفيديو
 * Video statistics
 */
export interface VideoStats {
    /** عدد مرات التشغيل / Play count */
    playCount: number;
    /** إجمالي وقت المشاهدة / Total watch time */
    totalWatchTime: number;
    /** متوسط وقت المشاهدة / Average watch time */
    averageWatchTime: number;
    /** عدد مرات التخطي / Skip count */
    skipCount: number;
    /** عدد مرات إعادة التشغيل / Replay count */
    replayCount: number;
    /** الجودة الأكثر استخداماً / Most used quality */
    mostUsedQuality: VideoQuality;
    /** آخر وقت مشاهدة / Last watch time */
    lastWatchTime: Date;
}

/**
 * خيارات تشغيل الفيديو
 * Video playback options
 */
export interface VideoPlaybackOptions {
    /** بدء التشغيل من وقت محدد / Start from specific time */
    startTime?: number;
    /** انتهاء التشغيل في وقت محدد / End at specific time */
    endTime?: number;
    /** تكرار التشغيل / Loop playback */
    loop?: boolean;
    /** جودة محددة / Specific quality */
    quality?: VideoQuality;
    /** سرعة التشغيل / Playback speed */
    speed?: number;
    /** مستوى الصوت / Volume level */
    volume?: number;
    /** كتم الصوت / Mute audio */
    muted?: boolean;
}

/**
 * نتيجة عملية الفيديو
 * Video operation result
 */
export interface VideoOperationResult {
    /** هل العملية نجحت / Was operation successful */
    success: boolean;
    /** رسالة النتيجة / Result message */
    message?: string;
    /** بيانات إضافية / Additional data */
    data?: any;
    /** وقت العملية / Operation timestamp */
    timestamp: Date;
}

/**
 * خطأ الفيديو
 * Video error
 */
export interface VideoError {
    /** كود الخطأ / Error code */
    code: string;
    /** رسالة الخطأ / Error message */
    message: string;
    /** تفاصيل الخطأ / Error details */
    details?: string;
    /** وقت حدوث الخطأ / Error timestamp */
    timestamp: Date;
    /** هل الخطأ قابل للإصلاح / Is error recoverable */
    recoverable: boolean;
}

/**
 * مدير الفيديو
 * Video manager interface
 */
export interface VideoManager {
    /** تشغيل الفيديو / Play video */
    play(): Promise<VideoOperationResult>;
    /** إيقاف الفيديو / Pause video */
    pause(): Promise<VideoOperationResult>;
    /** إيقاف الفيديو / Stop video */
    stop(): Promise<VideoOperationResult>;
    /** تغيير الجودة / Change quality */
    setQuality(quality: VideoQuality): Promise<VideoOperationResult>;
    /** تغيير مستوى الصوت / Change volume */
    setVolume(volume: number): Promise<VideoOperationResult>;
    /** تغيير سرعة التشغيل / Change playback rate */
    setPlaybackRate(rate: number): Promise<VideoOperationResult>;
    /** الانتقال لوقت محدد / Seek to time */
    seekTo(time: number): Promise<VideoOperationResult>;
    /** الحصول على معلومات الفيديو / Get video info */
    getVideoInfo(): Promise<VideoInfo>;
    /** الحصول على إحصائيات الفيديو / Get video stats */
    getVideoStats(): Promise<VideoStats>;
}
