/**
 * أنماط iframe الأساسية في الوضع المظلم
 * Basic iframe styles for dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeConfig } from './dark-mode-config';

/**
 * فئة أنماط iframe الأساسية
 * Basic iframe styles class
 */
export class DarkModeObserverOperationsAdvancedElementsGenericIframeCoreStyles {

    /** تطبيق أنماط خاصة حسب النوع / Apply type specific styles */
    public static applyTypeSpecificStyles(element: HTMLIFrameElement, type: string, config: DarkModeConfig): void {
        switch (type) {
            case 'youtube':
                DarkModeObserverOperationsAdvancedElementsGenericIframeCoreStylesPlatforms.applyYouTubeStyles(element);
                break;
            case 'vimeo':
                DarkModeObserverOperationsAdvancedElementsGenericIframeCoreStylesPlatforms.applyVimeoStyles(element);
                break;
            case 'facebook':
                DarkModeObserverOperationsAdvancedElementsGenericIframeCoreStylesPlatforms.applyFacebookStyles(element);
                break;
            case 'twitter':
                DarkModeObserverOperationsAdvancedElementsGenericIframeCoreStylesPlatforms.applyTwitterStyles(element);
                break;
            case 'instagram':
                DarkModeObserverOperationsAdvancedElementsGenericIframeCoreStylesPlatforms.applyInstagramStyles(element);
                break;
            case 'maps':
                DarkModeObserverOperationsAdvancedElementsGenericIframeCoreStylesPlatforms.applyMapsStyles(element);
                break;
            case 'video':
                DarkModeObserverOperationsAdvancedElementsGenericIframeCoreStylesPlatforms.applyVideoStyles(element);
                break;
            case 'advertisement':
                DarkModeObserverOperationsAdvancedElementsGenericIframeCoreStylesPlatforms.applyAdvertisementStyles(element);
                break;
            default:
                DarkModeObserverOperationsAdvancedElementsGenericIframeCoreStylesPlatforms.applyGenericStyles(element);
                break;
        }
    }



    /** إضافة عنصر بديل / Add placeholder */
    public static addIframePlaceholder(element: HTMLIFrameElement, config: DarkModeConfig): void {
        const placeholder = document.createElement('div');
        placeholder.className = 'iframe-placeholder';

        const type = DarkModeObserverOperationsAdvancedElementsGenericIframeCoreStylesPlatforms.detectIframeType(element);
        const placeholderText = DarkModeObserverOperationsAdvancedElementsGenericIframeCoreStylesPlatforms.getPlaceholderText(type);

        placeholder.innerHTML = `
            <div style="
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100%;
                background: #2a2a2a;
                border: 2px dashed rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                color: #ffffff;
                font-family: Arial, sans-serif;
                text-align: center;
                padding: 20px;
            ">
                <div style="font-size: 48px; margin-bottom: 16px;">📺</div>
                <div style="font-size: 16px; margin-bottom: 8px;">${placeholderText}</div>
                <div style="font-size: 12px; color: rgba(255, 255, 255, 0.7);">انقر للتحميل</div>
            </div>
        `;

        // إضافة حدث النقر لتحميل iframe
        placeholder.addEventListener('click', () => {
            placeholder.style.display = 'none';
            element.style.display = 'block';
        });

        // إخفاء iframe وإظهار البديل
        element.style.display = 'none';
        element.parentElement?.insertBefore(placeholder, element);
    }

    /** كشف نوع iframe / Detect iframe type */
    public static detectIframeType(element: HTMLIFrameElement): string {
        return DarkModeObserverOperationsAdvancedElementsGenericIframeCoreStylesPlatforms.detectIframeType(element);
    }

    /** الحصول على نص البديل / Get placeholder text */
    public static getPlaceholderText(type: string): string {
        return DarkModeObserverOperationsAdvancedElementsGenericIframeCoreStylesPlatforms.getPlaceholderText(type);
    }
}
