/**
 * عمليات شجرة المجلدات
 * Directory tree operations
 * 
 * هذا الملف يحتوي على عمليات شجرة المجلدات
 * This file contains directory tree operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import * as fs from 'fs';
import * as path from 'path';
import { SimpleVerificationConfig } from './simple-verification-types';
import { SimpleVerificationCoreUtilsDirectoryOperationsSingle } from './simple-verification-core-utils-directory-operations-single';

/**
 * فئة عمليات شجرة المجلدات
 * Directory tree operations class
 */
export class SimpleVerificationCoreUtilsDirectoryOperationsTree {

    /**
     * فحص شجرة المجلدات
     * Check directory tree
     */
    public static checkDirectoryTree(rootPath: string, config: SimpleVerificationConfig): {
        totalDirectories: number;
        validDirectories: number;
        invalidDirectories: number;
        totalIssues: number;
        averageScore: number;
        results: Array<{
            dirPath: string;
            isValid: boolean;
            issues: string[];
            score: number;
            fileCount: number;
            subdirectoryCount: number;
        }>;
    } {
        const results: Array<{
            dirPath: string;
            isValid: boolean;
            issues: string[];
            score: number;
            fileCount: number;
            subdirectoryCount: number;
        }> = [];

        let validDirectories = 0;
        let totalIssues = 0;
        let totalScore = 0;

        try {
            const directories = this.getAllDirectories(rootPath);

            for (const dirPath of directories) {
                const result = SimpleVerificationCoreUtilsDirectoryOperationsSingle.checkSingleDirectory(dirPath, config);
                
                results.push({
                    dirPath,
                    isValid: result.isValid,
                    issues: result.issues,
                    score: result.score,
                    fileCount: result.fileCount,
                    subdirectoryCount: result.subdirectoryCount
                });

                if (result.isValid) {
                    validDirectories++;
                }

                totalIssues += result.issues.length;
                totalScore += result.score;
            }

            return {
                totalDirectories: directories.length,
                validDirectories,
                invalidDirectories: directories.length - validDirectories,
                totalIssues,
                averageScore: directories.length > 0 ? Math.round(totalScore / directories.length) : 0,
                results
            };

        } catch (error) {
            return {
                totalDirectories: 0,
                validDirectories: 0,
                invalidDirectories: 0,
                totalIssues: 1,
                averageScore: 0,
                results: [{
                    dirPath: rootPath,
                    isValid: false,
                    issues: [`Error checking directory tree: ${error}`],
                    score: 0,
                    fileCount: 0,
                    subdirectoryCount: 0
                }]
            };
        }
    }

    /**
     * الحصول على جميع المجلدات
     * Get all directories
     */
    public static getAllDirectories(rootPath: string): string[] {
        const directories: string[] = [];

        try {
            const items = fs.readdirSync(rootPath, { withFileTypes: true });

            for (const item of items) {
                if (item.isDirectory()) {
                    const fullPath = path.join(rootPath, item.name);
                    
                    // تجاهل المجلدات المخفية والمجلدات الخاصة
                    if (!item.name.startsWith('.') && 
                        !['node_modules', 'dist', 'build'].includes(item.name)) {
                        directories.push(fullPath);
                        
                        // إضافة المجلدات الفرعية بشكل تكراري
                        const subdirectories = this.getAllDirectories(fullPath);
                        directories.push(...subdirectories);
                    }
                }
            }
        } catch (error) {
            // تجاهل الأخطاء في قراءة المجلدات
        }

        return directories;
    }

    /**
     * إنشاء خريطة شجرة المجلدات
     * Generate directory tree map
     */
    public static generateDirectoryTreeMap(rootPath: string): {
        tree: any;
        statistics: {
            totalDirectories: number;
            maxDepth: number;
            averageFilesPerDirectory: number;
            largestDirectory: string;
            largestDirectoryFileCount: number;
        };
    } {
        try {
            const tree = this.buildTreeStructure(rootPath, 0);
            const statistics = this.calculateTreeStatistics(rootPath);

            return {
                tree,
                statistics
            };

        } catch (error) {
            return {
                tree: { error: `Failed to generate tree: ${error}` },
                statistics: {
                    totalDirectories: 0,
                    maxDepth: 0,
                    averageFilesPerDirectory: 0,
                    largestDirectory: '',
                    largestDirectoryFileCount: 0
                }
            };
        }
    }

    /**
     * بناء بنية الشجرة
     * Build tree structure
     */
    private static buildTreeStructure(dirPath: string, depth: number): any {
        if (depth > 5) { // تجنب التكرار العميق
            return { name: path.basename(dirPath), type: 'directory', truncated: true };
        }

        try {
            const items = fs.readdirSync(dirPath, { withFileTypes: true });
            const node: any = {
                name: path.basename(dirPath),
                type: 'directory',
                children: []
            };

            for (const item of items) {
                if (item.name.startsWith('.') || 
                    ['node_modules', 'dist', 'build'].includes(item.name)) {
                    continue;
                }

                const fullPath = path.join(dirPath, item.name);

                if (item.isDirectory()) {
                    const childNode = this.buildTreeStructure(fullPath, depth + 1);
                    node.children.push(childNode);
                } else if (item.isFile()) {
                    node.children.push({
                        name: item.name,
                        type: 'file'
                    });
                }
            }

            return node;

        } catch (error) {
            return {
                name: path.basename(dirPath),
                type: 'directory',
                error: `Cannot read directory: ${error}`
            };
        }
    }

    /**
     * حساب إحصائيات الشجرة
     * Calculate tree statistics
     */
    private static calculateTreeStatistics(rootPath: string): {
        totalDirectories: number;
        maxDepth: number;
        averageFilesPerDirectory: number;
        largestDirectory: string;
        largestDirectoryFileCount: number;
    } {
        let totalDirectories = 0;
        let maxDepth = 0;
        let totalFiles = 0;
        let largestDirectory = '';
        let largestDirectoryFileCount = 0;

        const calculateRecursive = (dirPath: string, currentDepth: number) => {
            try {
                totalDirectories++;
                maxDepth = Math.max(maxDepth, currentDepth);

                const items = fs.readdirSync(dirPath, { withFileTypes: true });
                let fileCount = 0;

                for (const item of items) {
                    if (item.name.startsWith('.') || 
                        ['node_modules', 'dist', 'build'].includes(item.name)) {
                        continue;
                    }

                    const fullPath = path.join(dirPath, item.name);

                    if (item.isDirectory()) {
                        calculateRecursive(fullPath, currentDepth + 1);
                    } else if (item.isFile()) {
                        fileCount++;
                        totalFiles++;
                    }
                }

                if (fileCount > largestDirectoryFileCount) {
                    largestDirectoryFileCount = fileCount;
                    largestDirectory = dirPath;
                }

            } catch (error) {
                // تجاهل الأخطاء في قراءة المجلدات
            }
        };

        calculateRecursive(rootPath, 0);

        return {
            totalDirectories,
            maxDepth,
            averageFilesPerDirectory: totalDirectories > 0 ? Math.round(totalFiles / totalDirectories) : 0,
            largestDirectory,
            largestDirectoryFileCount
        };
    }
}
