/**
 * فاحص الوظائف الشامل
 * Comprehensive functionality tester
 *
 * هذا الملف يحتوي على أداة فحص شاملة لجميع وظائف التطبيق
 * This file contains comprehensive tool for testing all application functionality
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// إعادة تصدير الفاحصين المتخصصين
// Re-export specialized testers
export * from './dark-mode-functionality-tester';
export * from './security-functionality-tester';
export * from './video-quality-functionality-tester';

import { ValidationResult } from '../../shared/types/application-types';
import { DarkModeFunctionalityTester } from './dark-mode-functionality-tester';
import { SecurityFunctionalityTester } from './security-functionality-tester';
import { VideoQualityFunctionalityTester } from './video-quality-functionality-tester';

interface FunctionalityReport {
    readonly totalTests: number;
    readonly passedTests: number;
    readonly failedTests: number;
    readonly categories: Record<string, { passed: number; failed: number; total: number }>;
    readonly results: Array<{
        readonly name: string;
        readonly category: string;
        readonly passed: boolean;
        readonly duration: number;
        readonly error?: string;
    }>;
    readonly overallScore: number;
    readonly timestamp: Date;
}

/**
 * فاحص الوظائف الشامل
 * Comprehensive functionality tester
 */
export class FunctionalityTester {
    private readonly videoQualityTester: VideoQualityFunctionalityTester = new VideoQualityFunctionalityTester();
    private readonly darkModeTester: DarkModeFunctionalityTester = new DarkModeFunctionalityTester();
    private readonly securityTester: SecurityFunctionalityTester = new SecurityFunctionalityTester();
    /**
     * تهيئة جميع المكونات
     * Initialize all components
     */
    public async initializeComponents(): Promise<void> {
        await Promise.all([
            this.videoQualityTester.initializeComponents(),
            this.darkModeTester.initializeComponents(),
            this.securityTester.initializeComponents()
        ]);
    }

    /**
     * تشغيل جميع الاختبارات
     * Run all tests
     */
    public async runAllTests(): Promise<FunctionalityReport> {
        console.log('🧪 بدء فحص الوظائف الشامل / Starting comprehensive functionality testing...\n');

        await this.initializeComponents();

        const results: FunctionalityReport['results'] = [];
        const categories: Record<string, { passed: number; failed: number; total: number }> = {};

        // تشغيل اختبارات جودة الفيديو
        await this.runVideoQualityTests(results, categories);

        // تشغيل اختبارات الوضع المظلم
        await this.runDarkModeTests(results, categories);

        // تشغيل اختبارات الأمان
        await this.runSecurityTests(results, categories);

        await this.cleanupComponents();

        return this.generateReport(results, categories);
    }

    /**
     * تشغيل اختبارات جودة الفيديو
     * Run video quality tests
     */
    private async runVideoQualityTests(
        results: FunctionalityReport['results'],
        categories: Record<string, { passed: number; failed: number; total: number }>
    ): Promise<void> {
        const tests = [
            { name: 'Video Quality Control', test: () => this.videoQualityTester.testVideoQualityControl() },
            { name: 'Quality Button Management', test: () => this.videoQualityTester.testQualityButtonManagement() },
            { name: 'Video Quality Monitoring', test: () => this.videoQualityTester.testVideoQualityMonitoring() },
            { name: 'Video Quality Settings', test: () => this.videoQualityTester.testVideoQualitySettings() }
        ];

        for (const test of tests) {
            await this.runSingleTest(test.name, 'Video Management', test.test, results, categories);
        }
    }

    /**
     * تشغيل اختبارات الوضع المظلم
     * Run dark mode tests
     */
    private async runDarkModeTests(
        results: FunctionalityReport['results'],
        categories: Record<string, { passed: number; failed: number; total: number }>
    ): Promise<void> {
        const tests = [
            { name: 'Dark Mode Toggle', test: () => this.darkModeTester.testDarkModeToggle() },
            { name: 'Dark Mode Persistence', test: () => this.darkModeTester.testDarkModePersistence() },
            { name: 'Dark Mode Styles', test: () => this.darkModeTester.testDarkModeStyles() },
            { name: 'Dark Mode Monitoring', test: () => this.darkModeTester.testDarkModeMonitoring() },
            { name: 'Dark Mode Settings', test: () => this.darkModeTester.testDarkModeSettings() }
        ];

        for (const test of tests) {
            await this.runSingleTest(test.name, 'UI Management', test.test, results, categories);
        }
    }

    /**
     * تشغيل اختبارات الأمان
     * Run security tests
     */
    private async runSecurityTests(
        results: FunctionalityReport['results'],
        categories: Record<string, { passed: number; failed: number; total: number }>
    ): Promise<void> {
        const tests = [
            { name: 'Ad Blocking', test: () => this.securityTester.testAdBlocking() },
            { name: 'Content Sanitization', test: () => this.securityTester.testContentSanitization() },
            { name: 'Request Validation', test: () => this.securityTester.testRequestValidation() },
            { name: 'Threat Detection', test: () => this.securityTester.testThreatDetection() },
            { name: 'Security Settings', test: () => this.securityTester.testSecuritySettings() }
        ];

        for (const test of tests) {
            await this.runSingleTest(test.name, 'Security', test.test, results, categories);
        }
    }

    /**
     * تشغيل اختبار واحد
     * Run single test
     */
    private async runSingleTest(
        name: string,
        category: string,
        testFunction: () => Promise<ValidationResult>,
        results: FunctionalityReport['results'],
        categories: Record<string, { passed: number; failed: number; total: number }>
    ): Promise<void> {
        console.log(`🔍 تشغيل اختبار: ${name} / Running test: ${name}`);

        const startTime = Date.now();
        let passed = false;
        let error: string | undefined;

        try {
            const result = await testFunction();
            passed = result.isValid;
            if (!passed) {
                error = result.errors.map(e => e.message).join(', ');
            }
        } catch (err: any) {
            passed = false;
            error = err.message;
        }

        const endTime = Date.now();
        const duration = endTime - startTime;

        results.push({
            name,
            category,
            passed,
            duration,
            error
        });

        // تحديث إحصائيات الفئات
        if (!categories[category]) {
            categories[category] = { passed: 0, failed: 0, total: 0 };
        }
        categories[category].total++;
        if (passed) {
            categories[category].passed++;
            console.log(`✅ نجح / Passed: ${name} (${duration}ms)`);
        } else {
            categories[category].failed++;
            console.log(`❌ فشل / Failed: ${name} - ${error} (${duration}ms)`);
        }
    }

    /**
     * تنظيف المكونات
     * Cleanup components
     */
    private async cleanupComponents(): Promise<void> {
        await Promise.all([
            this.videoQualityTester.cleanup(),
            this.darkModeTester.cleanup(),
            this.securityTester.cleanup()
        ]);
    }

    /**
     * إنشاء التقرير
     * Generate report
     */
    private generateReport(
        results: FunctionalityReport['results'],
        categories: Record<string, { passed: number; failed: number; total: number }>
    ): FunctionalityReport {
        const totalTests = results.length;
        const passedTests = results.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;
        const overallScore = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;

        return {
            totalTests,
            passedTests,
            failedTests,
            categories,
            results,
            overallScore: Math.round(overallScore * 100) / 100,
            timestamp: new Date()
        };
    }
}