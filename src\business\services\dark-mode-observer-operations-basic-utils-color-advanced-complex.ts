/**
 * الوظائف المعقدة المتقدمة لأدوات الألوان
 * Complex advanced color utility functions
 *
 * هذا الملف يجمع جميع الوظائف المعقدة من الملفات المتخصصة
 * This file aggregates all complex functions from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// استيراد الفئات المتخصصة / Import specialized classes
import { DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysis } from './dark-mode-observer-operations-basic-utils-color-advanced-complex-analysis';
import { DarkModeObserverOperationsBasicUtilsColorAdvancedComplexSchemes } from './dark-mode-observer-operations-basic-utils-color-advanced-complex-schemes';

/**
 * فئة الوظائف المعقدة المتقدمة لأدوات الألوان
 * Complex advanced color utility functions class
 */
export class DarkModeObserverOperationsBasicUtilsColorAdvancedComplex {

    /** الحصول على ألوان متناسقة / Get harmonious colors */
    public static getHarmoniousColors(baseColor: string, count: number = 5): string[] {
        return DarkModeObserverOperationsBasicUtilsColorAdvancedComplexSchemes.getHarmoniousColors(baseColor, count);
    }

    /** الحصول على ألوان متدرجة / Get gradient colors */
    public static getGradientColors(startColor: string, endColor: string, steps: number = 10): string[] {
        return DarkModeObserverOperationsBasicUtilsColorAdvancedComplexSchemes.getGradientColors(startColor, endColor, steps);
    }

    /** الحصول على ألوان مكملة / Get complementary color scheme */
    public static getComplementaryScheme(baseColor: string): { primary: string; secondary: string; accent: string } {
        return DarkModeObserverOperationsBasicUtilsColorAdvancedComplexSchemes.getComplementaryScheme(baseColor);
    }

    /** الحصول على ألوان متشابهة / Get analogous colors */
    public static getAnalogousColors(baseColor: string, count: number = 3): string[] {
        return DarkModeObserverOperationsBasicUtilsColorAdvancedComplexSchemes.getAnalogousColors(baseColor, count);
    }

    /** إنشاء لوحة ألوان متوازنة / Create balanced color palette */
    public static createBalancedPalette(baseColor: string): {
        primary: string;
        secondary: string;
        accent: string;
        neutral: string;
        light: string;
        dark: string;
    } {
        return DarkModeObserverOperationsBasicUtilsColorAdvancedComplexSchemes.createBalancedPalette(baseColor);
    }

    /** تحليل اللون وإرجاع معلومات مفصلة / Analyze color and return detailed information */
    public static analyzeColor(color: string): {
        rgb: { r: number; g: number; b: number } | null;
        hsl: { h: number; s: number; l: number } | null;
        hex: string;
        isLight: boolean;
        isDark: boolean;
        luminance: number;
        temperature: 'warm' | 'cool' | 'neutral';
    } {
        return DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysis.analyzeColor(color);
    }


}
