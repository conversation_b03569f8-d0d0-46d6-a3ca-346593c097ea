/**
 * تكوين جودة الفيديو
 * Video quality configuration
 * 
 * هذا الملف يحتوي على تكوينات وثوابت جودة الفيديو
 * This file contains video quality configurations and constants
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQuality } from '@shared/types';

/**
 * معلومات جودة الفيديو
 * Video quality information
 */
export interface QualityInfo {
    readonly ytFormat: string;
    readonly height: number;
    readonly bitrate?: number;
    readonly fps?: number;
}

/**
 * تكوين مدير جودة الفيديو
 * Video quality manager configuration
 */
export interface VideoQualityConfig {
    readonly defaultQuality: VideoQuality;
    readonly autoDetectQuality: boolean;
    readonly hideQualitySelector: boolean;
    readonly enableQualityMonitoring: boolean;
    readonly retryAttempts: number;
    readonly retryDelay: number;
    readonly observeChanges: boolean;
    readonly forceQuality: boolean;
}

/**
 * الإعدادات الافتراضية لجودة الفيديو
 * Default video quality settings
 */
export const DEFAULT_VIDEO_QUALITY_CONFIG: VideoQualityConfig = {
    defaultQuality: '480p',
    autoDetectQuality: false,
    hideQualitySelector: false,
    enableQualityMonitoring: true,
    retryAttempts: 3,
    retryDelay: 1000,
    observeChanges: true,
    forceQuality: true
};

/**
 * خريطة جودة الفيديو
 * Video quality mapping
 */
export const VIDEO_QUALITY_MAP: Record<VideoQuality, QualityInfo> = {
    '144p': { 
        ytFormat: 'tiny', 
        height: 144, 
        bitrate: 100,
        fps: 30 
    },
    '240p': { 
        ytFormat: 'small', 
        height: 240, 
        bitrate: 300,
        fps: 30 
    },
    '360p': { 
        ytFormat: 'medium', 
        height: 360, 
        bitrate: 500,
        fps: 30 
    },
    '480p': { 
        ytFormat: 'large', 
        height: 480, 
        bitrate: 1000,
        fps: 30 
    },
    '720p': { 
        ytFormat: 'hd720', 
        height: 720, 
        bitrate: 2500,
        fps: 30 
    },
    '1080p': { 
        ytFormat: 'hd1080', 
        height: 1080, 
        bitrate: 5000,
        fps: 30 
    },
    'auto': { 
        ytFormat: 'auto', 
        height: 0, 
        bitrate: 0,
        fps: 0 
    }
};

/**
 * محددات جودة الفيديو
 * Video quality selectors
 */
export const VIDEO_QUALITY_SELECTORS = {
    // محددات المشغل
    PLAYER_SELECTORS: [
        '.html5-video-player',
        '.ytp-player',
        '#movie_player',
        'video'
    ],
    
    // محددات قائمة الجودة
    QUALITY_MENU_SELECTORS: [
        '.ytp-settings-menu',
        '.ytp-quality-menu',
        '.ytp-menuitem[role="menuitemradio"]',
        '.ytp-menuitem-label'
    ],
    
    // محددات أزرار الجودة
    QUALITY_BUTTON_SELECTORS: [
        '.ytp-settings-button',
        '.ytp-button[data-tooltip-target-id="ytp-settings-button"]',
        'button[aria-label*="Settings"]',
        'button[aria-label*="الإعدادات"]'
    ],
    
    // محددات عناصر الجودة
    QUALITY_ITEM_SELECTORS: [
        '.ytp-menuitem[aria-checked="true"]',
        '.ytp-menuitem-label',
        '.ytp-quality-menu .ytp-menuitem'
    ]
} as const;

/**
 * رسائل جودة الفيديو
 * Video quality messages
 */
export const VIDEO_QUALITY_MESSAGES = {
    QUALITY_SET: 'تم تعيين جودة الفيديو / Video quality set',
    QUALITY_CHANGED: 'تم تغيير جودة الفيديو / Video quality changed',
    QUALITY_HIDDEN: 'تم إخفاء محدد الجودة / Quality selector hidden',
    QUALITY_SHOWN: 'تم إظهار محدد الجودة / Quality selector shown',
    AUTO_QUALITY_DETECTED: 'تم اكتشاف الجودة تلقائياً / Auto quality detected',
    ERROR_SET_QUALITY: 'خطأ في تعيين الجودة / Error setting quality',
    ERROR_HIDE_QUALITY: 'خطأ في إخفاء الجودة / Error hiding quality',
    ERROR_DETECT_QUALITY: 'خطأ في اكتشاف الجودة / Error detecting quality',
    PLAYER_NOT_FOUND: 'لم يتم العثور على المشغل / Player not found',
    QUALITY_MENU_NOT_FOUND: 'لم يتم العثور على قائمة الجودة / Quality menu not found',
    RETRY_ATTEMPT: 'محاولة إعادة / Retry attempt',
    MAX_RETRIES_REACHED: 'تم الوصول للحد الأقصى من المحاولات / Max retries reached'
} as const;

/**
 * ثوابت جودة الفيديو
 * Video quality constants
 */
export const VIDEO_QUALITY_CONSTANTS = {
    DEFAULT_RETRY_DELAY: 1000,
    MAX_RETRY_ATTEMPTS: 3,
    QUALITY_CHECK_INTERVAL: 2000,
    PLAYER_READY_TIMEOUT: 10000,
    MENU_OPEN_DELAY: 500,
    QUALITY_APPLY_DELAY: 1000,
    OBSERVER_DEBOUNCE: 300,
    
    // أولويات الجودة للاكتشاف التلقائي
    QUALITY_PRIORITIES: [
        '1080p',
        '720p', 
        '480p',
        '360p',
        '240p',
        '144p'
    ] as VideoQuality[],
    
    // خصائص المراقبة
    OBSERVER_CONFIG: {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'aria-checked', 'aria-selected']
    },
    
    // أنماط الجودة المدعومة
    SUPPORTED_QUALITIES: [
        '144p',
        '240p', 
        '360p',
        '480p',
        '720p',
        '1080p',
        'auto'
    ] as VideoQuality[]
} as const;

/**
 * أنماط CSS لإخفاء محدد الجودة
 * CSS styles for hiding quality selector
 */
export const QUALITY_HIDING_STYLES = `
    /* إخفاء محدد الجودة */
    .ytp-settings-button,
    .ytp-button[data-tooltip-target-id="ytp-settings-button"],
    button[aria-label*="Settings"],
    button[aria-label*="الإعدادات"] {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        pointer-events: none !important;
    }
    
    /* إخفاء قائمة الجودة */
    .ytp-settings-menu,
    .ytp-quality-menu {
        display: none !important;
        visibility: hidden !important;
    }
    
    /* إخفاء عناصر الجودة */
    .ytp-menuitem[role="menuitemradio"] {
        display: none !important;
    }
` as const;

/**
 * معرفات CSS
 * CSS identifiers
 */
export const QUALITY_CSS_IDS = {
    HIDING_STYLES: 'youtube-quality-hiding-styles',
    CUSTOM_STYLES: 'youtube-quality-custom-styles'
} as const;

/**
 * أحداث جودة الفيديو
 * Video quality events
 */
export const VIDEO_QUALITY_EVENTS = {
    QUALITY_CHANGED: 'qualityChanged',
    QUALITY_SET: 'qualitySet',
    QUALITY_HIDDEN: 'qualityHidden',
    QUALITY_SHOWN: 'qualityShown',
    AUTO_QUALITY_DETECTED: 'autoQualityDetected',
    ERROR_OCCURRED: 'errorOccurred'
} as const;

/**
 * حالات جودة الفيديو
 * Video quality states
 */
export enum VideoQualityState {
    IDLE = 'idle',
    SETTING = 'setting',
    DETECTING = 'detecting',
    HIDING = 'hiding',
    SHOWING = 'showing',
    ERROR = 'error'
}

/**
 * أنواع أخطاء جودة الفيديو
 * Video quality error types
 */
export enum VideoQualityErrorType {
    PLAYER_NOT_FOUND = 'PLAYER_NOT_FOUND',
    QUALITY_MENU_NOT_FOUND = 'QUALITY_MENU_NOT_FOUND',
    QUALITY_NOT_SUPPORTED = 'QUALITY_NOT_SUPPORTED',
    TIMEOUT_ERROR = 'TIMEOUT_ERROR',
    NETWORK_ERROR = 'NETWORK_ERROR',
    UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * تقرير خطأ جودة الفيديو
 * Video quality error report
 */
export interface VideoQualityErrorReport {
    readonly errorType: VideoQualityErrorType;
    readonly message: string;
    readonly timestamp: Date;
    readonly quality?: VideoQuality;
    readonly retryCount?: number;
    readonly details?: Record<string, unknown>;
}
