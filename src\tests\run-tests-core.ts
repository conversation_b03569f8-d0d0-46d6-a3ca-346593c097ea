/**
 * عمليات مشغل الاختبارات الأساسية - ملف التفويض الرئيسي
 * Test runner core operations - Main delegation file
 *
 * هذا الملف يفوض جميع العمليات الأساسية للملفات المتخصصة
 * This file delegates all core operations to specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { TestRunnerCoreExecution } from './run-tests-core-execution';
import { TestRunnerCoreReporting } from './run-tests-core-reporting';
import {
    DEFAULT_EXECUTION_OPTIONS,
    DEFAULT_TEST_RUNNER_CONFIG,
    TEST_RUNNER_MESSAGES,
    TestExecutionOptions,
    TestReport,
    TestResult,
    TestRunnerConfig,
    TestSuiteResult
} from './run-tests-types';

/**
 * فئة عمليات مشغل الاختبارات الأساسية - التفويض
 * Test runner core operations class - Delegation
 */
export class TestRunnerCore {

    /**
     * تشغيل جميع الاختبارات - تفويض للوحدة المتخصصة
     * Run all tests - Delegate to specialized module
     */
    public static async runAllTests(
        config: TestRunnerConfig = DEFAULT_TEST_RUNNER_CONFIG,
        options: TestExecutionOptions = DEFAULT_EXECUTION_OPTIONS
    ): Promise<TestReport> {
        try {
            console.log(TEST_RUNNER_MESSAGES.INFO.STARTING_TESTS);

            const startTime = Date.now();
            const results: TestResult[] = [];

            // تشغيل اختبارات الوحدة - تفويض للوحدة المتخصصة
            // Run unit tests - Delegate to specialized module
            const unitTestResult = await this.runUnitTests(config, options);
            results.push(...unitTestResult.testResults);

            // تشغيل اختبارات التكامل - تفويض للوحدة المتخصصة
            // Run integration tests - Delegate to specialized module
            const integrationTestResult = await this.runIntegrationTests(config, options);
            results.push(...integrationTestResult.testResults);

            // تشغيل اختبارات النهاية إلى النهاية - تفويض للوحدة المتخصصة
            // Run end-to-end tests - Delegate to specialized module
            const e2eTestResult = await this.runE2ETests(config, options);
            results.push(...e2eTestResult.testResults);

            const endTime = Date.now();
            const duration = endTime - startTime;

            // إنشاء التقرير - تفويض للوحدة المتخصصة
            // Generate report - Delegate to specialized module
            const report = TestRunnerCoreReporting.generateReport(results, duration, config);

            // حفظ التقرير - تفويض للوحدة المتخصصة
            // Save report - Delegate to specialized module
            await TestRunnerCoreReporting.saveReport(report, config);

            // طباعة التقرير - تفويض للوحدة المتخصصة
            // Print report - Delegate to specialized module
            TestRunnerCoreReporting.printReport(report, config);

            return report;
        } catch (error) {
            throw new Error(`${TEST_RUNNER_MESSAGES.ERROR.EXECUTION_FAILED}: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
        }
    }

    /**
     * تشغيل اختبارات الوحدة - تفويض للوحدة المتخصصة
     * Run unit tests - Delegate to specialized module
     */
    public static async runUnitTests(
        config: TestRunnerConfig = DEFAULT_TEST_RUNNER_CONFIG,
        options: TestExecutionOptions = DEFAULT_EXECUTION_OPTIONS
    ): Promise<TestSuiteResult> {
        // تفويض العملية للوحدة المتخصصة
        // Delegate operation to specialized module
        return await TestRunnerCoreExecution.executeTestSuite('unit', config, options);
    }

    /**
     * تشغيل اختبارات التكامل - تفويض للوحدة المتخصصة
     * Run integration tests - Delegate to specialized module
     */
    public static async runIntegrationTests(
        config: TestRunnerConfig = DEFAULT_TEST_RUNNER_CONFIG,
        options: TestExecutionOptions = DEFAULT_EXECUTION_OPTIONS
    ): Promise<TestSuiteResult> {
        // تفويض العملية للوحدة المتخصصة
        // Delegate operation to specialized module
        return await TestRunnerCoreExecution.executeTestSuite('integration', config, options);
    }

    /**
     * تشغيل اختبارات النهاية إلى النهاية - تفويض للوحدة المتخصصة
     * Run end-to-end tests - Delegate to specialized module
     */
    public static async runE2ETests(
        config: TestRunnerConfig = DEFAULT_TEST_RUNNER_CONFIG,
        options: TestExecutionOptions = DEFAULT_EXECUTION_OPTIONS
    ): Promise<TestSuiteResult> {
        // تفويض العملية للوحدة المتخصصة
        // Delegate operation to specialized module
        return await TestRunnerCoreExecution.executeTestSuite('e2e', config, options);
    }

    /**
     * التحقق من صحة التكوين - تفويض للوحدة المتخصصة
     * Validate configuration - Delegate to specialized module
     */
    public static validateConfig(config: TestRunnerConfig): boolean {
        // تفويض العملية للوحدة المتخصصة
        // Delegate operation to specialized module
        return TestRunnerCoreExecution.validateConfig(config);
    }

    /**
     * التحقق من صحة خيارات التنفيذ - تفويض للوحدة المتخصصة
     * Validate execution options - Delegate to specialized module
     */
    public static validateOptions(options: TestExecutionOptions): boolean {
        // تفويض العملية للوحدة المتخصصة
        // Delegate operation to specialized module
        return TestRunnerCoreExecution.validateOptions(options);
    }

    /**
     * إنشاء تقرير التغطية - تفويض للوحدة المتخصصة
     * Generate coverage report - Delegate to specialized module
     */
    public static generateCoverageReport(results: TestResult[]) {
        // تفويض العملية للوحدة المتخصصة
        // Delegate operation to specialized module
        return TestRunnerCoreReporting.generateCoverageReport(results);
    }

    /**
     * إنشاء تقرير الأداء - تفويض للوحدة المتخصصة
     * Generate performance report - Delegate to specialized module
     */
    public static generatePerformanceReport(results: TestResult[]) {
        // تفويض العملية للوحدة المتخصصة
        // Delegate operation to specialized module
        return TestRunnerCoreReporting.generatePerformanceReport(results);
    }
}