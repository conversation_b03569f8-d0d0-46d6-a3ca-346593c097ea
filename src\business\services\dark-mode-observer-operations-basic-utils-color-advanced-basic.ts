/**
 * الوظائف الأساسية المتقدمة لأدوات الألوان
 * Basic advanced color utility functions
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeObserverOperationsBasicUtilsColorCore } from './dark-mode-observer-operations-basic-utils-color-core';

/**
 * فئة الوظائف الأساسية المتقدمة لأدوات الألوان
 * Basic advanced color utility functions class
 */
export class DarkModeObserverOperationsBasicUtilsColorAdvancedBasic {

    /** تفتيح اللون / Lighten color */
    public static lightenColor(color: string, amount: number): string {
        const rgb = DarkModeObserverOperationsBasicUtilsColorCore.parseColorToRgb(color);
        if (!rgb) {
            return color;
        }

        const lighten = (value: number): number => {
            return Math.min(255, Math.round(value + (255 - value) * amount));
        };

        return DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(
            lighten(rgb.r),
            lighten(rgb.g),
            lighten(rgb.b)
        );
    }

    /** تغميق اللون / Darken color */
    public static darkenColor(color: string, amount: number): string {
        const rgb = DarkModeObserverOperationsBasicUtilsColorCore.parseColorToRgb(color);
        if (!rgb) {
            return color;
        }

        const darken = (value: number): number => {
            return Math.max(0, Math.round(value * (1 - amount)));
        };

        return DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(
            darken(rgb.r),
            darken(rgb.g),
            darken(rgb.b)
        );
    }

    /** مزج لونين / Blend two colors */
    public static blendColors(color1: string, color2: string, ratio: number): string {
        const rgb1 = DarkModeObserverOperationsBasicUtilsColorCore.parseColorToRgb(color1);
        const rgb2 = DarkModeObserverOperationsBasicUtilsColorCore.parseColorToRgb(color2);

        if (!rgb1 || !rgb2) {
            return color1;
        }

        const blend = (value1: number, value2: number): number => {
            return Math.round(value1 * (1 - ratio) + value2 * ratio);
        };

        return DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(
            blend(rgb1.r, rgb2.r),
            blend(rgb1.g, rgb2.g),
            blend(rgb1.b, rgb2.b)
        );
    }

    /** الحصول على لون مكمل / Get complementary color */
    public static getComplementaryColor(color: string): string {
        const rgb = DarkModeObserverOperationsBasicUtilsColorCore.parseColorToRgb(color);
        if (!rgb) {
            return color;
        }

        return DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(
            255 - rgb.r,
            255 - rgb.g,
            255 - rgb.b
        );
    }

    /** تحويل اللون إلى تدرج رمادي / Convert color to grayscale */
    public static toGrayscale(color: string): string {
        const rgb = DarkModeObserverOperationsBasicUtilsColorCore.parseColorToRgb(color);
        if (!rgb) {
            return color;
        }

        // استخدام معادلة luminance للحصول على تدرج رمادي دقيق
        const gray = Math.round(rgb.r * 0.299 + rgb.g * 0.587 + rgb.b * 0.114);
        
        return DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(gray, gray, gray);
    }

    /** تعديل تشبع اللون / Adjust color saturation */
    public static adjustSaturation(color: string, amount: number): string {
        const rgb = DarkModeObserverOperationsBasicUtilsColorCore.parseColorToRgb(color);
        if (!rgb) {
            return color;
        }

        const hsl = this.rgbToHsl(rgb.r, rgb.g, rgb.b);
        hsl.s = Math.max(0, Math.min(100, hsl.s + amount));
        
        const newRgb = this.hslToRgb(hsl.h, hsl.s, hsl.l);
        return DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(newRgb.r, newRgb.g, newRgb.b);
    }

    /** تعديل سطوع اللون / Adjust color brightness */
    public static adjustBrightness(color: string, amount: number): string {
        const rgb = DarkModeObserverOperationsBasicUtilsColorCore.parseColorToRgb(color);
        if (!rgb) {
            return color;
        }

        const hsl = this.rgbToHsl(rgb.r, rgb.g, rgb.b);
        hsl.l = Math.max(0, Math.min(100, hsl.l + amount));
        
        const newRgb = this.hslToRgb(hsl.h, hsl.s, hsl.l);
        return DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(newRgb.r, newRgb.g, newRgb.b);
    }

    /** تحويل اللون إلى HSL / Convert color to HSL */
    public static rgbToHsl(r: number, g: number, b: number): { h: number; s: number; l: number } {
        r /= 255;
        g /= 255;
        b /= 255;

        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h: number, s: number;
        const l = (max + min) / 2;

        if (max === min) {
            h = s = 0; // achromatic
        } else {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

            switch (max) {
                case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                case g: h = (b - r) / d + 2; break;
                case b: h = (r - g) / d + 4; break;
                default: h = 0;
            }

            h /= 6;
        }

        return { h: h * 360, s: s * 100, l: l * 100 };
    }

    /** تحويل HSL إلى RGB / Convert HSL to RGB */
    public static hslToRgb(h: number, s: number, l: number): { r: number; g: number; b: number } {
        h /= 360;
        s /= 100;
        l /= 100;

        const hue2rgb = (p: number, q: number, t: number): number => {
            if (t < 0) t += 1;
            if (t > 1) t -= 1;
            if (t < 1/6) return p + (q - p) * 6 * t;
            if (t < 1/2) return q;
            if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
            return p;
        };

        let r: number, g: number, b: number;

        if (s === 0) {
            r = g = b = l; // achromatic
        } else {
            const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
            const p = 2 * l - q;
            r = hue2rgb(p, q, h + 1/3);
            g = hue2rgb(p, q, h);
            b = hue2rgb(p, q, h - 1/3);
        }

        return {
            r: Math.round(r * 255),
            g: Math.round(g * 255),
            b: Math.round(b * 255)
        };
    }
}
