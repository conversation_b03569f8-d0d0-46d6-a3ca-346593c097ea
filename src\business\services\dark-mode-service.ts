/**
 * خدمة الوضع المظلم الرئيسية
 * Main dark mode service
 * 
 * هذا الملف يحتوي على الخدمة الرئيسية لإدارة الوضع المظلم
 * This file contains the main service for managing dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ValidationResult } from '@shared/types';
import {
    DARK_MODE_MESSAGES,
    DarkModeConfig,
    DEFAULT_DARK_MODE_CONFIG
} from './dark-mode-config';
import { DarkModeObserver } from './dark-mode-observer';
import { DarkModeStylesApplicator } from './dark-mode-styles';

/**
 * فئة خدمة الوضع المظلم الرئيسية
 * Main dark mode service class
 */
export class DarkModeService {
    private readonly config: DarkModeConfig;
    private readonly stylesApplicator: DarkModeStylesApplicator;
    private readonly observer: DarkModeObserver;
    private isEnabled: boolean = false;

    /**
     * منشئ خدمة الوضع المظلم
     * Dark mode service constructor
     *
     * @param config - تكوين الوضع المظلم (اختياري)
     */
    constructor(config?: Partial<DarkModeConfig>) {
        this.config = { ...DEFAULT_DARK_MODE_CONFIG, ...config };
        this.stylesApplicator = new DarkModeStylesApplicator(this.config);
        this.observer = new DarkModeObserver(this.config, this.stylesApplicator);
    }

    /**
     * تفعيل الوضع المظلم
     * Enable dark mode
     * 
     * @returns Promise<ValidationResult> - نتيجة التفعيل
     * 
     * @example
     * ```typescript
     * const result = await darkModeService.enable();
     * if (result.isValid) {
     *     console.log('تم تفعيل الوضع المظلم');
     * }
     * ```
     */
    public async enable(): Promise<ValidationResult> {
        try {
            if (this.isEnabled) {
                return {
                    isValid: true,
                    errors: []
                };
            }

            // تطبيق الأنماط
            const stylesApplied = this.stylesApplicator.applyStyles();
            if (!stylesApplied) {
                return {
                    isValid: false,
                    errors: [{
                        field: 'styles',
                        message: 'فشل في تطبيق أنماط الوضع المظلم',
                        code: 'STYLES_APPLICATION_FAILED'
                    }]
                };
            }

            // بدء مراقبة التغييرات إذا كان مفعلاً
            if (this.config.observeChanges) {
                const observerStarted = this.observer.startObserving();
                if (!observerStarted) {
                    console.warn('تحذير: فشل في بدء مراقبة التغييرات');
                }
            }

            this.isEnabled = true;
            console.log(DARK_MODE_MESSAGES.ENABLED);

            return {
                isValid: true,
                errors: []
            };

        } catch (error) {
            console.error(DARK_MODE_MESSAGES.ERROR_ENABLE, error);
            return {
                isValid: false,
                errors: [{
                    field: 'enable',
                    message: `خطأ في تفعيل الوضع المظلم: ${error}`,
                    code: 'ENABLE_ERROR'
                }]
            };
        }
    }

    /**
     * إيقاف الوضع المظلم
     * Disable dark mode
     * 
     * @returns Promise<ValidationResult> - نتيجة الإيقاف
     * 
     * @example
     * ```typescript
     * const result = await darkModeService.disable();
     * if (result.isValid) {
     *     console.log('تم إيقاف الوضع المظلم');
     * }
     * ```
     */
    public async disable(): Promise<ValidationResult> {
        try {
            if (!this.isEnabled) {
                return {
                    isValid: true,
                    errors: []
                };
            }

            // إيقاف مراقبة التغييرات
            this.observer.stopObserving();

            // إزالة الأنماط
            const stylesRemoved = this.stylesApplicator.removeStyles();
            if (!stylesRemoved) {
                return {
                    isValid: false,
                    errors: [{
                        field: 'styles',
                        message: 'فشل في إزالة أنماط الوضع المظلم',
                        code: 'STYLES_REMOVAL_FAILED'
                    }]
                };
            }

            this.isEnabled = false;
            console.log(DARK_MODE_MESSAGES.DISABLED);

            return {
                isValid: true,
                errors: []
            };

        } catch (error) {
            console.error(DARK_MODE_MESSAGES.ERROR_DISABLE, error);
            return {
                isValid: false,
                errors: [{
                    field: 'disable',
                    message: `خطأ في إيقاف الوضع المظلم: ${error}`,
                    code: 'DISABLE_ERROR'
                }]
            };
        }
    }

    /**
     * تبديل حالة الوضع المظلم
     * Toggle dark mode state
     * 
     * @returns Promise<ValidationResult> - نتيجة التبديل
     * 
     * @example
     * ```typescript
     * const result = await darkModeService.toggle();
     * console.log('حالة الوضع المظلم:', darkModeService.isEnabledStatus());
     * ```
     */
    public async toggle(): Promise<ValidationResult> {
        if (this.isEnabled) {
            return await this.disable();
        } else {
            return await this.enable();
        }
    }

    /**
     * فحص حالة تفعيل الوضع المظلم
     * Check if dark mode is enabled
     * 
     * @returns true إذا كان مفعلاً
     */
    public isEnabledStatus(): boolean {
        return this.isEnabled;
    }

    /**
     * تحديث تكوين الوضع المظلم
     * Update dark mode configuration
     * 
     * @param newConfig - التكوين الجديد
     * @returns Promise<ValidationResult> - نتيجة التحديث
     */
    public async updateConfig(newConfig: Partial<DarkModeConfig>): Promise<ValidationResult> {
        try {
            // دمج التكوين الجديد
            Object.assign(this.config, newConfig);

            // إعادة تطبيق الأنماط إذا كان مفعلاً
            if (this.isEnabled) {
                const disableResult = await this.disable();
                if (!disableResult.isValid) {
                    return disableResult;
                }

                const enableResult = await this.enable();
                return enableResult;
            }

            return {
                isValid: true,
                errors: []
            };

        } catch (error) {
            return {
                isValid: false,
                errors: [{
                    field: 'config',
                    message: `خطأ في تحديث التكوين: ${error}`,
                    code: 'CONFIG_UPDATE_ERROR'
                }]
            };
        }
    }

    /**
     * الحصول على التكوين الحالي
     * Get current configuration
     * 
     * @returns التكوين الحالي
     */
    public getConfig(): DarkModeConfig {
        return { ...this.config };
    }

    /**
     * إعادة تطبيق الأنماط
     * Reapply styles
     * 
     * @returns Promise<ValidationResult> - نتيجة إعادة التطبيق
     */
    public async reapplyStyles(): Promise<ValidationResult> {
        try {
            if (!this.isEnabled) {
                return {
                    isValid: false,
                    errors: [{
                        field: 'reapply',
                        message: 'الوضع المظلم غير مفعل',
                        code: 'NOT_ENABLED'
                    }]
                };
            }

            const stylesApplied = this.stylesApplicator.applyStyles();
            if (!stylesApplied) {
                return {
                    isValid: false,
                    errors: [{
                        field: 'reapply',
                        message: 'فشل في إعادة تطبيق الأنماط',
                        code: 'REAPPLY_FAILED'
                    }]
                };
            }

            return {
                isValid: true,
                errors: []
            };

        } catch (error) {
            return {
                isValid: false,
                errors: [{
                    field: 'reapply',
                    message: `خطأ في إعادة تطبيق الأنماط: ${error}`,
                    code: 'REAPPLY_ERROR'
                }]
            };
        }
    }

    /**
     * تنظيف الموارد
     * Cleanup resources
     * 
     * @returns Promise<void>
     */
    public async cleanup(): Promise<void> {
        try {
            // إيقاف الوضع المظلم
            if (this.isEnabled) {
                await this.disable();
            }

            // إيقاف المراقبة
            this.observer.stopObserving();

            console.log('تم تنظيف موارد الوضع المظلم');

        } catch (error) {
            console.error('خطأ في تنظيف موارد الوضع المظلم:', error);
        }
    }
}
