/**
 * مراقبة الوضع المظلم - العمليات الأساسية
 * Dark mode monitoring - Core operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import {
    DARK_MODE_MESSAGES,
    DarkModeConfig
} from './dark-mode-config';
import { DarkModeObserverOperations } from './dark-mode-observer-operations';

/**
 * فئة العمليات الأساسية لمراقبة الوضع المظلم
 * Core operations for dark mode monitoring
 */
export class DarkModeObserverMonitoringCore {
    private readonly config: DarkModeConfig;
    private readonly operations: DarkModeObserverOperations;
    private readonly coreOperations: DarkModeObserverMonitoringCoreOperations;
    private observer: MutationObserver | null = null;
    private detectionInterval: number | null = null;
    private isObserving: boolean = false;

    /**
     * منشئ فئة مراقبة الوضع المظلم الأساسية
     * Dark mode monitoring core constructor
     */
    constructor(config: DarkModeConfig) {
        this.config = config;
        this.operations = new DarkModeObserverOperations(config);
        this.coreOperations = new DarkModeObserverMonitoringCoreOperations(config);
    }

    /**
     * بدء المراقبة
     * Start monitoring
     */
    public startMonitoring(): void {
        if (this.isObserving) {
            console.log(DARK_MODE_MESSAGES.ALREADY_OBSERVING);
            return;
        }

        try {
            this.setupMutationObserver();
            this.setupPeriodicDetection();
            this.isObserving = true;
            console.log(DARK_MODE_MESSAGES.OBSERVER_STARTED);
        } catch (error) {
            console.error(DARK_MODE_MESSAGES.OBSERVER_ERROR, error);
        }
    }

    /**
     * إيقاف المراقبة
     * Stop monitoring
     */
    public stopMonitoring(): void {
        if (!this.isObserving) {
            console.log(DARK_MODE_MESSAGES.NOT_OBSERVING);
            return;
        }

        try {
            this.cleanupObserver();
            this.cleanupInterval();
            this.isObserving = false;
            console.log(DARK_MODE_MESSAGES.OBSERVER_STOPPED);
        } catch (error) {
            console.error(DARK_MODE_MESSAGES.OBSERVER_ERROR, error);
        }
    }

    /**
     * إعداد مراقب التغييرات
     * Setup mutation observer
     */
    private setupMutationObserver(): void {
        if (this.observer) {
            this.observer.disconnect();
        }

        this.observer = new MutationObserver((mutations) => {
            this.coreOperations.handleMutations(mutations);
        });

        const observerConfig = this.coreOperations.getOptimizedObserverConfig();
        this.observer.observe(document.body, observerConfig);
    }

    /**
     * إعداد الكشف الدوري
     * Setup periodic detection
     */
    private setupPeriodicDetection(): void {
        if (this.detectionInterval) {
            clearInterval(this.detectionInterval);
        }

        this.detectionInterval = window.setInterval(() => {
            this.coreOperations.performPeriodicCheck();
        }, this.config.detectionInterval);
    }



    /**
     * تنظيف المراقب
     * Cleanup observer
     */
    private cleanupObserver(): void {
        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }
    }

    /**
     * تنظيف الفترة الزمنية
     * Cleanup interval
     */
    private cleanupInterval(): void {
        if (this.detectionInterval) {
            clearInterval(this.detectionInterval);
            this.detectionInterval = null;
        }
    }

    /**
     * الحصول على حالة المراقبة
     * Get monitoring status
     */
    public isMonitoring(): boolean {
        return this.isObserving;
    }

    /**
     * إعادة تشغيل المراقبة
     * Restart monitoring
     */
    public restartMonitoring(): void {
        this.stopMonitoring();
        setTimeout(() => {
            this.startMonitoring();
        }, 100);
    }
}
