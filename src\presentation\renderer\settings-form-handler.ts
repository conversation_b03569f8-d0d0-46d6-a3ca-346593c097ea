/**
 * معالج نماذج الإعدادات
 * Settings form handler
 * 
 * هذا الملف يحتوي على منطق معالجة نماذج الإعدادات
 * This file contains settings form handling logic
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ApplicationConfig, ValidationResult } from '@shared/types';

/**
 * فئة معالج نماذج الإعدادات
 * Settings form handler class
 */
export class SettingsFormHandler {
    private debounceTimers: Map<string, NodeJS.Timeout> = new Map();
    private readonly debounceDelay: number = 500; // 500ms

    /**
     * منشئ معالج النماذج
     * Form handler constructor
     */
    constructor() {
        // تهيئة المعالج
    }

    /**
     * التحقق من صحة الإعدادات
     * Validate settings
     * 
     * @param settings - إعدادات التطبيق
     * @returns نتيجة التحقق
     */
    public validateSettings(settings: ApplicationConfig): ValidationResult {
        const errors: string[] = [];
        const warnings: string[] = [];

        // التحقق من جودة الفيديو
        if (settings.videoQuality) {
            const validQualities = ['144p', '240p', '360p', '480p', '720p', '1080p', '1440p', '2160p'];
            if (!validQualities.includes(settings.videoQuality)) {
                errors.push(`جودة فيديو غير صالحة: ${settings.videoQuality} / Invalid video quality: ${settings.videoQuality}`);
            }
        }

        // التحقق من الوضع المظلم
        if (typeof settings.darkMode !== 'boolean' && settings.darkMode !== undefined) {
            errors.push('قيمة الوضع المظلم يجب أن تكون true أو false / Dark mode value must be true or false');
        }

        // التحقق من مانع الإعلانات
        if (typeof settings.adBlocker !== 'boolean' && settings.adBlocker !== undefined) {
            errors.push('قيمة مانع الإعلانات يجب أن تكون true أو false / Ad blocker value must be true or false');
        }

        // تحذيرات
        if (settings.videoQuality === '2160p') {
            warnings.push('جودة 4K قد تستهلك عرض نطاق ترددي عالي / 4K quality may consume high bandwidth');
        }

        if (settings.adBlocker === false) {
            warnings.push('إلغاء مانع الإعلانات قد يؤثر على تجربة المستخدم / Disabling ad blocker may affect user experience');
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * تطبيق إعداد واحد مع تأخير
     * Apply single setting with debounce
     * 
     * @param key - مفتاح الإعداد
     * @param value - قيمة الإعداد
     */
    public debouncedApplySetting(key: string, value: string | boolean | number): void {
        // إلغاء المؤقت السابق إن وجد
        const existingTimer = this.debounceTimers.get(key);
        if (existingTimer) {
            clearTimeout(existingTimer);
        }

        // إنشاء مؤقت جديد
        const timer = setTimeout(async () => {
            try {
                await this.applySingleSetting(key, value);
                this.debounceTimers.delete(key);
            } catch (error) {
                console.error(`خطأ في تطبيق الإعداد ${key} / Error applying setting ${key}:`, error);
            }
        }, this.debounceDelay);

        this.debounceTimers.set(key, timer);
    }

    /**
     * تطبيق إعداد واحد
     * Apply single setting
     * 
     * @param key - مفتاح الإعداد
     * @param value - قيمة الإعداد
     */
    private async applySingleSetting(key: string, value: string | boolean | number): Promise<void> {
        const partialSettings: Partial<ApplicationConfig> = {};
        partialSettings[key as keyof ApplicationConfig] = value as any;

        // التحقق من صحة الإعداد
        const validationResult = this.validateSettings(partialSettings as ApplicationConfig);
        if (!validationResult.isValid) {
            console.warn('إعداد غير صالح / Invalid setting:', validationResult.errors);
            return;
        }

        // إرسال الإعداد إلى العملية الرئيسية
        try {
            await window.settingsAPI.saveSettings(partialSettings as ApplicationConfig);
            console.log(`تم تطبيق الإعداد ${key} = ${value} / Applied setting ${key} = ${value}`);
        } catch (error) {
            console.error(`فشل في تطبيق الإعداد ${key} / Failed to apply setting ${key}:`, error);
        }
    }

    /**
     * تنسيق قيمة الإعداد للعرض
     * Format setting value for display
     * 
     * @param key - مفتاح الإعداد
     * @param value - قيمة الإعداد
     * @returns القيمة المنسقة
     */
    public formatSettingValue(key: string, value: any): string {
        switch (key) {
            case 'videoQuality':
                return this.formatVideoQuality(value);
            case 'darkMode':
                return value ? 'مفعل / Enabled' : 'معطل / Disabled';
            case 'adBlocker':
                return value ? 'مفعل / Enabled' : 'معطل / Disabled';
            default:
                return String(value);
        }
    }

    /**
     * تنسيق جودة الفيديو للعرض
     * Format video quality for display
     * 
     * @param quality - جودة الفيديو
     * @returns الجودة المنسقة
     */
    private formatVideoQuality(quality: string): string {
        const qualityMap: Record<string, string> = {
            '144p': '144p - جودة منخفضة / Low Quality',
            '240p': '240p - جودة منخفضة / Low Quality',
            '360p': '360p - جودة متوسطة / Medium Quality',
            '480p': '480p - جودة متوسطة / Medium Quality',
            '720p': '720p - جودة عالية / High Quality',
            '1080p': '1080p - جودة عالية جداً / Very High Quality',
            '1440p': '1440p - جودة فائقة / Ultra Quality',
            '2160p': '2160p - جودة 4K / 4K Quality'
        };

        return qualityMap[quality] || quality;
    }

    /**
     * الحصول على قائمة جودات الفيديو المتاحة
     * Get available video qualities
     * 
     * @returns قائمة الجودات
     */
    public getAvailableVideoQualities(): Array<{ value: string; label: string }> {
        return [
            { value: '144p', label: '144p - جودة منخفضة / Low Quality' },
            { value: '240p', label: '240p - جودة منخفضة / Low Quality' },
            { value: '360p', label: '360p - جودة متوسطة / Medium Quality' },
            { value: '480p', label: '480p - جودة متوسطة / Medium Quality' },
            { value: '720p', label: '720p - جودة عالية / High Quality' },
            { value: '1080p', label: '1080p - جودة عالية جداً / Very High Quality' },
            { value: '1440p', label: '1440p - جودة فائقة / Ultra Quality' },
            { value: '2160p', label: '2160p - جودة 4K / 4K Quality' }
        ];
    }

    /**
     * إنشاء ملخص الإعدادات
     * Create settings summary
     * 
     * @param settings - إعدادات التطبيق
     * @returns ملخص الإعدادات
     */
    public createSettingsSummary(settings: ApplicationConfig): string {
        const summary: string[] = [];

        if (settings.videoQuality) {
            summary.push(`جودة الفيديو: ${this.formatVideoQuality(settings.videoQuality)}`);
        }

        if (typeof settings.darkMode === 'boolean') {
            summary.push(`الوضع المظلم: ${settings.darkMode ? 'مفعل' : 'معطل'}`);
        }

        if (typeof settings.adBlocker === 'boolean') {
            summary.push(`مانع الإعلانات: ${settings.adBlocker ? 'مفعل' : 'معطل'}`);
        }

        return summary.join(' | ');
    }

    /**
     * تصدير الإعدادات كـ JSON
     * Export settings as JSON
     * 
     * @param settings - إعدادات التطبيق
     * @returns JSON string
     */
    public exportSettingsAsJSON(settings: ApplicationConfig): string {
        return JSON.stringify(settings, null, 2);
    }

    /**
     * استيراد الإعدادات من JSON
     * Import settings from JSON
     * 
     * @param jsonString - JSON string
     * @returns إعدادات التطبيق أو null في حالة الخطأ
     */
    public importSettingsFromJSON(jsonString: string): ApplicationConfig | null {
        try {
            const settings = JSON.parse(jsonString) as ApplicationConfig;
            const validationResult = this.validateSettings(settings);
            
            if (validationResult.isValid) {
                return settings;
            } else {
                console.error('إعدادات غير صالحة / Invalid settings:', validationResult.errors);
                return null;
            }
        } catch (error) {
            console.error('خطأ في تحليل JSON / Error parsing JSON:', error);
            return null;
        }
    }

    /**
     * تنظيف الموارد
     * Cleanup resources
     */
    public cleanup(): void {
        // إلغاء جميع المؤقتات
        this.debounceTimers.forEach((timer) => {
            clearTimeout(timer);
        });
        this.debounceTimers.clear();
    }
}
