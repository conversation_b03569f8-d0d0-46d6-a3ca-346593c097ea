/**
 * أنماط CSS الأساسية للوضع المظلم
 * Basic CSS styles for dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * أنماط CSS الأساسية للوضع المظلم
 * Basic CSS styles for dark mode
 */
export const DARK_MODE_BASE_STYLES = `
    /* الخلفية الرئيسية / Main background */
    body, html {
        background-color: #0f0f0f !important;
        color: #ffffff !important;
    }

    /* شريط التنقل / Navigation bar */
    #masthead-container {
        background-color: #212121 !important;
    }

    /* المحتوى الرئيسي / Main content */
    #content {
        background-color: #0f0f0f !important;
    }

    /* الشريط الجانبي / Sidebar */
    #secondary {
        background-color: #0f0f0f !important;
    }

    /* مشغل الفيديو / Video player */
    .html5-video-player {
        background-color: #000000 !important;
    }

    /* عناوين الفيديوهات / Video titles */
    #video-title {
        color: #ffffff !important;
    }

    /* أوصاف الفيديوهات / Video descriptions */
    #description {
        color: #aaaaaa !important;
        background-color: #212121 !important;
    }

    /* التعليقات / Comments */
    #comments {
        background-color: #0f0f0f !important;
    }

    .ytd-comment-thread-renderer {
        background-color: #212121 !important;
    }

    /* الأزرار / Buttons */
    .yt-spec-button-shape-next {
        background-color: #3f3f3f !important;
        color: #ffffff !important;
    }

    /* حقول الإدخال / Input fields */
    input, textarea {
        background-color: #3f3f3f !important;
        color: #ffffff !important;
        border: 1px solid #666666 !important;
    }

    /* القوائم المنسدلة / Dropdown menus */
    .ytp-popup {
        background-color: #212121 !important;
        color: #ffffff !important;
    }

    /* شريط التقدم / Progress bar */
    .ytp-progress-bar {
        background-color: #ff0000 !important;
    }

    /* أيقونات التحكم / Control icons */
    .ytp-button {
        color: #ffffff !important;
    }

    /* النصوص / Text elements */
    span, p, div {
        color: #ffffff !important;
    }

    /* الروابط / Links */
    a {
        color: #3ea6ff !important;
    }

    a:hover {
        color: #65b7ff !important;
    }

    /* الحدود / Borders */
    .border {
        border-color: #3f3f3f !important;
    }

    /* الظلال / Shadows */
    .shadow {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5) !important;
    }
`;

/**
 * أنماط CSS للمكونات المتقدمة
 * Advanced component CSS styles
 */
export const DARK_MODE_ADVANCED_STYLES = `
    /* قائمة الفيديوهات الجانبية / Side video list */
    #related {
        background-color: #0f0f0f !important;
    }

    .ytd-compact-video-renderer {
        background-color: #212121 !important;
    }

    /* شريط البحث / Search bar */
    #search-input {
        background-color: #3f3f3f !important;
        color: #ffffff !important;
    }

    /* نتائج البحث / Search results */
    .ytd-search-result {
        background-color: #212121 !important;
    }

    /* القنوات / Channels */
    .ytd-channel-renderer {
        background-color: #212121 !important;
    }

    /* قوائم التشغيل / Playlists */
    .ytd-playlist-renderer {
        background-color: #212121 !important;
    }

    /* الإشعارات / Notifications */
    .ytd-notification-renderer {
        background-color: #3f3f3f !important;
        color: #ffffff !important;
    }

    /* النوافذ المنبثقة / Popup windows */
    .ytd-popup-container {
        background-color: #212121 !important;
        border: 1px solid #3f3f3f !important;
    }

    /* أشرطة التمرير / Scrollbars */
    ::-webkit-scrollbar {
        width: 12px;
        background-color: #0f0f0f !important;
    }

    ::-webkit-scrollbar-thumb {
        background-color: #3f3f3f !important;
        border-radius: 6px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background-color: #5f5f5f !important;
    }

    /* التبويبات / Tabs */
    .ytd-tab-renderer {
        background-color: #212121 !important;
        color: #ffffff !important;
    }

    .ytd-tab-renderer[aria-selected="true"] {
        background-color: #3f3f3f !important;
    }

    /* البطاقات / Cards */
    .ytd-card {
        background-color: #212121 !important;
        border: 1px solid #3f3f3f !important;
    }

    /* الشارات / Badges */
    .ytd-badge {
        background-color: #ff0000 !important;
        color: #ffffff !important;
    }

    /* أزرار الاشتراك / Subscribe buttons */
    .ytd-subscribe-button-renderer {
        background-color: #cc0000 !important;
        color: #ffffff !important;
    }

    /* أزرار الإعجاب / Like buttons */
    .ytd-toggle-button-renderer {
        color: #ffffff !important;
    }

    /* معلومات الفيديو / Video info */
    .ytd-video-meta-block {
        color: #aaaaaa !important;
    }

    /* تواريخ النشر / Upload dates */
    .ytd-video-meta-block .published {
        color: #888888 !important;
    }

    /* عدد المشاهدات / View counts */
    .ytd-video-meta-block .view-count {
        color: #888888 !important;
    }
`;
