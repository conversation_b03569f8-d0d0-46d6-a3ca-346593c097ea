/**
 * معالجة عناصر الفيديو المتقدمة لمراقب الوضع المظلم
 * Advanced video element processing for dark mode observer
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// استيراد الفئات المتخصصة / Import specialized classes
import { DarkModeConfig } from './dark-mode-config';
import { DarkModeObserverOperationsAdvancedElementsVideoControls } from './dark-mode-observer-operations-advanced-elements-video-controls';
import { DarkModeObserverOperationsAdvancedElementsVideoCore } from './dark-mode-observer-operations-advanced-elements-video-core';

/**
 * فئة معالجة عناصر الفيديو المتقدمة
 * Advanced video element processing class
 */
export class DarkModeObserverOperationsAdvancedElementsVideo {

    /** معالجة عنصر الفيديو / Process video element */
    public static processVideoElement(element: HTMLVideoElement, config: DarkModeConfig): void {
        try {
            // المعالجة الأساسية
            DarkModeObserverOperationsAdvancedElementsVideoCore.processVideoElement(element, config);

            // كشف نوع الفيديو
            const videoType = DarkModeObserverOperationsAdvancedElementsVideoCore.detectVideoType(element);

            // معالجة عناصر التحكم
            DarkModeObserverOperationsAdvancedElementsVideoControls.processVideoControls(element, config);

            // إضافة عناصر تحكم مخصصة
            DarkModeObserverOperationsAdvancedElementsVideoControls.addCustomControls(element, config);

        } catch (error) {
            console.error('خطأ في معالجة عنصر الفيديو:', error);
        }
    }

    /** كشف نوع الفيديو / Detect video type */
    public static detectVideoType(element: HTMLVideoElement): string {
        return DarkModeObserverOperationsAdvancedElementsVideoCore.detectVideoType(element);
    }

    /** إضافة عنصر بديل للفيديو / Add video placeholder */
    public static addVideoPlaceholder(element: HTMLVideoElement, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsVideoCore.addVideoPlaceholder(element, config);
    }

    /** معالجة عناصر التحكم / Process controls */
    public static processVideoControls(element: HTMLVideoElement, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsVideoControls.processVideoControls(element, config);
    }

    /** إضافة عناصر تحكم مخصصة / Add custom controls */
    public static addCustomControls(element: HTMLVideoElement, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsVideoControls.addCustomControls(element, config);
    }
}