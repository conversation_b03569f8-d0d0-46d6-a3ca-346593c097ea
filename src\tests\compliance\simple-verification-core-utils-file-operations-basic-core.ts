/**
 * العمليات الأساسية الجوهرية للملفات - ملف التفويض
 * Core basic file operations - Delegation file
 *
 * هذا الملف يفوض جميع العمليات للملفات المتخصصة
 * This file delegates all operations to specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { SimpleVerificationCoreUtilsFileOperationsBasicCoreWalker } from './simple-verification-core-utils-file-operations-basic-core-walker';
import { SimpleVerificationConfig } from './simple-verification-types';

/**
 * فئة العمليات الأساسية الجوهرية للملفات - التفويض
 * Core basic file operations class - Delegation
 */
export class SimpleVerificationCoreUtilsFileOperationsBasicCore {

    /**
     * الحصول على جميع الملفات - تفويض لمستكشف الملفات
     * Get all files - Delegate to file walker
     */
    public static async getAllFiles(
        projectRoot: string,
        config: SimpleVerificationConfig
    ): Promise<string[]> {
        // تفويض الحصول على الملفات لمستكشف الملفات
        // Delegate file retrieval to file walker
        return await SimpleVerificationCoreUtilsFileOperationsBasicCoreWalker.getAllFiles(projectRoot, config);
    }

    /**
     * فحص وجود الملف - تفويض لوحدة الملفات
     * Check if file exists - Delegate to files module
     */
    public static fileExists(filePath: string): boolean {
        // تفويض فحص وجود الملف لوحدة الملفات
        // Delegate file existence check to files module
        return SimpleVerificationCoreUtilsFileOperationsBasicCoreFiles.fileExists(filePath);
    }

    /**
     * فحص وجود المجلد - تفويض لوحدة الملفات
     * Check if directory exists - Delegate to files module
     */
    public static directoryExists(dirPath: string): boolean {
        // تفويض فحص وجود المجلد لوحدة الملفات
        // Delegate directory existence check to files module
        return SimpleVerificationCoreUtilsFileOperationsBasicCoreFiles.directoryExists(dirPath);
    }

    /**
     * قراءة محتوى الملف - تفويض لوحدة الملفات
     * Read file content - Delegate to files module
     */
    public static readFileContent(filePath: string): string | null {
        // تفويض قراءة المحتوى لوحدة الملفات
        // Delegate content reading to files module
        return SimpleVerificationCoreUtilsFileOperationsBasicCoreFiles.readFileContent(filePath);
    }

    /**
     * تنظيف مسار الملف - تفويض لوحدة الملفات
     * Clean file path - Delegate to files module
     */
    public static cleanFilePath(filePath: string, projectRoot: string): string {
        // تفويض تنظيف المسار لوحدة الملفات
        // Delegate path cleaning to files module
        return SimpleVerificationCoreUtilsFileOperationsBasicCoreFiles.cleanFilePath(filePath, projectRoot);
    }

    /**
     * فحص امتداد الملف - تفويض لوحدة الملفات
     * Check file extension - Delegate to files module
     */
    public static hasValidExtension(filePath: string, validExtensions: string[]): boolean {
        // تفويض فحص الامتداد لوحدة الملفات
        // Delegate extension check to files module
        return SimpleVerificationCoreUtilsFileOperationsBasicCoreFiles.hasValidExtension(filePath, validExtensions);
    }

    /**
     * فحص حجم الملف - تفويض لوحدة الملفات
     * Check file size - Delegate to files module
     */
    public static getFileSize(filePath: string): number {
        // تفويض فحص الحجم لوحدة الملفات
        // Delegate size check to files module
        return SimpleVerificationCoreUtilsFileOperationsBasicCoreFiles.getFileSize(filePath);
    }

    /**
     * فحص تاريخ تعديل الملف - تفويض لوحدة الملفات
     * Check file modification date - Delegate to files module
     */
    public static getFileModificationDate(filePath: string): Date | null {
        // تفويض فحص تاريخ التعديل لوحدة الملفات
        // Delegate modification date check to files module
        return SimpleVerificationCoreUtilsFileOperationsBasicCoreFiles.getFileModificationDate(filePath);
    }

    /**
     * فحص صلاحيات الملف - تفويض لوحدة الملفات
     * Check file permissions - Delegate to files module
     */
    public static isFileReadable(filePath: string): boolean {
        // تفويض فحص صلاحيات القراءة لوحدة الملفات
        // Delegate read permissions check to files module
        return SimpleVerificationCoreUtilsFileOperationsBasicCoreFiles.isFileReadable(filePath);
    }

    /**
     * فحص إذا كان الملف قابل للكتابة - تفويض لوحدة الملفات
     * Check if file is writable - Delegate to files module
     */
    public static isFileWritable(filePath: string): boolean {
        // تفويض فحص صلاحيات الكتابة لوحدة الملفات
        // Delegate write permissions check to files module
        return SimpleVerificationCoreUtilsFileOperationsBasicCoreFiles.isFileWritable(filePath);
    }

    /**
     * الحصول على قائمة الملفات في مجلد - تفويض لوحدة الملفات
     * Get list of files in directory - Delegate to files module
     */
    public static getFilesInDirectory(dirPath: string): string[] {
        // تفويض الحصول على قائمة الملفات لوحدة الملفات
        // Delegate files list retrieval to files module
        return SimpleVerificationCoreUtilsFileOperationsBasicCoreFiles.getFilesInDirectory(dirPath);
    }
}
