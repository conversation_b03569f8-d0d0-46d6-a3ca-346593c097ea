/**
 * العمليات المتقدمة للملفات لأداة التحقق المبسطة - ملف التفويض
 * Simple verification advanced file operations - Delegation file
 * 
 * هذا الملف يفوض جميع العمليات للملفات المتخصصة
 * This file delegates all operations to specialized files
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { SimpleVerificationConfig } from './simple-verification-types';
import { SimpleVerificationCoreUtilsFileOperationsAdvancedSingle } from './simple-verification-core-utils-file-operations-advanced-single';
import { SimpleVerificationCoreUtilsFileOperationsAdvancedBatch } from './simple-verification-core-utils-file-operations-advanced-batch';

/**
 * فئة العمليات المتقدمة للملفات - التفويض
 * Advanced file operations class - Delegation
 */
export class SimpleVerificationCoreUtilsFileOperationsAdvanced {

    /**
     * فحص ملف واحد - تفويض للوحدة المتخصصة
     * Check single file - Delegate to specialized module
     */
    public static checkSingleFile(filePath: string, config: SimpleVerificationConfig): {
        isValid: boolean;
        issues: string[];
        score: number;
        lineCount: number;
        hasDocumentation: boolean;
        followsNamingConvention: boolean;
    } {
        // تفويض فحص الملف الواحد للوحدة المتخصصة
        // Delegate single file check to specialized module
        return SimpleVerificationCoreUtilsFileOperationsAdvancedSingle.checkSingleFile(filePath, config);
    }

    /**
     * فحص ملفات متعددة - تفويض للوحدة المجمعة
     * Check multiple files - Delegate to batch module
     */
    public static checkMultipleFiles(filePaths: string[], config: SimpleVerificationConfig): {
        totalFiles: number;
        validFiles: number;
        invalidFiles: number;
        totalIssues: number;
        averageScore: number;
        results: Array<{
            filePath: string;
            isValid: boolean;
            issues: string[];
            score: number;
            lineCount: number;
        }>;
    } {
        // تفويض فحص الملفات المتعددة للوحدة المجمعة
        // Delegate multiple files check to batch module
        return SimpleVerificationCoreUtilsFileOperationsAdvancedBatch.checkMultipleFiles(filePaths, config);
    }

    /**
     * تحليل أنماط الملفات - تفويض للوحدة المجمعة
     * Analyze file patterns - Delegate to batch module
     */
    public static analyzeFilePatterns(filePaths: string[]): {
        extensionCounts: Record<string, number>;
        sizeCounts: {
            small: number;    // < 50 lines
            medium: number;   // 50-200 lines
            large: number;    // > 200 lines
        };
        namingPatterns: {
            kebabCase: number;
            camelCase: number;
            pascalCase: number;
            snakeCase: number;
            other: number;
        };
    } {
        // تفويض تحليل أنماط الملفات للوحدة المجمعة
        // Delegate file patterns analysis to batch module
        return SimpleVerificationCoreUtilsFileOperationsAdvancedBatch.analyzeFilePatterns(filePaths);
    }

    /**
     * إنشاء تقرير مفصل - تفويض للوحدة المجمعة
     * Generate detailed report - Delegate to batch module
     */
    public static generateDetailedReport(filePaths: string[], config: SimpleVerificationConfig): {
        summary: {
            totalFiles: number;
            validFiles: number;
            averageScore: number;
            totalIssues: number;
        };
        patterns: {
            extensionCounts: Record<string, number>;
            sizeCounts: { small: number; medium: number; large: number };
            namingPatterns: { kebabCase: number; camelCase: number; pascalCase: number; snakeCase: number; other: number };
        };
        topIssues: Array<{ issue: string; count: number }>;
        recommendations: string[];
    } {
        // تفويض إنشاء التقرير المفصل للوحدة المجمعة
        // Delegate detailed report generation to batch module
        return SimpleVerificationCoreUtilsFileOperationsAdvancedBatch.generateDetailedReport(filePaths, config);
    }
}
