/**
 * الإعدادات الافتراضية
 * Default settings configuration
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQuality, WindowBounds } from '@shared/types';
import { SettingsManagerConfig, SyncOptions, MonitoringOptions, ExportOptions, ImportOptions } from './settings-config-types';
import { LogLevel, BACKUP_PATTERNS, ENCRYPTION_MODES, COMPRESSION_MODES, SYNC_MODES } from './settings-config-constants';

/**
 * الإعدادات الافتراضية لمدير الإعدادات / Default settings manager configuration
 */
export const DEFAULT_SETTINGS_MANAGER_CONFIG: SettingsManagerConfig = {
    storeFileName: 'youtube-dark-cyber-x-settings',
    enableBackup: true,
    backupInterval: 300000, // 5 minutes
    maxBackups: 5,
    validateOnLoad: true,
    autoSave: true,
    saveDelay: 1000,
    compressionEnabled: true,
    syncEnabled: false,
    logLevel: LogLevel.INFO
};

/**
 * حدود النافذة الافتراضية / Default window bounds
 */
export const DEFAULT_WINDOW_BOUNDS: WindowBounds = {
    x: 100,
    y: 100,
    width: 1200,
    height: 800
};

/**
 * جودة الفيديو الافتراضية / Default video quality
 */
export const DEFAULT_VIDEO_QUALITY: VideoQuality = '720p';

/**
 * الإعدادات الافتراضية للتطبيق / Default application settings
 */
export const DEFAULT_APPLICATION_SETTINGS = {
    // إعدادات النافذة / Window settings
    windowBounds: DEFAULT_WINDOW_BOUNDS,
    windowState: 'normal',
    windowPosition: 'center',
    
    // إعدادات الفيديو / Video settings
    videoQuality: DEFAULT_VIDEO_QUALITY,
    autoPlay: true,
    playbackSpeed: 1.0,
    volume: 50,
    muted: false,
    
    // إعدادات المظهر / Appearance settings
    darkMode: true,
    theme: 'dark',
    language: 'ar',
    fontSize: 14,
    
    // إعدادات الأمان / Security settings
    adBlocker: true,
    securityEnabled: true,
    privacyMode: false,
    
    // إعدادات التطبيق / Application settings
    autoUpdate: true,
    startupBehavior: 'restore',
    minimizeToTray: false,
    closeToTray: false
} as const;

/**
 * خيارات المزامنة الافتراضية / Default sync options
 */
export const DEFAULT_SYNC_OPTIONS: SyncOptions = {
    enabled: false,
    provider: 'local',
    interval: 300000, // 5 minutes
    conflictResolution: 'local',
    encryptInTransit: true
};

/**
 * خيارات المراقبة الافتراضية / Default monitoring options
 */
export const DEFAULT_MONITORING_OPTIONS: MonitoringOptions = {
    enabled: true,
    trackChanges: true,
    trackAccess: false,
    trackPerformance: true,
    logLevel: LogLevel.INFO,
    maxLogSize: 10 * 1024 * 1024, // 10MB
    retentionDays: 30
};

/**
 * خيارات التصدير الافتراضية / Default export options
 */
export const DEFAULT_EXPORT_OPTIONS: ExportOptions = {
    format: 'json',
    includeMetadata: true,
    includeDefaults: false,
    compress: false,
    encrypt: false
};

/**
 * خيارات الاستيراد الافتراضية / Default import options
 */
export const DEFAULT_IMPORT_OPTIONS: ImportOptions = {
    format: 'json',
    overwriteExisting: false,
    validateBeforeImport: true,
    createBackupBeforeImport: true,
    dryRun: false
};

/**
 * قواعد التحقق الافتراضية / Default validation rules
 */
export const DEFAULT_VALIDATION_RULES = {
    videoQuality: {
        type: 'string',
        required: true,
        allowedValues: ['144p', '240p', '360p', '480p', '720p', '1080p', '1440p', '2160p', 'auto'],
        errorMessage: 'جودة فيديو غير صالحة'
    },
    volume: {
        type: 'number',
        required: true,
        min: 0,
        max: 100,
        errorMessage: 'مستوى الصوت يجب أن يكون بين 0 و 100'
    },
    playbackSpeed: {
        type: 'number',
        required: true,
        allowedValues: [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2],
        errorMessage: 'سرعة تشغيل غير صالحة'
    },
    language: {
        type: 'string',
        required: true,
        allowedValues: ['ar', 'en', 'fr', 'es', 'de'],
        errorMessage: 'لغة غير صالحة'
    },
    theme: {
        type: 'string',
        required: true,
        allowedValues: ['light', 'dark', 'auto'],
        errorMessage: 'مظهر غير صالح'
    },
    darkMode: {
        type: 'boolean',
        required: true,
        errorMessage: 'الوضع المظلم يجب أن يكون قيمة منطقية'
    },
    adBlocker: {
        type: 'boolean',
        required: true,
        errorMessage: 'مانع الإعلانات يجب أن يكون قيمة منطقية'
    },
    autoPlay: {
        type: 'boolean',
        required: true,
        errorMessage: 'التشغيل التلقائي يجب أن يكون قيمة منطقية'
    },
    fontSize: {
        type: 'number',
        required: true,
        min: 8,
        max: 32,
        errorMessage: 'حجم الخط يجب أن يكون بين 8 و 32'
    }
} as const;

/**
 * فئات الإعدادات الافتراضية / Default settings categories
 */
export const DEFAULT_SETTINGS_CATEGORIES = [
    {
        id: 'window',
        name: 'إعدادات النافذة',
        description: 'إعدادات حجم وموقع النافذة',
        icon: 'window',
        order: 1,
        settings: ['windowBounds', 'windowState', 'windowPosition']
    },
    {
        id: 'video',
        name: 'إعدادات الفيديو',
        description: 'إعدادات تشغيل الفيديو والجودة',
        icon: 'video',
        order: 2,
        settings: ['videoQuality', 'autoPlay', 'playbackSpeed', 'volume', 'muted']
    },
    {
        id: 'appearance',
        name: 'إعدادات المظهر',
        description: 'إعدادات المظهر واللغة',
        icon: 'palette',
        order: 3,
        settings: ['darkMode', 'theme', 'language', 'fontSize']
    },
    {
        id: 'security',
        name: 'إعدادات الأمان',
        description: 'إعدادات الأمان والخصوصية',
        icon: 'shield',
        order: 4,
        settings: ['adBlocker', 'securityEnabled', 'privacyMode']
    },
    {
        id: 'application',
        name: 'إعدادات التطبيق',
        description: 'إعدادات عامة للتطبيق',
        icon: 'settings',
        order: 5,
        settings: ['autoUpdate', 'startupBehavior', 'minimizeToTray', 'closeToTray']
    }
] as const;

/**
 * ملفات التعريف الافتراضية / Default profiles
 */
export const DEFAULT_PROFILES = [
    {
        id: 'default',
        name: 'افتراضي',
        description: 'الملف الافتراضي للإعدادات',
        settings: DEFAULT_APPLICATION_SETTINGS,
        isDefault: true,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: 'performance',
        name: 'أداء عالي',
        description: 'إعدادات محسنة للأداء',
        settings: {
            ...DEFAULT_APPLICATION_SETTINGS,
            videoQuality: '480p' as VideoQuality,
            darkMode: true,
            adBlocker: true,
            securityEnabled: false
        },
        isDefault: false,
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        id: 'quality',
        name: 'جودة عالية',
        description: 'إعدادات محسنة للجودة',
        settings: {
            ...DEFAULT_APPLICATION_SETTINGS,
            videoQuality: '1080p' as VideoQuality,
            playbackSpeed: 1.0,
            volume: 75
        },
        isDefault: false,
        createdAt: new Date(),
        updatedAt: new Date()
    }
] as const;

/**
 * إعدادات النسخ الاحتياطي الافتراضية / Default backup settings
 */
export const DEFAULT_BACKUP_SETTINGS = {
    enabled: true,
    pattern: BACKUP_PATTERNS.ON_CHANGE,
    maxBackups: 10,
    compression: COMPRESSION_MODES.GZIP,
    encryption: ENCRYPTION_MODES.NONE,
    retentionDays: 30,
    autoCleanup: true
} as const;

/**
 * إعدادات الأمان الافتراضية / Default security settings
 */
export const DEFAULT_SECURITY_SETTINGS = {
    encryptionEnabled: false,
    encryptionMode: ENCRYPTION_MODES.AES_256,
    hashingEnabled: true,
    integrityCheckEnabled: true,
    accessLoggingEnabled: true,
    auditTrailEnabled: false
} as const;
