/**
 * معالجة عناصر Canvas في الوضع المظلم
 * Canvas element processing for dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeConfig } from './dark-mode-config';

/**
 * فئة معالجة عناصر Canvas
 * Canvas element processing class
 */
export class DarkModeObserverOperationsAdvancedElementsCanvas {

    /** معالجة عنصر Canvas / Process canvas element */
    public static processCanvasElement(element: HTMLCanvasElement, config: DarkModeConfig): void {
        try {
            // إضافة فئة الوضع المظلم
            element.classList.add('dark-mode-canvas');
            
            // تطبيق أنماط Canvas
            this.applyCanvasStyles(element, config);
            
            // معالجة محتوى Canvas
            this.processCanvasContent(element, config);
            
            // إضافة مراقبة للتغييرات
            this.addCanvasMonitoring(element, config);
            
        } catch (error) {
            console.error('خطأ في معالجة عنصر Canvas:', error);
        }
    }

    /** تطبيق أنماط Canvas / Apply canvas styles */
    private static applyCanvasStyles(element: HTMLCanvasElement, config: DarkModeConfig): void {
        const styles = {
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
            backgroundColor: '#1a1a1a',
            border: '1px solid rgba(255, 255, 255, 0.1)'
        };

        Object.assign(element.style, styles);

        // تطبيق أنماط إضافية حسب التكوين
        if (config.canvasEnhancements?.enabled) {
            if (config.canvasEnhancements.borderRadius) {
                element.style.borderRadius = `${config.canvasEnhancements.borderRadius}px`;
            }
            
            if (config.canvasEnhancements.shadowIntensity) {
                const intensity = config.canvasEnhancements.shadowIntensity;
                element.style.boxShadow = `0 4px 12px rgba(0, 0, 0, ${intensity})`;
            }
        }
    }

    /** معالجة محتوى Canvas / Process canvas content */
    private static processCanvasContent(element: HTMLCanvasElement, config: DarkModeConfig): void {
        const context = element.getContext('2d');
        if (!context) return;

        // تطبيق فلاتر على محتوى Canvas
        if (config.canvasEnhancements?.enabled) {
            context.filter = this.buildCanvasFilter(config);
        }

        // تطبيق خلفية مظلمة افتراضية
        this.applyDarkBackground(context, element);
    }

    /** بناء فلتر Canvas / Build canvas filter */
    private static buildCanvasFilter(config: DarkModeConfig): string {
        const filters: string[] = [];

        if (config.canvasEnhancements?.brightness) {
            filters.push(`brightness(${config.canvasEnhancements.brightness})`);
        }

        if (config.canvasEnhancements?.contrast) {
            filters.push(`contrast(${config.canvasEnhancements.contrast})`);
        }

        if (config.canvasEnhancements?.saturation) {
            filters.push(`saturate(${config.canvasEnhancements.saturation})`);
        }

        return filters.join(' ') || 'none';
    }

    /** تطبيق خلفية مظلمة / Apply dark background */
    private static applyDarkBackground(context: CanvasRenderingContext2D, canvas: HTMLCanvasElement): void {
        // حفظ الحالة الحالية
        context.save();
        
        // تطبيق خلفية مظلمة
        context.fillStyle = '#1a1a1a';
        context.fillRect(0, 0, canvas.width, canvas.height);
        
        // استعادة الحالة
        context.restore();
    }

    /** إضافة مراقبة Canvas / Add canvas monitoring */
    private static addCanvasMonitoring(element: HTMLCanvasElement, config: DarkModeConfig): void {
        // مراقبة تغيير الحجم
        const resizeObserver = new ResizeObserver(() => {
            this.onCanvasResize(element, config);
        });
        
        resizeObserver.observe(element);

        // مراقبة تغيير المحتوى
        this.addContentChangeMonitoring(element, config);
    }

    /** حدث تغيير حجم Canvas / Canvas resize event */
    private static onCanvasResize(element: HTMLCanvasElement, config: DarkModeConfig): void {
        // إعادة تطبيق الأنماط بعد تغيير الحجم
        this.applyCanvasStyles(element, config);
        
        // إعادة معالجة المحتوى
        this.processCanvasContent(element, config);
    }

    /** إضافة مراقبة تغيير المحتوى / Add content change monitoring */
    private static addContentChangeMonitoring(element: HTMLCanvasElement, config: DarkModeConfig): void {
        const context = element.getContext('2d');
        if (!context) return;

        // مراقبة استدعاءات الرسم
        const originalDrawImage = context.drawImage;
        context.drawImage = function(...args: any[]) {
            // تطبيق فلاتر قبل الرسم
            if (config.canvasEnhancements?.enabled) {
                this.filter = DarkModeObserverOperationsAdvancedElementsCanvas.buildCanvasFilter(config);
            }
            
            // استدعاء الدالة الأصلية
            return originalDrawImage.apply(this, args);
        };

        // مراقبة استدعاءات التعبئة
        const originalFillRect = context.fillRect;
        context.fillRect = function(x: number, y: number, width: number, height: number) {
            // تطبيق لون مظلم للتعبئة إذا كان اللون فاتح
            if (this.fillStyle && typeof this.fillStyle === 'string') {
                const color = this.fillStyle;
                if (DarkModeObserverOperationsAdvancedElementsCanvas.isLightColor(color)) {
                    this.fillStyle = DarkModeObserverOperationsAdvancedElementsCanvas.convertToDarkColor(color);
                }
            }
            
            // استدعاء الدالة الأصلية
            return originalFillRect.call(this, x, y, width, height);
        };
    }

    /** فحص إذا كان اللون فاتح / Check if color is light */
    private static isLightColor(color: string): boolean {
        // تحويل اللون إلى RGB
        const rgb = this.parseColor(color);
        if (!rgb) return false;
        
        // حساب السطوع
        const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
        return brightness > 128;
    }

    /** تحويل إلى لون غامق / Convert to dark color */
    private static convertToDarkColor(color: string): string {
        const rgb = this.parseColor(color);
        if (!rgb) return color;
        
        // تحويل إلى لون غامق
        const darkR = Math.max(0, rgb.r - 100);
        const darkG = Math.max(0, rgb.g - 100);
        const darkB = Math.max(0, rgb.b - 100);
        
        return `rgb(${darkR}, ${darkG}, ${darkB})`;
    }

    /** تحليل اللون / Parse color */
    private static parseColor(color: string): { r: number; g: number; b: number } | null {
        // تحليل RGB
        const rgbMatch = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
        if (rgbMatch) {
            return {
                r: parseInt(rgbMatch[1]),
                g: parseInt(rgbMatch[2]),
                b: parseInt(rgbMatch[3])
            };
        }
        
        // تحليل HEX
        const hexMatch = color.match(/^#([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i);
        if (hexMatch) {
            return {
                r: parseInt(hexMatch[1], 16),
                g: parseInt(hexMatch[2], 16),
                b: parseInt(hexMatch[3], 16)
            };
        }
        
        return null;
    }

    /** تطبيق تأثيرات خاصة / Apply special effects */
    public static applySpecialEffects(element: HTMLCanvasElement, effect: string): void {
        const context = element.getContext('2d');
        if (!context) return;

        switch (effect) {
            case 'glow':
                this.applyGlowEffect(context);
                break;
            case 'shadow':
                this.applyShadowEffect(context);
                break;
            case 'blur':
                this.applyBlurEffect(context);
                break;
            default:
                break;
        }
    }

    /** تطبيق تأثير التوهج / Apply glow effect */
    private static applyGlowEffect(context: CanvasRenderingContext2D): void {
        context.shadowColor = 'rgba(255, 255, 255, 0.5)';
        context.shadowBlur = 10;
        context.shadowOffsetX = 0;
        context.shadowOffsetY = 0;
    }

    /** تطبيق تأثير الظل / Apply shadow effect */
    private static applyShadowEffect(context: CanvasRenderingContext2D): void {
        context.shadowColor = 'rgba(0, 0, 0, 0.5)';
        context.shadowBlur = 5;
        context.shadowOffsetX = 2;
        context.shadowOffsetY = 2;
    }

    /** تطبيق تأثير الضبابية / Apply blur effect */
    private static applyBlurEffect(context: CanvasRenderingContext2D): void {
        context.filter = 'blur(2px)';
    }
}
