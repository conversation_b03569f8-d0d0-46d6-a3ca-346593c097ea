/**
 * ملف الأنماط لنافذة الإعدادات
 * Settings window stylesheet
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #ffffff;
    line-height: 1.6;
    overflow-x: hidden;
}

/* حاوي الإعدادات */
.settings-container {
    max-width: 480px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

/* رأس الإعدادات */
.settings-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #444;
}

.settings-header h1 {
    font-size: 24px;
    color: #00d4ff;
    margin-bottom: 8px;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.settings-header p {
    font-size: 14px;
    color: #aaa;
    font-style: italic;
}

/* رسائل النظام */
.message {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-weight: 500;
    text-align: center;
    animation: slideIn 0.3s ease-out;
}

.message.success {
    background-color: rgba(76, 175, 80, 0.2);
    border: 1px solid #4caf50;
    color: #4caf50;
}

.message.error {
    background-color: rgba(244, 67, 54, 0.2);
    border: 1px solid #f44336;
    color: #f44336;
}

/* نموذج الإعدادات */
.settings-form {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 25px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* مجموعة الإعدادات */
.setting-group {
    margin-bottom: 25px;
}

.setting-item {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    padding: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.setting-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: #00d4ff;
    box-shadow: 0 4px 16px rgba(0, 212, 255, 0.1);
}

/* تسميات الإعدادات */
.setting-label {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    gap: 12px;
}

.setting-label-select {
    display: block;
    margin-bottom: 12px;
}

.setting-label-select strong {
    display: block;
    color: #00d4ff;
    margin-bottom: 4px;
    font-size: 16px;
}

.setting-label-select small {
    color: #bbb;
    font-size: 13px;
}

/* نص الإعدادات */
.setting-text {
    flex: 1;
}

.setting-text strong {
    display: block;
    color: #00d4ff;
    margin-bottom: 4px;
    font-size: 16px;
}

.setting-text small {
    color: #bbb;
    font-size: 13px;
    line-height: 1.4;
}

/* مربعات الاختيار المخصصة */
.setting-checkbox {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 2px solid #555;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-top: 2px;
}

.setting-checkbox:checked+.checkmark {
    background-color: #00d4ff;
    border-color: #00d4ff;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.setting-checkbox:checked+.checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #000;
    font-weight: bold;
    font-size: 14px;
}

/* قوائم الاختيار */
.setting-select {
    width: 100%;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid #555;
    border-radius: 6px;
    color: #fff;
    font-size: 14px;
    transition: all 0.3s ease;
}

.setting-select:focus {
    outline: none;
    border-color: #00d4ff;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
}

.setting-select option {
    background-color: #2d2d2d;
    color: #fff;
    padding: 8px;
}

/* أزرار التحكم */
.settings-actions {
    display: flex;
    gap: 12px;
    margin-top: 30px;
    flex-wrap: wrap;
}

.btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.btn-primary {
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    color: #000;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #00b8e6 0%, #0088bb 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 212, 255, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #666 0%, #555 100%);
    color: #fff;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #777 0%, #666 100%);
    transform: translateY(-2px);
}

.btn-cancel {
    background: linear-gradient(135deg, #ff4444 0%, #cc3333 100%);
    color: #fff;
}

.btn-cancel:hover {
    background: linear-gradient(135deg, #ff5555 0%, #dd4444 100%);
    transform: translateY(-2px);
}

/* تذييل الإعدادات */
.settings-footer {
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid #444;
}

.info-section,
.warning-section {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
}

.info-section h3,
.warning-section h3 {
    color: #00d4ff;
    margin-bottom: 12px;
    font-size: 16px;
}

.info-section p,
.warning-section p {
    color: #ccc;
    font-size: 13px;
    margin-bottom: 6px;
}

.warning-section {
    border-left: 4px solid #ff9800;
}

.warning-section h3 {
    color: #ff9800;
}

/* فئة الإخفاء */
.hidden {
    display: none !important;
}

/* الرسوم المتحركة محسنة للأداء */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translate3d(0, -10px, 0);
        will-change: opacity, transform;
    }

    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
        will-change: auto;
    }
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 500px) {
    .settings-container {
        padding: 15px;
    }

    .settings-form {
        padding: 20px;
    }

    .settings-actions {
        flex-direction: column;
    }

    .btn {
        min-width: auto;
    }
}