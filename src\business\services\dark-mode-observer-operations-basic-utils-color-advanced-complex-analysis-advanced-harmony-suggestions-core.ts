/**
 * اقتراحات تحسين الألوان الأساسية المتقدم المعقد المتقدم
 * Advanced complex advanced core color improvement suggestions
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeObserverOperationsBasicUtilsColorCore } from './dark-mode-observer-operations-basic-utils-color-core';
import { DarkModeObserverOperationsBasicUtilsColorAdvancedBasic } from './dark-mode-observer-operations-basic-utils-color-advanced-basic';
import { DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic } from './dark-mode-observer-operations-basic-utils-color-advanced-complex-analysis-basic';

/**
 * فئة اقتراحات تحسين الألوان الأساسية المتقدم المعقد المتقدم
 * Advanced complex advanced core color improvement suggestions class
 */
export class DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisAdvancedHarmonySuggestionsCore {

    /** اقتراح تحسينات للألوان / Suggest color improvements */
    public static suggestColorImprovements(colors: string[]): {
        suggestions: string[];
        alternativeColors: string[];
        improvementScore: number;
    } {
        if (colors.length === 0) {
            return {
                suggestions: ['لا توجد ألوان للتحليل'],
                alternativeColors: [],
                improvementScore: 0
            };
        }

        const suggestions: string[] = [];
        const alternativeColors: string[] = [];
        let improvementScore = 0;

        // تحليل كل لون
        const analyses = colors.map(color => 
            DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(color)
        );

        // فحص التباين
        for (let i = 0; i < colors.length - 1; i++) {
            const contrast = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeContrast(colors[i], colors[i + 1]);
            if (!contrast.isAccessible) {
                suggestions.push(`تحسين التباين بين ${colors[i]} و ${colors[i + 1]}`);
                
                // اقتراح لون بديل
                const alternative = this.suggestBetterContrast(colors[i], colors[i + 1]);
                alternativeColors.push(alternative);
                improvementScore += 20;
            } else {
                alternativeColors.push(colors[i + 1]);
                improvementScore += 10;
            }
        }

        // فحص التوازن اللوني
        const temperatures = analyses.map(a => a.temperature);
        const warmCount = temperatures.filter(t => t === 'warm').length;
        const coolCount = temperatures.filter(t => t === 'cool').length;
        const neutralCount = temperatures.filter(t => t === 'neutral').length;

        if (Math.abs(warmCount - coolCount) > colors.length / 2) {
            suggestions.push('تحسين التوازن بين الألوان الدافئة والباردة');
            improvementScore += 15;
        }

        // فحص التشبع والسطوع
        const validAnalyses = analyses.filter(a => a.hsl !== null);
        if (validAnalyses.length > 0) {
            const avgSaturation = validAnalyses.reduce((sum, a) => sum + a.hsl!.s, 0) / validAnalyses.length;
            const avgLightness = validAnalyses.reduce((sum, a) => sum + a.hsl!.l, 0) / validAnalyses.length;

            if (avgSaturation < 20) {
                suggestions.push('زيادة التشبع للحيوية');
                improvementScore += 10;
            } else if (avgSaturation > 80) {
                suggestions.push('تقليل التشبع للتوازن');
                improvementScore += 10;
            }

            if (avgLightness < 20) {
                suggestions.push('زيادة السطوع للوضوح');
                improvementScore += 10;
            } else if (avgLightness > 80) {
                suggestions.push('تقليل السطوع للراحة');
                improvementScore += 10;
            }
        }

        // حساب النقاط النهائية
        const maxScore = colors.length * 20;
        improvementScore = Math.min(100, (improvementScore / maxScore) * 100);

        if (suggestions.length === 0) {
            suggestions.push('الألوان متوازنة ولا تحتاج تحسين');
            improvementScore = 100;
        }

        return {
            suggestions,
            alternativeColors,
            improvementScore: Math.round(improvementScore)
        };
    }

    /** اقتراح تباين أفضل / Suggest better contrast */
    public static suggestBetterContrast(color1: string, color2: string): string {
        const analysis1 = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(color1);
        const analysis2 = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(color2);

        if (!analysis1.hsl || !analysis2.hsl) {
            return color2; // إرجاع اللون الأصلي إذا فشل التحليل
        }

        const { h, s } = analysis2.hsl;
        let { l } = analysis2.hsl;

        // تعديل السطوع لتحسين التباين
        if (analysis1.hsl.l > 50) {
            // اللون الأول فاتح، اجعل الثاني أغمق
            l = Math.max(10, l - 30);
        } else {
            // اللون الأول غامق، اجعل الثاني أفتح
            l = Math.min(90, l + 30);
        }

        const newRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(h, s, l);
        return DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(newRgb.r, newRgb.g, newRgb.b);
    }

    /** تحسين لون واحد / Improve single color */
    public static improveSingleColor(color: string, context: 'background' | 'text' | 'accent' = 'background'): string {
        const analysis = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(color);
        
        if (!analysis.hsl) {
            return color; // إرجاع اللون الأصلي إذا فشل التحليل
        }

        let { h, s, l } = analysis.hsl;

        switch (context) {
            case 'background':
                // خلفيات يجب أن تكون هادئة
                s = Math.min(s, 30);
                l = l > 50 ? Math.min(l, 85) : Math.max(l, 15);
                break;
            case 'text':
                // النصوص يجب أن تكون واضحة
                s = Math.min(s, 20);
                l = l > 50 ? Math.min(l, 20) : Math.max(l, 80);
                break;
            case 'accent':
                // الألوان المميزة يجب أن تكون بارزة
                s = Math.max(s, 60);
                l = Math.max(30, Math.min(70, l));
                break;
        }

        const newRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(h, s, l);
        return DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(newRgb.r, newRgb.g, newRgb.b);
    }

    /** تحليل مشاكل الألوان / Analyze color problems */
    public static analyzeColorProblems(colors: string[]): {
        problems: string[];
        severity: 'low' | 'medium' | 'high';
        fixes: string[];
    } {
        const problems: string[] = [];
        const fixes: string[] = [];

        if (colors.length === 0) {
            return {
                problems: ['لا توجد ألوان للتحليل'],
                severity: 'high',
                fixes: ['إضافة ألوان للوحة']
            };
        }

        // فحص التباين
        let contrastIssues = 0;
        for (let i = 0; i < colors.length - 1; i++) {
            const contrast = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeContrast(colors[i], colors[i + 1]);
            if (!contrast.isAccessible) {
                contrastIssues++;
                problems.push(`تباين ضعيف بين ${colors[i]} و ${colors[i + 1]}`);
                fixes.push(`تحسين التباين بتعديل السطوع`);
            }
        }

        // فحص التنوع
        const analyses = colors.map(color => 
            DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(color)
        );

        const validAnalyses = analyses.filter(a => a.hsl !== null);
        if (validAnalyses.length > 1) {
            const hues = validAnalyses.map(a => a.hsl!.h);
            const hueRange = Math.max(...hues) - Math.min(...hues);
            
            if (hueRange < 30 && colors.length > 2) {
                problems.push('قلة التنوع في الألوان');
                fixes.push('إضافة ألوان متنوعة أكثر');
            }
        }

        // فحص التوازن
        const temperatures = analyses.map(a => a.temperature);
        const warmCount = temperatures.filter(t => t === 'warm').length;
        const coolCount = temperatures.filter(t => t === 'cool').length;

        if (colors.length > 2 && Math.abs(warmCount - coolCount) > colors.length * 0.7) {
            problems.push('عدم توازن في درجات الحرارة اللونية');
            fixes.push('إضافة ألوان متوازنة حرارياً');
        }

        // تحديد مستوى الخطورة
        let severity: 'low' | 'medium' | 'high';
        if (contrastIssues > colors.length / 2) {
            severity = 'high';
        } else if (problems.length > 2) {
            severity = 'medium';
        } else {
            severity = 'low';
        }

        if (problems.length === 0) {
            problems.push('لا توجد مشاكل واضحة');
            fixes.push('الألوان متوازنة');
            severity = 'low';
        }

        return {
            problems,
            severity,
            fixes
        };
    }
}
