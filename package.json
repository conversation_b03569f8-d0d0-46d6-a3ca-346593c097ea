{"name": "youtube-dark-cyber-x", "version": "1.0.0", "description": "مشغل YouTube مخصص مع ميزات متقدمة - Custom YouTube Player with Advanced Features", "main": "dist/presentation/main-application.js", "author": "AI Assistant", "license": "MIT", "homepage": "https://github.com/user/youtube-dark-cyber-x", "scripts": {"start": "npm run build:ts && electron .", "dev": "npm run build:ts && electron . --dev", "build": "npm run build:ts && electron-builder", "build-win": "npm run build:ts && electron-builder --win", "build-portable": "npm run build:ts && electron-builder --win portable", "build:ts": "tsc", "build:watch": "tsc --watch", "test": "jest", "test:unit": "jest --selectProjects unit", "test:integration": "jest --selectProjects integration", "test:e2e": "jest --selectProjects e2e", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:all": "ts-node src/tests/run-tests.ts", "test:ci": "jest --ci --coverage --watchAll=false", "verify": "ts-node src/tests/compliance/final-verification.ts", "verify:constitution": "ts-node src/tests/compliance/constitution-checker.ts", "verify:functionality": "ts-node src/tests/compliance/functionality-tester.ts", "lint": "eslint src/ --ext .ts", "lint:fix": "eslint src/ --ext .ts --fix", "format": "prettier --write src/**/*.ts", "type-check": "tsc --noEmit"}, "build": {"appId": "com.cyberdark.youtube-player", "productName": "YouTube Dark CyberX", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "config/**/*", "node_modules/**/*"], "win": {"target": [{"target": "portable", "arch": ["x64"]}, {"target": "nsis", "arch": ["x64"]}], "requestedExecutionLevel": "asInvoker"}, "portable": {"artifactName": "YouTubePlayer-Portable.exe"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "devDependencies": {"electron": "^28.2.0", "electron-builder": "^24.9.1", "eslint": "^8.50.0", "jest": "^29.7.0", "jest-extended": "^4.0.2", "spectron": "^19.0.0", "prettier": "^3.0.3", "typescript": "^5.3.3", "@types/node": "^20.11.5", "@types/electron": "^1.6.10", "@types/jest": "^29.5.12", "@testing-library/jest-dom": "^6.4.2", "@testing-library/dom": "^9.3.4", "ts-jest": "^29.1.2", "ts-node": "^10.9.1"}, "dependencies": {"electron-store": "^8.1.0", "electron-updater": "^6.1.4"}, "engines": {"node": ">=16.0.0"}, "keywords": ["youtube", "player", "electron", "desktop", "video", "dark-mode", "adblock", "quality-control"]}