/**
 * أداة التحقق من الدستور
 * Constitution verification tool
 * 
 * هذا الملف يحتوي على أداة التحقق من الالتزام بالدستور
 * This file contains the constitution compliance verification tool
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

const fs = require('fs');
const path = require('path');

/**
 * فحص حجم الملفات
 * Check file sizes
 */
function checkFileSizes() {
    const srcDir = path.join(__dirname, '..', 'src');
    const violations = [];
    
    function scanDirectory(dir) {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.ts') || item.endsWith('.js')) {
                const content = fs.readFileSync(fullPath, 'utf8');
                const lineCount = content.split('\n').length;
                
                if (lineCount > 200) {
                    violations.push({
                        file: path.relative(srcDir, fullPath),
                        lines: lineCount,
                        limit: 200
                    });
                }
            }
        }
    }
    
    scanDirectory(srcDir);
    return violations;
}

/**
 * فحص بنية المشروع
 * Check project structure
 */
function checkProjectStructure() {
    const requiredDirs = [
        'src/presentation',
        'src/business',
        'src/data',
        'src/shared',
        'src/infrastructure'
    ];
    
    const violations = [];
    
    for (const dir of requiredDirs) {
        if (!fs.existsSync(dir)) {
            violations.push(`Missing required directory: ${dir}`);
        }
    }
    
    return violations;
}

/**
 * فحص التسمية
 * Check naming conventions
 */
function checkNamingConventions() {
    const srcDir = path.join(__dirname, '..', 'src');
    const violations = [];
    
    function scanDirectory(dir) {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                // Check directory naming (kebab-case)
                if (!/^[a-z]+(-[a-z]+)*$/.test(item)) {
                    violations.push({
                        type: 'directory',
                        path: path.relative(srcDir, fullPath),
                        issue: 'Not kebab-case'
                    });
                }
                scanDirectory(fullPath);
            } else if (item.endsWith('.ts') || item.endsWith('.js')) {
                // Check file naming (kebab-case)
                const nameWithoutExt = item.replace(/\.(ts|js)$/, '');
                if (!/^[a-z]+(-[a-z]+)*$/.test(nameWithoutExt)) {
                    violations.push({
                        type: 'file',
                        path: path.relative(srcDir, fullPath),
                        issue: 'Not kebab-case'
                    });
                }
            }
        }
    }
    
    scanDirectory(srcDir);
    return violations;
}

/**
 * فحص التوثيق
 * Check documentation
 */
function checkDocumentation() {
    const srcDir = path.join(__dirname, '..', 'src');
    const violations = [];
    
    function scanDirectory(dir) {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                scanDirectory(fullPath);
            } else if (item.endsWith('.ts') || item.endsWith('.js')) {
                const content = fs.readFileSync(fullPath, 'utf8');
                
                // Check for file header documentation
                if (!content.includes('/**') || !content.includes('@author')) {
                    violations.push({
                        file: path.relative(srcDir, fullPath),
                        issue: 'Missing file header documentation'
                    });
                }
                
                // Check for function documentation
                const functionMatches = content.match(/export\s+(function|class|interface)/g);
                const docMatches = content.match(/\/\*\*[\s\S]*?\*\//g);
                
                if (functionMatches && functionMatches.length > (docMatches ? docMatches.length : 0)) {
                    violations.push({
                        file: path.relative(srcDir, fullPath),
                        issue: 'Missing function/class documentation'
                    });
                }
            }
        }
    }
    
    scanDirectory(srcDir);
    return violations;
}

/**
 * تشغيل التحقق الشامل
 * Run comprehensive verification
 */
function runVerification() {
    console.log('🔍 بدء التحقق من الدستور / Starting constitution verification...\n');
    
    // فحص حجم الملفات
    console.log('📏 فحص حجم الملفات / Checking file sizes...');
    const fileSizeViolations = checkFileSizes();
    
    // فحص بنية المشروع
    console.log('🏗️ فحص بنية المشروع / Checking project structure...');
    const structureViolations = checkProjectStructure();
    
    // فحص التسمية
    console.log('🏷️ فحص التسمية / Checking naming conventions...');
    const namingViolations = checkNamingConventions();
    
    // فحص التوثيق
    console.log('📚 فحص التوثيق / Checking documentation...');
    const docViolations = checkDocumentation();
    
    // عرض النتائج
    console.log('\n📊 نتائج التحقق / Verification Results:');
    console.log('=' .repeat(50));
    
    // حجم الملفات
    if (fileSizeViolations.length > 0) {
        console.log('\n❌ انتهاكات حجم الملفات / File Size Violations:');
        fileSizeViolations.forEach(v => {
            console.log(`  - ${v.file}: ${v.lines} lines (limit: ${v.limit})`);
        });
    } else {
        console.log('\n✅ حجم الملفات: مطابق / File Sizes: Compliant');
    }
    
    // بنية المشروع
    if (structureViolations.length > 0) {
        console.log('\n❌ انتهاكات بنية المشروع / Project Structure Violations:');
        structureViolations.forEach(v => {
            console.log(`  - ${v}`);
        });
    } else {
        console.log('\n✅ بنية المشروع: مطابقة / Project Structure: Compliant');
    }
    
    // التسمية
    if (namingViolations.length > 0) {
        console.log('\n❌ انتهاكات التسمية / Naming Convention Violations:');
        namingViolations.forEach(v => {
            console.log(`  - ${v.type}: ${v.path} (${v.issue})`);
        });
    } else {
        console.log('\n✅ التسمية: مطابقة / Naming Conventions: Compliant');
    }
    
    // التوثيق
    if (docViolations.length > 0) {
        console.log('\n❌ انتهاكات التوثيق / Documentation Violations:');
        docViolations.forEach(v => {
            console.log(`  - ${v.file}: ${v.issue}`);
        });
    } else {
        console.log('\n✅ التوثيق: مطابق / Documentation: Compliant');
    }
    
    // النتيجة الإجمالية
    const totalViolations = fileSizeViolations.length + structureViolations.length + 
                           namingViolations.length + docViolations.length;
    
    console.log('\n' + '='.repeat(50));
    console.log(`📈 إجمالي الانتهاكات / Total Violations: ${totalViolations}`);
    
    if (totalViolations === 0) {
        console.log('🎉 تهانينا! المشروع مطابق للدستور بالكامل / Congratulations! Project is fully compliant!');
        process.exit(0);
    } else {
        console.log('⚠️ يرجى إصلاح الانتهاكات المذكورة أعلاه / Please fix the violations listed above');
        process.exit(1);
    }
}

// تشغيل التحقق
runVerification();
