/**
 * أنواع التطبيق الأساسية
 * Core application types
 *
 * هذا الملف يحتوي على جميع تعريفات الأنواع المستخدمة في التطبيق
 * This file contains all type definitions used in the application
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// إعادة تصدير الأنواع من الملفات المتخصصة
// Re-export types from specialized files
export * from './settings-types';
export * from './window-types';

/**
 * مستوى التهديد الأمني
 * Security threat level
 */
export enum ThreatLevel {
    LOW = 'low',
    MEDIUM = 'medium',
    HIGH = 'high',
    CRITICAL = 'critical'
}

/**
 * إعدادات الأمان
 * Security configuration
 */
export interface SecurityConfig {
    /** تمكين مانع الإعلانات / Enable ad blocker */
    enableAdBlocker: boolean;
    /** تمكين كشف التهديدات / Enable threat detection */
    enableThreatDetection: boolean;
    /** مستوى الأمان / Security level */
    securityLevel: ThreatLevel;
    /** قائمة النطاقات المحظورة / Blocked domains */
    blockedDomains: string[];
    /** قائمة النطاقات المسموحة / Allowed domains */
    allowedDomains: string[];
    /** تمكين التسجيل الأمني / Enable security logging */
    enableSecurityLogging: boolean;
}

/**
 * معلومات النظام
 * System information
 */
export interface SystemInfo {
    /** نظام التشغيل / Operating system */
    platform: string;
    /** إصدار نظام التشغيل / OS version */
    osVersion: string;
    /** معمارية المعالج / CPU architecture */
    arch: string;
    /** إجمالي الذاكرة / Total memory */
    totalMemory: number;
    /** الذاكرة المتاحة / Available memory */
    availableMemory: number;
    /** معلومات المعالج / CPU info */
    cpuInfo: {
        model: string;
        cores: number;
        speed: number;
    };
}

/**
 * إحصائيات الأداء
 * Performance statistics
 */
export interface PerformanceStats {
    /** استخدام المعالج / CPU usage */
    cpuUsage: number;
    /** استخدام الذاكرة / Memory usage */
    memoryUsage: number;
    /** استخدام القرص / Disk usage */
    diskUsage: number;
    /** استخدام الشبكة / Network usage */
    networkUsage: {
        download: number;
        upload: number;
    };
    /** وقت التشغيل / Uptime */
    uptime: number;
    /** عدد العمليات النشطة / Active processes */
    activeProcesses: number;
}

/**
 * حالة التطبيق
 * Application state
 */
export interface ApplicationState {
    /** هل التطبيق يعمل / Is application running */
    isRunning: boolean;
    /** هل التطبيق جاهز / Is application ready */
    isReady: boolean;
    /** هل التطبيق متصل / Is application connected */
    isConnected: boolean;
    /** الإعدادات الحالية / Current settings */
    currentSettings: ApplicationSettings;
    /** إحصائيات الأداء / Performance stats */
    performanceStats: PerformanceStats;
    /** معلومات النظام / System info */
    systemInfo: SystemInfo;
    /** وقت آخر تحديث / Last updated */
    lastUpdated: Date;
}

/**
 * نتيجة العملية
 * Operation result
 */
export interface OperationResult<T = any> {
    /** هل العملية نجحت / Was operation successful */
    success: boolean;
    /** البيانات المرجعة / Returned data */
    data?: T;
    /** رسالة الخطأ / Error message */
    error?: string;
    /** كود الخطأ / Error code */
    errorCode?: string;
    /** تفاصيل إضافية / Additional details */
    details?: Record<string, any>;
    /** وقت العملية / Operation timestamp */
    timestamp: Date;
}

/**
 * خيارات العملية
 * Operation options
 */
export interface OperationOptions {
    /** مهلة العملية / Operation timeout */
    timeout?: number;
    /** عدد إعادة المحاولة / Retry count */
    retryCount?: number;
    /** تأخير إعادة المحاولة / Retry delay */
    retryDelay?: number;
    /** إلغاء العملية / Cancel operation */
    signal?: AbortSignal;
    /** معرف العملية / Operation ID */
    operationId?: string;
}

/**
 * معلومات الإصدار
 * Version information
 */
export interface VersionInfo {
    /** إصدار التطبيق / Application version */
    appVersion: string;
    /** إصدار Electron / Electron version */
    electronVersion: string;
    /** إصدار Node.js / Node.js version */
    nodeVersion: string;
    /** إصدار Chrome / Chrome version */
    chromeVersion: string;
    /** تاريخ البناء / Build date */
    buildDate: Date;
    /** معرف البناء / Build ID */
    buildId: string;
}
