/**
 * مدير المؤقتات والفترات الزمنية
 * Timer and interval manager
 *
 * هذا الملف يحتوي على منطق إدارة المؤقتات والفترات الزمنية
 * This file contains timer and interval management logic
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// إعادة تصدير المديرين المتخصصين
// Re-export specialized managers
export * from './interval-manager';
export * from './timeout-manager';

import { IntervalManager } from './interval-manager';
import { TimeoutManager } from './timeout-manager';

/**
 * مدير المؤقتات والفترات الزمنية الشامل
 * Comprehensive timer and interval manager
 */
export class TimerManager {
    private readonly timeoutManager: TimeoutManager = new TimeoutManager();
    private readonly intervalManager: IntervalManager = new IntervalManager();

    /**
     * الحصول على مدير المؤقتات
     * Gets the timeout manager
     *
     * @returns TimeoutManager - مدير المؤقتات / Timeout manager
     */
    public getTimeoutManager(): TimeoutManager {
        return this.timeoutManager;
    }

    /**
     * الحصول على مدير الفترات الزمنية
     * Gets the interval manager
     *
     * @returns IntervalManager - مدير الفترات الزمنية / Interval manager
     */
    public getIntervalManager(): IntervalManager {
        return this.intervalManager;
    }

    /**
     * إنشاء مؤقت مُدار
     * Creates a managed timer
     *
     * @param callback - دالة الاستدعاء / Callback function
     * @param delay - التأخير بالميلي ثانية / Delay in milliseconds
     * @returns NodeJS.Timeout - معرف المؤقت / Timer ID
     *
     * @example
     * ```typescript
     * const timerId = timerManager.createTimer(() => {
     *     console.log('Timer executed');
     * }, 1000);
     * ```
     */
    public createTimer(callback: () => void, delay: number): NodeJS.Timeout {
        return this.timeoutManager.createTimer(callback, delay);
    }

    /**
     * إنشاء فترة زمنية مُدارة
     * Creates a managed interval
     *
     * @param callback - دالة الاستدعاء / Callback function
     * @param delay - التأخير بالميلي ثانية / Delay in milliseconds
     * @returns NodeJS.Timeout - معرف الفترة / Interval ID
     *
     * @example
     * ```typescript
     * const intervalId = timerManager.createInterval(() => {
     *     console.log('Interval executed');
     * }, 1000);
     * ```
     */
    public createInterval(callback: () => void, delay: number): NodeJS.Timeout {
        return this.intervalManager.createInterval(callback, delay);
    }

    /**
     * تنظيف جميع المؤقتات والفترات الزمنية
     * Clears all timers and intervals
     *
     * @example
     * ```typescript
     * timerManager.clearAll();
     * ```
     */
    public clearAll(): void {
        this.timeoutManager.clearAll();
        this.intervalManager.clearAll();
    }

    /**
     * الحصول على عدد المؤقتات النشطة
     * Gets the count of active timers
     *
     * @returns number - عدد المؤقتات النشطة / Active timer count
     *
     * @example
     * ```typescript
     * const count = timerManager.getTimerCount();
     * console.log(`Active timers: ${count}`);
     * ```
     */
    public getTimerCount(): number {
        return this.timeoutManager.getTimerCount();
    }

    /**
     * الحصول على عدد الفترات الزمنية النشطة
     * Gets the count of active intervals
     *
     * @returns number - عدد الفترات الزمنية النشطة / Active interval count
     *
     * @example
     * ```typescript
     * const count = timerManager.getIntervalCount();
     * console.log(`Active intervals: ${count}`);
     * ```
     */
    public getIntervalCount(): number {
        return this.intervalManager.getIntervalCount();
    }

    /**
     * الحصول على إجمالي عدد المؤقتات والفترات الزمنية النشطة
     * Gets the total count of active timers and intervals
     *
     * @returns number - إجمالي العدد / Total count
     *
     * @example
     * ```typescript
     * const total = timerManager.getTotalCount();
     * console.log(`Total active: ${total}`);
     * ```
     */
    public getTotalCount(): number {
        return this.getTimerCount() + this.getIntervalCount();
    }
}
