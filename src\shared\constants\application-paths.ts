/**
 * مسارات التطبيق
 * Application paths
 * 
 * هذا الملف يحتوي على جميع مسارات التطبيق
 * This file contains all application paths
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import * as path from 'path';

/**
 * مسارات التطبيق الأساسية
 * Basic application paths
 */
export const APPLICATION_PATHS = {
    USER_DATA: 'userData',
    SETTINGS: 'settings.json',
    LOGS: 'logs',
    CACHE: 'cache',
    TEMP: 'temp',
    DOWNLOADS: 'downloads',
    SCREENSHOTS: 'screenshots',
    BACKUPS: 'backups',
    PLUGINS: 'plugins',
    THEMES: 'themes',
    EXTENSIONS: 'extensions',
    PROFILES: 'profiles'
} as const;

/**
 * مسارات الملفات
 * File paths
 */
export const FILE_PATHS = {
    MAIN_JS: '../../main.js',
    PRELOAD_YOUTUBE: '../../preload/youtube-preload.js',
    PRELOAD_SETTINGS: '../../preload/settings-preload.js',
    SETTINGS_HTML: '../../renderer/settings.html',
    INDEX_HTML: '../../renderer/index.html',
    PACKAGE_JSON: '../../../package.json',
    README_MD: '../../../README.md',
    LICENSE: '../../../LICENSE',
    CHANGELOG_MD: '../../../CHANGELOG.md'
} as const;

/**
 * مسارات الموارد
 * Resource paths
 */
export const RESOURCE_PATHS = {
    ICONS: 'assets/icons',
    IMAGES: 'assets/images',
    FONTS: 'assets/fonts',
    STYLES: 'assets/styles',
    SCRIPTS: 'assets/scripts',
    SOUNDS: 'assets/sounds',
    VIDEOS: 'assets/videos',
    DOCUMENTS: 'assets/documents',
    LOCALES: 'assets/locales',
    TEMPLATES: 'assets/templates'
} as const;

/**
 * مسارات الأيقونات
 * Icon paths
 */
export const ICON_PATHS = {
    APP_ICON: path.join(RESOURCE_PATHS.ICONS, 'app.ico'),
    TRAY_ICON: path.join(RESOURCE_PATHS.ICONS, 'tray.png'),
    WINDOW_ICON: path.join(RESOURCE_PATHS.ICONS, 'window.png'),
    SETTINGS_ICON: path.join(RESOURCE_PATHS.ICONS, 'settings.png'),
    PLAY_ICON: path.join(RESOURCE_PATHS.ICONS, 'play.png'),
    PAUSE_ICON: path.join(RESOURCE_PATHS.ICONS, 'pause.png'),
    STOP_ICON: path.join(RESOURCE_PATHS.ICONS, 'stop.png'),
    VOLUME_ICON: path.join(RESOURCE_PATHS.ICONS, 'volume.png'),
    MUTE_ICON: path.join(RESOURCE_PATHS.ICONS, 'mute.png'),
    FULLSCREEN_ICON: path.join(RESOURCE_PATHS.ICONS, 'fullscreen.png'),
    MINIMIZE_ICON: path.join(RESOURCE_PATHS.ICONS, 'minimize.png'),
    MAXIMIZE_ICON: path.join(RESOURCE_PATHS.ICONS, 'maximize.png'),
    CLOSE_ICON: path.join(RESOURCE_PATHS.ICONS, 'close.png'),
    BACK_ICON: path.join(RESOURCE_PATHS.ICONS, 'back.png'),
    FORWARD_ICON: path.join(RESOURCE_PATHS.ICONS, 'forward.png'),
    REFRESH_ICON: path.join(RESOURCE_PATHS.ICONS, 'refresh.png'),
    HOME_ICON: path.join(RESOURCE_PATHS.ICONS, 'home.png'),
    SEARCH_ICON: path.join(RESOURCE_PATHS.ICONS, 'search.png'),
    DOWNLOAD_ICON: path.join(RESOURCE_PATHS.ICONS, 'download.png'),
    UPLOAD_ICON: path.join(RESOURCE_PATHS.ICONS, 'upload.png'),
    SHARE_ICON: path.join(RESOURCE_PATHS.ICONS, 'share.png'),
    LIKE_ICON: path.join(RESOURCE_PATHS.ICONS, 'like.png'),
    DISLIKE_ICON: path.join(RESOURCE_PATHS.ICONS, 'dislike.png'),
    SUBSCRIBE_ICON: path.join(RESOURCE_PATHS.ICONS, 'subscribe.png'),
    NOTIFICATION_ICON: path.join(RESOURCE_PATHS.ICONS, 'notification.png'),
    ERROR_ICON: path.join(RESOURCE_PATHS.ICONS, 'error.png'),
    WARNING_ICON: path.join(RESOURCE_PATHS.ICONS, 'warning.png'),
    INFO_ICON: path.join(RESOURCE_PATHS.ICONS, 'info.png'),
    SUCCESS_ICON: path.join(RESOURCE_PATHS.ICONS, 'success.png')
} as const;

/**
 * مسارات الأنماط
 * Style paths
 */
export const STYLE_PATHS = {
    MAIN_CSS: path.join(RESOURCE_PATHS.STYLES, 'main.css'),
    DARK_THEME_CSS: path.join(RESOURCE_PATHS.STYLES, 'dark-theme.css'),
    LIGHT_THEME_CSS: path.join(RESOURCE_PATHS.STYLES, 'light-theme.css'),
    SETTINGS_CSS: path.join(RESOURCE_PATHS.STYLES, 'settings.css'),
    PLAYER_CSS: path.join(RESOURCE_PATHS.STYLES, 'player.css'),
    CONTROLS_CSS: path.join(RESOURCE_PATHS.STYLES, 'controls.css'),
    ANIMATIONS_CSS: path.join(RESOURCE_PATHS.STYLES, 'animations.css'),
    RESPONSIVE_CSS: path.join(RESOURCE_PATHS.STYLES, 'responsive.css'),
    PRINT_CSS: path.join(RESOURCE_PATHS.STYLES, 'print.css'),
    ACCESSIBILITY_CSS: path.join(RESOURCE_PATHS.STYLES, 'accessibility.css')
} as const;

/**
 * مسارات السكريبتات
 * Script paths
 */
export const SCRIPT_PATHS = {
    MAIN_JS: path.join(RESOURCE_PATHS.SCRIPTS, 'main.js'),
    UTILS_JS: path.join(RESOURCE_PATHS.SCRIPTS, 'utils.js'),
    API_JS: path.join(RESOURCE_PATHS.SCRIPTS, 'api.js'),
    PLAYER_JS: path.join(RESOURCE_PATHS.SCRIPTS, 'player.js'),
    SETTINGS_JS: path.join(RESOURCE_PATHS.SCRIPTS, 'settings.js'),
    ANALYTICS_JS: path.join(RESOURCE_PATHS.SCRIPTS, 'analytics.js'),
    SECURITY_JS: path.join(RESOURCE_PATHS.SCRIPTS, 'security.js'),
    PERFORMANCE_JS: path.join(RESOURCE_PATHS.SCRIPTS, 'performance.js'),
    VALIDATION_JS: path.join(RESOURCE_PATHS.SCRIPTS, 'validation.js'),
    LOCALIZATION_JS: path.join(RESOURCE_PATHS.SCRIPTS, 'localization.js')
} as const;

/**
 * مسارات قواعد البيانات
 * Database paths
 */
export const DATABASE_PATHS = {
    MAIN_DB: 'database/main.db',
    SETTINGS_DB: 'database/settings.db',
    CACHE_DB: 'database/cache.db',
    LOGS_DB: 'database/logs.db',
    ANALYTICS_DB: 'database/analytics.db',
    USER_DATA_DB: 'database/user-data.db',
    PREFERENCES_DB: 'database/preferences.db',
    HISTORY_DB: 'database/history.db',
    BOOKMARKS_DB: 'database/bookmarks.db',
    DOWNLOADS_DB: 'database/downloads.db'
} as const;

/**
 * مسارات ملفات السجل
 * Log file paths
 */
export const LOG_PATHS = {
    MAIN_LOG: path.join(APPLICATION_PATHS.LOGS, 'main.log'),
    ERROR_LOG: path.join(APPLICATION_PATHS.LOGS, 'error.log'),
    DEBUG_LOG: path.join(APPLICATION_PATHS.LOGS, 'debug.log'),
    PERFORMANCE_LOG: path.join(APPLICATION_PATHS.LOGS, 'performance.log'),
    SECURITY_LOG: path.join(APPLICATION_PATHS.LOGS, 'security.log'),
    NETWORK_LOG: path.join(APPLICATION_PATHS.LOGS, 'network.log'),
    USER_ACTION_LOG: path.join(APPLICATION_PATHS.LOGS, 'user-actions.log'),
    SYSTEM_LOG: path.join(APPLICATION_PATHS.LOGS, 'system.log'),
    CRASH_LOG: path.join(APPLICATION_PATHS.LOGS, 'crash.log'),
    AUDIT_LOG: path.join(APPLICATION_PATHS.LOGS, 'audit.log')
} as const;

/**
 * مسارات ملفات التكوين
 * Configuration file paths
 */
export const CONFIG_PATHS = {
    MAIN_CONFIG: 'config/main.json',
    USER_CONFIG: 'config/user.json',
    SYSTEM_CONFIG: 'config/system.json',
    SECURITY_CONFIG: 'config/security.json',
    NETWORK_CONFIG: 'config/network.json',
    PERFORMANCE_CONFIG: 'config/performance.json',
    THEME_CONFIG: 'config/theme.json',
    LANGUAGE_CONFIG: 'config/language.json',
    PLUGIN_CONFIG: 'config/plugins.json',
    EXTENSION_CONFIG: 'config/extensions.json'
} as const;

/**
 * مسارات ملفات النسخ الاحتياطي
 * Backup file paths
 */
export const BACKUP_PATHS = {
    SETTINGS_BACKUP: path.join(APPLICATION_PATHS.BACKUPS, 'settings-backup.json'),
    DATABASE_BACKUP: path.join(APPLICATION_PATHS.BACKUPS, 'database-backup.db'),
    CONFIG_BACKUP: path.join(APPLICATION_PATHS.BACKUPS, 'config-backup.json'),
    USER_DATA_BACKUP: path.join(APPLICATION_PATHS.BACKUPS, 'user-data-backup.json'),
    LOGS_BACKUP: path.join(APPLICATION_PATHS.BACKUPS, 'logs-backup.zip'),
    FULL_BACKUP: path.join(APPLICATION_PATHS.BACKUPS, 'full-backup.zip'),
    INCREMENTAL_BACKUP: path.join(APPLICATION_PATHS.BACKUPS, 'incremental-backup.zip'),
    AUTOMATIC_BACKUP: path.join(APPLICATION_PATHS.BACKUPS, 'auto-backup.zip'),
    MANUAL_BACKUP: path.join(APPLICATION_PATHS.BACKUPS, 'manual-backup.zip'),
    EMERGENCY_BACKUP: path.join(APPLICATION_PATHS.BACKUPS, 'emergency-backup.zip')
} as const;

/**
 * دالة للحصول على مسار المجلد الرئيسي للتطبيق
 * Function to get application root directory path
 * 
 * @returns مسار المجلد الرئيسي
 */
export function getApplicationRootPath(): string {
    return process.cwd();
}

/**
 * دالة للحصول على مسار مجلد بيانات المستخدم
 * Function to get user data directory path
 * 
 * @returns مسار مجلد بيانات المستخدم
 */
export function getUserDataPath(): string {
    return path.join(getApplicationRootPath(), APPLICATION_PATHS.USER_DATA);
}

/**
 * دالة للحصول على مسار مجلد السجلات
 * Function to get logs directory path
 * 
 * @returns مسار مجلد السجلات
 */
export function getLogsPath(): string {
    return path.join(getUserDataPath(), APPLICATION_PATHS.LOGS);
}

/**
 * دالة للحصول على مسار مجلد التخزين المؤقت
 * Function to get cache directory path
 * 
 * @returns مسار مجلد التخزين المؤقت
 */
export function getCachePath(): string {
    return path.join(getUserDataPath(), APPLICATION_PATHS.CACHE);
}

/**
 * دالة للحصول على مسار ملف الإعدادات
 * Function to get settings file path
 * 
 * @returns مسار ملف الإعدادات
 */
export function getSettingsPath(): string {
    return path.join(getUserDataPath(), APPLICATION_PATHS.SETTINGS);
}

/**
 * دالة للحصول على مسار مجلد النسخ الاحتياطية
 * Function to get backups directory path
 * 
 * @returns مسار مجلد النسخ الاحتياطية
 */
export function getBackupsPath(): string {
    return path.join(getUserDataPath(), APPLICATION_PATHS.BACKUPS);
}

/**
 * دالة للحصول على مسار مجلد التنزيلات
 * Function to get downloads directory path
 * 
 * @returns مسار مجلد التنزيلات
 */
export function getDownloadsPath(): string {
    return path.join(getUserDataPath(), APPLICATION_PATHS.DOWNLOADS);
}

/**
 * دالة للحصول على مسار مجلد لقطات الشاشة
 * Function to get screenshots directory path
 * 
 * @returns مسار مجلد لقطات الشاشة
 */
export function getScreenshotsPath(): string {
    return path.join(getUserDataPath(), APPLICATION_PATHS.SCREENSHOTS);
}

/**
 * دالة للحصول على مسار مجلد الإضافات
 * Function to get plugins directory path
 * 
 * @returns مسار مجلد الإضافات
 */
export function getPluginsPath(): string {
    return path.join(getUserDataPath(), APPLICATION_PATHS.PLUGINS);
}

/**
 * دالة للحصول على مسار مجلد السمات
 * Function to get themes directory path
 * 
 * @returns مسار مجلد السمات
 */
export function getThemesPath(): string {
    return path.join(getUserDataPath(), APPLICATION_PATHS.THEMES);
}

/**
 * دالة للتحقق من وجود مسار
 * Function to check if path exists
 * 
 * @param filePath - المسار المراد التحقق منه
 * @returns true إذا كان المسار موجود
 */
export function pathExists(filePath: string): boolean {
    try {
        const fs = require('fs');
        return fs.existsSync(filePath);
    } catch (error) {
        return false;
    }
}

/**
 * دالة لإنشاء مجلد إذا لم يكن موجود
 * Function to create directory if it doesn't exist
 * 
 * @param dirPath - مسار المجلد
 * @returns true إذا تم الإنشاء بنجاح
 */
export function ensureDirectoryExists(dirPath: string): boolean {
    try {
        const fs = require('fs');
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }
        return true;
    } catch (error) {
        return false;
    }
}

/**
 * دالة للحصول على حجم الملف
 * Function to get file size
 * 
 * @param filePath - مسار الملف
 * @returns حجم الملف بالبايت
 */
export function getFileSize(filePath: string): number {
    try {
        const fs = require('fs');
        const stats = fs.statSync(filePath);
        return stats.size;
    } catch (error) {
        return 0;
    }
}

/**
 * دالة للحصول على تاريخ تعديل الملف
 * Function to get file modification date
 * 
 * @param filePath - مسار الملف
 * @returns تاريخ التعديل
 */
export function getFileModificationDate(filePath: string): Date | null {
    try {
        const fs = require('fs');
        const stats = fs.statSync(filePath);
        return stats.mtime;
    } catch (error) {
        return null;
    }
}
