/**
 * العمليات الأساسية لمراقب الوضع المظلم
 * Basic operations for dark mode observer
 *
 * هذا الملف يجمع جميع العمليات الأساسية من الملفات المتخصصة
 * This file aggregates all basic operations from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeConfig } from './dark-mode-config';
import { DarkModeStylesApplicator } from './dark-mode-styles';

// استيراد الفئات المتخصصة / Import specialized classes
import { DarkModeObserverOperationsBasicCore } from './dark-mode-observer-operations-basic-core';

/**
 * فئة العمليات الأساسية لمراقب الوضع المظلم
 * Basic dark mode observer operations class
 */
export class DarkModeObserverOperationsBasic {
    private readonly config: DarkModeConfig;
    private readonly stylesApplicator: DarkModeStylesApplicator;
    private readonly coreOperations: DarkModeObserverOperationsBasicCore;

    constructor(config: DarkModeConfig, stylesApplicator: DarkModeStylesApplicator) {
        this.config = config;
        this.stylesApplicator = stylesApplicator;
        this.coreOperations = new DarkModeObserverOperationsBasicCore(config, stylesApplicator);
    }

    /** تطبيق الوضع المظلم على العنصر / Apply dark mode to element */
    public applyDarkModeToElement(element: Element): boolean {
        return this.coreOperations.applyDarkModeToElement(element);
    }

    /** إزالة الوضع المظلم من العنصر / Remove dark mode from element */
    public removeDarkModeFromElement(element: Element): boolean {
        return this.coreOperations.removeDarkModeFromElement(element);
    }

    /** تبديل الوضع المظلم / Toggle dark mode */
    public toggleDarkMode(element: Element): boolean {
        return this.coreOperations.toggleDarkMode(element);
    }

    /** التحقق من تطبيق الوضع المظلم / Check if dark mode is applied */
    public isDarkModeApplied(element: Element): boolean {
        return this.coreOperations.isDarkModeApplied(element);
    }

    /** التحقق من إمكانية تطبيق الوضع المظلم / Check if dark mode can be applied */
    public canApplyDarkMode(element: Element): boolean {
        return DarkModeObserverOperationsBasicUtils.canApplyDarkMode(element);
    }


}
