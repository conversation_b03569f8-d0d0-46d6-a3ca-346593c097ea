/**
 * مراقبة الوضع المظلم - أدوات الأحداث الأساسية الجوهرية الرئيسية
 * Dark mode monitoring - Basic core events utilities core main
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */


/**
 * فئة أدوات الأحداث الأساسية الجوهرية الرئيسية لمراقبة الوضع المظلم
 * Basic core events utilities core main for dark mode monitoring
 */
export class DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMain {

    /**
     * إنشاء مستمع أحداث محسن
     * Create optimized event listener
     */
    public static createOptimizedEventListener(
        eventType: string,
        callback: () => void,
        delay: number = 100
    ): EventListener {
        let timeoutId: NodeJS.Timeout;

        return () => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(callback, delay);
        };
    }

    /**
     * إنشاء مستمع أحداث مع تحكم في التكرار
     * Create throttled event listener
     */
    public static createThrottledEventListener(
        callback: () => void,
        delay: number = 250
    ): EventListener {
        let lastCall = 0;

        return () => {
            const now = Date.now();
            if (now - lastCall >= delay) {
                lastCall = now;
                callback();
            }
        };
    }

    /**
     * فحص دعم الأحداث
     * Check event support
     */
    public static checkEventSupport(eventType: string): boolean {
        try {
            const testElement = document.createElement('div');
            const eventName = `on${eventType}`;
            return eventName in testElement;
        } catch {
            return false;
        }
    }

    /**
     * إنشاء حدث مخصص
     * Create custom event
     */
    public static createCustomEvent(eventType: string, detail?: any): CustomEvent {
        return new CustomEvent(eventType, {
            detail,
            bubbles: true,
            cancelable: true
        });
    }

    /**
     * إرسال حدث مخصص
     * Dispatch custom event
     */
    public static dispatchCustomEvent(eventType: string, detail?: any, target: EventTarget = window): boolean {
        const event = this.createCustomEvent(eventType, detail);
        return target.dispatchEvent(event);
    }

    /**
     * تنظيف مستمعي الأحداث
     * Cleanup event listeners
     */
    public static cleanupEventListeners(listeners: Map<string, EventListener[]>): void {
        listeners.forEach((listenerArray, eventType) => {
            listenerArray.forEach(listener => {
                try {
                    window.removeEventListener(eventType, listener);
                    document.removeEventListener(eventType, listener);
                } catch (error) {
                    console.warn(`Failed to remove listener for ${eventType}:`, error);
                }
            });
        });
        listeners.clear();
    }

    /**
     * فحص حالة الأحداث
     * Check events state
     */
    public static checkEventsState(): {
        windowListeners: number;
        documentListeners: number;
        customEvents: string[];
    } {
        // هذا فحص تقريبي لحالة الأحداث
        return {
            windowListeners: 0, // لا يمكن الحصول على العدد الدقيق
            documentListeners: 0, // لا يمكن الحصول على العدد الدقيق
            customEvents: ['theme-change', 'player-update', 'dark-mode-applied']
        };
    }

    /**
     * تسجيل معلومات الأحداث
     * Log events information
     */
    public static logEventsInfo(): void {
        const state = this.checkEventsState();
        console.log('Events State:', state);
    }

    /**
     * فحص صحة مستمع الأحداث - تفويض للأدوات
     * Validate event listener - Delegate to utils
     */
    public static validateEventListener(listener: EventListener): boolean {
        return DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMainUtils.validateEventListener(listener);
    }

    /**
     * فحص صحة نوع الحدث - تفويض للأدوات
     * Validate event type - Delegate to utils
     */
    public static validateEventType(eventType: string): boolean {
        return DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMainUtils.validateEventType(eventType);
    }

    /**
     * إنشاء معرف فريد للحدث - تفويض للأدوات
     * Create unique event identifier - Delegate to utils
     */
    public static createEventId(eventType: string, target?: string): string {
        return DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMainUtils.createEventId(eventType, target);
    }

    /**
     * فحص ما إذا كان الحدث مدعوم في المتصفح - تفويض للأدوات
     * Check if event is supported in browser - Delegate to utils
     */
    public static isBrowserEventSupported(eventType: string): boolean {
        return DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMainUtils.isBrowserEventSupported(eventType);
    }

    /**
     * إنشاء مستمع أحداث آمن - تفويض للأدوات
     * Create safe event listener - Delegate to utils
     */
    public static createSafeEventListener(callback: () => void): EventListener {
        return DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMainUtils.createSafeEventListener(callback);
    }

    /**
     * فحص ما إذا كان العنصر المستهدف صالح - تفويض للأدوات
     * Check if target element is valid - Delegate to utils
     */
    public static isValidEventTarget(target: EventTarget | null): boolean {
        return DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMainUtils.isValidEventTarget(target);
    }

    /**
     * إنشاء تقرير حالة الأحداث - تفويض للأدوات
     * Create events status report - Delegate to utils
     */
    public static createEventsStatusReport(): {
        timestamp: number;
        supportedEvents: string[];
        customEventsCount: number;
        browserSupport: boolean;
    } {
        return DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMainUtils.createEventsStatusReport();
    }
}
