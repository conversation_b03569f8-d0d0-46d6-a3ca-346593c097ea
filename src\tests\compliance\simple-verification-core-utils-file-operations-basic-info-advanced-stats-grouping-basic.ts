/**
 * تجميع الملفات الأساسي
 * Basic file grouping operations
 * 
 * هذا الملف يحتوي على عمليات التجميع الأساسية للملفات
 * This file contains basic file grouping operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvanced } from './simple-verification-core-utils-file-operations-basic-info-advanced';

/**
 * فئة تجميع الملفات الأساسي
 * Basic file grouping class
 */
export class SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsGroupingBasic {

    /**
     * تجميع الملفات حسب الامتداد
     * Group files by extension
     */
    public static groupFilesByExtension(filePaths: string[]): Record<string, string[]> {
        const groups: Record<string, string[]> = {};

        for (const filePath of filePaths) {
            try {
                const info = SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvanced.getDetailedFileInfo(filePath);
                
                if (!info.exists) continue;
                
                const extension = info.extension.toLowerCase() || 'no-extension';
                
                if (!groups[extension]) {
                    groups[extension] = [];
                }
                
                groups[extension].push(filePath);

            } catch (error) {
                // تجاهل الملفات التي لا يمكن قراءتها
                continue;
            }
        }

        return groups;
    }

    /**
     * تجميع الملفات حسب الحجم
     * Group files by size range
     */
    public static groupFilesBySizeRange(filePaths: string[]): {
        small: string[];    // < 1KB
        medium: string[];   // 1KB - 1MB
        large: string[];    // 1MB - 10MB
        huge: string[];     // > 10MB
    } {
        const groups = {
            small: [] as string[],
            medium: [] as string[],
            large: [] as string[],
            huge: [] as string[]
        };

        const KB = 1024;
        const MB = KB * 1024;

        for (const filePath of filePaths) {
            try {
                const info = SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvanced.getDetailedFileInfo(filePath);
                
                if (!info.exists) continue;
                
                if (info.size < KB) {
                    groups.small.push(filePath);
                } else if (info.size < MB) {
                    groups.medium.push(filePath);
                } else if (info.size < 10 * MB) {
                    groups.large.push(filePath);
                } else {
                    groups.huge.push(filePath);
                }

            } catch (error) {
                // تجاهل الملفات التي لا يمكن قراءتها
                continue;
            }
        }

        return groups;
    }

    /**
     * تجميع الملفات حسب المجلد الأب
     * Group files by parent directory
     */
    public static groupFilesByParentDirectory(filePaths: string[]): Record<string, string[]> {
        const groups: Record<string, string[]> = {};

        for (const filePath of filePaths) {
            try {
                const info = SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvanced.getDetailedFileInfo(filePath);
                
                if (!info.exists) continue;
                
                const parentDir = info.directory || 'root';
                
                if (!groups[parentDir]) {
                    groups[parentDir] = [];
                }
                
                groups[parentDir].push(filePath);

            } catch (error) {
                // تجاهل الملفات التي لا يمكن قراءتها
                continue;
            }
        }

        return groups;
    }
}
