/**
 * الوظائف الأساسية لمعالجة الفيديو في الوضع المظلم
 * Core video processing functions for dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeConfig } from './dark-mode-config';

/**
 * فئة الوظائف الأساسية لمعالجة الفيديو
 * Core video processing functions class
 */
export class DarkModeObserverOperationsAdvancedElementsVideoCore {

    /** معالجة عنصر الفيديو الأساسي / Basic video element processing */
    public static processVideoElement(element: HTMLVideoElement, config: DarkModeConfig): void {
        try {
            // إضافة فئة الوضع المظلم
            element.classList.add('dark-mode-video');
            
            // تطبيق الأنماط الأساسية
            this.applyBaseStyles(element);
            
            // كشف نوع الفيديو وتطبيق الأنماط المناسبة
            const videoType = this.detectVideoType(element);
            this.applyTypeSpecificStyles(element, videoType, config);
            
        } catch (error) {
            console.error('خطأ في معالجة عنصر الفيديو:', error);
        }
    }

    /** تطبيق الأنماط الأساسية / Apply base styles */
    private static applyBaseStyles(element: HTMLVideoElement): void {
        const baseStyles = {
            borderRadius: '8px',
            transition: 'all 0.3s ease',
            maxWidth: '100%',
            height: 'auto',
            backgroundColor: '#000000'
        };

        Object.assign(element.style, baseStyles);
    }

    /** كشف نوع الفيديو / Detect video type */
    public static detectVideoType(element: HTMLVideoElement): string {
        const src = element.src.toLowerCase();
        const className = element.className.toLowerCase();
        const parentClassName = element.parentElement?.className.toLowerCase() || '';
        
        // فحص فيديو YouTube الرئيسي
        if (className.includes('video-stream') || 
            parentClassName.includes('html5-video-container') ||
            src.includes('googlevideo.com')) {
            return 'youtube-main';
        }
        
        // فحص الفيديوهات المصغرة
        if (className.includes('thumbnail') || className.includes('preview') ||
            parentClassName.includes('thumbnail') || element.width < 300) {
            return 'thumbnail';
        }
        
        // فحص الإعلانات
        if (className.includes('ad') || parentClassName.includes('ad') ||
            src.includes('doubleclick') || src.includes('googlesyndication')) {
            return 'advertisement';
        }
        
        // فحص الفيديوهات المدمجة
        if (className.includes('embed') || parentClassName.includes('embed')) {
            return 'embedded';
        }
        
        return 'default';
    }

    /** تطبيق أنماط حسب النوع / Apply type-specific styles */
    public static applyTypeSpecificStyles(element: HTMLVideoElement, type: string, config: DarkModeConfig): void {
        switch (type) {
            case 'youtube-main':
                this.applyYouTubeMainStyles(element, config);
                break;
            case 'thumbnail':
                this.applyThumbnailStyles(element);
                break;
            case 'advertisement':
                this.applyAdvertisementStyles(element);
                break;
            case 'embedded':
                this.applyEmbeddedStyles(element);
                break;
            default:
                this.applyDefaultStyles(element);
                break;
        }
    }

    /** تطبيق أنماط فيديو YouTube الرئيسي / Apply YouTube main video styles */
    private static applyYouTubeMainStyles(element: HTMLVideoElement, config: DarkModeConfig): void {
        const styles = {
            borderRadius: '12px',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.6)',
            border: '2px solid rgba(255, 255, 255, 0.1)',
            backgroundColor: '#000000'
        };

        Object.assign(element.style, styles);

        // تطبيق إعدادات خاصة بـ YouTube
        if (config.youtubeEnhancements?.enabled) {
            if (config.youtubeEnhancements.videoQuality) {
                this.setVideoQuality(element, config.youtubeEnhancements.videoQuality);
            }
        }
    }

    /** تطبيق أنماط الفيديوهات المصغرة / Apply thumbnail video styles */
    private static applyThumbnailStyles(element: HTMLVideoElement): void {
        const styles = {
            borderRadius: '6px',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.4)',
            cursor: 'pointer'
        };

        Object.assign(element.style, styles);
    }

    /** تطبيق أنماط الإعلانات / Apply advertisement styles */
    private static applyAdvertisementStyles(element: HTMLVideoElement): void {
        const styles = {
            borderRadius: '4px',
            border: '2px solid rgba(255, 0, 0, 0.3)',
            opacity: '0.7'
        };

        Object.assign(element.style, styles);

        // إضافة تسمية الإعلان
        this.addAdvertisementLabel(element);
    }

    /** تطبيق أنماط الفيديوهات المدمجة / Apply embedded video styles */
    private static applyEmbeddedStyles(element: HTMLVideoElement): void {
        const styles = {
            borderRadius: '8px',
            border: '1px solid rgba(255, 255, 255, 0.15)',
            boxShadow: '0 4px 16px rgba(0, 0, 0, 0.3)'
        };

        Object.assign(element.style, styles);
    }

    /** تطبيق أنماط افتراضية / Apply default styles */
    private static applyDefaultStyles(element: HTMLVideoElement): void {
        const styles = {
            borderRadius: '6px',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)'
        };

        Object.assign(element.style, styles);
    }

    /** تعيين جودة الفيديو / Set video quality */
    private static setVideoQuality(element: HTMLVideoElement, quality: string): void {
        // محاولة تعيين جودة الفيديو عبر YouTube API
        try {
            const player = (window as any).ytPlayer;
            if (player && player.setPlaybackQuality) {
                player.setPlaybackQuality(quality);
            }
        } catch (error) {
            console.warn('تعذر تعيين جودة الفيديو:', error);
        }
    }

    /** إضافة تسمية الإعلان / Add advertisement label */
    private static addAdvertisementLabel(element: HTMLVideoElement): void {
        const container = element.parentElement;
        if (!container) return;

        const label = document.createElement('div');
        label.className = 'dark-mode-ad-label';
        label.textContent = 'إعلان';
        
        const styles = {
            position: 'absolute',
            top: '8px',
            left: '8px',
            backgroundColor: 'rgba(255, 0, 0, 0.8)',
            color: '#ffffff',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            fontWeight: 'bold',
            zIndex: '1000'
        };

        Object.assign(label.style, styles);

        container.style.position = 'relative';
        container.appendChild(label);
    }

    /** إضافة عنصر بديل للفيديو / Add video placeholder */
    public static addVideoPlaceholder(element: HTMLVideoElement, config: DarkModeConfig): void {
        const placeholder = document.createElement('div');
        placeholder.className = 'dark-mode-video-placeholder';
        
        const styles = {
            width: element.style.width || '100%',
            height: element.style.height || '200px',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            border: '2px dashed rgba(255, 255, 255, 0.3)',
            borderRadius: element.style.borderRadius || '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'rgba(255, 255, 255, 0.6)',
            fontSize: '16px',
            fontFamily: 'Arial, sans-serif'
        };

        Object.assign(placeholder.style, styles);
        placeholder.textContent = 'فيديو غير متاح';

        // استبدال العنصر
        if (element.parentElement) {
            element.parentElement.replaceChild(placeholder, element);
        }
    }
}
