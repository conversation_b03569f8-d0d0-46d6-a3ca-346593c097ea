/**
 * معلومات الملفات الأساسية المتقدمة
 * Advanced basic file information operations
 * 
 * هذا الملف يحتوي على عمليات الحصول على معلومات الملفات الأساسية المتقدمة
 * This file contains advanced basic file information operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import * as fs from 'fs';
import { SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStats } from './simple-verification-core-utils-file-operations-basic-info-advanced-stats';
import { SimpleVerificationCoreUtilsFileOperationsBasicInfoCore } from './simple-verification-core-utils-file-operations-basic-info-core';

/**
 * فئة معلومات الملفات الأساسية المتقدمة
 * Advanced basic file information class
 */
export class SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvanced {

    /**
     * الحصول على معلومات الملف المفصلة
     * Get detailed file information
     */
    public static getDetailedFileInfo(filePath: string): {
        exists: boolean;
        size: number;
        extension: string;
        basename: string;
        directory: string;
        isReadable: boolean;
        isWritable: boolean;
        modificationDate: Date | null;
        creationDate: Date | null;
    } {
        try {
            if (!fs.existsSync(filePath)) {
                return {
                    exists: false,
                    size: 0,
                    extension: '',
                    basename: '',
                    directory: '',
                    isReadable: false,
                    isWritable: false,
                    modificationDate: null,
                    creationDate: null
                };
            }

            // الحصول على المعلومات الأساسية
            const basicInfo = SimpleVerificationCoreUtilsFileOperationsBasicInfoCore.getBasicFileInfo(filePath);
            const permissions = SimpleVerificationCoreUtilsFileOperationsBasicInfoCore.getBasicPermissions(filePath);
            const dates = SimpleVerificationCoreUtilsFileOperationsBasicInfoCore.getBasicDates(filePath);

            return {
                exists: basicInfo.exists,
                size: basicInfo.size,
                extension: basicInfo.extension,
                basename: basicInfo.basename,
                directory: basicInfo.directory,
                isReadable: permissions.isReadable,
                isWritable: permissions.isWritable,
                modificationDate: dates.modificationDate,
                creationDate: dates.creationDate
            };

        } catch (error) {
            return {
                exists: false,
                size: 0,
                extension: '',
                basename: '',
                directory: '',
                isReadable: false,
                isWritable: false,
                modificationDate: null,
                creationDate: null
            };
        }
    }

    /**
     * مقارنة ملفين
     * Compare two files
     */
    public static compareFiles(filePath1: string, filePath2: string): {
        bothExist: boolean;
        sameSize: boolean;
        sameModificationDate: boolean;
        sizeDifference: number;
        timeDifference: number;
    } {
        try {
            const info1 = this.getDetailedFileInfo(filePath1);
            const info2 = this.getDetailedFileInfo(filePath2);

            if (!info1.exists || !info2.exists) {
                return {
                    bothExist: false,
                    sameSize: false,
                    sameModificationDate: false,
                    sizeDifference: 0,
                    timeDifference: 0
                };
            }

            const sizeDifference = Math.abs(info1.size - info2.size);
            const timeDifference = info1.modificationDate && info2.modificationDate
                ? Math.abs(info1.modificationDate.getTime() - info2.modificationDate.getTime())
                : 0;

            return {
                bothExist: true,
                sameSize: info1.size === info2.size,
                sameModificationDate: timeDifference === 0,
                sizeDifference,
                timeDifference
            };

        } catch (error) {
            return {
                bothExist: false,
                sameSize: false,
                sameModificationDate: false,
                sizeDifference: 0,
                timeDifference: 0
            };
        }
    }

    /**
     * الحصول على إحصائيات مجموعة ملفات - تفويض لوحدة الإحصائيات
     * Get statistics for a group of files - Delegate to stats module
     */
    public static getFilesStatistics(filePaths: string[]): {
        totalFiles: number;
        existingFiles: number;
        totalSize: number;
        averageSize: number;
        largestFile: string;
        largestSize: number;
        smallestFile: string;
        smallestSize: number;
        extensionCounts: Record<string, number>;
    } {
        // تفويض الحصول على الإحصائيات لوحدة الإحصائيات
        // Delegate statistics retrieval to stats module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStats.getFilesStatistics(filePaths);
    }

    /**
     * فلترة الملفات حسب المعايير - تفويض لوحدة الإحصائيات
     * Filter files by criteria - Delegate to stats module
     */
    public static filterFilesByCriteria(
        filePaths: string[],
        criteria: {
            minSize?: number;
            maxSize?: number;
            extensions?: string[];
            modifiedAfter?: Date;
            modifiedBefore?: Date;
        }
    ): string[] {
        // تفويض فلترة الملفات لوحدة الإحصائيات
        // Delegate file filtering to stats module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStats.filterFilesByCriteria(filePaths, criteria);
    }
}
