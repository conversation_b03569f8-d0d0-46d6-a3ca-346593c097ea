/**
 * مطبق أنماط الوضع المظلم
 * Dark mode styles applicator
 *
 * هذا الملف يجمع جميع وظائف الأنماط من الملفات المتخصصة
 * This file aggregates all style functions from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeConfig } from './dark-mode-config';

// استيراد الفئات المتخصصة / Import specialized classes
import { DarkModeStylesOperations } from './dark-mode-styles-operations';

// إعادة تصدير الثوابت / Re-export constants
export {
    DARK_MODE_ADVANCED_STYLES, DARK_MODE_BASE_STYLES
} from './dark-mode-styles-css-base';

export {
    DARK_MODE_ENHANCEMENT_STYLES, DARK_MODE_PLAYER_STYLES
} from './dark-mode-styles-css-player';

/**
 * فئة مطبق أنماط الوضع المظلم
 * Dark mode styles applicator class
 */
export class DarkModeStylesApplicator {
    private readonly config: DarkModeConfig;
    private readonly operations: DarkModeStylesOperations;
    private styleElement: HTMLStyleElement | null = null;

    constructor(config: DarkModeConfig) {
        this.config = config;
        this.operations = new DarkModeStylesOperations(config);
    }

    /** تطبيق أنماط الوضع المظلم / Apply dark mode styles */
    public applyStyles(): boolean {
        if (typeof document === 'undefined') {
            return false;
        }

        try {
            // إزالة الأنماط الموجودة
            this.operations.removeStyleElementFromPage();

            // إنشاء عنصر جديد
            this.styleElement = this.operations.createStyleElement();

            // بناء CSS
            const css = this.operations.buildCompleteCSS();
            this.styleElement.textContent = css;

            // إضافة للصفحة
            return this.operations.addStyleElementToPage(this.styleElement);
        } catch (error) {
            console.error('خطأ في تطبيق الأنماط:', error);
            return false;
        }
    }

    /** إزالة أنماط الوضع المظلم / Remove dark mode styles */
    public removeStyles(): boolean {
        if (typeof document === 'undefined') {
            return false;
        }

        try {
            const success = this.operations.removeStyleElementFromPage();
            if (success) {
                this.styleElement = null;
            }
            return success;
        } catch (error) {
            console.error('خطأ في إزالة الأنماط:', error);
            return false;
        }
    }

    /** تطبيق أنماط مخصصة / Apply custom styles */
    public applyCustomStyles(customCSS: string): boolean {
        return this.operations.applyCustomStyles(customCSS);
    }

    /** إزالة الأنماط المخصصة / Remove custom styles */
    public removeCustomStyles(): boolean {
        return this.operations.removeCustomStyles();
    }

    /** التحقق من وجود الأنماط / Check if styles exist */
    public stylesExist(): boolean {
        return this.operations.stylesExist();
    }

    /** تنظيف جميع الأنماط / Clean up all styles */
    public cleanupAllStyles(): boolean {
        return this.operations.cleanupAllStyles();
    }
}
