/**
 * فحص جودة الكود
 * Code quality checking operations
 * 
 * هذا الملف يحتوي على عمليات فحص جودة الكود
 * This file contains code quality checking operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import * as fs from 'fs';
import * as path from 'path';
import {
    SimpleVerificationConfig,
    CodeQualityCheckResult,
    DEFAULT_EVALUATION_CRITERIA
} from './simple-verification-types';

/**
 * فئة فحص جودة الكود
 * Code quality checking class
 */
export class SimpleVerificationCoreOperationsQuality {

    /**
     * فحص جودة الكود
     * Check code quality
     */
    public static async checkCodeQuality(
        projectRoot: string,
        config: SimpleVerificationConfig,
        files: string[]
    ): Promise<CodeQualityCheckResult> {
        const issues: string[] = [];
        const recommendations: string[] = [];
        const complexityIssues: string[] = [];
        const typeIssues: string[] = [];
        const securityIssues: string[] = [];
        const performanceIssues: string[] = [];

        try {
            for (const file of files) {
                const content = fs.readFileSync(file, 'utf-8');

                // فحص التعقيد
                const complexityResult = this.checkComplexity(file, content);
                if (complexityResult.issues.length > 0) {
                    complexityIssues.push(...complexityResult.issues);
                    issues.push(...complexityResult.issues);
                }

                // فحص الأنواع
                const typeResult = this.checkTypes(file, content);
                if (typeResult.issues.length > 0) {
                    typeIssues.push(...typeResult.issues);
                    issues.push(...typeResult.issues);
                }

                // فحص الأمان
                const securityResult = this.checkSecurity(file, content);
                if (securityResult.issues.length > 0) {
                    securityIssues.push(...securityResult.issues);
                    issues.push(...securityResult.issues);
                }

                // فحص الأداء
                const performanceResult = this.checkPerformance(file, content);
                if (performanceResult.issues.length > 0) {
                    performanceIssues.push(...performanceResult.issues);
                    issues.push(...performanceResult.issues);
                }
            }

            // إضافة التوصيات
            if (complexityIssues.length > 0) {
                recommendations.push('Break down complex functions into smaller, focused functions');
                recommendations.push('Reduce cyclomatic complexity by simplifying conditional logic');
            }

            if (typeIssues.length > 0) {
                recommendations.push('Add proper TypeScript type annotations');
                recommendations.push('Remove usage of "any" type and use specific types');
            }

            if (securityIssues.length > 0) {
                recommendations.push('Fix security vulnerabilities and add input validation');
                recommendations.push('Use secure coding practices and avoid dangerous patterns');
            }

            if (performanceIssues.length > 0) {
                recommendations.push('Optimize performance bottlenecks');
                recommendations.push('Use efficient algorithms and data structures');
            }

            const score = this.calculateQualityScore(
                files.length,
                complexityIssues.length,
                typeIssues.length,
                securityIssues.length,
                performanceIssues.length
            );

            return {
                score,
                issues,
                recommendations,
                complexityIssues,
                typeIssues,
                securityIssues,
                performanceIssues,
                totalFiles: files.length,
                qualityFiles: files.length - issues.length
            };

        } catch (error) {
            issues.push(`Error during code quality check: ${error}`);
            return {
                score: 0,
                issues,
                recommendations: ['Fix errors and retry code quality check'],
                complexityIssues,
                typeIssues,
                securityIssues,
                performanceIssues,
                totalFiles: files.length,
                qualityFiles: 0
            };
        }
    }

    /**
     * فحص التعقيد
     * Check complexity
     */
    private static checkComplexity(file: string, content: string): { issues: string[] } {
        const issues: string[] = [];

        // فحص طول الدوال
        const functionMatches = content.match(/function\s+\w+\s*\([^)]*\)\s*\{[^}]*\}/g) || [];
        for (const func of functionMatches) {
            const lineCount = func.split('\n').length;
            if (lineCount > 20) {
                issues.push(`${file}: Function exceeds 20 lines (${lineCount} lines)`);
            }
        }

        // فحص التداخل العميق
        const indentationLevels = content.split('\n').map(line => {
            const match = line.match(/^(\s*)/);
            return match ? match[1].length : 0;
        });
        const maxIndentation = Math.max(...indentationLevels);
        if (maxIndentation > 16) { // 4 levels * 4 spaces
            issues.push(`${file}: Excessive nesting depth (${maxIndentation / 4} levels)`);
        }

        return { issues };
    }

    /**
     * فحص الأنواع
     * Check types
     */
    private static checkTypes(file: string, content: string): { issues: string[] } {
        const issues: string[] = [];

        // فحص استخدام any
        if (content.includes(': any') || content.includes('<any>')) {
            issues.push(`${file}: Uses 'any' type - should use specific types`);
        }

        // فحص المتغيرات بدون أنواع
        const variableMatches = content.match(/(?:let|const|var)\s+\w+\s*=/g) || [];
        for (const variable of variableMatches) {
            if (!variable.includes(':')) {
                issues.push(`${file}: Variable without type annotation: ${variable.trim()}`);
            }
        }

        return { issues };
    }

    /**
     * فحص الأمان
     * Check security
     */
    private static checkSecurity(file: string, content: string): { issues: string[] } {
        const issues: string[] = [];

        // فحص eval
        if (content.includes('eval(')) {
            issues.push(`${file}: Uses dangerous 'eval()' function`);
        }

        // فحص innerHTML
        if (content.includes('innerHTML')) {
            issues.push(`${file}: Uses 'innerHTML' which can lead to XSS`);
        }

        // فحص أسرار مكشوفة
        const secretPatterns = [
            /password\s*[:=]\s*['"][^'"]+['"]/i,
            /api[_-]?key\s*[:=]\s*['"][^'"]+['"]/i,
            /secret\s*[:=]\s*['"][^'"]+['"]/i
        ];

        for (const pattern of secretPatterns) {
            if (pattern.test(content)) {
                issues.push(`${file}: Contains hardcoded secrets`);
            }
        }

        return { issues };
    }

    /**
     * فحص الأداء
     * Check performance
     */
    private static checkPerformance(file: string, content: string): { issues: string[] } {
        const issues: string[] = [];

        // فحص الحلقات المتداخلة
        const nestedLoopPattern = /for\s*\([^}]*\{[^}]*for\s*\(/g;
        if (nestedLoopPattern.test(content)) {
            issues.push(`${file}: Contains nested loops which may impact performance`);
        }

        // فحص استخدام console.log في الإنتاج
        if (content.includes('console.log') && !file.includes('test')) {
            issues.push(`${file}: Contains console.log statements`);
        }

        return { issues };
    }

    /**
     * حساب نقاط جودة الكود
     * Calculate code quality score
     */
    private static calculateQualityScore(
        totalFiles: number,
        complexityIssues: number,
        typeIssues: number,
        securityIssues: number,
        performanceIssues: number
    ): number {
        if (totalFiles === 0) return 0;

        const maxPossibleScore = totalFiles * 4; // 4 criteria per file
        const actualViolations = complexityIssues + typeIssues + securityIssues + performanceIssues;
        const actualScore = maxPossibleScore - actualViolations;

        return Math.max(0, Math.round((actualScore / maxPossibleScore) * 100));
    }
}
