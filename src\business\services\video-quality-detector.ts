/**
 * كاشف جودة الفيديو
 * Video quality detector
 *
 * هذا الملف يجمع جميع عمليات كشف جودة الفيديو من الملفات المتخصصة
 * This file aggregates all video quality detection operations from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// استيراد العمليات الأساسية
// Import core operations
export { VideoQualityDetectorCore } from './video-quality-detector-core';

// استيراد العمليات المتقدمة
// Import advanced operations
export { VideoQualityDetectorAdvanced } from './video-quality-detector-advanced';

import { VideoQuality } from '@shared/types';
import { VideoQualityConfig } from './video-quality-config';
import { VideoQualityDetectorAdvanced } from './video-quality-detector-advanced';
import { VideoQualityDetectorCore } from './video-quality-detector-core';

/**
 * فئة كاشف جودة الفيديو / Video quality detector class
 */
export class VideoQualityDetector {
    private readonly coreDetector: VideoQualityDetectorCore;
    private readonly advancedDetector: VideoQualityDetectorAdvanced;

    /**
     * منشئ كاشف الجودة / Quality detector constructor
     */
    constructor(config: VideoQualityConfig) {
        this.coreDetector = new VideoQualityDetectorCore(config);
        this.advancedDetector = new VideoQualityDetectorAdvanced(config, this.coreDetector);
    }

    /**
     * اكتشاف جودة الفيديو الحالية / Detect current video quality
     */
    public detectCurrentQuality(): VideoQuality | null {
        return this.coreDetector.detectQualityFromVideo();
    }

    /**
     * اكتشاف متقدم للجودة / Advanced quality detection
     */
    public async detectQualityAdvanced(): Promise<VideoQuality | null> {
        return await this.advancedDetector.detectQualityAdvanced();
    }

    /**
     * اكتشاف الجودة من النص / Detect quality from text
     */
    public detectQualityFromText(text: string): VideoQuality | null {
        return this.coreDetector.detectQualityFromText(text);
    }

    /**
     * اكتشاف الجودة من عنصر / Detect quality from element
     */
    public detectQualityFromElement(element: Element): VideoQuality | null {
        return this.coreDetector.detectQualityFromElement(element);
    }

    /**
     * البحث عن عناصر الجودة / Find quality elements
     */
    public findQualityElements(): Element[] {
        return this.coreDetector.findQualityElements();
    }

    /**
     * اكتشاف الجودات المتاحة / Detect available qualities
     */
    public async detectAvailableQualities(): Promise<VideoQuality[]> {
        return await this.advancedDetector.detectAvailableQualities();
    }

    /**
     * الحصول على أفضل جودة متاحة / Get best available quality
     */
    public getBestAvailableQuality(availableQualities: VideoQuality[]): VideoQuality | null {
        return this.coreDetector.getBestAvailableQuality(availableQualities);
    }

    /**
     * الحصول على أقل جودة متاحة / Get lowest available quality
     */
    public getLowestAvailableQuality(availableQualities: VideoQuality[]): VideoQuality | null {
        return this.coreDetector.getLowestAvailableQuality(availableQualities);
    }

    /**
     * مقارنة الجودات / Compare qualities
     */
    public compareQualities(quality1: VideoQuality, quality2: VideoQuality): number {
        return this.coreDetector.compareQualities(quality1, quality2);
    }

    /**
     * التحقق من صحة الجودة / Validate quality
     */
    public isValidQuality(quality: string): quality is VideoQuality {
        return this.coreDetector.isValidQuality(quality);
    }

    /**
     * تحديث ذاكرة التخزين المؤقت / Update cache
     */
    public updateCache(key: string, quality: VideoQuality): void {
        this.coreDetector.updateCache(key, quality);
    }

    /**
     * الحصول من ذاكرة التخزين المؤقت / Get from cache
     */
    public getFromCache(key: string): VideoQuality | null {
        return this.coreDetector.getFromCache(key);
    }

    /**
     * مسح ذاكرة التخزين المؤقت / Clear cache
     */
    public clearCache(): void {
        this.coreDetector.clearCache();
    }

    /**
     * الحصول على الأخطاء / Get errors
     */
    public getErrors() {
        return this.advancedDetector.getErrors();
    }

    /**
     * مسح الأخطاء / Clear errors
     */
    public clearErrors(): void {
        this.advancedDetector.clearErrors();
    }

    /**
     * الحصول على إحصائيات الكشف / Get detection statistics
     */
    public getDetectionStatistics() {
        const coreStats = this.coreDetector.getDetectionStats();
        const advancedStats = this.advancedDetector.getDetectionStatistics();

        return {
            core: coreStats,
            advanced: advancedStats
        };
    }
}
