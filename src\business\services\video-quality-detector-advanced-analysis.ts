/**
 * التحليل المتقدم لكشف جودة الفيديو
 * Advanced analysis for video quality detection
 *
 * هذا الملف يجمع جميع وظائف التحليل من الملفات المتخصصة
 * This file aggregates all analysis functions from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQuality } from '@shared/types';
import { VideoQualityConfig, VideoQualityErrorReport } from './video-quality-config';

// استيراد الفئات المتخصصة / Import specialized classes
import { VideoQualityDetectorAnalysisRecommendations } from './video-quality-detector-analysis-recommendations';
import {
    DetectionStatistics,
    VideoQualityDetectorAnalysisStatistics
} from './video-quality-detector-analysis-statistics';

// إعادة تصدير الأنواع / Re-export types
export { DetectionStatistics };

/**
 * تقرير التحليل / Analysis report
 */
export interface AnalysisReport {
    currentQuality: VideoQuality | null;
    detectionMethod: string;
    confidence: number;
    alternativeQualities: VideoQuality[];
    detectionTime: number;
    errors: VideoQualityErrorReport[];
    recommendations: string[];
}

/**
 * فئة التحليل المتقدم لكشف جودة الفيديو
 * Advanced analysis class for video quality detection
 */
export class VideoQualityDetectorAdvancedAnalysis {
    private readonly config: VideoQualityConfig;
    private readonly statistics: VideoQualityDetectorAnalysisStatistics;
    private readonly recommendations: VideoQualityDetectorAnalysisRecommendations;

    constructor(config: VideoQualityConfig) {
        this.config = config;
        this.statistics = new VideoQualityDetectorAnalysisStatistics();
        this.recommendations = new VideoQualityDetectorAnalysisRecommendations();
    }

    /** تحليل شامل للكشف / Comprehensive detection analysis */
    public async performComprehensiveAnalysis(): Promise<AnalysisReport> {
        const startTime = performance.now();
        this.statistics.recordAttempt();

        try {
            // محاكاة تحليل بسيط
            const currentQuality = this.detectFromVideoElement();
            const detectionTime = performance.now() - startTime;

            this.statistics.recordDetectionTime(detectionTime);

            if (currentQuality) {
                this.statistics.recordSuccess();
            }

            const analysis = {
                currentQuality,
                method: 'video_element',
                confidence: currentQuality ? 85 : 0,
                alternatives: [] as VideoQuality[]
            };

            return {
                currentQuality: analysis.currentQuality,
                detectionMethod: analysis.method,
                confidence: analysis.confidence,
                alternativeQualities: analysis.alternatives,
                detectionTime,
                errors: this.statistics.getErrors(),
                recommendations: this.recommendations.generateRecommendations(analysis, this.statistics.getErrors())
            };

        } catch (error) {
            this.statistics.recordError('ANALYSIS_ERROR', `تحليل شامل: ${error}`);
            return {
                currentQuality: null,
                detectionMethod: 'failed',
                confidence: 0,
                alternativeQualities: [],
                detectionTime: performance.now() - startTime,
                errors: this.statistics.getErrors(),
                recommendations: ['إعادة المحاولة', 'فحص اتصال الإنترنت']
            };
        }
    }

    /** الحصول على إحصائيات الكشف / Get detection statistics */
    public getDetectionStatistics(): DetectionStatistics {
        return this.statistics.getDetectionStatistics();
    }

    /** إعادة تعيين الإحصائيات / Reset statistics */
    public resetStatistics(): void {
        this.statistics.resetStatistics();
    }

    /** كشف بسيط من عنصر الفيديو / Simple detection from video element */
    private detectFromVideoElement(): VideoQuality | null {
        try {
            const video = document.querySelector('video') as HTMLVideoElement;
            if (!video || !video.videoHeight) return null;

            const height = video.videoHeight;
            if (height >= 2160) return '2160p';
            if (height >= 1440) return '1440p';
            if (height >= 1080) return '1080p';
            if (height >= 720) return '720p';
            if (height >= 480) return '480p';
            if (height >= 360) return '360p';
            if (height >= 240) return '240p';
            return '144p';
        } catch (error) {
            this.statistics.recordError('VIDEO_ELEMENT_ERROR', `كشف من عنصر الفيديو: ${error}`);
            return null;
        }
    }
}
