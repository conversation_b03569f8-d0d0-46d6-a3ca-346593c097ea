/**
 * مراقبة وأحداث الوضع المظلم - ملف التفويض
 * Dark mode monitoring and events - Delegation file
 * 
 * هذا الملف يفوض جميع العمليات للملفات المتخصصة
 * This file delegates all operations to specialized files
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import {
    DARK_MODE_CONSTANTS,
    DARK_MODE_MESSAGES,
    DarkModeConfig
} from './dark-mode-config';
import { DarkModeObserverMonitoringCore } from './dark-mode-observer-monitoring-core';
import { DarkModeObserverMonitoringEvents } from './dark-mode-observer-monitoring-events';

/**
 * فئة مراقبة وأحداث الوضع المظلم - التفويض
 * Dark mode monitoring and events class - Delegation
 */
export class DarkModeObserverMonitoring {
    private readonly config: DarkModeConfig;
    private readonly coreMonitoring: DarkModeObserverMonitoringCore;
    private readonly eventMonitoring: DarkModeObserverMonitoringEvents;

    /**
     * منشئ فئة مراقبة وأحداث الوضع المظلم
     * Dark mode monitoring and events constructor
     */
    constructor(config: DarkModeConfig) {
        this.config = config;
        this.coreMonitoring = new DarkModeObserverMonitoringCore(config);
        this.eventMonitoring = new DarkModeObserverMonitoringEvents(config);
    }

    /**
     * بدء المراقبة - تفويض للوحدة الأساسية
     * Start monitoring - Delegate to core module
     */
    public startMonitoring(): void {
        // تفويض بدء المراقبة للوحدة الأساسية
        // Delegate start monitoring to core module
        this.coreMonitoring.startMonitoring();
    }

    /**
     * إيقاف المراقبة - تفويض للوحدة الأساسية
     * Stop monitoring - Delegate to core module
     */
    public stopMonitoring(): void {
        // تفويض إيقاف المراقبة للوحدة الأساسية
        // Delegate stop monitoring to core module
        this.coreMonitoring.stopMonitoring();
    }

    /**
     * بدء الاستماع للأحداث - تفويض لوحدة الأحداث
     * Start event listening - Delegate to events module
     */
    public startEventListening(): void {
        // تفويض بدء الاستماع للأحداث لوحدة الأحداث
        // Delegate start event listening to events module
        this.eventMonitoring.startEventListening();
    }

    /**
     * إيقاف الاستماع للأحداث - تفويض لوحدة الأحداث
     * Stop event listening - Delegate to events module
     */
    public stopEventListening(): void {
        // تفويض إيقاف الاستماع للأحداث لوحدة الأحداث
        // Delegate stop event listening to events module
        this.eventMonitoring.stopEventListening();
    }

    /**
     * بدء المراقبة الكاملة - دمج المراقبة والأحداث
     * Start full monitoring - Combine monitoring and events
     */
    public startFullMonitoring(): void {
        try {
            console.log(DARK_MODE_MESSAGES.OBSERVER_STARTING);
            
            // بدء المراقبة الأساسية
            this.startMonitoring();
            
            // بدء الاستماع للأحداث
            this.startEventListening();
            
            console.log(DARK_MODE_MESSAGES.OBSERVER_STARTED);
        } catch (error) {
            console.error(DARK_MODE_MESSAGES.OBSERVER_ERROR, error);
        }
    }

    /**
     * إيقاف المراقبة الكاملة - إيقاف المراقبة والأحداث
     * Stop full monitoring - Stop monitoring and events
     */
    public stopFullMonitoring(): void {
        try {
            console.log('Stopping full monitoring...');
            
            // إيقاف المراقبة الأساسية
            this.stopMonitoring();
            
            // إيقاف الاستماع للأحداث
            this.stopEventListening();
            
            console.log(DARK_MODE_MESSAGES.OBSERVER_STOPPED);
        } catch (error) {
            console.error(DARK_MODE_MESSAGES.OBSERVER_ERROR, error);
        }
    }

    /**
     * إعادة تشغيل المراقبة الكاملة
     * Restart full monitoring
     */
    public restartFullMonitoring(): void {
        console.log('Restarting full monitoring...');
        this.stopFullMonitoring();
        setTimeout(() => {
            this.startFullMonitoring();
        }, 200);
    }

    /**
     * الحصول على حالة المراقبة - تفويض للوحدة الأساسية
     * Get monitoring status - Delegate to core module
     */
    public isMonitoring(): boolean {
        // تفويض الحصول على حالة المراقبة للوحدة الأساسية
        // Delegate get monitoring status to core module
        return this.coreMonitoring.isMonitoring();
    }

    /**
     * الحصول على حالة الاستماع للأحداث - تفويض لوحدة الأحداث
     * Get event listening status - Delegate to events module
     */
    public isEventListening(): boolean {
        // تفويض الحصول على حالة الاستماع للأحداث لوحدة الأحداث
        // Delegate get event listening status to events module
        return this.eventMonitoring.isEventListening();
    }

    /**
     * إرسال حدث مخصص - تفويض لوحدة الأحداث
     * Dispatch custom event - Delegate to events module
     */
    public dispatchCustomEvent(eventType: string, detail: any): void {
        // تفويض إرسال الحدث المخصص لوحدة الأحداث
        // Delegate dispatch custom event to events module
        this.eventMonitoring.dispatchCustomEvent(eventType, detail);
    }

    /**
     * الحصول على حالة المراقبة الكاملة
     * Get full monitoring status
     */
    public getFullMonitoringStatus(): {
        coreMonitoring: boolean;
        eventListening: boolean;
        activeListeners: number;
        activeEventTypes: string[];
    } {
        return {
            coreMonitoring: this.isMonitoring(),
            eventListening: this.isEventListening(),
            activeListeners: this.eventMonitoring.getActiveListenersCount(),
            activeEventTypes: this.eventMonitoring.getActiveEventTypes()
        };
    }

    /**
     * إعادة تشغيل المراقبة الأساسية - تفويض للوحدة الأساسية
     * Restart core monitoring - Delegate to core module
     */
    public restartMonitoring(): void {
        // تفويض إعادة تشغيل المراقبة الأساسية للوحدة الأساسية
        // Delegate restart core monitoring to core module
        this.coreMonitoring.restartMonitoring();
    }

    /**
     * إعادة تشغيل الاستماع للأحداث - تفويض لوحدة الأحداث
     * Restart event listening - Delegate to events module
     */
    public restartEventListening(): void {
        // تفويض إعادة تشغيل الاستماع للأحداث لوحدة الأحداث
        // Delegate restart event listening to events module
        this.eventMonitoring.restartEventListening();
    }
}
