/**
 * معالجة العناصر العامة المتقدمة لمراقب الوضع المظلم
 * Advanced generic element processing for dark mode observer
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// استيراد الفئات المتخصصة / Import specialized classes
import { DarkModeConfig } from './dark-mode-config';
import { DarkModeObserverOperationsAdvancedElementsGenericForms } from './dark-mode-observer-operations-advanced-elements-generic-forms';
import { DarkModeObserverOperationsAdvancedElementsGenericIframe } from './dark-mode-observer-operations-advanced-elements-generic-iframe';

/**
 * فئة معالجة العناصر العامة المتقدمة
 * Advanced generic element processing class
 */
export class DarkModeObserverOperationsAdvancedElementsGeneric {

    /** معالجة عنصر iframe / Process iframe element */
    public static processIframeElement(element: HTMLIFrameElement, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsGenericIframe.processIframeElement(element, config);
    }

    /** معالجة عنصر النموذج / Process form element */
    public static processFormElement(element: HTMLFormElement, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsGenericForms.processFormElement(element, config);
    }

    /** كشف نوع iframe / Detect iframe type */
    public static detectIframeType(element: HTMLIFrameElement): string {
        return DarkModeObserverOperationsAdvancedElementsGenericIframe.detectIframeType(element);
    }

    /** إضافة عنصر بديل لـ iframe / Add iframe placeholder */
    public static addIframePlaceholder(element: HTMLIFrameElement, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsGenericIframe.addIframePlaceholder(element, config);
    }

    /** معالجة رسائل الخطأ / Process error messages */
    public static processErrorMessages(form: HTMLFormElement): void {
        DarkModeObserverOperationsAdvancedElementsGenericForms.processErrorMessages(form);
    }

    /** معالجة رسائل النجاح / Process success messages */
    public static processSuccessMessages(form: HTMLFormElement): void {
        DarkModeObserverOperationsAdvancedElementsGenericForms.processSuccessMessages(form);
    }

    /** معالجة عنصر عام / Process generic element */
    public static processGenericElement(element: Element, config: DarkModeConfig): void {
        try {
            const tagName = element.tagName.toLowerCase();

            switch (tagName) {
                case 'iframe':
                    this.processIframeElement(element as HTMLIFrameElement, config);
                    break;
                case 'form':
                    this.processFormElement(element as HTMLFormElement, config);
                    break;
                case 'div':
                case 'span':
                case 'section':
                case 'article':
                    this.processContainerElement(element as HTMLElement, config);
                    break;
                case 'a':
                    this.processLinkElement(element as HTMLAnchorElement, config);
                    break;
                case 'p':
                case 'h1':
                case 'h2':
                case 'h3':
                case 'h4':
                case 'h5':
                case 'h6':
                    this.processTextElement(element as HTMLElement, config);
                    break;
                default:
                    this.processDefaultElement(element as HTMLElement, config);
                    break;
            }

        } catch (error) {
            console.error('خطأ في معالجة العنصر العام:', error);
        }
    }

    /** معالجة عنصر الحاوي / Process container element */
    private static processContainerElement(element: HTMLElement, config: DarkModeConfig): void {
        // تطبيق أنماط أساسية للحاويات
        if (element.className.includes('container') || element.className.includes('wrapper')) {
            element.style.backgroundColor = 'rgba(255, 255, 255, 0.02)';
            element.style.borderRadius = '4px';
        }
    }

    /** معالجة عنصر الرابط / Process link element */
    private static processLinkElement(element: HTMLAnchorElement, config: DarkModeConfig): void {
        const styles = {
            color: '#4da6ff',
            textDecoration: 'none',
            transition: 'color 0.3s ease'
        };

        Object.assign(element.style, styles);

        // إضافة تأثيرات التفاعل
        element.addEventListener('mouseenter', () => {
            element.style.color = '#66b3ff';
            element.style.textDecoration = 'underline';
        });

        element.addEventListener('mouseleave', () => {
            element.style.color = '#4da6ff';
            element.style.textDecoration = 'none';
        });
    }

    /** معالجة عنصر النص / Process text element */
    private static processTextElement(element: HTMLElement, config: DarkModeConfig): void {
        const styles = {
            color: '#ffffff',
            lineHeight: '1.6'
        };

        Object.assign(element.style, styles);

        // تطبيق أنماط خاصة للعناوين
        const tagName = element.tagName.toLowerCase();
        if (tagName.startsWith('h')) {
            element.style.fontWeight = 'bold';
            element.style.marginBottom = '16px';
        }
    }

    /** معالجة عنصر افتراضي / Process default element */
    private static processDefaultElement(element: HTMLElement, config: DarkModeConfig): void {
        // تطبيق أنماط أساسية للعناصر غير المحددة
        if (element.style.backgroundColor && element.style.backgroundColor !== 'transparent') {
            element.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
        }

        if (element.style.color && element.style.color !== 'inherit') {
            element.style.color = '#ffffff';
        }
    }
}