/**
 * مطبق جودة الفيديو
 * Video quality applicator
 *
 * هذا الملف يحتوي على منطق تطبيق جودة الفيديو
 * This file contains logic for applying video quality
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// إعادة تصدير المكونات المتخصصة
// Re-export specialized components
export * from './video-quality-application-engine';
export * from './video-quality-dom-helper';

import { ValidationResult, VideoQuality } from '@shared/types';
import { VideoQualityApplicationEngine } from './video-quality-application-engine';
import {
    VIDEO_QUALITY_MESSAGES,
    VideoQualityConfig
} from './video-quality-config';
import { VideoQualityDomHelper } from './video-quality-dom-helper';

/**
 * فئة مطبق جودة الفيديو
 * Video quality applicator class
 */
export class VideoQualityApplicator {
    private readonly config: VideoQualityConfig;
    private readonly applicationEngine: VideoQualityApplicationEngine;
    private isApplying: boolean = false;

    /**
     * منشئ مطبق الجودة
     * Quality applicator constructor
     *
     * @param config - تكوين جودة الفيديو
     */
    constructor(config: VideoQualityConfig) {
        this.config = config;
        this.applicationEngine = new VideoQualityApplicationEngine(config);
    }

    /**
     * تطبيق جودة الفيديو
     * Apply video quality
     *
     * @param quality - الجودة المطلوبة
     * @returns Promise<ValidationResult> - نتيجة التطبيق
     */
    public async applyQuality(quality: VideoQuality): Promise<ValidationResult> {
        if (this.isApplying) {
            return {
                isValid: false,
                errors: [{
                    field: 'quality',
                    message: 'تطبيق الجودة قيد التنفيذ / Quality application in progress',
                    code: 'QUALITY_APPLYING'
                }]
            };
        }

        this.isApplying = true;
        this.applicationEngine.resetRetryCount();

        try {
            const result = await this.applyQualityWithRetry(quality);
            return result;
        } finally {
            this.isApplying = false;
        }
    }

    /**
     * تطبيق الجودة مع إعادة المحاولة
     * Apply quality with retry
     *
     * @param quality - الجودة المطلوبة
     * @returns Promise<ValidationResult>
     */
    private async applyQualityWithRetry(quality: VideoQuality): Promise<ValidationResult> {
        while (this.applicationEngine.getRetryCount() < this.config.retryAttempts) {
            try {
                const result = await this.applicationEngine.attemptQualityApplication(quality);
                if (result.isValid) {
                    console.log(`${VIDEO_QUALITY_MESSAGES.QUALITY_SET}: ${quality}`);
                    return result;
                }

                this.applicationEngine.incrementRetryCount();
                if (this.applicationEngine.getRetryCount() < this.config.retryAttempts) {
                    console.log(`${VIDEO_QUALITY_MESSAGES.RETRY_ATTEMPT} ${this.applicationEngine.getRetryCount()}`);
                    await this.delay(this.config.retryDelay);
                }

            } catch (error) {
                this.applicationEngine.incrementRetryCount();
                if (this.applicationEngine.getRetryCount() >= this.config.retryAttempts) {
                    return {
                        isValid: false,
                        errors: [{
                            field: 'quality',
                            message: `${VIDEO_QUALITY_MESSAGES.ERROR_SET_QUALITY}: ${error}`,
                            code: 'QUALITY_SET_ERROR'
                        }]
                    };
                }
                await this.delay(this.config.retryDelay);
            }
        }

        return {
            isValid: false,
            errors: [{
                field: 'quality',
                message: VIDEO_QUALITY_MESSAGES.MAX_RETRIES_REACHED,
                code: 'MAX_RETRIES_REACHED'
            }]
        };
    }

    /**
     * إخفاء محدد الجودة
     * Hide quality selector
     *
     * @returns ValidationResult
     */
    public hideQualitySelector(): ValidationResult {
        return VideoQualityDomHelper.injectHidingStyles();
    }

    /**
     * إظهار محدد الجودة
     * Show quality selector
     *
     * @returns ValidationResult
     */
    public showQualitySelector(): ValidationResult {
        return VideoQualityDomHelper.removeHidingStyles();
    }

    /**
     * تأخير
     * Delay
     *
     * @param ms - المدة بالميلي ثانية
     * @returns Promise<void>
     */
    private delay(ms: number): Promise<void> {
        return VideoQualityDomHelper.delay(ms);
    }

