/**
 * اختبارات تكامل طبقة الأمان
 * Security layer integration tests
 * 
 * هذا الملف يحتوي على اختبارات التكامل لطبقة الأمان
 * This file contains integration tests for security layer
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { SecurityLayer } from '@infrastructure/security/security-layer';
import { ResourceManager } from '@shared/utils/resource-manager';
import { ThreatLevel } from '@shared/types';

describe('SecurityLayer Integration Tests', () => {
    let securityLayer: SecurityLayer;
    let resourceManager: ResourceManager;

    beforeEach(() => {
        resourceManager = new ResourceManager();
        securityLayer = new SecurityLayer({
            enableAdBlocking: true,
            enableRequestValidation: true,
            enableContentSanitization: true,
            enableThreatReporting: true,
            strictMode: false
        }, resourceManager);
    });

    afterEach(() => {
        securityLayer.cleanup();
    });

    describe('Full Security Workflow', () => {
        it('should initialize and provide comprehensive protection', async () => {
            // Act
            const initResult = await securityLayer.initialize();
            const status = securityLayer.getStatus();

            // Assert
            expect(initResult.isValid).toBe(true);
            expect(status.isInitialized).toBe(true);
            expect(status.adBlockerEnabled).toBe(true);
            expect(status.requestValidationEnabled).toBe(true);
            expect(status.contentSanitizationEnabled).toBe(true);
            expect(status.threatReportingEnabled).toBe(true);
        });

        it('should handle complete ad blocking workflow', async () => {
            // Arrange
            await securityLayer.initialize();
            const adUrls = [
                'https://googleads.g.doubleclick.net/ads',
                'https://pagead2.googlesyndication.com/ads',
                'https://ads.yahoo.com/banner'
            ];
            const safeUrls = [
                'https://www.youtube.com/watch?v=123',
                'https://googlevideo.com/videoplayback'
            ];

            // Act & Assert
            adUrls.forEach(url => {
                expect(securityLayer.shouldBlockRequest(url)).toBe(true);
            });

            safeUrls.forEach(url => {
                expect(securityLayer.shouldBlockRequest(url)).toBe(false);
            });
        });

        it('should integrate request validation with threat reporting', async () => {
            // Arrange
            await securityLayer.initialize();
            const maliciousUrls = [
                'https://malware-site.com/download',
                'https://phishing-site.org/login',
                'javascript:alert("xss")'
            ];

            // Act
            maliciousUrls.forEach(url => {
                const result = securityLayer.validateRequest(url, 'GET');
                expect(result.isValid).toBe(false);
            });

            const report = securityLayer.getSecurityReport();

            // Assert
            expect(report.totalThreats).toBe(maliciousUrls.length);
            expect(report.securityLevel).toBeOneOf([ThreatLevel.HIGH, ThreatLevel.CRITICAL]);
            expect(report.recommendations).toContain('يُنصح بتفعيل الوضع الصارم للأمان');
        });

        it('should sanitize content while preserving safe elements', async () => {
            // Arrange
            await securityLayer.initialize();
            const mixedContent = `
                <div class="safe-content">
                    <h1>Safe Title</h1>
                    <p>Safe paragraph with <strong>bold text</strong></p>
                    <script>alert("malicious")</script>
                    <img src="safe-image.jpg" alt="Safe image">
                    <a href="javascript:alert('xss')">Malicious link</a>
                    <div onclick="malicious()">Unsafe div</div>
                </div>
            `;

            // Act
            const sanitized = securityLayer.sanitizeContent(mixedContent);

            // Assert
            expect(sanitized).toContain('Safe Title');
            expect(sanitized).toContain('<strong>bold text</strong>');
            expect(sanitized).toContain('safe-image.jpg');
            expect(sanitized).not.toContain('<script>');
            expect(sanitized).not.toContain('javascript:');
            expect(sanitized).not.toContain('onclick');
        });
    });

    describe('Configuration Changes', () => {
        it('should handle ad blocker toggle during runtime', async () => {
            // Arrange
            await securityLayer.initialize();
            const adUrl = 'https://ads.example.com/banner';

            // Act - Initially enabled
            expect(securityLayer.shouldBlockRequest(adUrl)).toBe(true);

            // Disable ad blocker
            const disableResult = securityLayer.disableAdBlocker();
            expect(disableResult.isValid).toBe(true);
            expect(securityLayer.shouldBlockRequest(adUrl)).toBe(false);

            // Re-enable ad blocker
            const enableResult = securityLayer.enableAdBlocker();
            expect(enableResult.isValid).toBe(true);
            expect(securityLayer.shouldBlockRequest(adUrl)).toBe(true);
        });

        it('should provide accurate security reports over time', async () => {
            // Arrange
            await securityLayer.initialize();

            // Act - Generate some security events
            for (let i = 0; i < 5; i++) {
                securityLayer.shouldBlockRequest(`https://ads-${i}.com/banner`);
                securityLayer.validateRequest(`https://malware-${i}.com`, 'GET');
            }

            const report = securityLayer.getSecurityReport();

            // Assert
            expect(report.blockedRequests).toBe(5);
            expect(report.totalThreats).toBe(5);
            expect(report.timestamp).toBeInstanceOf(Date);
            expect(report.recommendations).toBeInstanceOf(Array);
        });
    });

    describe('Resource Management Integration', () => {
        it('should properly manage resources through lifecycle', async () => {
            // Arrange
            const initialStats = resourceManager.getStats();

            // Act
            await securityLayer.initialize();
            
            // Simulate some activity
            for (let i = 0; i < 10; i++) {
                securityLayer.shouldBlockRequest(`https://test-${i}.com`);
            }

            const activeStats = resourceManager.getStats();
            
            // Cleanup
            securityLayer.cleanup();
            const finalStats = resourceManager.getStats();

            // Assert
            expect(activeStats.totalResources).toBeGreaterThanOrEqual(initialStats.totalResources);
            expect(finalStats.totalResources).toBeLessThanOrEqual(activeStats.totalResources);
        });

        it('should not have memory leaks after extended use', async () => {
            // Arrange
            await securityLayer.initialize();

            // Act - Simulate heavy usage
            for (let i = 0; i < 1000; i++) {
                securityLayer.shouldBlockRequest(`https://test-${i}.com`);
                securityLayer.validateRequest(`https://site-${i}.com`, 'GET');
                securityLayer.sanitizeContent(`<div>Content ${i}</div>`);
            }

            const hasLeaks = resourceManager.hasMemoryLeaks();

            // Assert
            expect(hasLeaks).toBe(false);
        });
    });

    describe('Error Handling and Recovery', () => {
        it('should handle initialization failures gracefully', async () => {
            // Arrange
            const faultySecurityLayer = new SecurityLayer({
                enableAdBlocking: true,
                enableRequestValidation: true,
                enableContentSanitization: true,
                enableThreatReporting: true,
                strictMode: false
            });

            // Mock a failure scenario
            jest.spyOn(faultySecurityLayer as any, 'adBlockerService', 'get').mockReturnValue({
                enable: () => ({ isValid: false, errors: [{ field: 'test', message: 'Mock error', code: 'MOCK' }] })
            });

            // Act
            const result = await faultySecurityLayer.initialize();

            // Assert
            expect(result.isValid).toBe(false);
            expect(result.errors).toHaveLength(1);
        });

        it('should continue operating with partial failures', async () => {
            // Arrange
            await securityLayer.initialize();

            // Act - Simulate partial failure by disabling ad blocker
            securityLayer.disableAdBlocker();

            // Assert - Other functions should still work
            const validationResult = securityLayer.validateRequest('https://malicious.com', 'GET');
            const sanitizedContent = securityLayer.sanitizeContent('<script>alert("test")</script>');
            const status = securityLayer.getStatus();

            expect(validationResult.isValid).toBe(false);
            expect(sanitizedContent).not.toContain('<script>');
            expect(status.requestValidationEnabled).toBe(true);
            expect(status.contentSanitizationEnabled).toBe(true);
        });
    });

    describe('Performance Under Load', () => {
        it('should maintain performance with concurrent operations', async () => {
            // Arrange
            await securityLayer.initialize();
            const startTime = Date.now();

            // Act - Simulate concurrent operations
            const promises = [];
            for (let i = 0; i < 100; i++) {
                promises.push(
                    Promise.resolve().then(() => {
                        securityLayer.shouldBlockRequest(`https://test-${i}.com`);
                        securityLayer.validateRequest(`https://site-${i}.com`, 'GET');
                        return securityLayer.sanitizeContent(`<div>Content ${i}</div>`);
                    })
                );
            }

            await Promise.all(promises);
            const endTime = Date.now();

            // Assert
            const duration = endTime - startTime;
            expect(duration).toBeLessThan(1000); // Should complete in less than 1 second
        });

        it('should handle rapid configuration changes', async () => {
            // Arrange
            await securityLayer.initialize();

            // Act - Rapid toggle operations
            for (let i = 0; i < 10; i++) {
                securityLayer.disableAdBlocker();
                securityLayer.enableAdBlocker();
            }

            const finalStatus = securityLayer.getStatus();

            // Assert
            expect(finalStatus.adBlockerEnabled).toBe(true);
            expect(finalStatus.isInitialized).toBe(true);
        });
    });

    describe('Real-world Scenarios', () => {
        it('should handle typical YouTube browsing session', async () => {
            // Arrange
            await securityLayer.initialize();
            const youtubeRequests = [
                'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'https://googlevideo.com/videoplayback?id=123',
                'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
                'https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxK.woff2',
                'https://googleads.g.doubleclick.net/ads/123', // Should be blocked
                'https://pagead2.googlesyndication.com/ads/456' // Should be blocked
            ];

            // Act
            const results = youtubeRequests.map(url => ({
                url,
                shouldBlock: securityLayer.shouldBlockRequest(url),
                isValid: securityLayer.validateRequest(url, 'GET').isValid
            }));

            // Assert
            const blockedCount = results.filter(r => r.shouldBlock).length;
            const validCount = results.filter(r => r.isValid).length;

            expect(blockedCount).toBe(2); // Only ad URLs should be blocked
            expect(validCount).toBe(4); // All non-ad URLs should be valid
        });
    });
});
