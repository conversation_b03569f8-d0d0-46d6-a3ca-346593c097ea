/**
 * أنواع إعدادات النوافذ
 * Window settings types
 * 
 * هذا الملف يحتوي على تعريفات أنواع إعدادات النوافذ والواجهة
 * This file contains window and interface settings type definitions
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * إعدادات النافذة الأساسية
 * Basic window settings
 */
export interface WindowSettings {
    /** عرض النافذة / Window width */
    width: number;
    /** ارتفاع النافذة / Window height */
    height: number;
    /** موقع X / X position */
    x?: number;
    /** موقع Y / Y position */
    y?: number;
    /** تكبير النافذة / Maximized */
    maximized?: boolean;
    /** ملء الشاشة / Fullscreen */
    fullscreen?: boolean;
    /** دائماً في المقدمة / Always on top */
    alwaysOnTop?: boolean;
    /** قابلة لتغيير الحجم / Resizable */
    resizable?: boolean;
    /** الحد الأدنى للعرض / Minimum width */
    minWidth?: number;
    /** الحد الأدنى للارتفاع / Minimum height */
    minHeight?: number;
    /** الحد الأقصى للعرض / Maximum width */
    maxWidth?: number;
    /** الحد الأقصى للارتفاع / Maximum height */
    maxHeight?: number;
}

/**
 * إعدادات شريط العنوان
 * Title bar settings
 */
export interface TitleBarSettings {
    /** إظهار شريط العنوان / Show title bar */
    show: boolean;
    /** لون الخلفية / Background color */
    backgroundColor?: string;
    /** لون النص / Text color */
    textColor?: string;
    /** عنوان النافذة / Window title */
    title?: string;
    /** إظهار الأيقونة / Show icon */
    showIcon?: boolean;
    /** مسار الأيقونة / Icon path */
    iconPath?: string;
    /** إظهار أزرار التحكم / Show control buttons */
    showControls?: boolean;
}

/**
 * إعدادات الشفافية
 * Transparency settings
 */
export interface TransparencySettings {
    /** تمكين الشفافية / Enable transparency */
    enabled: boolean;
    /** مستوى الشفافية / Transparency level */
    level: number;
    /** شفافية عند عدم التركيز / Transparency when unfocused */
    unfocusedLevel?: number;
    /** تأثير الضبابية / Blur effect */
    blurEffect?: boolean;
    /** نوع التأثير / Effect type */
    effectType?: 'none' | 'blur' | 'acrylic' | 'mica';
}

/**
 * إعدادات الحدود
 * Border settings
 */
export interface BorderSettings {
    /** إظهار الحدود / Show borders */
    show: boolean;
    /** عرض الحدود / Border width */
    width?: number;
    /** لون الحدود / Border color */
    color?: string;
    /** نمط الحدود / Border style */
    style?: 'solid' | 'dashed' | 'dotted' | 'double';
    /** نصف قطر الزوايا / Border radius */
    radius?: number;
}

/**
 * إعدادات الظلال
 * Shadow settings
 */
export interface ShadowSettings {
    /** إظهار الظلال / Show shadows */
    show: boolean;
    /** لون الظل / Shadow color */
    color?: string;
    /** ضبابية الظل / Shadow blur */
    blur?: number;
    /** إزاحة X / X offset */
    offsetX?: number;
    /** إزاحة Y / Y offset */
    offsetY?: number;
    /** انتشار الظل / Shadow spread */
    spread?: number;
}

/**
 * إعدادات الرسوم المتحركة
 * Animation settings
 */
export interface AnimationSettings {
    /** تمكين الرسوم المتحركة / Enable animations */
    enabled: boolean;
    /** مدة الرسوم المتحركة / Animation duration */
    duration: number;
    /** نوع التسهيل / Easing type */
    easing: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out';
    /** رسوم متحركة للفتح / Open animation */
    openAnimation?: 'fade' | 'slide' | 'scale' | 'bounce';
    /** رسوم متحركة للإغلاق / Close animation */
    closeAnimation?: 'fade' | 'slide' | 'scale' | 'bounce';
    /** رسوم متحركة لتغيير الحجم / Resize animation */
    resizeAnimation?: boolean;
}

/**
 * إعدادات الواجهة
 * Interface settings
 */
export interface InterfaceSettings {
    /** السمة / Theme */
    theme: 'light' | 'dark' | 'auto' | 'system';
    /** حجم الخط / Font size */
    fontSize: number;
    /** عائلة الخط / Font family */
    fontFamily: string;
    /** لون الخلفية الأساسي / Primary background color */
    primaryBackgroundColor?: string;
    /** لون الخلفية الثانوي / Secondary background color */
    secondaryBackgroundColor?: string;
    /** لون النص الأساسي / Primary text color */
    primaryTextColor?: string;
    /** لون النص الثانوي / Secondary text color */
    secondaryTextColor?: string;
    /** لون التمييز / Accent color */
    accentColor?: string;
}

/**
 * إعدادات شريط الأدوات
 * Toolbar settings
 */
export interface ToolbarSettings {
    /** إظهار شريط الأدوات / Show toolbar */
    show: boolean;
    /** موقع شريط الأدوات / Toolbar position */
    position: 'top' | 'bottom' | 'left' | 'right';
    /** حجم الأيقونات / Icon size */
    iconSize: 'small' | 'medium' | 'large';
    /** إظهار النصوص / Show text */
    showText: boolean;
    /** الأزرار المرئية / Visible buttons */
    visibleButtons: string[];
    /** الأزرار المخفية / Hidden buttons */
    hiddenButtons: string[];
}

/**
 * إعدادات شريط الحالة
 * Status bar settings
 */
export interface StatusBarSettings {
    /** إظهار شريط الحالة / Show status bar */
    show: boolean;
    /** موقع شريط الحالة / Status bar position */
    position: 'top' | 'bottom';
    /** العناصر المرئية / Visible items */
    visibleItems: string[];
    /** العناصر المخفية / Hidden items */
    hiddenItems: string[];
    /** تحديث تلقائي / Auto update */
    autoUpdate: boolean;
    /** فترة التحديث / Update interval */
    updateInterval: number;
}

/**
 * إعدادات النافذة الشاملة
 * Comprehensive window settings
 */
export interface ComprehensiveWindowSettings {
    /** إعدادات النافذة الأساسية / Basic window settings */
    window: WindowSettings;
    /** إعدادات شريط العنوان / Title bar settings */
    titleBar: TitleBarSettings;
    /** إعدادات الشفافية / Transparency settings */
    transparency: TransparencySettings;
    /** إعدادات الحدود / Border settings */
    borders: BorderSettings;
    /** إعدادات الظلال / Shadow settings */
    shadows: ShadowSettings;
    /** إعدادات الرسوم المتحركة / Animation settings */
    animations: AnimationSettings;
    /** إعدادات الواجهة / Interface settings */
    interface: InterfaceSettings;
    /** إعدادات شريط الأدوات / Toolbar settings */
    toolbar: ToolbarSettings;
    /** إعدادات شريط الحالة / Status bar settings */
    statusBar: StatusBarSettings;
}

/**
 * حالة النافذة
 * Window state
 */
export interface WindowState {
    /** معرف النافذة / Window ID */
    id: string;
    /** نوع النافذة / Window type */
    type: 'main' | 'settings' | 'about' | 'help' | 'popup';
    /** مفتوحة / Open */
    isOpen: boolean;
    /** مركزة / Focused */
    isFocused: boolean;
    /** مرئية / Visible */
    isVisible: boolean;
    /** مصغرة / Minimized */
    isMinimized: boolean;
    /** مكبرة / Maximized */
    isMaximized: boolean;
    /** ملء الشاشة / Fullscreen */
    isFullscreen: boolean;
    /** الإعدادات الحالية / Current settings */
    currentSettings: ComprehensiveWindowSettings;
}

/**
 * مدير النوافذ
 * Window manager interface
 */
export interface WindowManager {
    /** إنشاء نافذة / Create window */
    createWindow(type: string, settings: ComprehensiveWindowSettings): Promise<string>;
    /** إغلاق نافذة / Close window */
    closeWindow(id: string): Promise<boolean>;
    /** تحديث إعدادات النافذة / Update window settings */
    updateWindowSettings(id: string, settings: Partial<ComprehensiveWindowSettings>): Promise<boolean>;
    /** الحصول على حالة النافذة / Get window state */
    getWindowState(id: string): Promise<WindowState | null>;
    /** الحصول على جميع النوافذ / Get all windows */
    getAllWindows(): Promise<WindowState[]>;
}
