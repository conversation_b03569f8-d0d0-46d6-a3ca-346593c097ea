/**
 * فحص الامتثال للدستور
 * Constitution compliance checking operations
 * 
 * هذا الملف يحتوي على عمليات فحص الامتثال للدستور
 * This file contains constitution compliance checking operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import * as fs from 'fs';
import * as path from 'path';
import {
    SimpleVerificationConfig,
    ConstitutionCheckResult,
    DEFAULT_EVALUATION_CRITERIA
} from './simple-verification-types';

/**
 * فئة فحص الامتثال للدستور
 * Constitution compliance checking class
 */
export class SimpleVerificationCoreOperationsConstitution {

    /**
     * فحص الامتثال للدستور
     * Check constitution compliance
     */
    public static async checkConstitutionCompliance(
        projectRoot: string,
        config: SimpleVerificationConfig,
        files: string[]
    ): Promise<ConstitutionCheckResult> {
        const issues: string[] = [];
        const recommendations: string[] = [];
        const fileSizeViolations: string[] = [];
        const namingViolations: string[] = [];
        const documentationIssues: string[] = [];
        const structureIssues: string[] = [];

        try {
            for (const file of files) {
                const stats = fs.statSync(file);
                const content = fs.readFileSync(file, 'utf-8');
                const lineCount = content.split('\n').length;

                // فحص حجم الملف
                if (lineCount > config.maxFileLines) {
                    fileSizeViolations.push(`${file}: ${lineCount} lines (limit: ${config.maxFileLines})`);
                    issues.push(`File ${file} exceeds maximum line count: ${lineCount} > ${config.maxFileLines}`);
                }

                // فحص تسمية الملفات
                const fileName = path.basename(file);
                if (!this.isValidFileName(fileName)) {
                    namingViolations.push(`${file}: Invalid naming convention`);
                    issues.push(`File ${file} does not follow kebab-case naming convention`);
                }

                // فحص التوثيق
                if (!this.hasProperDocumentation(content)) {
                    documentationIssues.push(`${file}: Missing or insufficient documentation`);
                    issues.push(`File ${file} lacks proper JSDoc documentation`);
                }

                // فحص البنية
                if (!this.hasProperStructure(content)) {
                    structureIssues.push(`${file}: Poor code structure`);
                    issues.push(`File ${file} has structural issues`);
                }
            }

            // إضافة التوصيات
            if (fileSizeViolations.length > 0) {
                recommendations.push('Break down large files into smaller, focused modules');
                recommendations.push('Use barrel exports to maintain clean interfaces');
            }

            if (namingViolations.length > 0) {
                recommendations.push('Rename files to follow kebab-case convention');
                recommendations.push('Ensure all file names are descriptive and consistent');
            }

            if (documentationIssues.length > 0) {
                recommendations.push('Add comprehensive JSDoc documentation to all functions and classes');
                recommendations.push('Include bilingual documentation (Arabic/English)');
            }

            if (structureIssues.length > 0) {
                recommendations.push('Refactor code to follow single responsibility principle');
                recommendations.push('Improve code organization and modularity');
            }

            const score = this.calculateConstitutionScore(
                files.length,
                fileSizeViolations.length,
                namingViolations.length,
                documentationIssues.length,
                structureIssues.length
            );

            return {
                score,
                issues,
                recommendations,
                fileSizeViolations,
                namingViolations,
                documentationIssues,
                structureIssues,
                totalFiles: files.length,
                compliantFiles: files.length - issues.length
            };

        } catch (error) {
            issues.push(`Error during constitution compliance check: ${error}`);
            return {
                score: 0,
                issues,
                recommendations: ['Fix errors and retry constitution compliance check'],
                fileSizeViolations,
                namingViolations,
                documentationIssues,
                structureIssues,
                totalFiles: files.length,
                compliantFiles: 0
            };
        }
    }

    /**
     * التحقق من صحة اسم الملف
     * Check if file name is valid
     */
    private static isValidFileName(fileName: string): boolean {
        // فحص kebab-case للملفات TypeScript
        const kebabCasePattern = /^[a-z0-9]+(-[a-z0-9]+)*\.(ts|js|json|md)$/;
        return kebabCasePattern.test(fileName);
    }

    /**
     * التحقق من وجود توثيق مناسب
     * Check if file has proper documentation
     */
    private static hasProperDocumentation(content: string): boolean {
        // فحص وجود JSDoc
        const hasJSDoc = content.includes('/**') && content.includes('*/');
        const hasFileHeader = content.includes('@author') && content.includes('@version');
        const hasBilingualComments = content.includes('//') && (
            content.includes('تفويض') || content.includes('Delegate') ||
            content.includes('العملية') || content.includes('Operation')
        );
        
        return hasJSDoc && hasFileHeader && hasBilingualComments;
    }

    /**
     * التحقق من البنية المناسبة
     * Check if file has proper structure
     */
    private static hasProperStructure(content: string): boolean {
        // فحص وجود imports منظمة
        const hasOrganizedImports = content.includes('import') && !content.includes('import *');
        
        // فحص وجود exports
        const hasExports = content.includes('export');
        
        // فحص عدم وجود any type
        const noAnyType = !content.includes(': any') && !content.includes('<any>');
        
        return hasOrganizedImports && hasExports && noAnyType;
    }

    /**
     * حساب نقاط الامتثال للدستور
     * Calculate constitution compliance score
     */
    private static calculateConstitutionScore(
        totalFiles: number,
        fileSizeViolations: number,
        namingViolations: number,
        documentationIssues: number,
        structureIssues: number
    ): number {
        if (totalFiles === 0) return 0;

        const maxPossibleScore = totalFiles * 4; // 4 criteria per file
        const actualViolations = fileSizeViolations + namingViolations + documentationIssues + structureIssues;
        const actualScore = maxPossibleScore - actualViolations;

        return Math.max(0, Math.round((actualScore / maxPossibleScore) * 100));
    }
}
