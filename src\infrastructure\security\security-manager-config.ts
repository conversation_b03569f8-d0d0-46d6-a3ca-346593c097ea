/**
 * تكوين وأنواع مدير الأمان
 * Security manager configuration and types
 * 
 * هذا الملف يحتوي على التكوينات والأنواع الخاصة بمدير الأمان
 * This file contains security manager configurations and types
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ThreatLevel } from '@shared/types';

/**
 * تعداد أنواع التهديدات الأمنية
 * Security threat types enumeration
 */
export enum SecurityThreatType {
    XSS_ATTACK = 'XSS_ATTACK',
    SQL_INJECTION = 'SQL_INJECTION',
    MALICIOUS_URL = 'MALICIOUS_URL',
    SUSPICIOUS_SCRIPT = 'SUSPICIOUS_SCRIPT',
    UNAUTHORIZED_ACCESS = 'UNAUTHORIZED_ACCESS',
    RESOURCE_EXHAUSTION = 'RESOURCE_EXHAUSTION'
}

/**
 * واجهة تقرير التهديد الأمني
 * Security threat report interface
 */
export interface SecurityThreatReport {
    readonly threatType: SecurityThreatType;
    readonly threatLevel: ThreatLevel;
    readonly description: string;
    readonly timestamp: Date;
    readonly source: string;
    readonly blocked: boolean;
}

/**
 * واجهة إحصائيات الأمان
 * Security statistics interface
 */
export interface SecurityStats {
    readonly totalThreats: number;
    readonly blockedThreats: number;
    readonly allowedRequests: number;
    readonly highRiskThreats: number;
    readonly mediumRiskThreats: number;
    readonly lowRiskThreats: number;
    readonly startTime: Date;
}

/**
 * واجهة تكوين مدير الأمان
 * Security manager configuration interface
 */
export interface SecurityManagerConfig {
    readonly enableXSSProtection: boolean;
    readonly enableSQLInjectionProtection: boolean;
    readonly enableURLValidation: boolean;
    readonly enableScriptValidation: boolean;
    readonly maxThreatReports: number;
    readonly threatReportRetentionDays: number;
    readonly autoBlockHighRiskThreats: boolean;
    readonly logSecurityEvents: boolean;
}

/**
 * الإعدادات الافتراضية لمدير الأمان
 * Default security manager settings
 */
export const DEFAULT_SECURITY_MANAGER_CONFIG: SecurityManagerConfig = {
    enableXSSProtection: true,
    enableSQLInjectionProtection: true,
    enableURLValidation: true,
    enableScriptValidation: true,
    maxThreatReports: 1000,
    threatReportRetentionDays: 30,
    autoBlockHighRiskThreats: true,
    logSecurityEvents: true
};

/**
 * أنماط التهديدات الأمنية
 * Security threat patterns
 */
export const SECURITY_THREAT_PATTERNS = {
    XSS_PATTERNS: [
        /<script[^>]*>.*?<\/script>/gi,
        /javascript:/gi,
        /on\w+\s*=/gi,
        /<iframe[^>]*>.*?<\/iframe>/gi,
        /<object[^>]*>.*?<\/object>/gi,
        /<embed[^>]*>/gi,
        /eval\s*\(/gi,
        /document\.write/gi,
        /innerHTML/gi
    ],
    SQL_INJECTION_PATTERNS: [
        /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
        /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
        /('|\"|;|--|\*|\/\*|\*\/)/gi,
        /(\b(SCRIPT|DECLARE|CAST|CONVERT)\b)/gi
    ],
    MALICIOUS_URL_PATTERNS: [
        /javascript:/gi,
        /data:text\/html/gi,
        /vbscript:/gi,
        /file:\/\//gi,
        /ftp:\/\/.*\.(exe|bat|cmd|scr|pif|com)/gi
    ],
    SUSPICIOUS_SCRIPT_PATTERNS: [
        /eval\s*\(/gi,
        /Function\s*\(/gi,
        /setTimeout\s*\(/gi,
        /setInterval\s*\(/gi,
        /document\.write/gi,
        /document\.writeln/gi,
        /window\.open/gi,
        /location\.href/gi,
        /location\.replace/gi
    ]
} as const;

/**
 * رسائل الأمان
 * Security messages
 */
export const SECURITY_MESSAGES = {
    XSS_DETECTED: 'تم اكتشاف محاولة XSS / XSS attempt detected',
    SQL_INJECTION_DETECTED: 'تم اكتشاف محاولة SQL Injection / SQL injection attempt detected',
    MALICIOUS_URL_DETECTED: 'تم اكتشاف رابط ضار / Malicious URL detected',
    SUSPICIOUS_SCRIPT_DETECTED: 'تم اكتشاف سكريبت مشبوه / Suspicious script detected',
    UNAUTHORIZED_ACCESS_DETECTED: 'تم اكتشاف وصول غير مصرح / Unauthorized access detected',
    RESOURCE_EXHAUSTION_DETECTED: 'تم اكتشاف استنزاف موارد / Resource exhaustion detected',
    THREAT_BLOCKED: 'تم حظر التهديد / Threat blocked',
    THREAT_ALLOWED: 'تم السماح بالطلب / Request allowed',
    SECURITY_INITIALIZED: 'تم تهيئة نظام الأمان / Security system initialized',
    SECURITY_DISABLED: 'تم إيقاف نظام الأمان / Security system disabled'
} as const;

/**
 * حدود الأمان
 * Security limits
 */
export const SECURITY_MANAGER_LIMITS = {
    MAX_INPUT_LENGTH: 10000,
    MAX_URL_LENGTH: 2048,
    MAX_SCRIPT_LENGTH: 50000,
    MAX_THREAT_REPORTS_PER_MINUTE: 100,
    MAX_CONCURRENT_VALIDATIONS: 50,
    VALIDATION_TIMEOUT_MS: 5000
} as const;
