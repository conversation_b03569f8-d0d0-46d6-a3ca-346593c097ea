/**
 * أنواع مدير الوضع المظلم
 * Dark mode manager types
 * 
 * هذا الملف يحتوي على تعريفات أنواع المدير والواجهات للوضع المظلم
 * This file contains manager and interface type definitions for dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ValidationResult } from '@shared/types';
import {
    DarkModeConfig,
    DarkModeExportSettings,
    DarkModeImportOptions,
    DarkModeThemeColors,
    ThemeType
} from './dark-mode-config-types';
import {
    DarkModeApplicationOptions,
    DarkModeApplicationResult,
    DarkModeCompatibility,
    DarkModeExportData,
    DarkModeState,
    DarkModeStats,
    PerformanceIssue,
    PerformanceMetrics
} from './dark-mode-state-types';

/**
 * واجهة مدير الوضع المظلم
 * Dark mode manager interface
 */
export interface DarkModeManager {
    readonly isEnabled: boolean;
    readonly currentState: DarkModeState;
    readonly currentTheme: ThemeType;
    readonly config: DarkModeConfig;
    readonly stats: DarkModeStats;

    initialize(): Promise<ValidationResult>;
    enable(options?: DarkModeApplicationOptions): Promise<DarkModeApplicationResult>;
    disable(): Promise<ValidationResult>;
    toggle(): Promise<DarkModeApplicationResult>;
    setTheme(theme: ThemeType): Promise<DarkModeApplicationResult>;
    updateConfig(config: Partial<DarkModeConfig>): Promise<ValidationResult>;
    applyCustomStyles(styles: string): Promise<DarkModeApplicationResult>;
    detectSystemTheme(): ThemeType;
    exportSettings(options?: DarkModeExportSettings): DarkModeExportData;
    importSettings(data: DarkModeExportData, options?: DarkModeImportOptions): Promise<ValidationResult>;
    getCompatibility(): DarkModeCompatibility;
    cleanup(): void;
}

/**
 * واجهة مطبق الأنماط
 * Style applicator interface
 */
export interface DarkModeStyleApplicator {
    applyStyles(styles: string, options?: DarkModeApplicationOptions): Promise<DarkModeApplicationResult>;
    removeStyles(): Promise<ValidationResult>;
    injectCSS(css: string): Promise<ValidationResult>;
    generateThemeCSS(colors: DarkModeThemeColors): string;
    optimizeStyles(styles: string): string;
}

/**
 * واجهة كاشف الثيم
 * Theme detector interface
 */
export interface DarkModeThemeDetector {
    detectCurrentTheme(): ThemeType;
    detectSystemPreference(): ThemeType;
    detectBrowserTheme(): ThemeType;
    isSystemDarkMode(): boolean;
    watchSystemChanges(callback: (theme: ThemeType) => void): void;
    stopWatching(): void;
}

/**
 * واجهة مراقب الأداء
 * Performance monitor interface
 */
export interface DarkModePerformanceMonitor {
    startMonitoring(): void;
    stopMonitoring(): void;
    getMetrics(): PerformanceMetrics;
}

/**
 * واجهة مراقب DOM
 * DOM observer interface
 */
export interface DarkModeDOMObserver {
    startObserving(): Promise<ValidationResult>;
    stopObserving(): void;
    observeElement(element: Element): void;
    onMutation(callback: (mutations: MutationRecord[]) => void): void;
}

/**
 * واجهة مدير الأحداث
 * Event manager interface
 */
export interface DarkModeEventManager {
    addEventListener(event: string, callback: Function): void;
    removeEventListener(event: string, callback: Function): void;
    dispatchEvent(event: string, data?: any): void;
}

/**
 * واجهة مدير التخزين
 * Storage manager interface
 */
export interface DarkModeStorageManager {
    saveConfig(config: DarkModeConfig): Promise<ValidationResult>;
    loadConfig(): Promise<DarkModeConfig | null>;
    clearStorage(): Promise<ValidationResult>;
    exportData(): Promise<DarkModeExportData>;
}

/**
 * واجهة مدير الأنماط
 * Style manager interface
 */
export interface DarkModeStyleManager {
    createStyleSheet(): CSSStyleSheet;
    addRule(selector: string, styles: string): void;
    removeRule(selector: string): void;
    clearAllRules(): void;
}

/**
 * واجهة مدير التوافق
 * Compatibility manager interface
 */
export interface DarkModeCompatibilityManager {
    checkBrowserSupport(): DarkModeCompatibility;
    checkFeatureSupport(feature: string): boolean;
    getFallbackTheme(): ThemeType;
    applyFallback(): Promise<ValidationResult>;
    reportCompatibilityIssues(): string[];
}

/**
 * واجهة مدير الأداء
 * Performance manager interface
 */
export interface DarkModePerformanceManager {
    measurePerformance<T>(operation: () => T): { result: T; duration: number };
    trackMemoryUsage(): number;
    optimizeForPerformance(): Promise<ValidationResult>;
}

/**
 * نوع تقرير الأداء
 * Performance report type
 */
export type PerformanceReport = {
    readonly metrics: PerformanceMetrics;
    readonly issues: PerformanceIssue[];
    readonly recommendations: string[];
    readonly score: number;
};

/**
 * نوع مصنع المدير
 * Manager factory type
 */
export type ManagerFactory = {
    createStyleApplicator(): DarkModeStyleApplicator;
    createThemeDetector(): DarkModeThemeDetector;
    createPerformanceMonitor(): DarkModePerformanceMonitor;
    createDOMObserver(): DarkModeDOMObserver;
    createEventManager(): DarkModeEventManager;
    createStorageManager(): DarkModeStorageManager;
};

/**
 * نوع خيارات المدير
 * Manager options type
 */
export type ManagerOptions = {
    readonly enableLogging: boolean;
    readonly enablePerformanceMonitoring: boolean;
    readonly maxRetries: number;
    readonly timeout: number;
};

/**
 * نوع حالة المدير
 * Manager state type
 */
export type ManagerState = {
    readonly isInitialized: boolean;
    readonly isActive: boolean;
};
