/**
 * مدقق الإعدادات المتقدم
 * Advanced settings validator
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ApplicationConfig } from '@shared/types';
import {
    SETTINGS_MESSAGES,
    SETTINGS_VALIDATION_RULES,
    SettingsErrorReport,
    SettingsErrorType,
    SettingsValidationResult
} from './settings-config';
import { SettingsValidatorBasic } from './settings-validator-basic';

/**
 * فئة مدقق الإعدادات المتقدم
 */
export class SettingsValidatorAdvanced {
    private basicValidator: SettingsValidatorBasic;
    private validationErrors: SettingsErrorReport[] = [];

    constructor() {
        this.basicValidator = new SettingsValidatorBasic();
    }

    /**
     * التحقق من صحة جميع الإعدادات / Validate all settings
     */
    public validateAllSettings(settings: ApplicationConfig): SettingsValidationResult {
        this.validationErrors = [];
        const errors: Array<{ key: string; message: string; code: string; value?: unknown }> = [];
        const warnings: Array<{ key: string; message: string; value?: unknown }> = [];

        try {
            // التحقق من كل إعداد باستخدام المدقق الأساسي
            for (const [key, value] of Object.entries(settings)) {
                const validation = this.basicValidator.validateSingleSetting(
                    key as keyof ApplicationConfig, 
                    value
                );
                
                if (!validation.isValid) {
                    errors.push({
                        key,
                        message: validation.errors.join(', '),
                        code: 'VALIDATION_ERROR',
                        value
                    });
                }
            }

            // التحقق من التوافق بين الإعدادات
            const compatibilityCheck = this.validateSettingsCompatibility(settings);
            if (!compatibilityCheck.isValid) {
                errors.push(...compatibilityCheck.errors.map(error => ({
                    key: 'compatibility',
                    message: error,
                    code: 'COMPATIBILITY_ERROR'
                })));
            }

            // التحقق من الأداء
            const performanceCheck = this.validatePerformanceSettings(settings);
            if (performanceCheck.warnings.length > 0) {
                warnings.push(...performanceCheck.warnings.map(warning => ({
                    key: 'performance',
                    message: warning,
                    value: undefined
                })));
            }

            return {
                isValid: errors.length === 0,
                errors,
                warnings,
                validatedSettings: settings
            };

        } catch (error) {
            return {
                isValid: false,
                errors: [{
                    key: 'general',
                    message: `خطأ عام في التحقق: ${error}`,
                    code: 'GENERAL_ERROR'
                }],
                warnings: [],
                validatedSettings: settings
            };
        }
    }

    /**
     * التحقق من توافق الإعدادات / Validate settings compatibility
     */
    private validateSettingsCompatibility(settings: ApplicationConfig): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        try {
            // التحقق من توافق جودة الفيديو مع الأداء
            if (settings.videoQuality === '2160p' && settings.playbackSpeed > 1.5) {
                errors.push('جودة 4K مع سرعة تشغيل عالية قد تؤثر على الأداء');
            }

            // التحقق من توافق الوضع المظلم مع المظهر
            if (settings.darkMode === true && settings.theme === 'light') {
                errors.push('تضارب بين الوضع المظلم والمظهر الفاتح');
            }

            // التحقق من توافق مستوى الصوت مع التشغيل التلقائي
            if (settings.autoPlay === true && settings.volume === 0) {
                errors.push('التشغيل التلقائي مع كتم الصوت قد لا يكون مفيداً');
            }

        } catch (error) {
            errors.push(`خطأ في فحص التوافق: ${error}`);
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * التحقق من إعدادات الأداء / Validate performance settings
     */
    private validatePerformanceSettings(settings: ApplicationConfig): { 
        isValid: boolean; 
        warnings: string[] 
    } {
        const warnings: string[] = [];

        try {
            // تحذيرات الأداء
            if (settings.videoQuality === '2160p') {
                warnings.push('جودة 4K تتطلب اتصال إنترنت سريع ومعالج قوي');
            }

            if (settings.playbackSpeed > 1.75) {
                warnings.push('سرعة التشغيل العالية قد تؤثر على جودة الصوت');
            }

            if (settings.adBlocker === false) {
                warnings.push('إيقاف مانع الإعلانات قد يؤثر على سرعة التحميل');
            }

        } catch (error) {
            warnings.push(`خطأ في فحص الأداء: ${error}`);
        }

        return {
            isValid: true, // التحذيرات لا تجعل الإعدادات غير صالحة
            warnings
        };
    }

    /**
     * إصلاح الإعدادات التالفة / Fix corrupted settings
     */
    public fixCorruptedSettings(settings: ApplicationConfig): ApplicationConfig {
        const fixedSettings = { ...settings };

        try {
            // إصلاح جودة الفيديو
            if (!['144p', '240p', '360p', '480p', '720p', '1080p', '1440p', '2160p', 'auto'].includes(fixedSettings.videoQuality)) {
                fixedSettings.videoQuality = 'auto';
            }

            // إصلاح مستوى الصوت
            if (typeof fixedSettings.volume !== 'number' || fixedSettings.volume < 0 || fixedSettings.volume > 100) {
                fixedSettings.volume = 50;
            }

            // إصلاح سرعة التشغيل
            const validSpeeds = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2];
            if (!validSpeeds.includes(fixedSettings.playbackSpeed)) {
                fixedSettings.playbackSpeed = 1;
            }

            // إصلاح اللغة
            if (!['ar', 'en', 'fr', 'es', 'de'].includes(fixedSettings.language)) {
                fixedSettings.language = 'ar';
            }

            // إصلاح المظهر
            if (!['light', 'dark', 'auto'].includes(fixedSettings.theme)) {
                fixedSettings.theme = 'auto';
            }

            // إصلاح القيم المنطقية
            if (typeof fixedSettings.darkMode !== 'boolean') {
                fixedSettings.darkMode = false;
            }

            if (typeof fixedSettings.adBlocker !== 'boolean') {
                fixedSettings.adBlocker = true;
            }

            if (typeof fixedSettings.autoPlay !== 'boolean') {
                fixedSettings.autoPlay = false;
            }

        } catch (error) {
            console.error('خطأ في إصلاح الإعدادات:', error);
        }

        return fixedSettings;
    }

    /**
     * تحسين الإعدادات للأداء / Optimize settings for performance
     */
    public optimizeForPerformance(settings: ApplicationConfig): ApplicationConfig {
        const optimizedSettings = { ...settings };

        try {
            // تحسين جودة الفيديو حسب الأداء
            if (optimizedSettings.videoQuality === '2160p') {
                optimizedSettings.videoQuality = '1080p'; // تقليل الجودة للأداء
            }

            // تحسين سرعة التشغيل
            if (optimizedSettings.playbackSpeed > 1.5) {
                optimizedSettings.playbackSpeed = 1.25; // تقليل السرعة قليلاً
            }

            // تفعيل مانع الإعلانات للأداء
            optimizedSettings.adBlocker = true;

        } catch (error) {
            console.error('خطأ في تحسين الإعدادات:', error);
        }

        return optimizedSettings;
    }

    /**
     * تنظيف الأخطاء / Clear errors
     */
    public clearErrors(): void {
        this.validationErrors = [];
        this.basicValidator.clearErrors();
    }

    /**
     * الحصول على الأخطاء / Get errors
     */
    public getErrors(): SettingsErrorReport[] {
        return [...this.validationErrors, ...this.basicValidator.getErrors()];
    }
}
