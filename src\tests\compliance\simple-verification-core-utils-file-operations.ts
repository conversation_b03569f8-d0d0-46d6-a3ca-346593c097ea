/**
 * عمليات الملفات لأداة التحقق المبسطة - ملف التفويض
 * Simple verification file operations - Delegation file
 *
 * هذا الملف يفوض جميع العمليات للملفات المتخصصة
 * This file delegates all operations to specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { SimpleVerificationCoreUtilsFileOperationsAdvanced } from './simple-verification-core-utils-file-operations-advanced';
import { SimpleVerificationCoreUtilsFileOperationsBasic } from './simple-verification-core-utils-file-operations-basic';
import { SimpleVerificationConfig } from './simple-verification-types';

/**
 * فئة عمليات الملفات للتحقق المبسط - التفويض
 * Simple verification file operations class - Delegation
 */
export class SimpleVerificationCoreUtilsFileOperations {

    /**
     * الحصول على جميع الملفات - تفويض للوحدة الأساسية
     * Get all files - Delegate to basic module
     */
    public static async getAllFiles(
        projectRoot: string,
        config: SimpleVerificationConfig
    ): Promise<string[]> {
        // تفويض الحصول على الملفات للوحدة الأساسية
        // Delegate file retrieval to basic module
        return await SimpleVerificationCoreUtilsFileOperationsBasic.getAllFiles(projectRoot, config);
    }

    /**
     * فحص ملف واحد - تفويض للوحدة المتقدمة
     * Check single file - Delegate to advanced module
     */
    public static checkSingleFile(filePath: string, config: SimpleVerificationConfig): {
        isValid: boolean;
        issues: string[];
        score: number;
        lineCount: number;
        hasDocumentation: boolean;
        followsNamingConvention: boolean;
    } {
        // تفويض فحص الملف للوحدة المتقدمة
        // Delegate file check to advanced module
        return SimpleVerificationCoreUtilsFileOperationsAdvanced.checkSingleFile(filePath, config);
    }

    /**
     * تنظيف مسار الملف - تفويض للوحدة الأساسية
     * Clean file path - Delegate to basic module
     */
    public static cleanFilePath(filePath: string, projectRoot: string): string {
        // تفويض تنظيف المسار للوحدة الأساسية
        // Delegate path cleaning to basic module
        return SimpleVerificationCoreUtilsFileOperationsBasic.cleanFilePath(filePath, projectRoot);
    }

    /**
     * فحص وجود الملف - تفويض للوحدة الأساسية
     * Check if file exists - Delegate to basic module
     */
    public static fileExists(filePath: string): boolean {
        // تفويض فحص وجود الملف للوحدة الأساسية
        // Delegate file existence check to basic module
        return SimpleVerificationCoreUtilsFileOperationsBasic.fileExists(filePath);
    }

    /**
     * قراءة محتوى الملف - تفويض للوحدة الأساسية
     * Read file content - Delegate to basic module
     */
    public static readFileContent(filePath: string): string | null {
        // تفويض قراءة المحتوى للوحدة الأساسية
        // Delegate content reading to basic module
        return SimpleVerificationCoreUtilsFileOperationsBasic.readFileContent(filePath);
    }

    /**
     * حساب إحصائيات المشروع
     * Calculate project statistics
     */
    public static calculateProjectStatistics(files: string[]): {
        totalFiles: number;
        totalLines: number;
        averageFileSize: number;
        largestFile: string;
        largestFileSize: number;
        smallestFile: string;
        smallestFileSize: number;
        filesByExtension: Record<string, number>;
        directoryCount: number;
    } {
        let totalLines = 0;
        let largestFile = '';
        let largestFileSize = 0;
        let smallestFile = '';
        let smallestFileSize = Number.MAX_SAFE_INTEGER;
        const filesByExtension: Record<string, number> = {};
        const directories = new Set<string>();

        for (const file of files) {
            try {
                // تفويض قراءة المحتوى للوحدة الأساسية
                // Delegate content reading to basic module
                const content = SimpleVerificationCoreUtilsFileOperationsBasic.readFileContent(file);
                if (!content) continue;

                const lineCount = content.split('\n').length;
                totalLines += lineCount;

                // أكبر ملف
                if (lineCount > largestFileSize) {
                    largestFileSize = lineCount;
                    largestFile = file;
                }

                // أصغر ملف
                if (lineCount < smallestFileSize) {
                    smallestFileSize = lineCount;
                    smallestFile = file;
                }

                // تفويض الحصول على معلومات الملف للوحدة الأساسية
                // Delegate file info retrieval to basic module
                const fileInfo = SimpleVerificationCoreUtilsFileOperationsBasic.getBasicFileInfo(file);
                filesByExtension[fileInfo.extension] = (filesByExtension[fileInfo.extension] || 0) + 1;
                directories.add(fileInfo.directory);

            } catch (error) {
                // تجاهل الملفات التي لا يمكن قراءتها
                continue;
            }
        }

        return {
            totalFiles: files.length,
            totalLines,
            averageFileSize: files.length > 0 ? totalLines / files.length : 0,
            largestFile,
            largestFileSize,
            smallestFile: smallestFileSize === Number.MAX_SAFE_INTEGER ? '' : smallestFile,
            smallestFileSize: smallestFileSize === Number.MAX_SAFE_INTEGER ? 0 : smallestFileSize,
            filesByExtension,
            directoryCount: directories.size
        };
    }
}