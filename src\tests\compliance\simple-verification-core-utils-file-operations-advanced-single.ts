/**
 * فحص الملف الواحد المتقدم
 * Advanced single file checking operations
 * 
 * هذا الملف يحتوي على عمليات فحص الملف الواحد المتقدمة
 * This file contains advanced single file checking operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import * as fs from 'fs';
import * as path from 'path';
import { SimpleVerificationConfig } from './simple-verification-types';

/**
 * فئة فحص الملف الواحد المتقدم
 * Advanced single file checking class
 */
export class SimpleVerificationCoreUtilsFileOperationsAdvancedSingle {

    /**
     * فحص ملف واحد
     * Check single file
     */
    public static checkSingleFile(filePath: string, config: SimpleVerificationConfig): {
        isValid: boolean;
        issues: string[];
        score: number;
        lineCount: number;
        hasDocumentation: boolean;
        followsNamingConvention: boolean;
    } {
        const issues: string[] = [];
        let score = 100;
        let lineCount = 0;
        let hasDocumentation = false;
        let followsNamingConvention = false;

        try {
            const content = fs.readFileSync(filePath, 'utf-8');
            lineCount = content.split('\n').length;

            // فحص حجم الملف
            if (lineCount > config.maxFileSize) {
                issues.push(`File too large: ${lineCount} lines (limit: ${config.maxFileSize})`);
                score -= 20;
            }

            // فحص التوثيق
            hasDocumentation = this.checkDocumentation(content);
            if (!hasDocumentation) {
                issues.push('Missing proper documentation');
                score -= 15;
            }

            // فحص تسمية الملف
            followsNamingConvention = this.checkNamingConvention(filePath);
            if (!followsNamingConvention) {
                issues.push('File name does not follow kebab-case convention');
                score -= 10;
            }

            // فحص جودة الكود
            const qualityResult = this.checkCodeQuality(content);
            if (qualityResult.issues.length > 0) {
                issues.push(...qualityResult.issues);
                score -= qualityResult.penalty;
            }

            // فحص الأمان
            const securityResult = this.checkSecurity(content);
            if (securityResult.issues.length > 0) {
                issues.push(...securityResult.issues);
                score -= securityResult.penalty;
            }

            return {
                isValid: issues.length === 0,
                issues,
                score: Math.max(0, score),
                lineCount,
                hasDocumentation,
                followsNamingConvention
            };

        } catch (error) {
            issues.push(`Error reading file: ${error}`);
            return {
                isValid: false,
                issues,
                score: 0,
                lineCount: 0,
                hasDocumentation: false,
                followsNamingConvention: false
            };
        }
    }

    /**
     * فحص التوثيق
     * Check documentation
     */
    private static checkDocumentation(content: string): boolean {
        // فحص وجود JSDoc
        const hasJSDoc = content.includes('/**') && content.includes('*/');
        
        // فحص وجود معلومات الملف
        const hasFileInfo = content.includes('@author') && content.includes('@version');
        
        // فحص وجود تعليقات ثنائية اللغة
        const hasBilingualComments = content.includes('//') && (
            content.includes('تفويض') || content.includes('Delegate') ||
            content.includes('العملية') || content.includes('Operation')
        );

        return hasJSDoc && hasFileInfo && hasBilingualComments;
    }

    /**
     * فحص تسمية الملف
     * Check file naming convention
     */
    private static checkNamingConvention(filePath: string): boolean {
        const fileName = path.basename(filePath);
        
        // فحص kebab-case للملفات TypeScript
        const kebabCasePattern = /^[a-z0-9]+(-[a-z0-9]+)*\.(ts|js|json|md)$/;
        
        return kebabCasePattern.test(fileName);
    }

    /**
     * فحص جودة الكود
     * Check code quality
     */
    private static checkCodeQuality(content: string): { issues: string[]; penalty: number } {
        const issues: string[] = [];
        let penalty = 0;

        // فحص استخدام any
        if (content.includes(': any') || content.includes('<any>')) {
            issues.push('Uses "any" type - should use specific types');
            penalty += 10;
        }

        // فحص طول الدوال
        const functionMatches = content.match(/function\s+\w+\s*\([^)]*\)\s*\{[^}]*\}/g) || [];
        for (const func of functionMatches) {
            const lineCount = func.split('\n').length;
            if (lineCount > 20) {
                issues.push(`Function exceeds 20 lines (${lineCount} lines)`);
                penalty += 5;
            }
        }

        // فحص التعقيد
        const indentationLevels = content.split('\n').map(line => {
            const match = line.match(/^(\s*)/);
            return match ? match[1].length : 0;
        });
        const maxIndentation = Math.max(...indentationLevels);
        if (maxIndentation > 16) { // 4 levels * 4 spaces
            issues.push(`Excessive nesting depth (${maxIndentation / 4} levels)`);
            penalty += 10;
        }

        return { issues, penalty };
    }

    /**
     * فحص الأمان
     * Check security
     */
    private static checkSecurity(content: string): { issues: string[]; penalty: number } {
        const issues: string[] = [];
        let penalty = 0;

        // فحص eval
        if (content.includes('eval(')) {
            issues.push('Uses dangerous "eval()" function');
            penalty += 20;
        }

        // فحص innerHTML
        if (content.includes('innerHTML')) {
            issues.push('Uses "innerHTML" which can lead to XSS');
            penalty += 15;
        }

        // فحص أسرار مكشوفة
        const secretPatterns = [
            /password\s*[:=]\s*['"][^'"]+['"]/i,
            /api[_-]?key\s*[:=]\s*['"][^'"]+['"]/i,
            /secret\s*[:=]\s*['"][^'"]+['"]/i
        ];

        for (const pattern of secretPatterns) {
            if (pattern.test(content)) {
                issues.push('Contains hardcoded secrets');
                penalty += 25;
                break; // Only count once
            }
        }

        return { issues, penalty };
    }
}
