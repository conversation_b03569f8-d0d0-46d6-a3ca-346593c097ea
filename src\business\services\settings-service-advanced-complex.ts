/**
 * العمليات المتقدمة المعقدة لخدمة الإعدادات
 * Advanced complex settings service operations
 * 
 * هذا الملف يحتوي على العمليات المتقدمة المعقدة لإدارة الإعدادات
 * This file contains advanced complex settings management operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ApplicationConfig } from '@shared/types';
import { ResourceManager } from '@shared/utils/resource-manager';
import { SettingsBackupManager } from './settings-backup';
import {
    SETTINGS_MESSAGES,
    SettingsManagerConfig,
    SettingsStatistics
} from './settings-config';
import { SettingsServiceCore } from './settings-service-core';

/**
 * فئة العمليات المتقدمة المعقدة لخدمة الإعدادات
 * Advanced complex settings service operations class
 */
export class SettingsServiceAdvancedComplex {
    private readonly coreService: SettingsServiceCore;
    private readonly config: SettingsManagerConfig;
    private readonly backupManager: SettingsBackupManager;
    private readonly resourceManager: ResourceManager;
    private autoSaveTimer: NodeJS.Timeout | null = null;

    /**
     * منشئ العمليات المتقدمة المعقدة / Advanced complex operations constructor
     */
    constructor(
        coreService: SettingsServiceCore,
        config: SettingsManagerConfig,
        backupManager: SettingsBackupManager,
        resourceManager: ResourceManager
    ) {
        this.coreService = coreService;
        this.config = config;
        this.backupManager = backupManager;
        this.resourceManager = resourceManager;
    }

    /**
     * الحصول على إحصائيات الإعدادات / Get settings statistics
     */
    public async getSettingsStatistics(): Promise<SettingsStatistics> {
        try {
            const settings = this.coreService.getAllSettings();
            const defaultSettings = this.coreService.getDefaultSettings();
            
            let customizedCount = 0;
            let totalSize = 0;
            const settingTypes: Record<string, number> = {};

            for (const [key, value] of Object.entries(settings)) {
                const defaultValue = (defaultSettings as any)[key];
                
                if (JSON.stringify(value) !== JSON.stringify(defaultValue)) {
                    customizedCount++;
                }

                totalSize += JSON.stringify(value).length;

                const type = typeof value;
                settingTypes[type] = (settingTypes[type] || 0) + 1;
            }

            const backupList = await this.backupManager.getBackupList();

            return {
                totalSettings: Object.keys(settings).length,
                customizedSettings: customizedCount,
                defaultSettings: Object.keys(settings).length - customizedCount,
                totalSizeBytes: totalSize,
                settingTypes,
                lastModified: new Date(),
                backupCount: backupList.length,
                isValid: this.coreService.validateCurrentSettings().isValid
            };

        } catch (error) {
            console.error('خطأ في الحصول على إحصائيات الإعدادات:', error);
            throw error;
        }
    }

    /**
     * مزامنة الإعدادات / Sync settings
     */
    public async syncSettings(): Promise<boolean> {
        try {
            // التحقق من تكامل الإعدادات
            const validation = this.coreService.validateCurrentSettings();
            if (!validation.isValid) {
                console.warn('مشاكل في تكامل الإعدادات، سيتم الإصلاح...');
                this.coreService.fixCorruptedSettings();
            }

            // إنشاء نسخة احتياطية
            const backupCreated = await this.createAutomaticBackup();
            if (!backupCreated) {
                console.warn('فشل في إنشاء نسخة احتياطية أثناء المزامنة');
            }

            // تنظيف النسخ الاحتياطية القديمة
            await this.backupManager.cleanupOldBackups(this.config.maxBackups || 10);

            console.log('تم مزامنة الإعدادات بنجاح');
            return true;

        } catch (error) {
            console.error('خطأ في مزامنة الإعدادات:', error);
            return false;
        }
    }

    /**
     * تشغيل الحفظ التلقائي / Start auto save
     */
    public startAutoSave(intervalMs: number = 300000): void { // 5 دقائق افتراضياً
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
        }

        this.autoSaveTimer = setInterval(async () => {
            try {
                await this.createAutomaticBackup();
                console.log('تم إنشاء نسخة احتياطية تلقائية');
            } catch (error) {
                console.error('خطأ في الحفظ التلقائي:', error);
            }
        }, intervalMs);

        console.log(`تم تشغيل الحفظ التلقائي كل ${intervalMs / 1000} ثانية`);
    }

    /**
     * إيقاف الحفظ التلقائي / Stop auto save
     */
    public stopAutoSave(): void {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
            this.autoSaveTimer = null;
            console.log('تم إيقاف الحفظ التلقائي');
        }
    }

    /**
     * مراقبة تغييرات الإعدادات / Monitor settings changes
     */
    public startMonitoring(): void {
        this.coreService.addChangeListener((change) => {
            console.log(`تغيير في الإعداد: ${change.key}`, {
                oldValue: change.oldValue,
                newValue: change.newValue,
                timestamp: change.timestamp
            });

            // إنشاء نسخة احتياطية عند التغييرات المهمة
            if (this.isImportantSetting(change.key)) {
                this.createAutomaticBackup().catch(error => {
                    console.error('خطأ في إنشاء نسخة احتياطية للتغيير المهم:', error);
                });
            }
        });

        console.log('تم تشغيل مراقبة تغييرات الإعدادات');
    }

    /**
     * التحقق من كون الإعداد مهماً / Check if setting is important
     */
    private isImportantSetting(key: string): boolean {
        const importantSettings = [
            'videoQuality',
            'darkMode',
            'adBlocker',
            'autoplay',
            'volume'
        ];

        return importantSettings.includes(key);
    }

    /**
     * إنشاء نسخة احتياطية تلقائية / Create automatic backup
     */
    private async createAutomaticBackup(): Promise<boolean> {
        try {
            const settings = this.coreService.getAllSettings();
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const description = `نسخة احتياطية تلقائية - ${timestamp}`;

            const backupInfo = await this.backupManager.createBackup(settings, description);
            
            return !!backupInfo;

        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية التلقائية:', error);
            return false;
        }
    }

    /**
     * تحليل استخدام الإعدادات / Analyze settings usage
     */
    public async analyzeUsage(): Promise<{
        mostChanged: Array<{ key: string; changeCount: number }>;
        leastUsed: Array<{ key: string; lastUsed: Date | null }>;
        recommendations: string[];
    }> {
        try {
            const settings = this.coreService.getAllSettings();
            const defaultSettings = this.coreService.getDefaultSettings();
            
            const analysis = {
                mostChanged: [] as Array<{ key: string; changeCount: number }>,
                leastUsed: [] as Array<{ key: string; lastUsed: Date | null }>,
                recommendations: [] as string[]
            };

            // تحليل الإعدادات المخصصة
            for (const [key, value] of Object.entries(settings)) {
                const defaultValue = (defaultSettings as any)[key];
                
                if (JSON.stringify(value) === JSON.stringify(defaultValue)) {
                    analysis.leastUsed.push({
                        key,
                        lastUsed: null
                    });
                }
            }

            // توصيات التحسين
            if (analysis.leastUsed.length > 5) {
                analysis.recommendations.push('يمكن تحسين الأداء بإزالة الإعدادات غير المستخدمة');
            }

            const statistics = await this.getSettingsStatistics();
            if (statistics.totalSizeBytes > 10000) {
                analysis.recommendations.push('حجم الإعدادات كبير، يُنصح بالتحسين');
            }

            if (statistics.backupCount > 20) {
                analysis.recommendations.push('عدد النسخ الاحتياطية كبير، يُنصح بالتنظيف');
            }

            return analysis;

        } catch (error) {
            console.error('خطأ في تحليل استخدام الإعدادات:', error);
            throw error;
        }
    }

    /**
     * تطبيق التوصيات / Apply recommendations
     */
    public async applyRecommendations(): Promise<boolean> {
        try {
            const analysis = await this.analyzeUsage();
            let applied = false;

            // تطبيق تحسين الإعدادات
            if (analysis.recommendations.includes('يمكن تحسين الأداء بإزالة الإعدادات غير المستخدمة')) {
                // تحسين الإعدادات سيتم في ملف منفصل
                applied = true;
            }

            // تنظيف النسخ الاحتياطية
            if (analysis.recommendations.includes('عدد النسخ الاحتياطية كبير، يُنصح بالتنظيف')) {
                await this.backupManager.cleanupOldBackups(10);
                applied = true;
            }

            if (applied) {
                console.log('تم تطبيق التوصيات بنجاح');
            }

            return applied;

        } catch (error) {
            console.error('خطأ في تطبيق التوصيات:', error);
            return false;
        }
    }

    /**
     * تنظيف الموارد / Cleanup resources
     */
    public cleanup(): void {
        this.stopAutoSave();
        this.resourceManager.cleanup();
    }
}
