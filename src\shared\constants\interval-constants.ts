/**
 * ثوابت الفترات الزمنية
 * Interval constants
 * 
 * هذا الملف يحتوي على فترات التحديث والمراقبة
 * This file contains update and monitoring intervals
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * فترات التحديث
 * Update intervals
 */
export const INTERVALS = {
    // فترات مراقبة الأداء / Performance monitoring intervals
    PERFORMANCE_CHECK: 5000, // 5 ثواني
    MEMORY_MONITOR: 10000, // 10 ثواني
    CPU_MONITOR: 15000, // 15 ثانية
    
    // فترات التحديث التلقائي / Auto-update intervals
    SETTINGS_AUTO_SAVE: 30000, // 30 ثانية
    CACHE_REFRESH: 300000, // 5 دقائق
    LOG_ROTATION: 3600000, // ساعة واحدة
    
    // فترات فحص الحالة / Health check intervals
    SERVICE_HEALTH_CHECK: 60000, // دقيقة واحدة
    NETWORK_HEALTH_CHECK: 30000, // 30 ثانية
    SYSTEM_HEALTH_CHECK: 120000, // دقيقتان
    
    // فترات التنظيف / Cleanup intervals
    TEMPORARY_FILES_CLEANUP: 1800000, // 30 دقيقة
    MEMORY_CLEANUP: 600000, // 10 دقائق
    CACHE_CLEANUP: 3600000, // ساعة واحدة
    
    // فترات YouTube / YouTube intervals
    VIDEO_QUALITY_CHECK: 10000, // 10 ثواني
    AD_BLOCKER_CHECK: 5000, // 5 ثواني
    DARK_MODE_CHECK: 15000, // 15 ثانية
    
    // فترات واجهة المستخدم / UI intervals
    UI_UPDATE: 1000, // ثانية واحدة
    PROGRESS_UPDATE: 500, // نصف ثانية
    STATUS_UPDATE: 2000 // ثانيتان
} as const;

/**
 * حدود الوقت القصوى
 * Maximum time limits
 */
export const MAX_LIMITS = {
    // حدود العمليات / Operation limits
    MAX_OPERATION_TIME: 300000, // 5 دقائق
    MAX_RETRY_ATTEMPTS: 5,
    MAX_CONCURRENT_OPERATIONS: 10,
    
    // حدود الذاكرة / Memory limits
    MAX_MEMORY_USAGE: 1024 * 1024 * 1024, // 1 GB
    MAX_CACHE_SIZE: 500 * 1024 * 1024, // 500 MB
    MAX_LOG_SIZE: 100 * 1024 * 1024, // 100 MB
    
    // حدود الملفات / File limits
    MAX_FILE_SIZE: 50 * 1024 * 1024, // 50 MB
    MAX_FILES_COUNT: 1000,
    MAX_BACKUP_COUNT: 10,
    
    // حدود الشبكة / Network limits
    MAX_REQUEST_SIZE: 10 * 1024 * 1024, // 10 MB
    MAX_RESPONSE_SIZE: 50 * 1024 * 1024, // 50 MB
    MAX_CONCURRENT_REQUESTS: 5,
    
    // حدود واجهة المستخدم / UI limits
    MAX_NOTIFICATION_COUNT: 5,
    MAX_DIALOG_COUNT: 3,
    MAX_WINDOW_COUNT: 10,
    
    // حدود YouTube / YouTube limits
    MAX_VIDEO_QUALITY_RETRIES: 3,
    MAX_AD_BLOCKER_RETRIES: 5,
    MAX_DARK_MODE_RETRIES: 2
} as const;

/**
 * حدود الوقت الدنيا
 * Minimum time limits
 */
export const MIN_LIMITS = {
    // حدود العمليات / Operation limits
    MIN_OPERATION_TIME: 100, // 100 ميلي ثانية
    MIN_RETRY_DELAY: 500, // نصف ثانية
    MIN_TIMEOUT: 1000, // ثانية واحدة
    
    // حدود واجهة المستخدم / UI limits
    MIN_DEBOUNCE_DELAY: 50, // 50 ميلي ثانية
    MIN_ANIMATION_DURATION: 100, // 100 ميلي ثانية
    MIN_UPDATE_INTERVAL: 100, // 100 ميلي ثانية
    
    // حدود الشبكة / Network limits
    MIN_REQUEST_TIMEOUT: 1000, // ثانية واحدة
    MIN_CONNECTION_TIMEOUT: 2000, // ثانيتان
    MIN_RETRY_INTERVAL: 1000, // ثانية واحدة
    
    // حدود YouTube / YouTube limits
    MIN_VIDEO_QUALITY_CHECK_INTERVAL: 5000, // 5 ثواني
    MIN_AD_BLOCKER_CHECK_INTERVAL: 2000, // ثانيتان
    MIN_DARK_MODE_CHECK_INTERVAL: 3000 // 3 ثواني
} as const;

/**
 * مضاعفات إعادة المحاولة
 * Retry multipliers
 */
export const RETRY_MULTIPLIERS = {
    EXPONENTIAL_BACKOFF: 2,
    LINEAR_BACKOFF: 1,
    FIBONACCI_BACKOFF: 1.618,
    CUSTOM_BACKOFF: 1.5
} as const;
