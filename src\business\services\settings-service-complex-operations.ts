/**
 * العمليات المعقدة لخدمة الإعدادات
 * Complex settings service operations
 * 
 * هذا الملف يحتوي على العمليات المعقدة لإدارة الإعدادات
 * This file contains complex settings management operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DEFAULT_APPLICATION_CONFIG } from '@shared/constants';
import { ApplicationConfig, ValidationResult } from '@shared/types';
import { ResourceManager } from '@shared/utils/resource-manager';
import Store from 'electron-store';
import {
    SETTINGS_MESSAGES,
    SettingChangeInfo,
    SettingsManagerConfig
} from './settings-config';
import { SettingsServiceBasicOperations } from './settings-service-basic-operations';
import { SettingsValidator } from './settings-validator';

/**
 * فئة العمليات المعقدة لخدمة الإعدادات
 * Complex settings service operations class
 */
export class SettingsServiceComplexOperations {
    private readonly basicOperations: SettingsServiceBasicOperations;
    private readonly validator: SettingsValidator;
    private readonly resourceManager: ResourceManager;

    /**
     * منشئ العمليات المعقدة / Complex operations constructor
     */
    constructor(
        basicOperations: SettingsServiceBasicOperations,
        validator: SettingsValidator,
        resourceManager: ResourceManager
    ) {
        this.basicOperations = basicOperations;
        this.validator = validator;
        this.resourceManager = resourceManager;
    }

    /**
     * تعيين إعدادات متعددة / Set multiple settings
     */
    public async setMultipleSettings(settings: Partial<ApplicationConfig>): Promise<boolean> {
        try {
            // التحقق من صحة جميع الإعدادات
            const validation = this.validator.validatePartialSettings(settings);
            if (!validation.isValid) {
                console.error(SETTINGS_MESSAGES.VALIDATION_ERROR, validation.errors);
                return false;
            }

            const changes: SettingChangeInfo[] = [];

            // تطبيق جميع التغييرات
            for (const [key, value] of Object.entries(settings)) {
                const typedKey = key as keyof ApplicationConfig;
                const oldValue = this.basicOperations.getSetting(typedKey);
                
                const success = await this.basicOperations.setSetting(typedKey, value);
                if (!success) {
                    console.error(`فشل في تعيين الإعداد: ${key}`);
                    return false;
                }
                
                changes.push({
                    key,
                    oldValue,
                    newValue: value,
                    timestamp: new Date()
                });
            }

            console.log(SETTINGS_MESSAGES.SETTINGS_UPDATED);
            return true;

        } catch (error) {
            console.error(SETTINGS_MESSAGES.ERROR_SET_SETTINGS, error);
            return false;
        }
    }

    /**
     * إعادة تعيين الإعدادات للقيم الافتراضية / Reset settings to defaults
     */
    public resetToDefaults(): boolean {
        try {
            const oldSettings = this.basicOperations.getAllSettings();
            
            // حذف جميع الإعدادات الحالية
            for (const key of Object.keys(oldSettings) as Array<keyof ApplicationConfig>) {
                this.basicOperations.deleteSetting(key);
            }

            // تعيين القيم الافتراضية
            for (const [key, value] of Object.entries(DEFAULT_APPLICATION_CONFIG)) {
                this.basicOperations.setSetting(key as keyof ApplicationConfig, value);
            }

            console.log(SETTINGS_MESSAGES.SETTINGS_RESET);
            return true;

        } catch (error) {
            console.error(SETTINGS_MESSAGES.ERROR_RESET_SETTINGS, error);
            return false;
        }
    }

    /**
     * التحقق من صحة الإعدادات / Validate settings
     */
    public validateCurrentSettings(): ValidationResult {
        const settings = this.basicOperations.getAllSettings();
        return this.validator.validateSettings(settings);
    }

    /**
     * إصلاح الإعدادات التالفة / Fix corrupted settings
     */
    public fixCorruptedSettings(): boolean {
        try {
            const validation = this.validateCurrentSettings();
            if (validation.isValid) {
                return true; // لا حاجة للإصلاح
            }

            console.warn('إصلاح الإعدادات التالفة...', validation.errors);

            // إصلاح الإعدادات بالقيم الافتراضية
            const currentSettings = this.basicOperations.getAllSettings();
            const fixedSettings = { ...DEFAULT_APPLICATION_CONFIG };

            // الاحتفاظ بالقيم الصحيحة
            for (const [key, value] of Object.entries(currentSettings)) {
                const typedKey = key as keyof ApplicationConfig;
                const singleValidation = this.validator.validateSingleSetting(typedKey, value);
                
                if (singleValidation.isValid) {
                    (fixedSettings as any)[key] = value;
                }
            }

            // تطبيق الإعدادات المصلحة
            this.setMultipleSettings(fixedSettings);

            console.log('تم إصلاح الإعدادات بنجاح');
            return true;

        } catch (error) {
            console.error('خطأ في إصلاح الإعدادات:', error);
            return false;
        }
    }

    /**
     * مقارنة الإعدادات / Compare settings
     */
    public compareSettings(settings1: ApplicationConfig, settings2: ApplicationConfig): {
        differences: Array<{
            key: string;
            value1: any;
            value2: any;
        }>;
        identical: boolean;
    } {
        const differences: Array<{ key: string; value1: any; value2: any }> = [];

        // مقارنة جميع المفاتيح
        const allKeys = new Set([
            ...Object.keys(settings1),
            ...Object.keys(settings2)
        ]);

        for (const key of allKeys) {
            const value1 = (settings1 as any)[key];
            const value2 = (settings2 as any)[key];

            if (JSON.stringify(value1) !== JSON.stringify(value2)) {
                differences.push({
                    key,
                    value1,
                    value2
                });
            }
        }

        return {
            differences,
            identical: differences.length === 0
        };
    }

    /**
     * دمج الإعدادات / Merge settings
     */
    public mergeSettings(
        baseSettings: ApplicationConfig,
        newSettings: Partial<ApplicationConfig>,
        strategy: 'overwrite' | 'preserve' = 'overwrite'
    ): ApplicationConfig {
        const merged = { ...baseSettings };

        for (const [key, value] of Object.entries(newSettings)) {
            const typedKey = key as keyof ApplicationConfig;
            
            if (strategy === 'overwrite' || !(typedKey in merged)) {
                (merged as any)[key] = value;
            }
        }

        return merged;
    }

    /**
     * تصفية الإعدادات / Filter settings
     */
    public filterSettings(
        settings: ApplicationConfig,
        predicate: (key: string, value: any) => boolean
    ): Partial<ApplicationConfig> {
        const filtered: Partial<ApplicationConfig> = {};

        for (const [key, value] of Object.entries(settings)) {
            if (predicate(key, value)) {
                (filtered as any)[key] = value;
            }
        }

        return filtered;
    }

    /**
     * تحويل الإعدادات / Transform settings
     */
    public transformSettings(
        settings: ApplicationConfig,
        transformer: (key: string, value: any) => any
    ): ApplicationConfig {
        const transformed = { ...settings };

        for (const [key, value] of Object.entries(transformed)) {
            (transformed as any)[key] = transformer(key, value);
        }

        return transformed;
    }

    /**
     * البحث في الإعدادات / Search settings
     */
    public searchSettings(
        settings: ApplicationConfig,
        searchTerm: string
    ): Array<{ key: string; value: any; match: string }> {
        const results: Array<{ key: string; value: any; match: string }> = [];
        const lowerSearchTerm = searchTerm.toLowerCase();

        for (const [key, value] of Object.entries(settings)) {
            const keyMatch = key.toLowerCase().includes(lowerSearchTerm);
            const valueMatch = String(value).toLowerCase().includes(lowerSearchTerm);

            if (keyMatch || valueMatch) {
                results.push({
                    key,
                    value,
                    match: keyMatch ? 'key' : 'value'
                });
            }
        }

        return results;
    }

    /**
     * تنظيف الموارد / Cleanup resources
     */
    public cleanup(): void {
        this.basicOperations.cleanup();
        this.resourceManager.cleanup();
    }
}
