/**
 * العمليات المتخصصة لأداة التحقق المبسطة - ملف التفويض
 * Simple verification specialized operations - Delegation file
 * 
 * هذا الملف يفوض جميع العمليات للملفات المتخصصة
 * This file delegates all operations to specialized files
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import {
    SimpleVerificationConfig,
    ConstitutionCheckResult,
    CodeQualityCheckResult,
    ProjectStructureCheckResult
} from './simple-verification-types';
import { SimpleVerificationCoreOperationsConstitution } from './simple-verification-core-operations-constitution';
import { SimpleVerificationCoreOperationsQuality } from './simple-verification-core-operations-quality';
import { SimpleVerificationCoreOperationsStructure } from './simple-verification-core-operations-structure';

/**
 * فئة العمليات المتخصصة للتحقق المبسط - التفويض
 * Simple verification specialized operations class - Delegation
 */
export class SimpleVerificationCoreOperations {

    /**
     * فحص الامتثال للدستور - تفويض لوحدة الدستور
     * Check constitution compliance - Delegate to constitution module
     */
    public static async checkConstitutionCompliance(
        projectRoot: string,
        config: SimpleVerificationConfig,
        files: string[]
    ): Promise<ConstitutionCheckResult> {
        // تفويض فحص الامتثال للدستور لوحدة الدستور
        // Delegate constitution compliance check to constitution module
        return await SimpleVerificationCoreOperationsConstitution.checkConstitutionCompliance(projectRoot, config, files);
    }

    /**
     * فحص جودة الكود - تفويض لوحدة الجودة
     * Check code quality - Delegate to quality module
     */
    public static async checkCodeQuality(
        projectRoot: string,
        config: SimpleVerificationConfig,
        files: string[]
    ): Promise<CodeQualityCheckResult> {
        // تفويض فحص جودة الكود لوحدة الجودة
        // Delegate code quality check to quality module
        return await SimpleVerificationCoreOperationsQuality.checkCodeQuality(projectRoot, config, files);
    }

    /**
     * فحص بنية المشروع - تفويض لوحدة البنية
     * Check project structure - Delegate to structure module
     */
    public static async checkProjectStructure(
        projectRoot: string,
        config: SimpleVerificationConfig
    ): Promise<ProjectStructureCheckResult> {
        // تفويض فحص بنية المشروع لوحدة البنية
        // Delegate project structure check to structure module
        return await SimpleVerificationCoreOperationsStructure.checkProjectStructure(projectRoot, config);
    }
}
