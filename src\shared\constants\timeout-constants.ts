/**
 * ثوابت المهل الزمنية
 * Timeout constants
 * 
 * هذا الملف يحتوي على المهل الزمنية الأساسية
 * This file contains basic timeout constants
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * مهل التطبيق العامة
 * General application timeouts
 */
export const TIMEOUTS = {
    // مهل التهيئة / Initialization timeouts
    APPLICATION_STARTUP: 30000, // 30 ثانية
    WINDOW_READY: 10000, // 10 ثواني
    SERVICE_INITIALIZATION: 15000, // 15 ثانية
    
    // مهل الشبكة / Network timeouts
    HTTP_REQUEST: 10000, // 10 ثواني
    WEBSOCKET_CONNECTION: 5000, // 5 ثواني
    API_CALL: 15000, // 15 ثانية
    
    // مهل العمليات / Operation timeouts
    FILE_OPERATION: 30000, // 30 ثانية
    DATABASE_OPERATION: 20000, // 20 ثانية
    CACHE_OPERATION: 5000, // 5 ثواني
    
    // مهل واجهة المستخدم / UI timeouts
    USER_INPUT: 30000, // 30 ثانية
    DIALOG_TIMEOUT: 60000, // دقيقة واحدة
    NOTIFICATION_DISPLAY: 5000, // 5 ثواني
    
    // مهل الإعدادات / Settings timeouts
    SETTINGS_SAVE: 10000, // 10 ثواني
    SETTINGS_LOAD: 5000, // 5 ثواني
    SETTINGS_VALIDATION: 3000, // 3 ثواني
    
    // مهل YouTube / YouTube timeouts
    YOUTUBE_PAGE_LOAD: 30000, // 30 ثانية
    VIDEO_QUALITY_DETECTION: 10000, // 10 ثواني
    VIDEO_QUALITY_APPLICATION: 15000, // 15 ثانية
    DARK_MODE_APPLICATION: 5000, // 5 ثواني
    AD_BLOCKER_APPLICATION: 10000, // 10 ثواني
    
    // مهل الأداء / Performance timeouts
    PERFORMANCE_MONITORING: 30000, // 30 ثانية
    MEMORY_CHECK: 60000, // دقيقة واحدة
    CPU_CHECK: 30000, // 30 ثانية
    
    // مهل التنظيف / Cleanup timeouts
    RESOURCE_CLEANUP: 300000, // 5 دقائق
    CACHE_CLEANUP: 600000, // 10 دقائق
    LOG_CLEANUP: 3600000, // ساعة واحدة
    
    // مهل إعادة المحاولة / Retry timeouts
    RETRY_DELAY: 2000, // ثانيتان
    EXPONENTIAL_BACKOFF_BASE: 1000, // ثانية واحدة
    MAX_RETRY_DELAY: 30000 // 30 ثانية
} as const;

/**
 * تأخيرات العمليات
 * Operation delays
 */
export const DELAYS = {
    // تأخيرات التهيئة / Initialization delays
    STARTUP_DELAY: 1000, // ثانية واحدة
    SERVICE_START_DELAY: 500, // نصف ثانية
    WINDOW_SHOW_DELAY: 200, // 200 ميلي ثانية
    
    // تأخيرات واجهة المستخدم / UI delays
    DEBOUNCE_DELAY: 300, // 300 ميلي ثانية
    THROTTLE_DELAY: 100, // 100 ميلي ثانية
    ANIMATION_DELAY: 250, // 250 ميلي ثانية
    
    // تأخيرات الإعدادات / Settings delays
    SETTINGS_APPLY_DELAY: 500, // نصف ثانية
    VALIDATION_DELAY: 200, // 200 ميلي ثانية
    AUTO_SAVE_DELAY: 2000, // ثانيتان
    
    // تأخيرات YouTube / YouTube delays
    VIDEO_QUALITY_APPLY_DELAY: 1000, // ثانية واحدة
    DARK_MODE_APPLY_DELAY: 300, // 300 ميلي ثانية
    AD_BLOCKER_APPLY_DELAY: 500, // نصف ثانية
    
    // تأخيرات الشبكة / Network delays
    REQUEST_DELAY: 100, // 100 ميلي ثانية
    RETRY_DELAY: 1000, // ثانية واحدة
    CONNECTION_DELAY: 2000, // ثانيتان
    
    // تأخيرات التنظيف / Cleanup delays
    CLEANUP_DELAY: 5000, // 5 ثواني
    SHUTDOWN_DELAY: 3000, // 3 ثواني
    FORCE_QUIT_DELAY: 10000 // 10 ثواني
} as const;
