/**
 * معالجة العناصر المتقدمة لمراقب الوضع المظلم
 * Advanced element processing for dark mode observer
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// استيراد الفئات المتخصصة / Import specialized classes
import { DarkModeObserverOperationsAdvancedElementsVideo } from './dark-mode-observer-operations-advanced-elements-video';
import { DarkModeObserverOperationsAdvancedElementsImage } from './dark-mode-observer-operations-advanced-elements-image';
import { DarkModeObserverOperationsAdvancedElementsGeneric } from './dark-mode-observer-operations-advanced-elements-generic';
import { DarkModeConfig } from './dark-mode-config';

/**
 * فئة معالجة العناصر المتقدمة
 * Advanced element processing class
 */
export class DarkModeObserverOperationsAdvancedElements {
    private readonly config: DarkModeConfig;

    constructor(config: DarkModeConfig) {
        this.config = config;
    }

    /** معالجة عنصر جديد / Process new element */
    public processNewElement(element: Element): void {
        if (!(element instanceof HTMLElement)) {
            return;
        }

        // التحقق من نوع العنصر
        const tagName = element.tagName.toLowerCase();
        
        // معالجة العناصر الخاصة
        switch (tagName) {
            case 'video':
                DarkModeObserverOperationsAdvancedElementsVideo.processVideoElement(element as HTMLVideoElement, this.config);
                break;
                
            case 'iframe':
                DarkModeObserverOperationsAdvancedElementsGeneric.processIframeElement(element as HTMLIFrameElement, this.config);
                break;
                
            case 'img':
                DarkModeObserverOperationsAdvancedElementsImage.processImageElement(element as HTMLImageElement, this.config);
                break;
                
            case 'canvas':
                DarkModeObserverOperationsAdvancedElementsImage.processCanvasElement(element as HTMLCanvasElement, this.config);
                break;
                
            default:
                DarkModeObserverOperationsAdvancedElementsGeneric.processGenericElement(element, this.config);
                break;
        }
    }

    /** معالجة عنصر الفيديو / Process video element */
    public processVideoElement(element: HTMLVideoElement): void {
        DarkModeObserverOperationsAdvancedElementsVideo.processVideoElement(element, this.config);
    }

    /** معالجة عنصر iframe / Process iframe element */
    public processIframeElement(element: HTMLIFrameElement): void {
        DarkModeObserverOperationsAdvancedElementsGeneric.processIframeElement(element, this.config);
    }

    /** معالجة عنصر الصورة / Process image element */
    public processImageElement(element: HTMLImageElement): void {
        DarkModeObserverOperationsAdvancedElementsImage.processImageElement(element, this.config);
    }

    /** معالجة عنصر Canvas / Process canvas element */
    public processCanvasElement(element: HTMLCanvasElement): void {
        DarkModeObserverOperationsAdvancedElementsImage.processCanvasElement(element, this.config);
    }

    /** معالجة عنصر عام / Process generic element */
    public processGenericElement(element: HTMLElement): void {
        DarkModeObserverOperationsAdvancedElementsGeneric.processGenericElement(element, this.config);
    }
}
