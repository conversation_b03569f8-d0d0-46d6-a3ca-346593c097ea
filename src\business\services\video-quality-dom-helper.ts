/**
 * مساعد DOM لجودة الفيديو
 * Video quality DOM helper
 * 
 * هذا الملف يحتوي على دوال مساعدة للتعامل مع DOM
 * This file contains helper functions for DOM manipulation
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQuality, ValidationResult } from '@shared/types';
import { 
    VIDEO_QUALITY_SELECTORS,
    VIDEO_QUALITY_CONSTANTS,
    VIDEO_QUALITY_MESSAGES,
    QUALITY_HIDING_STYLES,
    QUALITY_CSS_IDS
} from './video-quality-config';

/**
 * فئة مساعد DOM لجودة الفيديو
 * Video quality DOM helper class
 */
export class VideoQualityDomHelper {
    /**
     * انتظار تحميل المشغل
     * Wait for player to load
     * 
     * @returns Promise<Element | null>
     */
    public static async waitForPlayer(): Promise<Element | null> {
        if (typeof document === 'undefined') {
            return null;
        }

        const startTime = Date.now();
        
        while (Date.now() - startTime < VIDEO_QUALITY_CONSTANTS.PLAYER_READY_TIMEOUT) {
            for (const selector of VIDEO_QUALITY_SELECTORS.PLAYER_SELECTORS) {
                const player = document.querySelector(selector);
                if (player) {
                    return player;
                }
            }
            
            await VideoQualityDomHelper.delay(100);
        }
        
        return null;
    }

    /**
     * البحث عن زر الإعدادات
     * Find settings button
     * 
     * @returns Element | null
     */
    public static findSettingsButton(): Element | null {
        if (typeof document === 'undefined') {
            return null;
        }

        for (const selector of VIDEO_QUALITY_SELECTORS.QUALITY_BUTTON_SELECTORS) {
            const button = document.querySelector(selector);
            if (button) {
                return button;
            }
        }
        return null;
    }

    /**
     * البحث عن عنصر الجودة في القائمة
     * Find quality menu item
     * 
     * @param quality - الجودة المطلوبة
     * @returns Element | null
     */
    public static findQualityMenuItem(quality: VideoQuality): Element | null {
        if (typeof document === 'undefined') {
            return null;
        }

        const qualityItems = document.querySelectorAll('.ytp-quality-menu .ytp-menuitem');
        
        for (const item of qualityItems) {
            const label = item.querySelector('.ytp-menuitem-label');
            if (label && label.textContent) {
                const text = label.textContent.toLowerCase().trim();
                if (text.includes(quality.toLowerCase()) || 
                    text.includes(quality.replace('p', ''))) {
                    return item;
                }
            }
        }
        
        return null;
    }

    /**
     * حقن أنماط الإخفاء
     * Inject hiding styles
     * 
     * @returns ValidationResult
     */
    public static injectHidingStyles(): ValidationResult {
        try {
            if (typeof document === 'undefined') {
                return {
                    isValid: false,
                    errors: [{
                        field: 'environment',
                        message: 'لا يمكن حقن الأنماط في بيئة Node.js / Cannot inject styles in Node.js environment',
                        code: 'INVALID_ENVIRONMENT'
                    }]
                };
            }

            const existingStyle = document.getElementById(QUALITY_CSS_IDS.HIDING_STYLES);
            if (existingStyle) {
                return { isValid: true, errors: [] };
            }

            const style = document.createElement('style');
            style.id = QUALITY_CSS_IDS.HIDING_STYLES;
            style.textContent = QUALITY_HIDING_STYLES;
            document.head.appendChild(style);

            return { isValid: true, errors: [] };

        } catch (error) {
            return {
                isValid: false,
                errors: [{
                    field: 'injection',
                    message: `خطأ في حقن الأنماط: ${error}`,
                    code: 'INJECTION_ERROR'
                }]
            };
        }
    }

    /**
     * إزالة أنماط الإخفاء
     * Remove hiding styles
     * 
     * @returns ValidationResult
     */
    public static removeHidingStyles(): ValidationResult {
        try {
            if (typeof document === 'undefined') {
                return {
                    isValid: false,
                    errors: [{
                        field: 'environment',
                        message: 'لا يمكن إزالة الأنماط في بيئة Node.js / Cannot remove styles in Node.js environment',
                        code: 'INVALID_ENVIRONMENT'
                    }]
                };
            }

            const style = document.getElementById(QUALITY_CSS_IDS.HIDING_STYLES);
            if (style) {
                style.remove();
            }

            return { isValid: true, errors: [] };

        } catch (error) {
            return {
                isValid: false,
                errors: [{
                    field: 'removal',
                    message: `خطأ في إزالة الأنماط: ${error}`,
                    code: 'REMOVAL_ERROR'
                }]
            };
        }
    }

    /**
     * النقر على عنصر بأمان
     * Safe click on element
     * 
     * @param element - العنصر المراد النقر عليه
     * @returns ValidationResult
     */
    public static safeClick(element: Element): ValidationResult {
        try {
            if (!element) {
                return {
                    isValid: false,
                    errors: [{
                        field: 'element',
                        message: 'العنصر غير موجود / Element not found',
                        code: 'ELEMENT_NOT_FOUND'
                    }]
                };
            }

            // محاولة النقر بطرق مختلفة
            if (element instanceof HTMLElement) {
                element.click();
            } else {
                const event = new MouseEvent('click', {
                    view: window,
                    bubbles: true,
                    cancelable: true
                });
                element.dispatchEvent(event);
            }

            return { isValid: true, errors: [] };

        } catch (error) {
            return {
                isValid: false,
                errors: [{
                    field: 'click',
                    message: `خطأ في النقر: ${error}`,
                    code: 'CLICK_ERROR'
                }]
            };
        }
    }

    /**
     * تأخير
     * Delay
     * 
     * @param ms - المدة بالميلي ثانية
     * @returns Promise<void>
     */
    public static delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * التحقق من وجود قائمة الجودة
     * Check if quality menu exists
     * 
     * @returns boolean
     */
    public static hasQualityMenu(): boolean {
        if (typeof document === 'undefined') {
            return false;
        }

        const menu = document.querySelector('.ytp-quality-menu');
        return menu !== null;
    }

    /**
     * انتظار ظهور قائمة الجودة
     * Wait for quality menu to appear
     * 
     * @param timeout - مهلة الانتظار بالميلي ثانية
     * @returns Promise<boolean>
     */
    public static async waitForQualityMenu(timeout: number = 3000): Promise<boolean> {
        if (typeof document === 'undefined') {
            return false;
        }

        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            if (VideoQualityDomHelper.hasQualityMenu()) {
                return true;
            }
            await VideoQualityDomHelper.delay(100);
        }
        
        return false;
    }
}
