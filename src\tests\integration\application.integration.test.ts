/**
 * اختبارات تكامل التطبيق الشاملة
 * Comprehensive application integration tests
 *
 * هذا الملف يحتوي على اختبارات التكامل الشاملة للتطبيق
 * This file contains comprehensive integration tests for the application
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// إعادة تصدير المكونات المتخصصة
// Re-export specialized components
export * from './application-integration-mocks';
export * from './application-integration-core-tests';

import { ApplicationIntegrationCoreTests } from './application-integration-core-tests';
import { mockElectron, mockNodeModules, mockDOM } from './application-integration-mocks';

// تهيئة الاختبارات
// Initialize tests
const coreTests = new ApplicationIntegrationCoreTests();

// Mock Node.js modules globally
jest.mock('fs', () => mockNodeModules.fs);
jest.mock('path', () => mockNodeModules.path);
jest.mock('os', () => mockNodeModules.os);

// تشغيل الاختبارات الأساسية
// Run core tests
describe('Application Integration Tests', () => {
    beforeAll(() => {
        coreTests.setupTests();
    });

    afterAll(() => {
        coreTests.cleanupTests();
    });

    // تشغيل اختبارات التهيئة
    // Run initialization tests
    coreTests.testApplicationInitialization();

    // تشغيل اختبارات إدارة النوافذ
    // Run window management tests
    coreTests.testWindowManagement();

    // تشغيل اختبارات إدارة الإعدادات
    // Run settings management tests
    coreTests.testSettingsManagement();

