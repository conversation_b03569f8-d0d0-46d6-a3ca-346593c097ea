/**
 * العمليات الأساسية الجوهرية لمراقب الوضع المظلم
 * Core basic operations for dark mode observer
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import {
    DARK_MODE_CONSTANTS,
    DARK_MODE_MESSAGES,
    DarkModeConfig
} from './dark-mode-config';
import { DarkModeStylesApplicator } from './dark-mode-styles';

/**
 * فئة العمليات الأساسية الجوهرية
 * Core basic operations class
 */
export class DarkModeObserverOperationsBasicCore {
    private readonly config: DarkModeConfig;
    private readonly stylesApplicator: DarkModeStylesApplicator;

    constructor(config: DarkModeConfig, stylesApplicator: DarkModeStylesApplicator) {
        this.config = config;
        this.stylesApplicator = stylesApplicator;
    }

    /** تطبيق الوضع المظلم على العنصر / Apply dark mode to element */
    public applyDarkModeToElement(element: Element): boolean {
        try {
            if (!element || !(element instanceof HTMLElement)) {
                console.warn(DARK_MODE_MESSAGES.WARNINGS.INVALID_ELEMENT);
                return false;
            }

            // تطبيق الأنماط الأساسية
            this.applyBasicStyles(element);
            
            // تطبيق الأنماط المخصصة
            if (this.config.enableCustomStyles) {
                this.applyCustomStyles(element);
            }

            return true;
        } catch (error) {
            console.error('خطأ في تطبيق الوضع المظلم على العنصر:', error);
            return false;
        }
    }

    /** إزالة الوضع المظلم من العنصر / Remove dark mode from element */
    public removeDarkModeFromElement(element: Element): boolean {
        try {
            if (!element || !(element instanceof HTMLElement)) {
                return false;
            }

            // إزالة الأنماط المطبقة
            this.removeAppliedStyles(element);
            
            // إزالة الفئات المضافة
            this.removeAddedClasses(element);

            return true;
        } catch (error) {
            console.error('خطأ في إزالة الوضع المظلم من العنصر:', error);
            return false;
        }
    }

    /** تبديل الوضع المظلم / Toggle dark mode */
    public toggleDarkMode(element: Element): boolean {
        if (this.isDarkModeApplied(element)) {
            return this.removeDarkModeFromElement(element);
        } else {
            return this.applyDarkModeToElement(element);
        }
    }

    /** التحقق من تطبيق الوضع المظلم / Check if dark mode is applied */
    public isDarkModeApplied(element: Element): boolean {
        if (!element || !(element instanceof HTMLElement)) {
            return false;
        }

        // التحقق من وجود فئات الوضع المظلم
        const darkModeClasses = ['dark-mode', 'dark-theme', 'night-mode'];
        for (const className of darkModeClasses) {
            if (element.classList.contains(className)) {
                return true;
            }
        }

        // التحقق من الأنماط المطبقة
        const computedStyle = window.getComputedStyle(element);
        const backgroundColor = computedStyle.backgroundColor;
        
        // التحقق من كون الخلفية داكنة
        if (backgroundColor && backgroundColor !== 'rgba(0, 0, 0, 0)') {
            const rgb = backgroundColor.match(/\d+/g);
            if (rgb && rgb.length >= 3) {
                const brightness = (parseInt(rgb[0]) + parseInt(rgb[1]) + parseInt(rgb[2])) / 3;
                return brightness < 128; // إذا كان متوسط RGB أقل من 128، فالخلفية داكنة
            }
        }

        return false;
    }

    /** تطبيق الأنماط الأساسية / Apply basic styles */
    private applyBasicStyles(element: HTMLElement): void {
        const tagName = element.tagName.toLowerCase();
        
        switch (tagName) {
            case 'body':
            case 'html':
                element.style.backgroundColor = this.config.customThemeColors.backgroundColor;
                element.style.color = this.config.customThemeColors.textColor;
                break;
                
            case 'div':
            case 'section':
            case 'article':
                element.style.backgroundColor = this.config.customThemeColors.backgroundColor;
                element.style.color = this.config.customThemeColors.textColor;
                break;
                
            case 'button':
                element.style.backgroundColor = this.config.customThemeColors.buttonColor;
                element.style.color = this.config.customThemeColors.textColor;
                element.style.borderColor = this.config.customThemeColors.borderColor;
                break;
                
            case 'input':
            case 'textarea':
                element.style.backgroundColor = this.config.customThemeColors.inputColor;
                element.style.color = this.config.customThemeColors.textColor;
                element.style.borderColor = this.config.customThemeColors.borderColor;
                break;
                
            case 'a':
                element.style.color = this.config.customThemeColors.linkColor;
                break;
        }
    }

    /** تطبيق الأنماط المخصصة / Apply custom styles */
    private applyCustomStyles(element: HTMLElement): void {
        // تطبيق الانتقالات
        if (this.config.enableTransitions) {
            element.style.transition = `all ${this.config.transitionDuration}ms ease`;
        }

        // تطبيق الأنماط المخصصة الإضافية
        if (this.config.customStyles) {
            const customStyleElement = document.createElement('style');
            customStyleElement.textContent = this.config.customStyles;
            document.head.appendChild(customStyleElement);
        }
    }

    /** إزالة الأنماط المطبقة / Remove applied styles */
    private removeAppliedStyles(element: HTMLElement): void {
        const stylesToRemove = [
            'background-color',
            'color',
            'border-color',
            'transition'
        ];

        stylesToRemove.forEach(style => {
            element.style.removeProperty(style);
        });
    }

    /** إزالة الفئات المضافة / Remove added classes */
    private removeAddedClasses(element: HTMLElement): void {
        const classesToRemove = [
            'dark-mode',
            'dark-theme',
            'night-mode'
        ];

        classesToRemove.forEach(className => {
            element.classList.remove(className);
        });
    }
}
