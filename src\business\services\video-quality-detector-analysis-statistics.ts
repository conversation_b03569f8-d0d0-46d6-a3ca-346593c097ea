/**
 * إحصائيات تحليل كشف جودة الفيديو
 * Video quality detection analysis statistics
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQualityErrorType, VideoQualityErrorReport } from './video-quality-config';

/**
 * إحصائيات الكشف / Detection statistics
 */
export interface DetectionStatistics {
    totalAttempts: number;
    successfulDetections: number;
    failedDetections: number;
    successRate: number;
    averageDetectionTime: number;
    lastDetectionTime: Date | null;
    errorsByType: Map<VideoQualityErrorType, number>;
}

/**
 * فئة إحصائيات تحليل الكشف
 * Detection analysis statistics class
 */
export class VideoQualityDetectorAnalysisStatistics {
    private detectionErrors: VideoQualityErrorReport[] = [];
    private detectionAttempts: number = 0;
    private successfulDetections: number = 0;
    private detectionTimes: number[] = [];

    /** تسجيل محاولة كشف / Record detection attempt */
    public recordAttempt(): void {
        this.detectionAttempts++;
    }

    /** تسجيل كشف ناجح / Record successful detection */
    public recordSuccess(): void {
        this.successfulDetections++;
    }

    /** تسجيل وقت الكشف / Record detection time */
    public recordDetectionTime(time: number): void {
        this.detectionTimes.push(time);
    }

    /** تسجيل خطأ / Record error */
    public recordError(type: VideoQualityErrorType, message: string): void {
        const error: VideoQualityErrorReport = {
            type,
            message,
            timestamp: new Date(),
            context: {
                attempts: this.detectionAttempts,
                url: window.location.href
            }
        };

        this.detectionErrors.push(error);

        // الحد من عدد الأخطاء المحفوظة
        const MAX_ERROR_HISTORY = 100;
        if (this.detectionErrors.length > MAX_ERROR_HISTORY) {
            this.detectionErrors = this.detectionErrors.slice(-MAX_ERROR_HISTORY / 2);
        }
    }

    /** الحصول على إحصائيات الكشف / Get detection statistics */
    public getDetectionStatistics(): DetectionStatistics {
        const failedDetections = this.detectionAttempts - this.successfulDetections;
        const successRate = this.detectionAttempts > 0 ? 
            (this.successfulDetections / this.detectionAttempts) * 100 : 0;
        
        const averageDetectionTime = this.detectionTimes.length > 0 ?
            this.detectionTimes.reduce((sum, time) => sum + time, 0) / this.detectionTimes.length : 0;

        const errorsByType = new Map<VideoQualityErrorType, number>();
        for (const error of this.detectionErrors) {
            errorsByType.set(error.type, (errorsByType.get(error.type) || 0) + 1);
        }

        return {
            totalAttempts: this.detectionAttempts,
            successfulDetections: this.successfulDetections,
            failedDetections,
            successRate,
            averageDetectionTime,
            lastDetectionTime: this.detectionTimes.length > 0 ? new Date() : null,
            errorsByType
        };
    }

    /** إعادة تعيين الإحصائيات / Reset statistics */
    public resetStatistics(): void {
        this.detectionErrors = [];
        this.detectionAttempts = 0;
        this.successfulDetections = 0;
        this.detectionTimes = [];
    }

    /** الحصول على الأخطاء / Get errors */
    public getErrors(): VideoQualityErrorReport[] {
        return [...this.detectionErrors];
    }

    /** مسح الأخطاء / Clear errors */
    public clearErrors(): void {
        this.detectionErrors = [];
    }

    /** الحصول على إحصائيات مفصلة / Get detailed statistics */
    public getDetailedStatistics(): {
        basic: DetectionStatistics;
        errorDetails: VideoQualityErrorReport[];
        timeAnalysis: {
            minTime: number;
            maxTime: number;
            medianTime: number;
        };
    } {
        const basic = this.getDetectionStatistics();
        const sortedTimes = [...this.detectionTimes].sort((a, b) => a - b);
        
        return {
            basic,
            errorDetails: this.getErrors(),
            timeAnalysis: {
                minTime: sortedTimes.length > 0 ? sortedTimes[0] : 0,
                maxTime: sortedTimes.length > 0 ? sortedTimes[sortedTimes.length - 1] : 0,
                medianTime: sortedTimes.length > 0 ? 
                    sortedTimes[Math.floor(sortedTimes.length / 2)] : 0
            }
        };
    }

    /** تصدير الإحصائيات / Export statistics */
    public exportStatistics(): string {
        const stats = this.getDetailedStatistics();
        return JSON.stringify(stats, null, 2);
    }

    /** استيراد الإحصائيات / Import statistics */
    public importStatistics(data: string): boolean {
        try {
            const parsed = JSON.parse(data);
            if (parsed.basic && parsed.errorDetails) {
                this.detectionAttempts = parsed.basic.totalAttempts || 0;
                this.successfulDetections = parsed.basic.successfulDetections || 0;
                this.detectionErrors = parsed.errorDetails || [];
                this.detectionTimes = [];
                return true;
            }
            return false;
        } catch (error) {
            console.error('خطأ في استيراد الإحصائيات:', error);
            return false;
        }
    }
}
