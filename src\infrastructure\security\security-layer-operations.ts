/**
 * عمليات طبقة الأمان
 * Security layer operations
 *
 * هذا الملف يجمع جميع عمليات طبقة الأمان من الملفات المتخصصة
 * This file aggregates all security layer operations from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// استيراد العمليات الأساسية
// Import core operations
export { SecurityLayerCoreOperations } from './security-layer-core-operations';

// استيراد مدير الحالة
// Import state manager
export { SecurityLayerStateManager } from './security-layer-state-manager';

import { ResourceManager } from '@shared/utils/resource-manager';
import { AdBlockerService } from './ad-blocker';
import { SecurityLayerConfig, SecurityScanResult, ThreatInfo } from './security-layer-config';
import { SecurityLayerCoreOperations } from './security-layer-core-operations';
import { SecurityLayerStateManager } from './security-layer-state-manager';
import { SecurityManager } from './security-manager';

/**
 * عمليات طبقة الأمان
 * Security layer operations class
 */
export class SecurityLayerOperations {
    private readonly coreOperations: SecurityLayerCoreOperations;
    private readonly stateManager: SecurityLayerStateManager;

    /**
     * منشئ عمليات طبقة الأمان / Security layer operations constructor
     */
    constructor(
        config: SecurityLayerConfig,
        adBlocker: AdBlockerService,
        securityManager: SecurityManager,
        resourceManager: ResourceManager
    ) {
        this.coreOperations = new SecurityLayerCoreOperations(
            config,
            adBlocker,
            securityManager,
            resourceManager
        );
        this.stateManager = new SecurityLayerStateManager();
    }

    /**
     * تهيئة طبقة الأمان / Initialize security layer
     */
    public async initialize(): Promise<boolean> {
        return await this.coreOperations.initialize();
    }

    /**
     * فحص أمني شامل / Comprehensive security scan
     */
    public async performSecurityScan(url: string): Promise<SecurityScanResult> {
        const scanId = this.coreOperations.generateScanId();

        if (this.stateManager.hasActiveScan(url)) {
            return await this.stateManager.getActiveScan(url)!;
        }

        const scanPromise = this.coreOperations.executeScan(url, scanId);
        this.stateManager.addActiveScan(url, scanPromise);

        try {
            const result = await scanPromise;
            this.stateManager.updateStatistics(result, result.scanDuration);
            return result;
        } finally {
            this.stateManager.removeActiveScan(url);
        }
    }

    /**
     * حظر التهديد / Block threat
     */
    public async blockThreat(threatInfo: ThreatInfo): Promise<boolean> {
        this.stateManager.addThreatToCache(threatInfo);
        return await this.coreOperations.blockThreat(threatInfo);
    }

    /**
     * إلغاء حظر التهديد / Unblock threat
     */
    public async unblockThreat(threatId: string): Promise<boolean> {
        const threatInfo = this.stateManager.getThreatFromCache(threatId);
        if (!threatInfo) return false;

        const result = await this.coreOperations.unblockThreat(threatInfo);
        if (result) {
            this.stateManager.removeThreatFromCache(threatId);
        }
        return result;
    }

    /**
     * تنظيف المحتوى / Sanitize content
     */
    public async sanitizeContent(content: string): Promise<string> {
        const result = await this.coreOperations.sanitizeContent(content);
        this.stateManager.incrementSanitizedContent();
        return result;
    }

    /**
     * الحصول على الحالة الحالية / Get current state
     */
    public getState(): any {
        return this.stateManager.getState();
    }

    /**
     * الحصول على الإحصائيات / Get statistics
     */
    public getStatistics(): any {
        return this.stateManager.getStatistics();
    }

    /**
     * تنظيف الموارد / Cleanup resources
     */
    public cleanup(): void {
        this.stateManager.cleanup();
        this.coreOperations.cleanup();
    }
}
