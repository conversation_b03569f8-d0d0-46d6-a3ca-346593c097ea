/**
 * إحصائيات الملفات المتقدمة الجوهرية
 * Core advanced file statistics operations
 * 
 * هذا الملف يحتوي على العمليات الجوهرية لإحصائيات الملفات المتقدمة
 * This file contains core advanced file statistics operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvanced } from './simple-verification-core-utils-file-operations-basic-info-advanced';

/**
 * فئة إحصائيات الملفات المتقدمة الجوهرية
 * Core advanced file statistics class
 */
export class SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsCore {

    /**
     * الحصول على إحصائيات مجموعة ملفات
     * Get statistics for a group of files
     */
    public static getFilesStatistics(filePaths: string[]): {
        totalFiles: number;
        existingFiles: number;
        totalSize: number;
        averageSize: number;
        largestFile: string;
        largestSize: number;
        smallestFile: string;
        smallestSize: number;
        extensionCounts: Record<string, number>;
    } {
        let existingFiles = 0;
        let totalSize = 0;
        let largestFile = '';
        let largestSize = 0;
        let smallestFile = '';
        let smallestSize = Number.MAX_SAFE_INTEGER;
        const extensionCounts: Record<string, number> = {};

        for (const filePath of filePaths) {
            try {
                const info = SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvanced.getDetailedFileInfo(filePath);
                
                if (!info.exists) continue;
                
                existingFiles++;
                totalSize += info.size;

                // أكبر ملف
                if (info.size > largestSize) {
                    largestSize = info.size;
                    largestFile = filePath;
                }

                // أصغر ملف
                if (info.size < smallestSize) {
                    smallestSize = info.size;
                    smallestFile = filePath;
                }

                // عدد الامتدادات
                const extension = info.extension.toLowerCase();
                extensionCounts[extension] = (extensionCounts[extension] || 0) + 1;

            } catch (error) {
                // تجاهل الملفات التي لا يمكن قراءتها
                continue;
            }
        }

        return {
            totalFiles: filePaths.length,
            existingFiles,
            totalSize,
            averageSize: existingFiles > 0 ? totalSize / existingFiles : 0,
            largestFile,
            largestSize,
            smallestFile: smallestSize === Number.MAX_SAFE_INTEGER ? '' : smallestFile,
            smallestSize: smallestSize === Number.MAX_SAFE_INTEGER ? 0 : smallestSize,
            extensionCounts
        };
    }

    /**
     * فلترة الملفات حسب المعايير
     * Filter files by criteria
     */
    public static filterFilesByCriteria(
        filePaths: string[],
        criteria: {
            minSize?: number;
            maxSize?: number;
            extensions?: string[];
            modifiedAfter?: Date;
            modifiedBefore?: Date;
        }
    ): string[] {
        return filePaths.filter(filePath => {
            try {
                const info = SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvanced.getDetailedFileInfo(filePath);
                
                if (!info.exists) return false;

                // فحص الحجم
                if (criteria.minSize !== undefined && info.size < criteria.minSize) return false;
                if (criteria.maxSize !== undefined && info.size > criteria.maxSize) return false;

                // فحص الامتداد
                if (criteria.extensions && !criteria.extensions.includes(info.extension.toLowerCase())) return false;

                // فحص تاريخ التعديل
                if (criteria.modifiedAfter && info.modificationDate && info.modificationDate < criteria.modifiedAfter) return false;
                if (criteria.modifiedBefore && info.modificationDate && info.modificationDate > criteria.modifiedBefore) return false;

                return true;

            } catch (error) {
                return false;
            }
        });
    }

    /**
     * ترتيب الملفات حسب الحجم
     * Sort files by size
     */
    public static sortFilesBySize(filePaths: string[], ascending: boolean = true): string[] {
        return filePaths.sort((a, b) => {
            try {
                const infoA = SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvanced.getDetailedFileInfo(a);
                const infoB = SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvanced.getDetailedFileInfo(b);
                
                if (!infoA.exists || !infoB.exists) return 0;
                
                return ascending ? infoA.size - infoB.size : infoB.size - infoA.size;
            } catch (error) {
                return 0;
            }
        });
    }

    /**
     * ترتيب الملفات حسب تاريخ التعديل
     * Sort files by modification date
     */
    public static sortFilesByModificationDate(filePaths: string[], ascending: boolean = true): string[] {
        return filePaths.sort((a, b) => {
            try {
                const infoA = SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvanced.getDetailedFileInfo(a);
                const infoB = SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvanced.getDetailedFileInfo(b);
                
                if (!infoA.exists || !infoB.exists || !infoA.modificationDate || !infoB.modificationDate) return 0;
                
                const timeA = infoA.modificationDate.getTime();
                const timeB = infoB.modificationDate.getTime();
                
                return ascending ? timeA - timeB : timeB - timeA;
            } catch (error) {
                return 0;
            }
        });
    }
}
