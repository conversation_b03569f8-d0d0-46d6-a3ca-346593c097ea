/**
 * اختبارات وحدة متحكم YouTube - ملف التفويض الرئيسي
 * YouTube controller unit tests - Main delegation file
 *
 * هذا الملف يفوض جميع اختبارات متحكم YouTube للملفات المتخصصة
 * This file delegates all YouTube controller tests to specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { YouTubeControllerTestCore } from './youtube-controller-test-core';
import {
    YouTubeControllerTestSetup
} from './youtube-controller-test-types';

// Mock SecurityLayer
jest.mock('@infrastructure/security/security-layer');

describe('YouTubeController - Complete Test Suite', () => {
    let testSetup: YouTubeControllerTestSetup;

    beforeEach(async () => {
        // إنشاء إعداد الاختبار باستخدام الوحدة الأساسية
        // Create test setup using core module
        testSetup = await YouTubeControllerTestCore.createTestSetup();
    });

    afterEach(async () => {
        // تنظيف إعداد الاختبار باستخدام الوحدة الأساسية
        // Cleanup test setup using core module
        await YouTubeControllerTestCore.cleanupTestSetup(testSetup);
    });

    describe('Initialization Tests', () => {
        it('should initialize successfully', async () => {
            // تشغيل اختبار التهيئة باستخدام الوحدة الأساسية
            // Run initialization test using core module
            const result = await YouTubeControllerTestCore.runInitializationTest(testSetup);

            // التحقق من النتائج
            // Verify results
            expect(result.success).toBe(true);
            expect(result.message).toContain('تم تهيئة متحكم YouTube بنجاح');
        });
    });

    describe('Video Loading Tests', () => {
        it('should load video successfully', async () => {
            // تشغيل اختبار تحميل الفيديو باستخدام الوحدة الأساسية
            // Run video loading test using core module
            const result = await YouTubeControllerTestCore.runVideoLoadTest(testSetup);

            // التحقق من النتائج
            // Verify results
            expect(result.success).toBe(true);
            expect(result.message).toContain('تم تحميل الفيديو بنجاح');
        });
    });

    describe('Settings Application Tests', () => {
        it('should apply settings successfully', async () => {
            // تشغيل اختبار تطبيق الإعدادات باستخدام الوحدة الأساسية
            // Run settings application test using core module
            const result = await YouTubeControllerTestCore.runSettingsApplicationTest(testSetup);

            // التحقق من النتائج
            // Verify results
            expect(result.success).toBe(true);
            expect(result.message).toContain('تم تطبيق الإعدادات بنجاح');
        });
    });

    describe('Error Handling Tests', () => {
        it('should handle errors correctly', async () => {
            // تشغيل اختبار معالجة الأخطاء باستخدام الوحدة الأساسية
            // Run error handling test using core module
            const result = await YouTubeControllerTestCore.runErrorHandlingTest(testSetup);

            // التحقق من النتائج
            // Verify results
            expect(result.success).toBe(true);
            expect(result.message).toContain('تم التعامل مع الخطأ بشكل صحيح');
        });
    });

    describe('Performance Tests', () => {
        it('should execute tests efficiently', async () => {
            // قياس أداء الاختبارات باستخدام الوحدة الأساسية
            // Measure test performance using core module
            const performanceResult = await YouTubeControllerTestCore.measureTestPerformance(async () => {
                const initResult = await YouTubeControllerTestCore.runInitializationTest(testSetup);
                const videoResult = await YouTubeControllerTestCore.runVideoLoadTest(testSetup);
                const settingsResult = await YouTubeControllerTestCore.runSettingsApplicationTest(testSetup);

                return { initResult, videoResult, settingsResult };
            });

            // التحقق من الأداء
            // Verify performance
            expect(performanceResult.executionTime).toBeLessThan(5000); // أقل من 5 ثوان
            expect(performanceResult.memoryUsage).toBeLessThan(50 * 1024 * 1024); // أقل من 50MB
        });
    });
});
