/**
 * عمليات النوافذ
 * Window operations
 * 
 * هذا الملف يحتوي على عمليات إدارة النوافذ
 * This file contains window management operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { BASE_URLS, DEFAULT_WINDOW_BOUNDS, FILE_PATHS } from '@shared/constants';
import { ApplicationConfig, WindowBounds, WindowInfo } from '@shared/types';
import { BrowserWindow } from 'electron';
import * as path from 'path';

/**
 * فئة عمليات النوافذ
 * Window operations class
 */
export class WindowOperations {
    private readonly windows: Map<number, WindowInfo> = new Map();

    /**
     * إنشاء نافذة جديدة
     * Create new window
     * 
     * @param options - خيارات النافذة
     * @returns النافذة المنشأة
     */
    public createWindow(options: Electron.BrowserWindowConstructorOptions): BrowserWindow {
        const window = new BrowserWindow(options);
        
        // تسجيل النافذة
        // Register window
        this.registerWindow(window);
        
        return window;
    }

    /**
     * تسجيل النافذة في النظام
     * Register window in system
     * 
     * @param window - النافذة المراد تسجيلها
     */
    public registerWindow(window: BrowserWindow): void {
        const windowInfo: WindowInfo = {
            id: window.id,
            title: window.getTitle() || 'Unknown',
            bounds: window.getBounds(),
            isVisible: window.isVisible(),
            isMinimized: window.isMinimized(),
            isMaximized: window.isMaximized(),
            isFullScreen: window.isFullScreen(),
            createdAt: new Date(),
            lastFocused: new Date()
        };

        this.windows.set(window.id, windowInfo);

        // إعداد مستمعي الأحداث
        // Setup event listeners
        this.setupWindowEventListeners(window);
    }

    /**
     * إلغاء تسجيل النافذة
     * Unregister window
     * 
     * @param windowId - معرف النافذة
     */
    public unregisterWindow(windowId: number): void {
        this.windows.delete(windowId);
    }

    /**
     * إعداد مستمعي أحداث النافذة
     * Setup window event listeners
     * 
     * @param window - النافذة
     */
    private setupWindowEventListeners(window: BrowserWindow): void {
        // حدث الإغلاق
        // Close event
        window.on('closed', () => {
            this.unregisterWindow(window.id);
        });

        // حدث التركيز
        // Focus event
        window.on('focus', () => {
            const windowInfo = this.windows.get(window.id);
            if (windowInfo) {
                windowInfo.lastFocused = new Date();
                this.windows.set(window.id, windowInfo);
            }
        });

        // حدث تغيير الحجم
        // Resize event
        window.on('resize', () => {
            const windowInfo = this.windows.get(window.id);
            if (windowInfo) {
                windowInfo.bounds = window.getBounds();
                this.windows.set(window.id, windowInfo);
            }
        });

        // حدث التحريك
        // Move event
        window.on('move', () => {
            const windowInfo = this.windows.get(window.id);
            if (windowInfo) {
                windowInfo.bounds = window.getBounds();
                this.windows.set(window.id, windowInfo);
            }
        });

        // حدث التكبير
        // Maximize event
        window.on('maximize', () => {
            const windowInfo = this.windows.get(window.id);
            if (windowInfo) {
                windowInfo.isMaximized = true;
                this.windows.set(window.id, windowInfo);
            }
        });

        // حدث إلغاء التكبير
        // Unmaximize event
        window.on('unmaximize', () => {
            const windowInfo = this.windows.get(window.id);
            if (windowInfo) {
                windowInfo.isMaximized = false;
                this.windows.set(window.id, windowInfo);
            }
        });

        // حدث التصغير
        // Minimize event
        window.on('minimize', () => {
            const windowInfo = this.windows.get(window.id);
            if (windowInfo) {
                windowInfo.isMinimized = true;
                this.windows.set(window.id, windowInfo);
            }
        });

        // حدث إلغاء التصغير
        // Restore event
        window.on('restore', () => {
            const windowInfo = this.windows.get(window.id);
            if (windowInfo) {
                windowInfo.isMinimized = false;
                this.windows.set(window.id, windowInfo);
            }
        });
    }

    /**
     * الحصول على معلومات النافذة
     * Get window info
     * 
     * @param windowId - معرف النافذة
     * @returns معلومات النافذة أو null
     */
    public getWindowInfo(windowId: number): WindowInfo | null {
        return this.windows.get(windowId) || null;
    }

    /**
     * الحصول على جميع النوافذ المسجلة
     * Get all registered windows
     * 
     * @returns قائمة معلومات النوافذ
     */
    public getAllWindows(): WindowInfo[] {
        return Array.from(this.windows.values());
    }

    /**
     * البحث عن نافذة بالعنوان
     * Find window by title
     * 
     * @param title - عنوان النافذة
     * @returns معلومات النافذة أو null
     */
    public findWindowByTitle(title: string): WindowInfo | null {
        for (const windowInfo of this.windows.values()) {
            if (windowInfo.title === title) {
                return windowInfo;
            }
        }
        return null;
    }

    /**
     * إغلاق نافذة
     * Close window
     * 
     * @param windowId - معرف النافذة
     * @returns true إذا تم الإغلاق بنجاح
     */
    public closeWindow(windowId: number): boolean {
        try {
            const window = BrowserWindow.fromId(windowId);
            if (window && !window.isDestroyed()) {
                window.close();
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error closing window:', error);
            return false;
        }
    }

    /**
     * تركيز نافذة
     * Focus window
     * 
     * @param windowId - معرف النافذة
     * @returns true إذا تم التركيز بنجاح
     */
    public focusWindow(windowId: number): boolean {
        try {
            const window = BrowserWindow.fromId(windowId);
            if (window && !window.isDestroyed()) {
                window.focus();
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error focusing window:', error);
            return false;
        }
    }

    /**
     * تصغير نافذة
     * Minimize window
     * 
     * @param windowId - معرف النافذة
     * @returns true إذا تم التصغير بنجاح
     */
    public minimizeWindow(windowId: number): boolean {
        try {
            const window = BrowserWindow.fromId(windowId);
            if (window && !window.isDestroyed()) {
                window.minimize();
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error minimizing window:', error);
            return false;
        }
    }

    /**
     * تكبير نافذة
     * Maximize window
     * 
     * @param windowId - معرف النافذة
     * @returns true إذا تم التكبير بنجاح
     */
    public maximizeWindow(windowId: number): boolean {
        try {
            const window = BrowserWindow.fromId(windowId);
            if (window && !window.isDestroyed()) {
                if (window.isMaximized()) {
                    window.unmaximize();
                } else {
                    window.maximize();
                }
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error maximizing window:', error);
            return false;
        }
    }

    /**
     * تغيير حجم النافذة
     * Resize window
     * 
     * @param windowId - معرف النافذة
     * @param bounds - الحدود الجديدة
     * @returns true إذا تم التغيير بنجاح
     */
    public resizeWindow(windowId: number, bounds: WindowBounds): boolean {
        try {
            const window = BrowserWindow.fromId(windowId);
            if (window && !window.isDestroyed()) {
                window.setBounds(bounds);
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error resizing window:', error);
            return false;
        }
    }

    /**
     * تحديد موقع النافذة
     * Set window position
     * 
     * @param windowId - معرف النافذة
     * @param x - الموقع الأفقي
     * @param y - الموقع العمودي
     * @returns true إذا تم التحديد بنجاح
     */
    public setWindowPosition(windowId: number, x: number, y: number): boolean {
        try {
            const window = BrowserWindow.fromId(windowId);
            if (window && !window.isDestroyed()) {
                window.setPosition(x, y);
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error setting window position:', error);
            return false;
        }
    }

    /**
     * تحديد حجم النافذة
     * Set window size
     * 
     * @param windowId - معرف النافذة
     * @param width - العرض
     * @param height - الارتفاع
     * @returns true إذا تم التحديد بنجاح
     */
    public setWindowSize(windowId: number, width: number, height: number): boolean {
        try {
            const window = BrowserWindow.fromId(windowId);
            if (window && !window.isDestroyed()) {
                window.setSize(width, height);
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error setting window size:', error);
            return false;
        }
    }

    /**
     * تنظيف الموارد
     * Cleanup resources
     */
    public cleanup(): void {
        this.windows.clear();
    }
}
