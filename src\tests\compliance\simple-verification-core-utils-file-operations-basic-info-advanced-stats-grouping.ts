/**
 * تجميع الملفات المتقدم - ملف التفويض
 * Advanced file grouping operations - Delegation file
 * 
 * هذا الملف يفوض جميع العمليات للملفات المتخصصة
 * This file delegates all operations to specialized files
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsGroupingBasic } from './simple-verification-core-utils-file-operations-basic-info-advanced-stats-grouping-basic';
import { SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsGroupingAdvanced } from './simple-verification-core-utils-file-operations-basic-info-advanced-stats-grouping-advanced';

/**
 * فئة تجميع الملفات المتقدم - التفويض
 * Advanced file grouping class - Delegation
 */
export class SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsGrouping {

    /**
     * تجميع الملفات حسب الامتداد - تفويض للوحدة الأساسية
     * Group files by extension - Delegate to basic module
     */
    public static groupFilesByExtension(filePaths: string[]): Record<string, string[]> {
        // تفويض تجميع الملفات حسب الامتداد للوحدة الأساسية
        // Delegate file grouping by extension to basic module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsGroupingBasic.groupFilesByExtension(filePaths);
    }

    /**
     * تجميع الملفات حسب الحجم - تفويض للوحدة الأساسية
     * Group files by size range - Delegate to basic module
     */
    public static groupFilesBySizeRange(filePaths: string[]): {
        small: string[];    // < 1KB
        medium: string[];   // 1KB - 1MB
        large: string[];    // 1MB - 10MB
        huge: string[];     // > 10MB
    } {
        // تفويض تجميع الملفات حسب الحجم للوحدة الأساسية
        // Delegate file grouping by size to basic module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsGroupingBasic.groupFilesBySizeRange(filePaths);
    }

    /**
     * تجميع الملفات حسب المجلد الأب - تفويض للوحدة الأساسية
     * Group files by parent directory - Delegate to basic module
     */
    public static groupFilesByParentDirectory(filePaths: string[]): Record<string, string[]> {
        // تفويض تجميع الملفات حسب المجلد الأب للوحدة الأساسية
        // Delegate file grouping by parent directory to basic module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsGroupingBasic.groupFilesByParentDirectory(filePaths);
    }

    /**
     * تجميع الملفات حسب تاريخ التعديل - تفويض للوحدة المتقدمة
     * Group files by modification date range - Delegate to advanced module
     */
    public static groupFilesByModificationDateRange(filePaths: string[]): {
        today: string[];
        thisWeek: string[];
        thisMonth: string[];
        older: string[];
    } {
        // تفويض تجميع الملفات حسب تاريخ التعديل للوحدة المتقدمة
        // Delegate file grouping by modification date to advanced module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsGroupingAdvanced.groupFilesByModificationDateRange(filePaths);
    }

    /**
     * تجميع الملفات حسب الصلاحيات - تفويض للوحدة المتقدمة
     * Group files by permissions - Delegate to advanced module
     */
    public static groupFilesByPermissions(filePaths: string[]): {
        readOnly: string[];
        writeOnly: string[];
        readWrite: string[];
        noAccess: string[];
    } {
        // تفويض تجميع الملفات حسب الصلاحيات للوحدة المتقدمة
        // Delegate file grouping by permissions to advanced module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsGroupingAdvanced.groupFilesByPermissions(filePaths);
    }
}
