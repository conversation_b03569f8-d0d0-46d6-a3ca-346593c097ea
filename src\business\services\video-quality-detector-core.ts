/**
 * العمليات الأساسية لكشف جودة الفيديو
 * Core video quality detection operations
 * 
 * هذا الملف يحتوي على العمليات الأساسية لكشف جودة الفيديو
 * This file contains core video quality detection operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQuality } from '@shared/types';
import { 
    VideoQualityConfig,
    VIDEO_QUALITY_MAP,
    VIDEO_QUALITY_SELECTORS,
    VIDEO_QUALITY_CONSTANTS,
    VIDEO_QUALITY_MESSAGES,
    VideoQualityErrorType,
    VideoQualityErrorReport
} from './video-quality-config';

/**
 * فئة العمليات الأساسية لكشف جودة الفيديو
 * Core video quality detection operations class
 */
export class VideoQualityDetectorCore {
    private readonly config: VideoQualityConfig;
    private detectionCache: Map<string, VideoQuality> = new Map();
    private lastDetectionTime: number = 0;

    /**
     * منشئ العمليات الأساسية / Core operations constructor
     */
    constructor(config: VideoQualityConfig) {
        this.config = config;
    }

    /**
     * اكتشاف جودة الفيديو من النص / Detect quality from text
     */
    public detectQualityFromText(text: string): VideoQuality | null {
        if (!text) return null;

        const normalizedText = text.toLowerCase().trim();
        
        // البحث عن الجودة في النص
        for (const [quality, patterns] of Object.entries(VIDEO_QUALITY_MAP)) {
            for (const pattern of patterns) {
                if (normalizedText.includes(pattern.toLowerCase())) {
                    return quality as VideoQuality;
                }
            }
        }

        return null;
    }

    /**
     * اكتشاف الجودة من عنصر DOM / Detect quality from DOM element
     */
    public detectQualityFromElement(element: Element): VideoQuality | null {
        if (!element) return null;

        try {
            // فحص النص المباشر
            const textContent = element.textContent?.trim();
            if (textContent) {
                const qualityFromText = this.detectQualityFromText(textContent);
                if (qualityFromText) return qualityFromText;
            }

            // فحص الخصائص
            const ariaLabel = element.getAttribute('aria-label');
            if (ariaLabel) {
                const qualityFromAria = this.detectQualityFromText(ariaLabel);
                if (qualityFromAria) return qualityFromAria;
            }

            // فحص data attributes
            const dataQuality = element.getAttribute('data-quality');
            if (dataQuality) {
                const qualityFromData = this.detectQualityFromText(dataQuality);
                if (qualityFromData) return qualityFromData;
            }

            return null;
        } catch (error) {
            console.warn('خطأ في اكتشاف الجودة من العنصر:', error);
            return null;
        }
    }

    /**
     * البحث عن عناصر الجودة / Find quality elements
     */
    public findQualityElements(): Element[] {
        const elements: Element[] = [];

        try {
            // البحث باستخدام المحددات المختلفة
            for (const selector of VIDEO_QUALITY_SELECTORS.QUALITY_BUTTONS) {
                const found = document.querySelectorAll(selector);
                elements.push(...Array.from(found));
            }

            // البحث في قائمة الإعدادات
            for (const selector of VIDEO_QUALITY_SELECTORS.SETTINGS_MENU) {
                const found = document.querySelectorAll(selector);
                elements.push(...Array.from(found));
            }

            // إزالة المكررات
            return Array.from(new Set(elements));
        } catch (error) {
            console.warn('خطأ في البحث عن عناصر الجودة:', error);
            return [];
        }
    }

    /**
     * اكتشاف الجودة من عنصر الفيديو / Detect quality from video element
     */
    public detectQualityFromVideo(): VideoQuality | null {
        try {
            const videoElement = document.querySelector('video') as HTMLVideoElement;
            if (!videoElement) return null;

            // فحص خصائص الفيديو
            const videoHeight = videoElement.videoHeight;
            const videoWidth = videoElement.videoWidth;

            if (videoHeight && videoWidth) {
                return this.mapResolutionToQuality(videoWidth, videoHeight);
            }

            return null;
        } catch (error) {
            console.warn('خطأ في اكتشاف الجودة من الفيديو:', error);
            return null;
        }
    }

    /**
     * تحويل الدقة إلى جودة / Map resolution to quality
     */
    public mapResolutionToQuality(width: number, height: number): VideoQuality | null {
        // تحديد الجودة بناءً على الارتفاع
        if (height >= 2160) return '2160p';
        if (height >= 1440) return '1440p';
        if (height >= 1080) return '1080p';
        if (height >= 720) return '720p';
        if (height >= 480) return '480p';
        if (height >= 360) return '360p';
        if (height >= 240) return '240p';
        if (height >= 144) return '144p';

        return null;
    }

    /**
     * التحقق من صحة الجودة / Validate quality
     */
    public isValidQuality(quality: string): quality is VideoQuality {
        return Object.keys(VIDEO_QUALITY_MAP).includes(quality);
    }

    /**
     * الحصول على أفضل جودة متاحة / Get best available quality
     */
    public getBestAvailableQuality(availableQualities: VideoQuality[]): VideoQuality | null {
        if (!availableQualities || availableQualities.length === 0) return null;

        // ترتيب الجودات من الأعلى إلى الأقل
        const qualityOrder: VideoQuality[] = ['2160p', '1440p', '1080p', '720p', '480p', '360p', '240p', '144p', 'auto'];
        
        for (const quality of qualityOrder) {
            if (availableQualities.includes(quality)) {
                return quality;
            }
        }

        return availableQualities[0];
    }

    /**
     * الحصول على أقل جودة متاحة / Get lowest available quality
     */
    public getLowestAvailableQuality(availableQualities: VideoQuality[]): VideoQuality | null {
        if (!availableQualities || availableQualities.length === 0) return null;

        // ترتيب الجودات من الأقل إلى الأعلى
        const qualityOrder: VideoQuality[] = ['144p', '240p', '360p', '480p', '720p', '1080p', '1440p', '2160p', 'auto'];
        
        for (const quality of qualityOrder) {
            if (availableQualities.includes(quality)) {
                return quality;
            }
        }

        return availableQualities[0];
    }

    /**
     * مقارنة الجودات / Compare qualities
     */
    public compareQualities(quality1: VideoQuality, quality2: VideoQuality): number {
        const qualityValues: Record<VideoQuality, number> = {
            '144p': 144,
            '240p': 240,
            '360p': 360,
            '480p': 480,
            '720p': 720,
            '1080p': 1080,
            '1440p': 1440,
            '2160p': 2160,
            'auto': 9999
        };

        return qualityValues[quality1] - qualityValues[quality2];
    }

    /**
     * تحديث ذاكرة التخزين المؤقت / Update cache
     */
    public updateCache(key: string, quality: VideoQuality): void {
        this.detectionCache.set(key, quality);
        this.lastDetectionTime = Date.now();

        // تنظيف الذاكرة المؤقتة إذا كانت كبيرة
        if (this.detectionCache.size > VIDEO_QUALITY_CONSTANTS.MAX_CACHE_SIZE) {
            const firstKey = this.detectionCache.keys().next().value;
            this.detectionCache.delete(firstKey);
        }
    }

    /**
     * الحصول من ذاكرة التخزين المؤقت / Get from cache
     */
    public getFromCache(key: string): VideoQuality | null {
        const cached = this.detectionCache.get(key);
        if (cached && (Date.now() - this.lastDetectionTime) < VIDEO_QUALITY_CONSTANTS.CACHE_DURATION) {
            return cached;
        }
        return null;
    }

    /**
     * مسح ذاكرة التخزين المؤقت / Clear cache
     */
    public clearCache(): void {
        this.detectionCache.clear();
        this.lastDetectionTime = 0;
    }

    /**
     * الحصول على إحصائيات الكشف / Get detection statistics
     */
    public getDetectionStats(): {
        cacheSize: number;
        lastDetectionTime: number;
        cacheHitRate: number;
    } {
        return {
            cacheSize: this.detectionCache.size,
            lastDetectionTime: this.lastDetectionTime,
            cacheHitRate: this.detectionCache.size > 0 ? 0.8 : 0 // تقدير تقريبي
        };
    }
}
