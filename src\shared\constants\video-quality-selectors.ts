/**
 * محددات جودة الفيديو
 * Video quality selectors
 * 
 * هذا الملف يحتوي على محددات CSS وأحداث جودة الفيديو
 * This file contains CSS selectors and video quality events
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQuality } from '@shared/types';

/**
 * محددات CSS لأزرار جودة الفيديو في YouTube
 * CSS selectors for YouTube video quality buttons
 */
export const YOUTUBE_QUALITY_SELECTORS = {
    SETTINGS_BUTTON: '.ytp-settings-button',
    QUALITY_MENU: '.ytp-quality-menu',
    QUALITY_BUTTON: '.ytp-menuitem[role="menuitemradio"]',
    QUALITY_TEXT: '.ytp-menuitem-label',
    SETTINGS_MENU: '.ytp-settings-menu',
    PANEL_MENU: '.ytp-panel-menu'
} as const;

/**
 * نصوص جودة الفيديو في YouTube
 * YouTube video quality texts
 */
export const YOUTUBE_QUALITY_TEXTS: Record<VideoQuality, string[]> = {
    '144p': ['144p', '144'],
    '240p': ['240p', '240'],
    '360p': ['360p', '360'],
    '480p': ['480p', '480'],
    '720p': ['720p', '720', 'HD'],
    '1080p': ['1080p', '1080', 'Full HD', 'FHD'],
    '1440p': ['1440p', '1440', 'QHD', '2K'],
    '2160p': ['2160p', '2160', '4K', 'UHD', 'Ultra HD']
} as const;

/**
 * أوقات انتظار تطبيق جودة الفيديو
 * Video quality application timeouts
 */
export const VIDEO_QUALITY_TIMEOUTS = {
    DETECTION_TIMEOUT: 10000, // 10 ثواني
    APPLICATION_TIMEOUT: 15000, // 15 ثانية
    RETRY_DELAY: 2000, // ثانيتان
    MAX_RETRIES: 3,
    BUTTON_CLICK_DELAY: 500, // نصف ثانية
    MENU_OPEN_DELAY: 300, // 300 ميلي ثانية
    QUALITY_CHANGE_DELAY: 1000 // ثانية واحدة
} as const;

/**
 * رسائل حالة جودة الفيديو
 * Video quality status messages
 */
export const VIDEO_QUALITY_MESSAGES = {
    APPLYING: 'تطبيق جودة الفيديو... / Applying video quality...',
    APPLIED: 'تم تطبيق جودة الفيديو / Video quality applied',
    FAILED: 'فشل في تطبيق جودة الفيديو / Failed to apply video quality',
    NOT_AVAILABLE: 'جودة الفيديو غير متاحة / Video quality not available',
    DETECTING: 'اكتشاف جودات الفيديو المتاحة... / Detecting available video qualities...',
    DETECTED: 'تم اكتشاف جودات الفيديو / Video qualities detected',
    RETRYING: 'إعادة المحاولة... / Retrying...',
    TIMEOUT: 'انتهت مهلة تطبيق جودة الفيديو / Video quality application timeout'
} as const;

/**
 * أحداث جودة الفيديو
 * Video quality events
 */
export const VIDEO_QUALITY_EVENTS = {
    QUALITY_CHANGED: 'video-quality:changed',
    QUALITY_APPLYING: 'video-quality:applying',
    QUALITY_APPLIED: 'video-quality:applied',
    QUALITY_FAILED: 'video-quality:failed',
    QUALITIES_DETECTED: 'video-quality:detected',
    QUALITY_MENU_OPENED: 'video-quality:menu-opened',
    QUALITY_MENU_CLOSED: 'video-quality:menu-closed'
} as const;

/**
 * أنماط CSS لإخفاء أزرار الجودة
 * CSS styles for hiding quality buttons
 */
export const QUALITY_HIDING_STYLES = {
    HIDE_BUTTON: 'display: none !important;',
    HIDE_MENU: 'visibility: hidden !important;',
    DISABLE_BUTTON: 'pointer-events: none !important; opacity: 0.5 !important;'
} as const;

/**
 * إعدادات مراقبة جودة الفيديو
 * Video quality monitoring settings
 */
export const QUALITY_MONITORING = {
    CHECK_INTERVAL: 5000, // 5 ثواني
    MAX_CHECK_ATTEMPTS: 10,
    STABILITY_CHECK_DURATION: 30000, // 30 ثانية
    QUALITY_CHANGE_THRESHOLD: 3 // عدد التغييرات المسموح بها
} as const;

/**
 * إعدادات الجودة التلقائية
 * Auto quality settings
 */
export const AUTO_QUALITY_SETTINGS = {
    ENABLE_AUTO_QUALITY: true,
    PREFERRED_QUALITY: '480p' as VideoQuality,
    FALLBACK_QUALITY: '360p' as VideoQuality,
    MIN_QUALITY: '240p' as VideoQuality,
    MAX_QUALITY: '1080p' as VideoQuality,
    BANDWIDTH_THRESHOLD: 5000, // kbps
    QUALITY_STEP_DOWN_THRESHOLD: 2000, // kbps
    QUALITY_STEP_UP_THRESHOLD: 8000 // kbps
} as const;
