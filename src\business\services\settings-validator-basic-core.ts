/**
 * مدقق الإعدادات الأساسي - العمليات الأساسية
 * Basic settings validator - Core operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ApplicationConfig, ValidationResult } from '@shared/types';

/**
 * فئة العمليات الأساسية لمدقق الإعدادات
 */
export class SettingsValidatorBasicCore {
    constructor() {
        // منشئ بسيط
    }

    /**
     * التحقق من صحة إعداد واحد / Validate single setting
     */
    public validateSingleSetting<K extends keyof ApplicationConfig>(
        key: K,
        value: ApplicationConfig[K]
    ): ValidationResult {
        try {
            const errors: string[] = [];

            // التحقق من وجود المفتاح
            if (!this.isValidKey(key)) {
                errors.push(`مفتاح غير صالح: ${String(key)}`);
                return { isValid: false, errors };
            }

            // التحقق من النوع
            const typeValidation = this.validateType(key, value);
            if (!typeValidation.isValid) {
                errors.push(...typeValidation.errors);
            }

            // التحقق من القيود
            const constraintValidation = this.validateConstraints(key, value);
            if (!constraintValidation.isValid) {
                errors.push(...constraintValidation.errors);
            }

            return {
                isValid: errors.length === 0,
                errors
            };

        } catch (error) {
            return {
                isValid: false,
                errors: [`خطأ في التحقق من الإعداد ${String(key)}: ${error}`]
            };
        }
    }

    /**
     * التحقق من صحة النوع / Validate type
     */
    public validateType<K extends keyof ApplicationConfig>(
        key: K,
        value: ApplicationConfig[K]
    ): ValidationResult {
        const errors: string[] = [];

        try {
            switch (key) {
                case 'videoQuality':
                    if (typeof value !== 'string') {
                        errors.push('جودة الفيديو يجب أن تكون نص');
                    }
                    break;

                case 'darkMode':
                    if (typeof value !== 'boolean') {
                        errors.push('الوضع المظلم يجب أن يكون قيمة منطقية');
                    }
                    break;

                case 'adBlocker':
                    if (typeof value !== 'boolean') {
                        errors.push('مانع الإعلانات يجب أن يكون قيمة منطقية');
                    }
                    break;

                case 'autoPlay':
                    if (typeof value !== 'boolean') {
                        errors.push('التشغيل التلقائي يجب أن يكون قيمة منطقية');
                    }
                    break;

                case 'volume':
                    if (typeof value !== 'number') {
                        errors.push('مستوى الصوت يجب أن يكون رقم');
                    }
                    break;

                case 'playbackSpeed':
                    if (typeof value !== 'number') {
                        errors.push('سرعة التشغيل يجب أن تكون رقم');
                    }
                    break;

                case 'language':
                    if (typeof value !== 'string') {
                        errors.push('اللغة يجب أن تكون نص');
                    }
                    break;

                case 'theme':
                    if (typeof value !== 'string') {
                        errors.push('المظهر يجب أن يكون نص');
                    }
                    break;

                default:
                    // للإعدادات الأخرى، نتحقق من النوع العام
                    if (value === null || value === undefined) {
                        errors.push(`القيمة لا يمكن أن تكون فارغة للإعداد ${String(key)}`);
                    }
                    break;
            }

        } catch (error) {
            errors.push(`خطأ في التحقق من نوع الإعداد ${String(key)}: ${error}`);
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * التحقق من صحة المفتاح / Validate key
     */
    public isValidKey<K extends keyof ApplicationConfig>(key: K): boolean {
        const validKeys: Array<keyof ApplicationConfig> = [
            'videoQuality', 'darkMode', 'adBlocker', 'autoPlay', 
            'volume', 'playbackSpeed', 'language', 'theme'
        ];
        return validKeys.includes(key);
    }

    /**
     * التحقق من القيم الفارغة / Validate empty values
     */
    public validateNotEmpty<K extends keyof ApplicationConfig>(
        key: K,
        value: ApplicationConfig[K]
    ): ValidationResult {
        const errors: string[] = [];

        try {
            if (value === null || value === undefined) {
                errors.push(`القيمة لا يمكن أن تكون فارغة للإعداد ${String(key)}`);
            }

            if (typeof value === 'string' && value.trim() === '') {
                errors.push(`النص لا يمكن أن يكون فارغ للإعداد ${String(key)}`);
            }

            if (Array.isArray(value) && value.length === 0) {
                errors.push(`المصفوفة لا يمكن أن تكون فارغة للإعداد ${String(key)}`);
            }

        } catch (error) {
            errors.push(`خطأ في التحقق من القيم الفارغة للإعداد ${String(key)}: ${error}`);
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * التحقق من النوع الأساسي / Validate basic type
     */
    public validateBasicType(value: unknown, expectedType: string): ValidationResult {
        const errors: string[] = [];

        try {
            const actualType = Array.isArray(value) ? 'array' : typeof value;

            if (actualType !== expectedType) {
                errors.push(`نوع البيانات خاطئ. متوقع: ${expectedType}، فعلي: ${actualType}`);
            }

        } catch (error) {
            errors.push(`خطأ في التحقق من النوع الأساسي: ${error}`);
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }
}
