/**
 * مراقبة خدمة جودة الفيديو - المتقدمة
 * Video quality service monitoring - Advanced
 *
 * هذا الملف يجمع جميع وظائف المراقبة المتقدمة من الملفات المتخصصة
 * This file aggregates all advanced monitoring functions from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ValidationResult, VideoQuality } from '@shared/types';
import { ResourceManager } from '@shared/utils/resource-manager';
import { VideoQualityConfig } from './video-quality-config';

// استيراد الفئات المتخصصة / Import specialized classes
import {
    VideoQualityServiceMonitoringAdvancedCore
} from './video-quality-service-monitoring-advanced-core';
import {
    MonitoringStatistics,
    QualityChangeEvent,
    VideoQualityServiceMonitoringAdvancedStatistics
} from './video-quality-service-monitoring-advanced-statistics';

// إعادة تصدير الأنواع / Re-export types
export { MonitoringStatistics, QualityChangeEvent };

/**
 * فئة المراقبة المتقدمة لخدمة جودة الفيديو
 * Advanced video quality service monitoring class
 */
export class VideoQualityServiceMonitoringAdvanced {
    private readonly coreOperations: VideoQualityServiceMonitoringAdvancedCore;
    private readonly statisticsManager: VideoQualityServiceMonitoringAdvancedStatistics;

    constructor(resourceManager: ResourceManager, config: VideoQualityConfig) {
        this.coreOperations = new VideoQualityServiceMonitoringAdvancedCore(resourceManager, config);
        this.statisticsManager = new VideoQualityServiceMonitoringAdvancedStatistics();
    }

    /**
     * بدء المراقبة المتقدمة / Start advanced monitoring
     */
    public async startAdvancedMonitoring(): Promise<ValidationResult> {
        try {
            // بدء تسجيل الإحصائيات
            this.statisticsManager.startStatisticsRecording();

            // بدء مراقبة الأداء
            const performanceResult = await this.coreOperations.startPerformanceMonitoring();
            if (!performanceResult.isValid) {
                return performanceResult;
            }

            // بدء المراقبة الدورية
            const periodicResult = await this.coreOperations.startPeriodicMonitoring();
            if (!periodicResult.isValid) {
                return periodicResult;
            }

            return {
                isValid: true,
                errors: []
            };

        } catch (error) {
            return {
                isValid: false,
                errors: [`خطأ في بدء المراقبة المتقدمة: ${error}`]
            };
        }
    }

    /**
     * إيقاف المراقبة المتقدمة / Stop advanced monitoring
     */
    public async stopAdvancedMonitoring(): Promise<ValidationResult> {
        try {
            // إيقاف تسجيل الإحصائيات
            this.statisticsManager.stopStatisticsRecording();

            // إيقاف مراقبة الأداء
            const performanceResult = await this.coreOperations.stopPerformanceMonitoring();

            // إيقاف المراقبة الدورية
            const periodicResult = await this.coreOperations.stopPeriodicMonitoring();

            const errors: string[] = [];
            if (!performanceResult.isValid) {
                errors.push(...performanceResult.errors);
            }
            if (!periodicResult.isValid) {
                errors.push(...periodicResult.errors);
            }

            return {
                isValid: errors.length === 0,
                errors
            };

        } catch (error) {
            return {
                isValid: false,
                errors: [`خطأ في إيقاف المراقبة المتقدمة: ${error}`]
            };
        }
    }

    /**
     * الحصول على جودة الفيديو الحالية / Get current video quality
     */
    public getCurrentVideoQuality(): VideoQuality | null {
        return this.coreOperations.getCurrentVideoQuality();
    }

    /**
     * تسجيل فحص الجودة / Record quality check
     */
    public recordQualityCheck(quality: VideoQuality): void {
        this.statisticsManager.recordQualityCheck(quality);
    }

    /** إجراء فحص صحة النظام / Perform health check */
    public performHealthCheck() {
        return this.coreOperations.performHealthCheck();
    }

    /** فحص أداء الفيديو / Check video performance */
    public checkVideoPerformance() {
        return this.coreOperations.checkVideoPerformance();
    }

    /** الحصول على الإحصائيات / Get statistics */
    public getStatistics(): MonitoringStatistics {
        return this.statisticsManager.getStatistics();
    }

    /** الحصول على تاريخ الجودة / Get quality history */
    public getQualityHistory(): QualityChangeEvent[] {
        return this.statisticsManager.getQualityHistory();
    }

    /** الحصول على إحصائيات مفصلة / Get detailed statistics */
    public getDetailedStatistics() {
        return this.statisticsManager.getDetailedStatistics();
    }

    /** إعادة تعيين الإحصائيات / Reset statistics */
    public resetStatistics(): void {
        this.statisticsManager.resetStatistics();
    }

    /** تصدير الإحصائيات / Export statistics */
    public exportStatistics(): string {
        return this.statisticsManager.exportStatistics();
    }

    /** استيراد الإحصائيات / Import statistics */
    public importStatistics(jsonData: string): boolean {
        return this.statisticsManager.importStatistics(jsonData);
    }

    /** تنظيف الموارد / Cleanup resources */
    public async cleanup(): Promise<void> {
        try {
            await this.stopAdvancedMonitoring();
            await this.coreOperations.cleanup();
            this.resetStatistics();
        } catch (error) {
            console.error('خطأ في تنظيف الموارد:', error);
        }
    }
}
