/**
 * ثوابت CSS للوضع المظلم
 * Dark mode CSS constants
 *
 * هذا الملف يحتوي على ثوابت CSS المستخدمة في الوضع المظلم
 * This file contains CSS constants used in dark mode
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// إعادة تصدير المكونات المتخصصة
// Re-export specialized components
export * from './dark-mode-css-selectors';
export * from './dark-mode-css-styles';


import {
    DARK_MODE_CSS_CLASSES,
    DARK_MODE_CSS_PROPERTIES,
    DARK_MODE_CSS_SELECTORS
} from './dark-mode-css-selectors';
import {
    DARK_MODE_FILTERS,
    DARK_MODE_STYLE_TEMPLATES,
    DARK_MODE_TRANSITIONS
} from './dark-mode-css-styles';

/**
 * تجميع جميع ثوابت CSS
 * All CSS constants combined
 */
export const DARK_MODE_CSS_CONSTANTS = {
    SELECTORS: DARK_MODE_CSS_SELECTORS,
    CLASSES: DARK_MODE_CSS_CLASSES,
    PROPERTIES: DARK_MODE_CSS_PROPERTIES,
    TRANSITIONS: DARK_MODE_TRANSITIONS,
    FILTERS: DARK_MODE_FILTERS,
    TEMPLATES: DARK_MODE_STYLE_TEMPLATES
} as const;
