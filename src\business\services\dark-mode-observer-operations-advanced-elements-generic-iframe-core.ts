/**
 * الوظائف الأساسية لمعالجة iframe في الوضع المظلم
 * Core iframe processing functions for dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeConfig } from './dark-mode-config';
import { DarkModeObserverOperationsAdvancedElementsGenericIframeCoreStyles } from './dark-mode-observer-operations-advanced-elements-generic-iframe-core-styles';

/**
 * فئة الوظائف الأساسية لمعالجة iframe
 * Core iframe processing functions class
 */
export class DarkModeObserverOperationsAdvancedElementsGenericIframeCore {

    /** معالجة عنصر iframe / Process iframe element */
    public static processIframeElement(element: HTMLIFrameElement, config: DarkModeConfig): void {
        try {
            // إضافة فئة الوضع المظلم
            element.classList.add('dark-mode-iframe');

            // تطبيق الأنماط الأساسية
            this.applyBaseStyles(element);

            // كشف نوع iframe
            const iframeType = this.detectIframeType(element);

            // تطبيق أنماط خاصة حسب النوع
            this.applyTypeSpecificStyles(element, iframeType, config);

        } catch (error) {
            console.error('خطأ في معالجة عنصر iframe:', error);
        }
    }

    /** تطبيق الأنماط الأساسية / Apply base styles */
    public static applyBaseStyles(element: HTMLIFrameElement): void {
        const styles = {
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: '8px',
            backgroundColor: '#1a1a1a',
            filter: 'brightness(0.9) contrast(1.1)',
            transition: 'all 0.3s ease'
        };

        Object.assign(element.style, styles);
    }

    /** كشف نوع iframe / Detect iframe type */
    public static detectIframeType(element: HTMLIFrameElement): string {
        const src = element.src || '';
        const className = element.className.toLowerCase();

        // فحص المصدر
        if (src.includes('youtube.com') || src.includes('youtu.be')) {
            return 'youtube';
        }

        if (src.includes('vimeo.com')) {
            return 'vimeo';
        }

        if (src.includes('facebook.com')) {
            return 'facebook';
        }

        if (src.includes('twitter.com') || src.includes('x.com')) {
            return 'twitter';
        }

        if (src.includes('instagram.com')) {
            return 'instagram';
        }

        if (src.includes('maps.google.com') || src.includes('google.com/maps')) {
            return 'maps';
        }

        // فحص الفئات
        if (className.includes('video') || className.includes('player')) {
            return 'video';
        }

        if (className.includes('map')) {
            return 'map';
        }

        if (className.includes('social')) {
            return 'social';
        }

        if (className.includes('ad') || className.includes('advertisement')) {
            return 'advertisement';
        }

        return 'generic';
    }

    /** تطبيق أنماط خاصة حسب النوع / Apply type specific styles */
    public static applyTypeSpecificStyles(element: HTMLIFrameElement, type: string, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsGenericIframeCoreStyles.applyTypeSpecificStyles(element, type, config);
    }

    /** إضافة عنصر بديل / Add placeholder */
    public static addIframePlaceholder(element: HTMLIFrameElement, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsGenericIframeCoreStyles.addIframePlaceholder(element, config);
    }
}
