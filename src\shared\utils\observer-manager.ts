/**
 * مدير المراقبات
 * Observer manager
 *
 * هذا الملف يحتوي على منطق إدارة المراقبات والأحداث
 * This file contains observer and event management logic
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// إعادة تصدير المديرين المتخصصين
// Re-export specialized managers
export * from './event-listener-manager';
export * from './mutation-observer-manager';

import { EventListenerManager } from './event-listener-manager';
import { MutationObserverManager } from './mutation-observer-manager';

/**
 * مدير المراقبات الشامل
 * Comprehensive observer manager
 */
export class ObserverManager {
    private readonly mutationObserverManager: MutationObserverManager = new MutationObserverManager();
    private readonly eventListenerManager: EventListenerManager = new EventListenerManager();
    private readonly intersectionObservers: Set<IntersectionObserver> = new Set();
    private readonly resizeObservers: Set<ResizeObserver> = new Set();

    /**
     * الحصول على مدير مراقبات التحولات
     * Gets the mutation observer manager
     *
     * @returns MutationObserverManager - مدير مراقبات التحولات / Mutation observer manager
     */
    public getMutationObserverManager(): MutationObserverManager {
        return this.mutationObserverManager;
    }

    /**
     * الحصول على مدير مستمعي الأحداث
     * Gets the event listener manager
     *
     * @returns EventListenerManager - مدير مستمعي الأحداث / Event listener manager
     */
    public getEventListenerManager(): EventListenerManager {
        return this.eventListenerManager;
    }

    /**
     * إضافة مراقب التقاطع للإدارة
     * Adds an intersection observer for management
     *
     * @param observer - مراقب التقاطع / Intersection observer
     * @returns IntersectionObserver - مراقب التقاطع / Intersection observer
     *
     * @example
     * ```typescript
     * const observer = new IntersectionObserver(() => {});
     * observerManager.addIntersectionObserver(observer);
     * ```
     */
    public addIntersectionObserver(observer: IntersectionObserver): IntersectionObserver {
        this.intersectionObservers.add(observer);
        return observer;
    }

    /**
     * إضافة مراقب تغيير الحجم للإدارة
     * Adds a resize observer for management
     *
     * @param observer - مراقب تغيير الحجم / Resize observer
     * @returns ResizeObserver - مراقب تغيير الحجم / Resize observer
     *
     * @example
     * ```typescript
     * const observer = new ResizeObserver(() => {});
     * observerManager.addResizeObserver(observer);
     * ```
     */
    public addResizeObserver(observer: ResizeObserver): ResizeObserver {
        this.resizeObservers.add(observer);
        return observer;
    }

    /**
     * إزالة مراقب التقاطع من الإدارة
     * Removes an intersection observer from management
     *
     * @param observer - مراقب التقاطع / Intersection observer
     *
     * @example
     * ```typescript
     * observerManager.removeIntersectionObserver(observer);
     * ```
     */
    public removeIntersectionObserver(observer: IntersectionObserver): void {
        observer.disconnect();
        this.intersectionObservers.delete(observer);
    }

    /**
     * إزالة مراقب تغيير الحجم من الإدارة
     * Removes a resize observer from management
     *
     * @param observer - مراقب تغيير الحجم / Resize observer
     *
     * @example
     * ```typescript
     * observerManager.removeResizeObserver(observer);
     * ```
     */
    public removeResizeObserver(observer: ResizeObserver): void {
        observer.disconnect();
        this.resizeObservers.delete(observer);
    }
    /**
     * إنشاء مراقب تقاطع مُدار
     * Creates a managed intersection observer
     *
     * @param callback - دالة الاستدعاء / Callback function
     * @param options - خيارات المراقب / Observer options
     * @returns IntersectionObserver - مراقب التقاطع / Intersection observer
     *
     * @example
     * ```typescript
     * const observer = observerManager.createIntersectionObserver((entries) => {
     *     console.log('Intersections:', entries);
     * });
     * ```
     */
    public createIntersectionObserver(
        callback: IntersectionObserverCallback,
        options?: IntersectionObserverInit
    ): IntersectionObserver {
        const observer = new IntersectionObserver(callback, options);
        this.intersectionObservers.add(observer);
        return observer;
    }

    /**
     * إنشاء مراقب تغيير الحجم مُدار
     * Creates a managed resize observer
     *
     * @param callback - دالة الاستدعاء / Callback function
     * @returns ResizeObserver - مراقب تغيير الحجم / Resize observer
     *
     * @example
     * ```typescript
     * const observer = observerManager.createResizeObserver((entries) => {
     *     console.log('Resize:', entries);
     * });
     * ```
     */
    public createResizeObserver(callback: ResizeObserverCallback): ResizeObserver {
        const observer = new ResizeObserver(callback);
        this.resizeObservers.add(observer);
        return observer;
    }

    /**
     * تنظيف جميع المراقبات والأحداث
     * Clears all observers and events
     *
     * @example
     * ```typescript
     * observerManager.clearAll();
     * ```
     */
    public clearAll(): void {
        this.mutationObserverManager.clearAll();
        this.eventListenerManager.clearAll();

        for (const observer of this.intersectionObservers) {
            observer.disconnect();
        }
        this.intersectionObservers.clear();

        for (const observer of this.resizeObservers) {
            observer.disconnect();
        }
        this.resizeObservers.clear();
    }

    /**
     * الحصول على عدد المراقبات النشطة
     * Gets the count of active observers
     *
     * @returns number - عدد المراقبات النشطة / Active observer count
     *
     * @example
     * ```typescript
     * const count = observerManager.getObserverCount();
     * ```
     */
    public getObserverCount(): number {
        return this.mutationObserverManager.getObserverCount() +
            this.intersectionObservers.size +
            this.resizeObservers.size;
    }

    /**
     * الحصول على عدد مستمعي الأحداث النشطة
     * Gets the count of active event listeners
     *
     * @returns number - عدد مستمعي الأحداث النشطة / Active event listener count
     *
     * @example
     * ```typescript
     * const count = observerManager.getEventListenerCount();
     * ```
     */
    public getEventListenerCount(): number {
        return this.eventListenerManager.getEventListenerCount();
    }
}
