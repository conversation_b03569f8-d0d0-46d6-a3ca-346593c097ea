/**
 * إعداد الاختبارات العامة
 * Global test setup
 * 
 * هذا الملف يحتوي على الإعدادات العامة لجميع الاختبارات
 * This file contains global setup for all tests
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import 'jest-extended';

// Mock Electron APIs
const mockElectron = {
    ipcMain: {
        handle: jest.fn(),
        on: jest.fn(),
        removeAllListeners: jest.fn()
    },
    ipcRenderer: {
        invoke: jest.fn(),
        send: jest.fn(),
        on: jest.fn(),
        removeAllListeners: jest.fn()
    },
    app: {
        getPath: jest.fn(() => '/mock/path'),
        quit: jest.fn(),
        on: jest.fn(),
        whenReady: jest.fn(() => Promise.resolve())
    },
    BrowserWindow: jest.fn().mockImplementation(() => ({
        loadURL: jest.fn(),
        loadFile: jest.fn(),
        on: jest.fn(),
        webContents: {
            on: jest.fn(),
            send: jest.fn(),
            executeJavaScript: jest.fn()
        },
        show: jest.fn(),
        hide: jest.fn(),
        close: jest.fn(),
        destroy: jest.fn(),
        isDestroyed: jest.fn(() => false),
        getBounds: jest.fn(() => ({ x: 0, y: 0, width: 1200, height: 800 })),
        setBounds: jest.fn(),
        setSize: jest.fn(),
        center: jest.fn(),
        setMenuBarVisibility: jest.fn()
    })),
    Menu: {
        buildFromTemplate: jest.fn(),
        setApplicationMenu: jest.fn()
    },
    shell: {
        openExternal: jest.fn()
    },
    dialog: {
        showMessageBox: jest.fn(),
        showOpenDialog: jest.fn(),
        showSaveDialog: jest.fn()
    }
};

// Mock Node.js modules
jest.mock('electron', () => mockElectron);

jest.mock('fs', () => ({
    existsSync: jest.fn(),
    readFileSync: jest.fn(),
    writeFileSync: jest.fn(),
    mkdirSync: jest.fn(),
    promises: {
        readFile: jest.fn(),
        writeFile: jest.fn(),
        mkdir: jest.fn()
    }
}));

jest.mock('path', () => ({
    join: jest.fn((...args) => args.join('/')),
    resolve: jest.fn((...args) => args.join('/')),
    dirname: jest.fn((path) => path.split('/').slice(0, -1).join('/')),
    basename: jest.fn((path) => path.split('/').pop()),
    extname: jest.fn((path) => {
        const parts = path.split('.');
        return parts.length > 1 ? '.' + parts.pop() : '';
    })
}));

// Mock DOM APIs for jsdom environment
Object.defineProperty(window, 'electronAPI', {
    value: {
        updateSettings: jest.fn(() => Promise.resolve({ isValid: true, errors: [] })),
        toggleAdBlocker: jest.fn(() => Promise.resolve({ isValid: true, errors: [] })),
        toggleDarkMode: jest.fn(() => Promise.resolve({ isValid: true, errors: [] })),
        setVideoQuality: jest.fn(() => Promise.resolve({ isValid: true, errors: [] })),
        getSettings: jest.fn(() => Promise.resolve({
            videoQuality: 'auto',
            darkModeEnabled: true,
            adBlockEnabled: true,
            autoApplySettings: true,
            windowBounds: { width: 1200, height: 800 }
        }))
    },
    writable: true
});

// Mock MutationObserver
global.MutationObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    disconnect: jest.fn(),
    takeRecords: jest.fn()
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn()
}));

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn()
}));

// Mock console methods to reduce noise in tests
const originalConsole = { ...console };
global.console = {
    ...console,
    log: jest.fn(),
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
};

// Mock timers
jest.useFakeTimers();

// Global test utilities
global.testUtils = {
    // Helper to create mock DOM elements
    createMockElement: (tagName: string, attributes: Record<string, string> = {}) => {
        const element = document.createElement(tagName);
        Object.entries(attributes).forEach(([key, value]) => {
            element.setAttribute(key, value);
        });
        return element;
    },

    // Helper to wait for async operations
    waitFor: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

    // Helper to create mock events
    createMockEvent: (type: string, properties: Record<string, any> = {}) => {
        const event = new Event(type);
        Object.assign(event, properties);
        return event;
    },

    // Helper to restore console
    restoreConsole: () => {
        global.console = originalConsole;
    },

    // Helper to create mock validation result
    createMockValidationResult: (isValid: boolean = true, errors: any[] = []) => ({
        isValid,
        errors
    }),

    // Helper to create mock application config
    createMockApplicationConfig: (overrides: Record<string, any> = {}) => ({
        videoQuality: 'auto',
        darkModeEnabled: true,
        adBlockEnabled: true,
        autoApplySettings: true,
        windowBounds: { width: 1200, height: 800 },
        ...overrides
    })
};

// Setup and teardown hooks
beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    
    // Reset fake timers
    jest.clearAllTimers();
    
    // Clear DOM
    document.body.innerHTML = '';
    document.head.innerHTML = '';
});

afterEach(() => {
    // Run any pending timers
    jest.runOnlyPendingTimers();
    
    // Clear any remaining timeouts/intervals
    jest.clearAllTimers();
});

beforeAll(() => {
    // Setup global test environment
    process.env.NODE_ENV = 'test';
    process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = 'true';
});

afterAll(() => {
    // Cleanup after all tests
    jest.useRealTimers();
    global.testUtils.restoreConsole();
});

// Custom matchers
expect.extend({
    toBeValidationResult(received) {
        const pass = received && 
                    typeof received.isValid === 'boolean' && 
                    Array.isArray(received.errors);
        
        if (pass) {
            return {
                message: () => `expected ${received} not to be a valid ValidationResult`,
                pass: true
            };
        } else {
            return {
                message: () => `expected ${received} to be a valid ValidationResult with isValid and errors properties`,
                pass: false
            };
        }
    },

    toHaveValidationErrors(received, expectedCount?: number) {
        const pass = received && 
                    received.isValid === false && 
                    Array.isArray(received.errors) &&
                    (expectedCount === undefined || received.errors.length === expectedCount);
        
        if (pass) {
            return {
                message: () => `expected ${received} not to have ${expectedCount || 'any'} validation errors`,
                pass: true
            };
        } else {
            return {
                message: () => `expected ${received} to have ${expectedCount || 'some'} validation errors`,
                pass: false
            };
        }
    }
});

// Type declarations for custom matchers
declare global {
    namespace jest {
        interface Matchers<R> {
            toBeValidationResult(): R;
            toHaveValidationErrors(expectedCount?: number): R;
        }
    }
    
    interface Window {
        electronAPI: any;
    }
    
    var testUtils: {
        createMockElement: (tagName: string, attributes?: Record<string, string>) => HTMLElement;
        waitFor: (ms: number) => Promise<void>;
        createMockEvent: (type: string, properties?: Record<string, any>) => Event;
        restoreConsole: () => void;
        createMockValidationResult: (isValid?: boolean, errors?: any[]) => any;
        createMockApplicationConfig: (overrides?: Record<string, any>) => any;
    };
}
