/**
 * مراقبة الوضع المظلم - إدارة الأحداث
 * Dark mode monitoring - Event management
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import {
    DarkModeConfig
} from './dark-mode-config';

/**
 * فئة إدارة أحداث مراقبة الوضع المظلم
 * Dark mode monitoring event management class
 */
export class DarkModeObserverMonitoringEvents {
    private readonly config: DarkModeConfig;
    private eventListeners: Map<string, EventListener[]> = new Map();
    private isListening: boolean = false;
    private eventsCore: DarkModeObserverMonitoringEventsCore;

    /**
     * منشئ فئة إدارة أحداث مراقبة الوضع المظلم
     * Dark mode monitoring event management constructor
     */
    constructor(config: DarkModeConfig) {
        this.config = config;
        this.eventsCore = new DarkModeObserverMonitoringEventsCore(config);
    }

    /**
     * بدء الاستماع للأحداث
     * Start listening to events
     */
    public startEventListening(): void {
        if (this.isListening) {
            console.log('Event listening already started');
            return;
        }

        try {
            this.eventsCore.setupDOMContentLoadedListener();
            this.eventsCore.setupVisibilityChangeListener();
            this.eventsCore.setupResizeListener();
            this.eventsCore.setupFocusListeners();
            this.eventsCore.setupCustomEventListeners();
            this.eventsCore.setupPlayerEventListeners();
            this.eventsCore.setupNetworkEventListeners();
            this.eventsCore.setupHistoryEventListeners();
            this.isListening = true;
            this.eventsCore.setListeningState(true);
            console.log('Event listening started successfully');
        } catch (error) {
            console.error('Error starting event listening:', error);
        }
    }

    /**
     * إيقاف الاستماع للأحداث
     * Stop listening to events
     */
    public stopEventListening(): void {
        if (!this.isListening) {
            console.log('Event listening not active');
            return;
        }

        try {
            this.removeAllEventListeners();
            this.eventsCore.cleanup();
            this.isListening = false;
            this.eventsCore.setListeningState(false);
            console.log('Event listening stopped successfully');
        } catch (error) {
            console.error('Error stopping event listening:', error);
        }
    }











    /**
     * إضافة مستمع حدث
     * Add event listener
     */
    private addEventListener(eventType: string, listener: EventListener, target: EventTarget): void {
        try {
            target.addEventListener(eventType, listener);

            if (!this.eventListeners.has(eventType)) {
                this.eventListeners.set(eventType, []);
            }
            this.eventListeners.get(eventType)!.push(listener);
        } catch (error) {
            console.error(`Error adding event listener for ${eventType}:`, error);
        }
    }

    /**
     * إزالة جميع مستمعي الأحداث
     * Remove all event listeners
     */
    private removeAllEventListeners(): void {
        this.eventListeners.forEach((listeners, eventType) => {
            listeners.forEach(listener => {
                try {
                    // إزالة من المستندات والنوافذ
                    document.removeEventListener(eventType, listener);
                    window.removeEventListener(eventType, listener);
                } catch (error) {
                    console.error(`Error removing event listener for ${eventType}:`, error);
                }
            });
        });

        this.eventListeners.clear();
    }

    /**
     * تشغيل تطبيق الوضع المظلم
     * Trigger dark mode application
     */
    private triggerDarkModeApplication(): void {
        // إرسال حدث مخصص لتطبيق الوضع المظلم
        const event = new CustomEvent('applydarkmode', {
            detail: {
                timestamp: Date.now(),
                source: 'monitoring-events'
            }
        });

        document.dispatchEvent(event);
    }

    /**
     * إرسال حدث مخصص
     * Dispatch custom event
     */
    public dispatchCustomEvent(eventType: string, detail: any): void {
        try {
            const event = new CustomEvent(eventType, {
                detail: {
                    ...detail,
                    timestamp: Date.now(),
                    source: 'dark-mode-monitoring'
                }
            });

            document.dispatchEvent(event);
        } catch (error) {
            console.error(`Error dispatching custom event ${eventType}:`, error);
        }
    }

    /**
     * الحصول على حالة الاستماع
     * Get listening status
     */
    public isEventListening(): boolean {
        return this.isListening;
    }

    /**
     * إعادة تشغيل الاستماع للأحداث
     * Restart event listening
     */
    public restartEventListening(): void {
        this.stopEventListening();
        setTimeout(() => {
            this.startEventListening();
        }, 100);
    }

    /**
     * الحصول على عدد مستمعي الأحداث النشطين
     * Get active event listeners count
     */
    public getActiveListenersCount(): number {
        let count = 0;
        this.eventListeners.forEach(listeners => {
            count += listeners.length;
        });
        return count;
    }

    /**
     * الحصول على أنواع الأحداث النشطة
     * Get active event types
     */
    public getActiveEventTypes(): string[] {
        return Array.from(this.eventListeners.keys());
    }
}
