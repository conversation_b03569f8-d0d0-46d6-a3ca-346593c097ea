/**
 * معالجة محتوى iframe المتقدمة في الوضع المظلم
 * Advanced iframe content processing for dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeConfig } from './dark-mode-config';

/**
 * فئة معالجة محتوى iframe المتقدمة
 * Advanced iframe content processing class
 */
export class DarkModeObserverOperationsAdvancedElementsGenericIframeContentProcessing {

    /** معالجة المحتوى القابل للوصول / Process accessible content */
    public static processAccessibleContent(element: HTMLIFrameElement, config: DarkModeConfig): void {
        const contentDocument = element.contentDocument || element.contentWindow?.document;
        
        if (!contentDocument) return;

        // تطبيق أنماط الوضع المظلم على المحتوى
        this.applyDarkModeToContent(contentDocument, config);
        
        // مراقبة تغييرات المحتوى
        this.observeContentChanges(contentDocument, config);
        
        // معالجة الروابط والنماذج
        this.processContentElements(contentDocument, config);
    }

    /** معالجة المحتوى غير القابل للوصول / Process inaccessible content */
    public static processInaccessibleContent(element: HTMLIFrameElement, config: DarkModeConfig): void {
        // تطبيق فلاتر CSS على iframe نفسه
        this.applyCSSFilters(element);
        
        // إضافة overlay للتحكم
        this.addControlOverlay(element, config);
        
        // مراقبة تحميل المحتوى
        this.monitorContentLoading(element, config);
    }

    /** تطبيق الوضع المظلم على المحتوى / Apply dark mode to content */
    public static applyDarkModeToContent(document: Document, config: DarkModeConfig): void {
        // إضافة CSS للوضع المظلم
        const darkModeCSS = this.generateDarkModeCSS();
        this.injectCSS(document, darkModeCSS);
        
        // معالجة عناصر محددة
        this.processSpecificElements(document, config);
    }

    /** معالجة عناصر محددة / Process specific elements */
    public static processSpecificElements(document: Document, config: DarkModeConfig): void {
        // معالجة الصور
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            img.style.filter = 'brightness(0.9) contrast(1.1)';
        });
        
        // معالجة الفيديوهات
        const videos = document.querySelectorAll('video');
        videos.forEach(video => {
            video.style.filter = 'brightness(0.9) contrast(1.1)';
        });
        
        // معالجة النماذج
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            this.processFormInIframe(form as HTMLFormElement, config);
        });
    }

    /** معالجة النموذج داخل iframe / Process form in iframe */
    public static processFormInIframe(form: HTMLFormElement, config: DarkModeConfig): void {
        form.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
        form.style.border = '1px solid rgba(255, 255, 255, 0.1)';
        form.style.borderRadius = '8px';
        form.style.padding = '16px';
    }

    /** مراقبة تغييرات المحتوى / Observe content changes */
    public static observeContentChanges(document: Document, config: DarkModeConfig): void {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            this.processNewElement(node as Element, config);
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    /** معالجة عنصر جديد / Process new element */
    public static processNewElement(element: Element, config: DarkModeConfig): void {
        const tagName = element.tagName.toLowerCase();
        
        switch (tagName) {
            case 'img':
                (element as HTMLImageElement).style.filter = 'brightness(0.9) contrast(1.1)';
                break;
            case 'video':
                (element as HTMLVideoElement).style.filter = 'brightness(0.9) contrast(1.1)';
                break;
            case 'form':
                this.processFormInIframe(element as HTMLFormElement, config);
                break;
        }
    }

    /** معالجة عناصر المحتوى / Process content elements */
    public static processContentElements(document: Document, config: DarkModeConfig): void {
        // معالجة الروابط
        const links = document.querySelectorAll('a');
        links.forEach(link => {
            link.style.color = '#4da6ff';
        });
        
        // معالجة النصوص
        const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6');
        textElements.forEach(element => {
            if (!element.style.color) {
                element.style.color = '#ffffff';
            }
        });
        
        // معالجة الخلفيات
        const backgroundElements = document.querySelectorAll('div, section, article, main, aside, header, footer');
        backgroundElements.forEach(element => {
            if (!element.style.backgroundColor) {
                element.style.backgroundColor = '#2a2a2a';
            }
        });
    }

    /** تطبيق فلاتر CSS / Apply CSS filters */
    public static applyCSSFilters(element: HTMLIFrameElement): void {
        element.style.filter = 'brightness(0.8) contrast(1.2) hue-rotate(180deg)';
    }

    /** إضافة overlay للتحكم / Add control overlay */
    public static addControlOverlay(element: HTMLIFrameElement, config: DarkModeConfig): void {
        const overlay = document.createElement('div');
        overlay.className = 'iframe-control-overlay';
        overlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.1);
            pointer-events: none;
            z-index: 1;
        `;
        
        if (element.parentElement) {
            element.parentElement.style.position = 'relative';
            element.parentElement.appendChild(overlay);
        }
    }

    /** مراقبة تحميل المحتوى / Monitor content loading */
    public static monitorContentLoading(element: HTMLIFrameElement, config: DarkModeConfig): void {
        element.addEventListener('load', () => {
            // إعادة محاولة معالجة المحتوى بعد التحميل
            setTimeout(() => {
                this.processAccessibleContent(element, config);
            }, 1000);
        });
    }

    /** توليد CSS للوضع المظلم / Generate dark mode CSS */
    public static generateDarkModeCSS(): string {
        return `
            /* الأنماط الأساسية للوضع المظلم */
            body {
                background-color: #1a1a1a !important;
                color: #ffffff !important;
            }
            
            /* الخلفيات */
            div, section, article, main, aside, header, footer {
                background-color: #2a2a2a !important;
            }
            
            /* النصوص */
            p, span, h1, h2, h3, h4, h5, h6, li, td, th {
                color: #ffffff !important;
            }
            
            /* الروابط */
            a {
                color: #4da6ff !important;
            }
            
            a:hover {
                color: #66b3ff !important;
            }
            
            /* عناصر الإدخال */
            input, textarea, select {
                background-color: #333333 !important;
                color: #ffffff !important;
                border: 1px solid rgba(255, 255, 255, 0.3) !important;
            }
            
            /* الأزرار */
            button {
                background-color: #4da6ff !important;
                color: #ffffff !important;
                border: none !important;
            }
            
            /* الصور */
            img {
                filter: brightness(0.9) contrast(1.1) !important;
            }
            
            /* الفيديو */
            video {
                filter: brightness(0.9) contrast(1.1) !important;
            }
        `;
    }

    /** حقن CSS في المستند / Inject CSS into document */
    public static injectCSS(document: Document, css: string): void {
        const style = document.createElement('style');
        style.id = 'dark-mode-iframe-styles';
        style.textContent = css;
        
        // إزالة الأنماط السابقة إن وجدت
        const existingStyle = document.getElementById('dark-mode-iframe-styles');
        if (existingStyle) {
            existingStyle.remove();
        }
        
        document.head.appendChild(style);
    }
}
