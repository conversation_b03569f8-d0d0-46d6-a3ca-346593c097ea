/**
 * تأثيرات CSS للوضع المظلم
 * Dark mode CSS effects
 * 
 * هذا الملف يحتوي على تأثيرات CSS المستخدمة في الوضع المظلم
 * This file contains CSS effects used in dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * قيم الانتقال للوضع المظلم
 * Dark mode transition values
 */
export const DARK_MODE_TRANSITIONS = {
    // سرعات الانتقال
    // Transition speeds
    SPEEDS: {
        FAST: '0.2s ease',
        NORMAL: '0.3s ease',
        SLOW: '0.5s ease',
        VERY_SLOW: '1s ease'
    },

    // أنواع الانتقال
    // Transition types
    TYPES: {
        EASE: 'ease',
        EASE_IN: 'ease-in',
        EASE_OUT: 'ease-out',
        EASE_IN_OUT: 'ease-in-out',
        LINEAR: 'linear',
        CUBIC_BEZIER: 'cubic-bezier(0.4, 0, 0.2, 1)'
    },

    // خصائص الانتقال
    // Transition properties
    PROPERTIES: {
        ALL: 'all',
        BACKGROUND: 'background-color',
        COLOR: 'color',
        BORDER: 'border-color',
        OPACITY: 'opacity',
        TRANSFORM: 'transform'
    }
} as const;

/**
 * مرشحات الصور للوضع المظلم
 * Dark mode image filters
 */
export const DARK_MODE_FILTERS = {
    // مرشحات الإضاءة
    // Brightness filters
    BRIGHTNESS: {
        VERY_LOW: 'brightness(0.6)',
        LOW: 'brightness(0.8)',
        NORMAL: 'brightness(1)',
        HIGH: 'brightness(1.2)',
        VERY_HIGH: 'brightness(1.4)'
    },

    // مرشحات التباين
    // Contrast filters
    CONTRAST: {
        LOW: 'contrast(0.8)',
        NORMAL: 'contrast(1)',
        HIGH: 'contrast(1.2)',
        VERY_HIGH: 'contrast(1.4)',
        EXTREME: 'contrast(1.6)'
    },

    // مرشحات أخرى
    // Other filters
    EFFECTS: {
        INVERT: 'invert(1)',
        SEPIA: 'sepia(0.1)',
        GRAYSCALE: 'grayscale(0.1)',
        HUE_ROTATE: 'hue-rotate(180deg)',
        SATURATE: 'saturate(1.2)'
    },

    // مرشحات مركبة
    // Combined filters
    COMBINED: {
        DARK_IMAGE: 'brightness(0.8) contrast(1.2)',
        SOFT_INVERT: 'invert(1) brightness(0.9)',
        WARM_DARK: 'brightness(0.8) sepia(0.1) saturate(1.1)',
        COOL_DARK: 'brightness(0.8) hue-rotate(180deg) saturate(0.9)'
    }
} as const;

/**
 * تأثيرات الحركة للوضع المظلم
 * Dark mode animation effects
 */
export const DARK_MODE_ANIMATIONS = {
    // أنواع الحركة
    // Animation types
    TYPES: {
        FADE_IN: 'fadeIn',
        FADE_OUT: 'fadeOut',
        SLIDE_IN: 'slideIn',
        SLIDE_OUT: 'slideOut',
        SCALE_IN: 'scaleIn',
        SCALE_OUT: 'scaleOut'
    },

    // مدة الحركة
    // Animation durations
    DURATIONS: {
        FAST: '200ms',
        NORMAL: '300ms',
        SLOW: '500ms',
        VERY_SLOW: '1000ms'
    },

    // منحنيات الحركة
    // Animation curves
    CURVES: {
        EASE: 'ease',
        EASE_IN: 'ease-in',
        EASE_OUT: 'ease-out',
        EASE_IN_OUT: 'ease-in-out',
        BOUNCE: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
        SMOOTH: 'cubic-bezier(0.4, 0, 0.2, 1)'
    },

    // تأخير الحركة
    // Animation delays
    DELAYS: {
        NONE: '0ms',
        SHORT: '100ms',
        MEDIUM: '200ms'
    }
} as const;

/**
 * تأثيرات الظلال للوضع المظلم
 * Dark mode shadow effects
 */
export const DARK_MODE_SHADOWS = {
    // ظلال النصوص
    // Text shadows
    TEXT: {
        NONE: 'none',
        SUBTLE: '0 1px 2px rgba(0, 0, 0, 0.5)',
        NORMAL: '0 2px 4px rgba(0, 0, 0, 0.7)',
        STRONG: '0 3px 6px rgba(0, 0, 0, 0.9)'
    },

    // ظلال الصناديق
    // Box shadows
    BOX: {
        NONE: 'none',
        SUBTLE: '0 1px 3px rgba(0, 0, 0, 0.3)',
        NORMAL: '0 2px 6px rgba(0, 0, 0, 0.4)',
        ELEVATED: '0 4px 12px rgba(0, 0, 0, 0.5)',
        FLOATING: '0 8px 24px rgba(0, 0, 0, 0.6)'
    },

    // ظلال داخلية
    // Inset shadows
    INSET: {
        SUBTLE: 'inset 0 1px 2px rgba(0, 0, 0, 0.3)',
        NORMAL: 'inset 0 2px 4px rgba(0, 0, 0, 0.4)',
        DEEP: 'inset 0 3px 6px rgba(0, 0, 0, 0.5)'
    }
} as const;

/**
 * تأثيرات التدرج للوضع المظلم
 * Dark mode gradient effects
 */
export const DARK_MODE_GRADIENTS = {
    // تدرجات خطية
    // Linear gradients
    LINEAR: {
        DARK_TO_DARKER: 'linear-gradient(180deg, #1a1a1a 0%, #0a0a0a 100%)',
        GRAY_TO_BLACK: 'linear-gradient(180deg, #2a2a2a 0%, #000000 100%)',
        SUBTLE_DARK: 'linear-gradient(180deg, #1e1e1e 0%, #141414 100%)',
        HEADER_GRADIENT: 'linear-gradient(90deg, #202020 0%, #181818 100%)'
    },

    // تدرجات شعاعية
    // Radial gradients
    RADIAL: {
        CENTER_DARK: 'radial-gradient(circle, #1a1a1a 0%, #0a0a0a 100%)',
        SPOTLIGHT: 'radial-gradient(circle, #2a2a2a 0%, #000000 70%)'
    },

    // تدرجات مخروطية
    // Conic gradients
    CONIC: {
        DARK_SPIN: 'conic-gradient(from 0deg, #1a1a1a, #2a2a2a, #1a1a1a)'
    }
} as const;
