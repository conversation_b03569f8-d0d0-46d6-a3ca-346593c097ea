/**
 * أنواع تحليل توافق لوحة الألوان المتقدم المتقدم المعقد المتقدم
 * Advanced complex advanced advanced palette compatibility analysis types
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * نتيجة تحليل التوافق المتقدم
 * Advanced compatibility analysis result
 */
export interface AdvancedCompatibilityResult {
    readonly harmonyScore: number;
    readonly balanceScore: number;
    readonly accessibilityScore: number;
    readonly aestheticScore: number;
    readonly overallScore: number;
    readonly detailedAnalysis: {
        readonly harmony: string;
        readonly balance: string;
        readonly accessibility: string;
        readonly aesthetic: string;
    };
    readonly improvements: string[];
}

/**
 * تحليل التوافق التفصيلي
 * Detailed compatibility analysis
 */
export interface DetailedCompatibilityAnalysis {
    readonly harmony: string;
    readonly balance: string;
    readonly accessibility: string;
    readonly aesthetic: string;
}

/**
 * نتيجة تحليل التوافق المعقد
 * Complex compatibility analysis result
 */
export interface ComplexCompatibilityResult {
    readonly primaryScore: number;
    readonly secondaryScore: number;
    readonly tertiaryScore: number;
    readonly quaternaryScore: number;
    readonly combinedScore: number;
    readonly analysis: {
        readonly primary: string;
        readonly secondary: string;
        readonly tertiary: string;
        readonly quaternary: string;
    };
    readonly recommendations: string[];
}

/**
 * معايير التوافق المتقدمة
 * Advanced compatibility criteria
 */
export interface AdvancedCompatibilityCriteria {
    readonly harmonyWeight: number;
    readonly balanceWeight: number;
    readonly accessibilityWeight: number;
    readonly aestheticWeight: number;
    readonly minimumScore: number;
    readonly maximumScore: number;
}

/**
 * نتيجة تحليل التوافق الشامل
 * Comprehensive compatibility analysis result
 */
export interface ComprehensiveCompatibilityResult {
    readonly basicAnalysis: AdvancedCompatibilityResult;
    readonly complexAnalysis: ComplexCompatibilityResult;
    readonly finalScore: number;
    readonly finalRecommendations: string[];
    readonly isCompatible: boolean;
}

/**
 * إعدادات تحليل التوافق
 * Compatibility analysis settings
 */
export interface CompatibilityAnalysisSettings {
    readonly enableHarmonyAnalysis: boolean;
    readonly enableBalanceAnalysis: boolean;
    readonly enableAccessibilityAnalysis: boolean;
    readonly enableAestheticAnalysis: boolean;
    readonly strictMode: boolean;
    readonly customWeights?: AdvancedCompatibilityCriteria;
}

/**
 * نتيجة تحليل التوافق المتقدم المعقد
 * Advanced complex compatibility analysis result
 */
export interface AdvancedComplexCompatibilityResult {
    readonly primaryCompatibility: AdvancedCompatibilityResult;
    readonly secondaryCompatibility: ComplexCompatibilityResult;
    readonly tertiaryCompatibility: ComprehensiveCompatibilityResult;
    readonly overallCompatibility: number;
    readonly finalRecommendations: string[];
    readonly isFullyCompatible: boolean;
}

/**
 * معايير التقييم المتقدمة
 * Advanced evaluation criteria
 */
export interface AdvancedEvaluationCriteria {
    readonly harmonyThreshold: number;
    readonly balanceThreshold: number;
    readonly accessibilityThreshold: number;
    readonly aestheticThreshold: number;
    readonly overallThreshold: number;
    readonly strictEvaluation: boolean;
}

/**
 * نتيجة التقييم الشامل
 * Comprehensive evaluation result
 */
export interface ComprehensiveEvaluationResult {
    readonly scores: {
        readonly harmony: number;
        readonly balance: number;
        readonly accessibility: number;
        readonly aesthetic: number;
        readonly overall: number;
    };
    readonly analysis: {
        readonly harmony: string;
        readonly balance: string;
        readonly accessibility: string;
        readonly aesthetic: string;
        readonly overall: string;
    };
    readonly recommendations: {
        readonly harmony: string[];
        readonly balance: string[];
        readonly accessibility: string[];
        readonly aesthetic: string[];
        readonly overall: string[];
    };
    readonly isAcceptable: boolean;
    readonly qualityLevel: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR';
}

/**
 * إعدادات التحليل المتقدم
 * Advanced analysis settings
 */
export interface AdvancedAnalysisSettings {
    readonly enableDetailedAnalysis: boolean;
    readonly enableComplexAnalysis: boolean;
    readonly enableComprehensiveAnalysis: boolean;
    readonly customCriteria?: AdvancedEvaluationCriteria;
    readonly outputFormat: 'BASIC' | 'DETAILED' | 'COMPREHENSIVE';
}

/**
 * ثوابت التحليل المتقدم
 * Advanced analysis constants
 */
export const ADVANCED_ANALYSIS_CONSTANTS = {
    DEFAULT_HARMONY_WEIGHT: 0.25,
    DEFAULT_BALANCE_WEIGHT: 0.25,
    DEFAULT_ACCESSIBILITY_WEIGHT: 0.25,
    DEFAULT_AESTHETIC_WEIGHT: 0.25,
    MINIMUM_SCORE: 0,
    MAXIMUM_SCORE: 100,
    EXCELLENT_THRESHOLD: 90,
    GOOD_THRESHOLD: 75,
    FAIR_THRESHOLD: 60,
    POOR_THRESHOLD: 0
} as const;

/**
 * رسائل التحليل المتقدم
 * Advanced analysis messages
 */
export const ADVANCED_ANALYSIS_MESSAGES = {
    NO_COLORS: 'لا توجد ألوان للتحليل',
    EXCELLENT_HARMONY: 'تناغم ممتاز بين الألوان',
    GOOD_HARMONY: 'تناغم جيد بين الألوان',
    FAIR_HARMONY: 'تناغم مقبول بين الألوان',
    POOR_HARMONY: 'تناغم ضعيف بين الألوان',
    EXCELLENT_BALANCE: 'توازن ممتاز في الألوان',
    GOOD_BALANCE: 'توازن جيد في الألوان',
    FAIR_BALANCE: 'توازن مقبول في الألوان',
    POOR_BALANCE: 'توازن ضعيف في الألوان',
    EXCELLENT_ACCESSIBILITY: 'إمكانية وصول ممتازة',
    GOOD_ACCESSIBILITY: 'إمكانية وصول جيدة',
    FAIR_ACCESSIBILITY: 'إمكانية وصول مقبولة',
    POOR_ACCESSIBILITY: 'إمكانية وصول ضعيفة',
    EXCELLENT_AESTHETIC: 'جمالية ممتازة',
    GOOD_AESTHETIC: 'جمالية جيدة',
    FAIR_AESTHETIC: 'جمالية مقبولة',
    POOR_AESTHETIC: 'جمالية ضعيفة'
} as const;
