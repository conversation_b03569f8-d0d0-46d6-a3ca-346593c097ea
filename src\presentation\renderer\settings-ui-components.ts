/**
 * مكونات واجهة المستخدم للإعدادات
 * Settings UI components
 * 
 * هذا الملف يحتوي على مكونات واجهة المستخدم للإعدادات
 * This file contains settings UI components
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ApplicationConfig } from '@shared/types';

/**
 * فئة مكونات واجهة المستخدم للإعدادات
 * Settings UI components class
 */
export class SettingsUIComponents {
    private readonly formElement: HTMLFormElement | null;
    private readonly saveButton: HTMLButtonElement | null;
    private readonly resetButton: HTMLButtonElement | null;
    private readonly cancelButton: HTMLButtonElement | null;
    private readonly statusElement: HTMLElement | null;

    /**
     * منشئ مكونات واجهة المستخدم
     * UI components constructor
     */
    constructor() {
        this.formElement = document.getElementById('settings-form') as HTMLFormElement;
        this.saveButton = document.getElementById('save-button') as HTMLButtonElement;
        this.resetButton = document.getElementById('reset-button') as HTMLButtonElement;
        this.cancelButton = document.getElementById('cancel-button') as HTMLButtonElement;
        this.statusElement = document.getElementById('status-message') as HTMLElement;
    }

    /**
     * الحصول على عنصر النموذج
     * Get form element
     * 
     * @returns عنصر النموذج أو null
     */
    public getFormElement(): HTMLFormElement | null {
        return this.formElement;
    }

    /**
     * الحصول على زر الحفظ
     * Get save button
     * 
     * @returns زر الحفظ أو null
     */
    public getSaveButton(): HTMLButtonElement | null {
        return this.saveButton;
    }

    /**
     * الحصول على زر الإعادة
     * Get reset button
     * 
     * @returns زر الإعادة أو null
     */
    public getResetButton(): HTMLButtonElement | null {
        return this.resetButton;
    }

    /**
     * الحصول على زر الإلغاء
     * Get cancel button
     * 
     * @returns زر الإلغاء أو null
     */
    public getCancelButton(): HTMLButtonElement | null {
        return this.cancelButton;
    }

    /**
     * الحصول على عنصر الحالة
     * Get status element
     * 
     * @returns عنصر الحالة أو null
     */
    public getStatusElement(): HTMLElement | null {
        return this.statusElement;
    }

    /**
     * عرض رسالة الحالة
     * Show status message
     * 
     * @param message - الرسالة المراد عرضها
     * @param type - نوع الرسالة (success, error, warning, info)
     */
    public showStatusMessage(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info'): void {
        if (!this.statusElement) {
            console.warn('Status element not found');
            return;
        }

        // إزالة الكلاسات السابقة
        // Remove previous classes
        this.statusElement.className = 'status-message';
        
        // إضافة كلاس النوع
        // Add type class
        this.statusElement.classList.add(`status-${type}`);
        
        // تعيين النص
        // Set text
        this.statusElement.textContent = message;
        
        // إظهار العنصر
        // Show element
        this.statusElement.style.display = 'block';
        
        // إخفاء الرسالة بعد 5 ثوان
        // Hide message after 5 seconds
        setTimeout(() => {
            this.hideStatusMessage();
        }, 5000);
    }

    /**
     * إخفاء رسالة الحالة
     * Hide status message
     */
    public hideStatusMessage(): void {
        if (this.statusElement) {
            this.statusElement.style.display = 'none';
            this.statusElement.textContent = '';
        }
    }

    /**
     * تفعيل أو تعطيل الأزرار
     * Enable or disable buttons
     * 
     * @param enabled - true للتفعيل، false للتعطيل
     */
    public setButtonsEnabled(enabled: boolean): void {
        if (this.saveButton) {
            this.saveButton.disabled = !enabled;
        }
        if (this.resetButton) {
            this.resetButton.disabled = !enabled;
        }
        if (this.cancelButton) {
            this.cancelButton.disabled = !enabled;
        }
    }

    /**
     * تفعيل أو تعطيل النموذج
     * Enable or disable form
     * 
     * @param enabled - true للتفعيل، false للتعطيل
     */
    public setFormEnabled(enabled: boolean): void {
        if (!this.formElement) {
            return;
        }

        const inputs = this.formElement.querySelectorAll('input, select, textarea');
        inputs.forEach((input) => {
            (input as HTMLInputElement).disabled = !enabled;
        });
    }

    /**
     * الحصول على قيم النموذج
     * Get form values
     * 
     * @returns قيم النموذج
     */
    public getFormValues(): Record<string, any> {
        if (!this.formElement) {
            return {};
        }

        const formData = new FormData(this.formElement);
        const values: Record<string, any> = {};

        for (const [key, value] of formData.entries()) {
            // معالجة القيم المختلفة
            // Handle different value types
            if (typeof value === 'string') {
                // فحص إذا كانت القيمة boolean
                // Check if value is boolean
                if (value === 'true' || value === 'false') {
                    values[key] = value === 'true';
                }
                // فحص إذا كانت القيمة رقم
                // Check if value is number
                else if (!isNaN(Number(value)) && value !== '') {
                    values[key] = Number(value);
                }
                // قيمة نصية
                // String value
                else {
                    values[key] = value;
                }
            } else {
                values[key] = value;
            }
        }

        return values;
    }

    /**
     * تعيين قيم النموذج
     * Set form values
     * 
     * @param values - القيم المراد تعيينها
     */
    public setFormValues(values: Record<string, any>): void {
        if (!this.formElement) {
            return;
        }

        Object.entries(values).forEach(([key, value]) => {
            const element = this.formElement!.querySelector(`[name="${key}"]`) as HTMLInputElement;
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = Boolean(value);
                } else if (element.type === 'radio') {
                    if (element.value === String(value)) {
                        element.checked = true;
                    }
                } else {
                    element.value = String(value);
                }
            }
        });
    }

    /**
     * إعادة تعيين النموذج
     * Reset form
     */
    public resetForm(): void {
        if (this.formElement) {
            this.formElement.reset();
        }
    }

    /**
     * التحقق من صحة النموذج
     * Validate form
     * 
     * @returns true إذا كان النموذج صحيح
     */
    public validateForm(): boolean {
        if (!this.formElement) {
            return false;
        }

        // فحص الحقول المطلوبة
        // Check required fields
        const requiredFields = this.formElement.querySelectorAll('[required]');
        for (const field of requiredFields) {
            const input = field as HTMLInputElement;
            if (!input.value.trim()) {
                this.showStatusMessage(`الحقل "${input.name}" مطلوب`, 'error');
                input.focus();
                return false;
            }
        }

        // فحص صحة البريد الإلكتروني
        // Validate email fields
        const emailFields = this.formElement.querySelectorAll('input[type="email"]');
        for (const field of emailFields) {
            const input = field as HTMLInputElement;
            if (input.value && !this.isValidEmail(input.value)) {
                this.showStatusMessage(`البريد الإلكتروني غير صحيح في الحقل "${input.name}"`, 'error');
                input.focus();
                return false;
            }
        }

        return true;
    }

    /**
     * فحص صحة البريد الإلكتروني
     * Validate email
     * 
     * @param email - البريد الإلكتروني المراد فحصه
     * @returns true إذا كان البريد الإلكتروني صحيح
     */
    private isValidEmail(email: string): boolean {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * إضافة مستمع حدث للزر
     * Add event listener to button
     * 
     * @param buttonType - نوع الزر
     * @param callback - دالة الاستدعاء
     */
    public addButtonEventListener(
        buttonType: 'save' | 'reset' | 'cancel',
        callback: (event: Event) => void
    ): void {
        let button: HTMLButtonElement | null = null;
        
        switch (buttonType) {
            case 'save':
                button = this.saveButton;
                break;
            case 'reset':
                button = this.resetButton;
                break;
            case 'cancel':
                button = this.cancelButton;
                break;
        }

        if (button) {
            button.addEventListener('click', callback);
        }
    }

    /**
     * إضافة مستمع حدث للنموذج
     * Add event listener to form
     * 
     * @param eventType - نوع الحدث
     * @param callback - دالة الاستدعاء
     */
    public addFormEventListener(
        eventType: 'submit' | 'change' | 'input',
        callback: (event: Event) => void
    ): void {
        if (this.formElement) {
            this.formElement.addEventListener(eventType, callback);
        }
    }
}
