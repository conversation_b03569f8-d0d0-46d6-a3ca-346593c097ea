# 🔨 تعليمات البناء التفصيلية / Detailed Build Instructions

## 📋 المتطلبات الأساسية / Prerequisites

### 1. تثبيت Node.js
```bash
# تحميل من الموقع الرسمي
https://nodejs.org/

# التحقق من التثبيت
node --version  # يجب أن يكون 16.0.0 أو أحدث
npm --version   # يجب أن يكون 7.0.0 أو أحدث
```

### 2. تثبيت Git (اختياري)
```bash
# تحميل من الموقع الرسمي
https://git-scm.com/

# التحقق من التثبيت
git --version
```

## 🚀 خطوات البناء / Build Steps

### الطريقة الأولى: البناء التلقائي / Automatic Build

```bash
# 1. تحميل المشروع
git clone https://github.com/user/youtube-dark-cyber-x.git
cd youtube-dark-cyber-x

# 2. تشغيل سكريبت البناء التلقائي
node scripts/build.js portable

# أو للحصول على مثبت
node scripts/build.js win
```

### الطريقة الثانية: البناء اليدوي / Manual Build

```bash
# 1. تثبيت التبعيات
npm install

# 2. تشغيل الاختبارات (اختياري)
npm test

# 3. بناء التطبيق
npm run build-portable  # للإصدار المحمول
# أو
npm run build-win      # للمثبت
```

## 📁 أنواع البناء المتاحة / Available Build Types

### 1. الإصدار المحمول / Portable Version
```bash
npm run build-portable
```
- **الملف الناتج**: `dist/YouTubePlayer-Portable.exe`
- **الحجم**: ~150-200 MB
- **المميزات**: 
  - لا يحتاج تثبيت
  - يعمل من أي مجلد
  - يحفظ الإعدادات في نفس المجلد

### 2. المثبت / Installer Version
```bash
npm run build-win
```
- **الملف الناتج**: `dist/YouTube Dark CyberX Setup.exe`
- **الحجم**: ~100-150 MB
- **المميزات**:
  - تثبيت تقليدي في Program Files
  - اختصارات سطح المكتب وقائمة ابدأ
  - إلغاء تثبيت من لوحة التحكم

### 3. البناء الشامل / Complete Build
```bash
npm run build
```
- ينتج كلا النوعين معاً

## 🔧 تخصيص البناء / Build Customization

### تعديل إعدادات البناء / Modify Build Settings

قم بتعديل ملف `package.json` في قسم `build`:

```json
{
  "build": {
    "appId": "com.cyberdark.youtube-player",
    "productName": "YouTube Dark CyberX",
    "directories": {
      "output": "dist"
    },
    "win": {
      "target": [
        {
          "target": "portable",
          "arch": ["x64"]
        }
      ],
      "icon": "assets/icons/app-icon.ico"
    }
  }
}
```

### إضافة أيقونات مخصصة / Add Custom Icons

1. ضع الأيقونات في `assets/icons/`
2. تأكد من الأحجام المطلوبة:
   - `app-icon.ico` (16x16, 32x32, 48x48, 256x256)
   - `app-icon.png` (256x256)

### تخصيص الإعدادات الافتراضية / Customize Default Settings

قم بتعديل `config/settings.json`:

```json
{
  "defaultSettings": {
    "adBlockEnabled": true,
    "darkModeEnabled": true,
    "videoQuality": "720p",
    "hideQualityButton": true,
    "autoApplySettings": true
  }
}
```

## 🧪 اختبار البناء / Testing the Build

### 1. اختبار الإصدار المحمول
```bash
# بعد البناء، اختبر الملف
./dist/YouTubePlayer-Portable.exe
```

### 2. اختبار المثبت
```bash
# تشغيل المثبت
./dist/YouTube\ Dark\ CyberX\ Setup.exe

# اختبار التطبيق المثبت
# ابحث عن التطبيق في قائمة ابدأ
```

## 🐛 حل المشاكل الشائعة / Troubleshooting

### مشكلة: فشل في تثبيت التبعيات
```bash
# حل 1: مسح cache
npm cache clean --force
rm -rf node_modules
npm install

# حل 2: استخدام yarn بدلاً من npm
npm install -g yarn
yarn install
yarn build
```

### مشكلة: خطأ في بناء الأيقونات
```bash
# تأكد من وجود الأيقونات
ls assets/icons/

# إنشاء أيقونة افتراضية إذا لم تكن موجودة
# يمكن استخدام أي صورة PNG 256x256
```

### مشكلة: حجم الملف كبير جداً
```bash
# تحسين البناء
npm run build -- --publish=never

# أو تعديل package.json لاستبعاد ملفات غير ضرورية
"files": [
  "src/**/*",
  "assets/icons/**/*",
  "config/settings.json"
]
```

### مشكلة: التطبيق لا يعمل على Windows 7
```bash
# تعديل target في package.json
"win": {
  "target": [
    {
      "target": "nsis",
      "arch": ["ia32", "x64"]
    }
  ]
}
```

## 📊 معلومات البناء / Build Information

### أحجام الملفات المتوقعة / Expected File Sizes
- **الكود المصدري**: ~2-5 MB
- **التبعيات**: ~100-150 MB
- **الملف النهائي**: ~150-200 MB

### أوقات البناء المتوقعة / Expected Build Times
- **جهاز سريع**: 2-5 دقائق
- **جهاز متوسط**: 5-10 دقائق
- **جهاز بطيء**: 10-20 دقيقة

### متطلبات النظام للبناء / System Requirements for Building
- **RAM**: 4 GB كحد أدنى، 8 GB مُوصى به
- **مساحة القرص**: 2 GB مساحة فارغة
- **المعالج**: أي معالج حديث
- **نظام التشغيل**: Windows 10/11, macOS, Linux

## 🔄 التحديثات التلقائية / Automatic Updates

### إعداد التحديثات / Setup Updates

1. قم بتعديل `config/settings.json`:
```json
{
  "updates": {
    "checkForUpdates": true,
    "autoUpdate": false,
    "updateServer": "https://api.github.com/repos/user/youtube-dark-cyber-x/releases"
  }
}
```

2. أضف سكريبت النشر:
```bash
npm run build
npm run publish  # ينشر على GitHub Releases
```

## 📝 ملاحظات مهمة / Important Notes

1. **الأيقونات**: تأكد من وجود جميع الأيقونات المطلوبة
2. **الترخيص**: تأكد من أن جميع التبعيات متوافقة مع الترخيص
3. **الاختبار**: اختبر التطبيق على أجهزة مختلفة قبل التوزيع
4. **الأمان**: فحص الملفات بمضاد الفيروسات قبل التوزيع
5. **التوثيق**: تأكد من تحديث التوثيق مع كل إصدار

## 🎯 نصائح للتحسين / Optimization Tips

### تقليل حجم الملف / Reduce File Size
```bash
# استبعاد ملفات التطوير
"files": [
  "!src/tests/**/*",
  "!docs/**/*",
  "!scripts/**/*"
]
```

### تحسين الأداء / Performance Optimization
```bash
# تفعيل ضغط الكود
"build": {
  "compression": "maximum",
  "nsis": {
    "oneClick": false
  }
}
```

### أمان إضافي / Additional Security
```bash
# توقيع الكود (يتطلب شهادة)
"win": {
  "certificateFile": "path/to/certificate.p12",
  "certificatePassword": "password"
}
```
