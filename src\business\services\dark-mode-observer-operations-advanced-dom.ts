/**
 * عمليات DOM المتقدمة لمراقب الوضع المظلم
 * Advanced DOM operations for dark mode observer
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeConfig } from './dark-mode-config';

/**
 * فئة عمليات DOM المتقدمة
 * Advanced DOM operations class
 */
export class DarkModeObserverOperationsAdvancedDom {
    private readonly config: DarkModeConfig;

    constructor(config: DarkModeConfig) {
        this.config = config;
    }

    /** معالجة العقد المضافة / Process added nodes */
    public processAddedNodes(nodes: NodeList): void {
        nodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
                const element = node as Element;
                this.processNewElement(element);
                
                // معالجة العناصر الفرعية
                const children = element.querySelectorAll('*');
                children.forEach(child => this.processNewElement(child));
            }
        });
    }

    /** معالجة العقد المحذوفة / Process removed nodes */
    public processRemovedNodes(nodes: NodeList): void {
        nodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
                const element = node as Element;
                this.cleanupRemovedElement(element);
            }
        });
    }

    /** معالجة التغييرات في الخصائص / Process attribute changes */
    public processAttributeChanges(target: Element, attributeName: string): void {
        if (!target || !attributeName) {
            return;
        }

        // معالجة تغييرات الفئات
        if (attributeName === 'class') {
            this.handleClassChanges(target);
        }

        // معالجة تغييرات الأنماط
        if (attributeName === 'style') {
            this.handleStyleChanges(target);
        }

        // معالجة تغييرات المعرف
        if (attributeName === 'id') {
            this.handleIdChanges(target);
        }
    }

    /** معالجة تغييرات الفئات / Handle class changes */
    public handleClassChanges(element: Element): void {
        if (!(element instanceof HTMLElement)) {
            return;
        }

        // التحقق من إضافة فئات جديدة
        const classList = Array.from(element.classList);
        
        // معالجة فئات الوضع المظلم
        if (classList.some(cls => cls.includes('dark') || cls.includes('night'))) {
            this.applyDarkModeStyles(element);
        }
        
        // معالجة فئات الوضع الفاتح
        if (classList.some(cls => cls.includes('light') || cls.includes('bright'))) {
            this.removeDarkModeStyles(element);
        }
    }

    /** معالجة تغييرات الأنماط / Handle style changes */
    public handleStyleChanges(element: Element): void {
        if (!(element instanceof HTMLElement)) {
            return;
        }

        // التحقق من تغييرات الخلفية
        const backgroundColor = element.style.backgroundColor;
        if (backgroundColor) {
            this.adjustForBackgroundChange(element, backgroundColor);
        }

        // التحقق من تغييرات النص
        const color = element.style.color;
        if (color) {
            this.adjustForColorChange(element, color);
        }
    }

    /** معالجة تغييرات المعرف / Handle ID changes */
    public handleIdChanges(element: Element): void {
        const id = element.id;
        
        // معالجة معرفات خاصة بـ YouTube
        if (id && this.isYouTubeSpecialId(id)) {
            this.applyYouTubeSpecificStyles(element, id);
        }
    }

    /** معالجة عنصر جديد / Process new element */
    private processNewElement(element: Element): void {
        if (!(element instanceof HTMLElement)) {
            return;
        }

        // تطبيق الأنماط العامة
        if (this.shouldApplyDarkMode(element)) {
            this.applyDarkModeStyles(element);
        }
    }

    /** تنظيف عنصر محذوف / Cleanup removed element */
    private cleanupRemovedElement(element: Element): void {
        // إزالة المراقبين المرتبطين بالعنصر
        this.removeElementObservers(element);
        
        // تنظيف الذاكرة
        this.cleanupElementMemory(element);
    }

    /** تطبيق أنماط الوضع المظلم / Apply dark mode styles */
    private applyDarkModeStyles(element: HTMLElement): void {
        element.style.backgroundColor = this.config.customThemeColors.backgroundColor;
        element.style.color = this.config.customThemeColors.textColor;
    }

    /** إزالة أنماط الوضع المظلم / Remove dark mode styles */
    private removeDarkModeStyles(element: HTMLElement): void {
        element.style.removeProperty('background-color');
        element.style.removeProperty('color');
    }

    /** التحقق من ضرورة تطبيق الوضع المظلم / Check if should apply dark mode */
    private shouldApplyDarkMode(element: HTMLElement): boolean {
        // تجنب العناصر المستثناة
        const excludedTags = ['script', 'style', 'meta', 'link'];
        if (excludedTags.includes(element.tagName.toLowerCase())) {
            return false;
        }

        // تجنب العناصر المخفية
        if (element.style.display === 'none' || element.hidden) {
            return false;
        }

        return true;
    }

    /** التحقق من معرف YouTube خاص / Check if YouTube special ID */
    private isYouTubeSpecialId(id: string): boolean {
        const youtubeIds = [
            'masthead',
            'content',
            'secondary',
            'player',
            'comments',
            'description'
        ];
        
        return youtubeIds.some(ytId => id.includes(ytId));
    }

    /** تطبيق أنماط خاصة بـ YouTube / Apply YouTube specific styles */
    private applyYouTubeSpecificStyles(element: Element, id: string): void {
        if (!(element instanceof HTMLElement)) {
            return;
        }

        switch (true) {
            case id.includes('masthead'):
                element.style.backgroundColor = '#212121';
                break;
                
            case id.includes('content'):
                element.style.backgroundColor = '#0f0f0f';
                break;
                
            case id.includes('player'):
                element.style.backgroundColor = '#000000';
                break;
                
            case id.includes('comments'):
                element.style.backgroundColor = '#0f0f0f';
                break;
        }
    }

    /** تعديل للتغيير في الخلفية / Adjust for background change */
    private adjustForBackgroundChange(element: HTMLElement, backgroundColor: string): void {
        // تحليل لون الخلفية وتعديل لون النص تبعاً لذلك
        const isLight = this.isLightColor(backgroundColor);
        element.style.color = isLight ? '#000000' : '#ffffff';
    }

    /** تعديل للتغيير في اللون / Adjust for color change */
    private adjustForColorChange(element: HTMLElement, color: string): void {
        // التأكد من التباين المناسب
        const backgroundColor = element.style.backgroundColor || '#0f0f0f';
        if (!this.hasGoodContrast(color, backgroundColor)) {
            element.style.color = this.config.customThemeColors.textColor;
        }
    }

    /** التحقق من كون اللون فاتح / Check if color is light */
    private isLightColor(color: string): boolean {
        // تحليل بسيط للون
        const rgb = color.match(/\d+/g);
        if (rgb && rgb.length >= 3) {
            const brightness = (parseInt(rgb[0]) + parseInt(rgb[1]) + parseInt(rgb[2])) / 3;
            return brightness > 128;
        }
        return false;
    }

    /** التحقق من التباين الجيد / Check if has good contrast */
    private hasGoodContrast(color1: string, color2: string): boolean {
        // تحقق بسيط من التباين
        return Math.abs(this.getColorBrightness(color1) - this.getColorBrightness(color2)) > 64;
    }

    /** الحصول على سطوع اللون / Get color brightness */
    private getColorBrightness(color: string): number {
        const rgb = color.match(/\d+/g);
        if (rgb && rgb.length >= 3) {
            return (parseInt(rgb[0]) + parseInt(rgb[1]) + parseInt(rgb[2])) / 3;
        }
        return 0;
    }

    /** إزالة مراقبي العنصر / Remove element observers */
    private removeElementObservers(element: Element): void {
        // تنظيف المراقبين المرتبطين بالعنصر
        // هذا يتطلب تتبع المراقبين المضافين
    }

    /** تنظيف ذاكرة العنصر / Cleanup element memory */
    private cleanupElementMemory(element: Element): void {
        // تنظيف أي مراجع أو بيانات مرتبطة بالعنصر
        // لمنع تسريب الذاكرة
    }
}
