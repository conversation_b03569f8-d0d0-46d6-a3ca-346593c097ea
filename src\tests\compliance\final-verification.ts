/**
 * أداة التحقق النهائي الشاملة
 * Comprehensive final verification tool
 * 
 * هذا الملف يحتوي على أداة التحقق النهائي الشاملة للتطبيق
 * This file contains comprehensive final verification tool for the application
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { FinalVerificationCore } from './final-verification-core';
import { FinalVerificationReporter } from './final-verification-reporter';
import { VerificationConfig, VerificationResult } from './final-verification-types';

/**
 * أداة التحقق النهائي الشاملة
 * Comprehensive final verification tool
 */
export class FinalVerification {
    private readonly projectRoot: string;

    constructor(projectRoot: string = process.cwd()) {
        this.projectRoot = projectRoot;
    }

    /**
     * تشغيل التحقق النهائي الشامل
     * Run comprehensive final verification
     */
    public async runFinalVerification(): Promise<VerificationResult> {
        console.log('🎯 بدء التحقق النهائي الشامل / Starting comprehensive final verification...\n');

        // 1. فحص الامتثال للدستور
        const constitutionReport = await FinalVerificationCore.runConstitutionVerification(this.projectRoot);

        // 2. فحص الوظائف
        const functionalityReport = await FinalVerificationCore.runFunctionalityTests(this.projectRoot);

        // 3. تشغيل الاختبارات
        const testReport = await FinalVerificationCore.runUnitTests(this.projectRoot);

        // 4. إنشاء التقرير النهائي
        const verificationResult = this.generateFinalReport(
            constitutionReport,
            functionalityReport,
            testReport
        );

        // 5. حفظ التقرير
        const config: VerificationConfig = {
            enableConstitutionCheck: true,
            enableFunctionalityTests: true,
            enableUnitTests: true,
            enablePerformanceTests: false,
            enableSecurityTests: false,
            minCoverageThreshold: 80,
            minScoreThreshold: 70,
            outputFormat: 'text',
            outputPath: './reports/final-verification.txt',
            verbose: true
        };
        await FinalVerificationReporter.saveReport(verificationResult, config);

        // 6. طباعة النتائج
        console.log(FinalVerificationReporter.generateTextReport(verificationResult));

        return verificationResult;
    }

    /**
     * إنشاء التقرير النهائي
     * Generate final report
     */
    private generateFinalReport(
        constitutionReport: any,
        functionalityReport: any,
        testReport: any
    ): VerificationResult {
        // حساب نقاط الامتثال للدستور
        const constitutionScore = constitutionReport.complianceScore;
        const constitutionIssues = constitutionReport.totalIssues;
        const criticalConstitutionIssues = constitutionReport.errorCount;

        // حساب نقاط الوظائف
        const functionalityScore = functionalityReport.overallScore;
        const functionalityPassed = functionalityReport.passedTests;
        const functionalityTotal = functionalityReport.totalTests;

        // حساب نقاط الاختبارات
        const testCoverage = testReport.overallCoverage;
        const testsPassed = testReport.totalPassed;
        const testsTotal = testReport.totalTests;

        // حساب النقاط الإجمالية
        const overallScore = FinalVerificationCore.calculateOverallScore(
            constitutionScore,
            functionalityScore,
            testCoverage
        );

        // تحديد الدرجة
        const grade = FinalVerificationCore.determineGrade(overallScore);

        // إنشاء التوصيات
        const recommendations = FinalVerificationCore.generateRecommendations(
            constitutionReport,
            functionalityReport,
            testReport
        );

        // إنشاء قائمة المشاكل الحرجة
        const criticalIssues = FinalVerificationCore.identifyCriticalIssues(
            constitutionReport,
            functionalityReport,
            testReport
        );

        return {
            constitutionCompliance: {
                score: constitutionScore,
                issues: constitutionIssues,
                criticalIssues: criticalConstitutionIssues
            },
            functionalityTests: {
                score: functionalityScore,
                passedTests: functionalityPassed,
                totalTests: functionalityTotal
            },
            unitTests: {
                coverage: testCoverage,
                passedTests: testsPassed,
                totalTests: testsTotal
            },
            overallScore: Math.round(overallScore * 100) / 100,
            grade,
            recommendations,
            criticalIssues,
            timestamp: new Date()
        };
    }








}

// تشغيل التحقق النهائي إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    const verification = new FinalVerification();
    verification.runFinalVerification()
        .then((result) => {
            const isSuccess = result.overallScore >= 80 && result.criticalIssues.length === 0;
            process.exit(isSuccess ? 0 : 1);
        })
        .catch((error) => {
            console.error('❌ فشل في التحقق النهائي / Failed final verification:', error);
            process.exit(1);
        });
}


