/**
 * ثوابت النظام للوضع المظلم
 * Dark mode system constants
 * 
 * هذا الملف يحتوي على ثوابت النظام للوضع المظلم
 * This file contains system constants for dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * ثوابت الرسائل للوضع المظلم
 * Dark mode message constants
 */
export const DARK_MODE_MESSAGE_CONSTANTS = {
    // رسائل النجاح
    // Success messages
    SUCCESS: {
        ENABLED: 'تم تفعيل الوضع المظلم بنجاح / Dark mode enabled successfully',
        DISABLED: 'تم إلغاء الوضع المظلم بنجاح / Dark mode disabled successfully',
        APPLIED: 'تم تطبيق الوضع المظلم / Dark mode applied',
        DETECTED: 'تم اكتشاف الوضع المظلم تلقائياً / Dark mode auto-detected'
    },

    // رسائل الخطأ
    // Error messages
    ERROR: {
        ENABLE_FAILED: 'فشل في تفعيل الوضع المظلم / Failed to enable dark mode',
        DISABLE_FAILED: 'فشل في إلغاء الوضع المظلم / Failed to disable dark mode',
        APPLY_FAILED: 'فشل في تطبيق الوضع المظلم / Failed to apply dark mode',
        DETECTION_FAILED: 'فشل في اكتشاف الوضع المظلم / Failed to detect dark mode',
        INVALID_CONFIG: 'تكوين غير صحيح للوضع المظلم / Invalid dark mode configuration'
    },

    // رسائل التحذير
    // Warning messages
    WARNING: {
        ALREADY_ENABLED: 'الوضع المظلم مفعل بالفعل / Dark mode already enabled',
        ALREADY_DISABLED: 'الوضع المظلم معطل بالفعل / Dark mode already disabled',
        PARTIAL_SUPPORT: 'دعم جزئي للوضع المظلم / Partial dark mode support',
        PERFORMANCE_IMPACT: 'قد يؤثر على الأداء / May impact performance'
    },

    // رسائل المعلومات
    // Info messages
    INFO: {
        INITIALIZING: 'جاري تهيئة الوضع المظلم / Initializing dark mode',
        APPLYING_STYLES: 'جاري تطبيق الأنماط / Applying styles',
        OBSERVING_CHANGES: 'جاري مراقبة التغييرات / Observing changes',
        CLEANUP: 'جاري تنظيف الموارد / Cleaning up resources'
    }
} as const;

/**
 * ثوابت التوقيتات للوضع المظلم
 * Dark mode timing constants
 */
export const DARK_MODE_TIMING_CONSTANTS = {
    // مهلة زمنية للعمليات
    // Operation timeouts
    TIMEOUTS: {
        APPLY_STYLES: 5000,
        DETECT_CHANGES: 3000,
        TRANSITION_COMPLETE: 1000,
        OBSERVER_DEBOUNCE: 500,
        RETRY_DELAY: 1000
    },

    // فترات المراقبة
    // Monitoring intervals
    INTERVALS: {
        STYLE_CHECK: 2000,
        THEME_DETECTION: 5000,
        PERFORMANCE_MONITOR: 10000,
        CLEANUP_CHECK: 30000
    },

    // عدد المحاولات
    // Retry counts
    RETRIES: {
        APPLY_STYLES: 3,
        DETECT_THEME: 5,
        OBSERVER_SETUP: 2,
        STYLE_INJECTION: 3
    },

    // تأخيرات الانتقال
    // Transition delays
    DELAYS: {
        FADE_IN: 200,
        FADE_OUT: 150,
        SLIDE_IN: 300,
        SLIDE_OUT: 250,
        SCALE_IN: 100,
        SCALE_OUT: 100
    }
} as const;

/**
 * ثوابت المراقبة للوضع المظلم
 * Dark mode monitoring constants
 */
export const DARK_MODE_MONITORING_CONSTANTS = {
    // إعدادات MutationObserver
    // MutationObserver settings
    OBSERVER_CONFIG: {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'style', 'data-theme'],
        characterData: false
    },

    // محددات المراقبة
    // Monitoring selectors
    WATCH_SELECTORS: [
        'body',
        '#masthead',
        '#guide',
        '#content',
        '#movie_player',
        '.ytd-app'
    ],

    // أحداث المراقبة
    // Monitoring events
    EVENTS: {
        THEME_CHANGE: 'themechange',
        STYLE_APPLIED: 'styleapplied',
        DARK_MODE_TOGGLE: 'darkmodetoggle',
        CONFIG_UPDATE: 'configupdate'
    },

    // حدود الأداء
    // Performance limits
    PERFORMANCE: {
        MAX_MUTATIONS_PER_BATCH: 100,
        MAX_STYLE_RULES: 500,
        MAX_OBSERVER_CALLBACKS: 10,
        DEBOUNCE_THRESHOLD: 50
    }
} as const;

/**
 * ثوابت التوافق للوضع المظلم
 * Dark mode compatibility constants
 */
export const DARK_MODE_COMPATIBILITY_CONSTANTS = {
    // دعم المتصفحات
    // Browser support
    BROWSER_SUPPORT: {
        CHROME: 76,
        FIREFOX: 67,
        SAFARI: 12.1,
        EDGE: 79,
        OPERA: 62
    },

    // ميزات CSS المطلوبة
    // Required CSS features
    CSS_FEATURES: [
        'css-variables',
        'css-filters',
        'css-transitions',
        'css-transforms',
        'css-grid'
    ],

    // APIs المطلوبة
    // Required APIs
    REQUIRED_APIS: [
        'MutationObserver',
        'getComputedStyle',
        'querySelector',
        'addEventListener',
        'localStorage'
    ],

    // خصائص الوسائط المدعومة
    // Supported media features
    MEDIA_FEATURES: [
        'prefers-color-scheme',
        'prefers-reduced-motion',
        'prefers-contrast',
        'forced-colors'
    ]
} as const;

/**
 * ثوابت الأداء للوضع المظلم
 * Dark mode performance constants
 */
export const DARK_MODE_PERFORMANCE_CONSTANTS = {
    // حدود الذاكرة
    // Memory limits
    MEMORY_LIMITS: {
        MAX_STYLE_CACHE_SIZE: 1024 * 1024, // 1MB
        MAX_OBSERVER_CACHE_SIZE: 512 * 1024, // 512KB
        MAX_THEME_CACHE_SIZE: 256 * 1024, // 256KB
        CACHE_CLEANUP_THRESHOLD: 0.8 // 80%
    },

    // حدود المعالجة
    // Processing limits
    PROCESSING_LIMITS: {
        MAX_CONCURRENT_OPERATIONS: 5,
        MAX_STYLE_RULES_PER_BATCH: 100,
        MAX_DOM_MUTATIONS_PER_BATCH: 50,
        BATCH_PROCESSING_DELAY: 16 // 60fps
    },

    // مقاييس الأداء
    // Performance metrics
    PERFORMANCE_METRICS: {
        TARGET_FRAME_RATE: 60,
        MAX_FRAME_TIME: 16.67, // ms
        MAX_STYLE_APPLICATION_TIME: 100, // ms
        MAX_OBSERVER_CALLBACK_TIME: 5 // ms
    },

    // إعدادات التحسين
    // Optimization settings
    OPTIMIZATION: {
        ENABLE_STYLE_BATCHING: true,
        ENABLE_OBSERVER_DEBOUNCING: true,
        ENABLE_CACHE_COMPRESSION: true,
        ENABLE_LAZY_LOADING: true
    }
} as const;

/**
 * ثوابت الأمان للوضع المظلم
 * Dark mode security constants
 */
export const DARK_MODE_SECURITY_CONSTANTS = {
    // قائمة المحددات المسموحة
    // Allowed selectors whitelist
    ALLOWED_SELECTORS: [
        'body',
        'html',
        '#content',
        '#masthead',
        '#guide',
        '.ytd-app',
        '.ytd-page-manager',
        '.ytd-browse',
        '.ytd-watch-flexy'
    ],

    // قائمة الخصائص المسموحة
    // Allowed properties whitelist
    ALLOWED_PROPERTIES: [
        'background-color',
        'color',
        'border-color',
        'transition',
        'filter',
        'opacity'
    ],

    // قائمة القيم المحظورة
    // Blocked values blacklist
    BLOCKED_VALUES: [
        'javascript:',
        'data:',
        'vbscript:',
        'expression(',
        'url(',
        '@import'
    ],

    // حدود الأمان
    // Security limits
    SECURITY_LIMITS: {
        MAX_STYLE_LENGTH: 10000,
        MAX_SELECTOR_LENGTH: 500,
        MAX_PROPERTY_LENGTH: 100,
        MAX_VALUE_LENGTH: 1000
    }
} as const;
