/**
 * عمليات الملفات الجوهرية الأساسية
 * Core basic file operations
 * 
 * هذا الملف يحتوي على عمليات الملفات الجوهرية الأساسية
 * This file contains core basic file operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import * as fs from 'fs';
import * as path from 'path';

/**
 * فئة عمليات الملفات الجوهرية الأساسية
 * Core basic file operations class
 */
export class SimpleVerificationCoreUtilsFileOperationsBasicCoreFiles {

    /**
     * فحص وجود الملف
     * Check if file exists
     */
    public static fileExists(filePath: string): boolean {
        try {
            return fs.existsSync(filePath) && fs.statSync(filePath).isFile();
        } catch (error) {
            return false;
        }
    }

    /**
     * فحص وجود المجلد
     * Check if directory exists
     */
    public static directoryExists(dirPath: string): boolean {
        try {
            return fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory();
        } catch (error) {
            return false;
        }
    }

    /**
     * قراءة محتوى الملف
     * Read file content
     */
    public static readFileContent(filePath: string): string | null {
        try {
            return fs.readFileSync(filePath, 'utf-8');
        } catch (error) {
            return null;
        }
    }

    /**
     * تنظيف مسار الملف
     * Clean file path
     */
    public static cleanFilePath(filePath: string, projectRoot: string): string {
        return path.relative(projectRoot, filePath).replace(/\\/g, '/');
    }

    /**
     * فحص امتداد الملف
     * Check file extension
     */
    public static hasValidExtension(filePath: string, validExtensions: string[]): boolean {
        const extension = path.extname(filePath).toLowerCase();
        return validExtensions.includes(extension);
    }

    /**
     * فحص حجم الملف
     * Check file size
     */
    public static getFileSize(filePath: string): number {
        try {
            if (!fs.existsSync(filePath)) {
                return 0;
            }
            const stats = fs.statSync(filePath);
            return stats.size;
        } catch (error) {
            return 0;
        }
    }

    /**
     * فحص تاريخ تعديل الملف
     * Check file modification date
     */
    public static getFileModificationDate(filePath: string): Date | null {
        try {
            if (!fs.existsSync(filePath)) {
                return null;
            }
            const stats = fs.statSync(filePath);
            return stats.mtime;
        } catch (error) {
            return null;
        }
    }

    /**
     * فحص صلاحيات الملف
     * Check file permissions
     */
    public static isFileReadable(filePath: string): boolean {
        try {
            fs.accessSync(filePath, fs.constants.R_OK);
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * فحص إذا كان الملف قابل للكتابة
     * Check if file is writable
     */
    public static isFileWritable(filePath: string): boolean {
        try {
            fs.accessSync(filePath, fs.constants.W_OK);
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * الحصول على قائمة الملفات في مجلد
     * Get list of files in directory
     */
    public static getFilesInDirectory(dirPath: string): string[] {
        try {
            if (!fs.existsSync(dirPath) || !fs.statSync(dirPath).isDirectory()) {
                return [];
            }

            const items = fs.readdirSync(dirPath);
            const files: string[] = [];

            for (const item of items) {
                const fullPath = path.join(dirPath, item);
                if (fs.statSync(fullPath).isFile()) {
                    files.push(fullPath);
                }
            }

            return files;
        } catch (error) {
            return [];
        }
    }
}
