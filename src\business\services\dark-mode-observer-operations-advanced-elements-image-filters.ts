/**
 * فلاتر الصور في الوضع المظلم
 * Image filters for dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeConfig } from './dark-mode-config';

/**
 * فئة فلاتر الصور
 * Image filters class
 */
export class DarkModeObserverOperationsAdvancedElementsImageFilters {

    /** تطبيق فلاتر الصورة / Apply image filters */
    public static applyImageFilters(element: HTMLImageElement, config: DarkModeConfig): void {
        if (!config.imageEnhancements?.enabled) return;

        let filters: string[] = [];

        // فلتر السطوع
        if (config.imageEnhancements.brightness && config.imageEnhancements.brightness !== 1) {
            filters.push(`brightness(${config.imageEnhancements.brightness})`);
        }

        // فلتر التباين
        if (config.imageEnhancements.contrast && config.imageEnhancements.contrast !== 1) {
            filters.push(`contrast(${config.imageEnhancements.contrast})`);
        }

        // فلتر التشبع
        if (config.imageEnhancements.saturation && config.imageEnhancements.saturation !== 1) {
            filters.push(`saturate(${config.imageEnhancements.saturation})`);
        }

        // فلتر الصبغة
        if (config.imageEnhancements.hueRotate && config.imageEnhancements.hueRotate !== 0) {
            filters.push(`hue-rotate(${config.imageEnhancements.hueRotate}deg)`);
        }

        // فلتر الضبابية
        if (config.imageEnhancements.blur && config.imageEnhancements.blur !== 0) {
            filters.push(`blur(${config.imageEnhancements.blur}px)`);
        }

        // تطبيق الفلاتر
        if (filters.length > 0) {
            const existingFilter = element.style.filter || '';
            const newFilters = filters.join(' ');
            element.style.filter = existingFilter ? `${existingFilter} ${newFilters}` : newFilters;
        }
    }

    /** تطبيق فلاتر افتراضية للوضع المظلم / Apply default dark mode filters */
    public static applyDefaultDarkModeFilters(element: HTMLImageElement): void {
        const defaultFilters = [
            'brightness(0.85)',
            'contrast(1.1)',
            'saturate(0.9)'
        ];

        element.style.filter = defaultFilters.join(' ');
    }

    /** تطبيق فلاتر حسب نوع الصورة / Apply filters by image type */
    public static applyTypeSpecificFilters(element: HTMLImageElement, imageType: string): void {
        switch (imageType) {
            case 'avatar':
                this.applyAvatarFilters(element);
                break;
            case 'thumbnail':
                this.applyThumbnailFilters(element);
                break;
            case 'banner':
                this.applyBannerFilters(element);
                break;
            case 'icon':
                this.applyIconFilters(element);
                break;
            default:
                this.applyDefaultDarkModeFilters(element);
                break;
        }
    }

    /** تطبيق فلاتر الصور الرمزية / Apply avatar filters */
    private static applyAvatarFilters(element: HTMLImageElement): void {
        const filters = [
            'brightness(0.9)',
            'contrast(1.05)',
            'saturate(1.1)'
        ];

        element.style.filter = filters.join(' ');
    }

    /** تطبيق فلاتر الصور المصغرة / Apply thumbnail filters */
    private static applyThumbnailFilters(element: HTMLImageElement): void {
        const filters = [
            'brightness(0.8)',
            'contrast(1.2)',
            'saturate(0.95)'
        ];

        element.style.filter = filters.join(' ');
    }

    /** تطبيق فلاتر البانرات / Apply banner filters */
    private static applyBannerFilters(element: HTMLImageElement): void {
        const filters = [
            'brightness(0.75)',
            'contrast(1.15)',
            'saturate(0.9)'
        ];

        element.style.filter = filters.join(' ');
    }

    /** تطبيق فلاتر الأيقونات / Apply icon filters */
    private static applyIconFilters(element: HTMLImageElement): void {
        const filters = [
            'brightness(0.9)',
            'contrast(1.1)'
        ];

        element.style.filter = filters.join(' ');
    }

    /** إزالة جميع الفلاتر / Remove all filters */
    public static removeAllFilters(element: HTMLImageElement): void {
        element.style.filter = 'none';
    }

    /** تطبيق فلتر مؤقت / Apply temporary filter */
    public static applyTemporaryFilter(element: HTMLImageElement, filter: string, duration: number = 3000): void {
        const originalFilter = element.style.filter;
        element.style.filter = filter;

        setTimeout(() => {
            element.style.filter = originalFilter;
        }, duration);
    }

    /** تطبيق فلتر تدريجي / Apply gradual filter */
    public static applyGradualFilter(element: HTMLImageElement, targetFilter: string, duration: number = 1000): void {
        element.style.transition = `filter ${duration}ms ease`;
        element.style.filter = targetFilter;

        setTimeout(() => {
            element.style.transition = '';
        }, duration);
    }
}
