/**
 * مدير إعدادات التطبيق
 * Application settings manager
 *
 * هذا الملف يحتوي على منطق إدارة إعدادات التطبيق
 * This file contains application settings management logic
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 *
 * @requires @business/services/settings-service للخدمة الرئيسية
 * @requires @business/services/settings-config للتكوين والثوابت
 * @requires @business/services/settings-validator للتحقق من الإعدادات
 * @requires @business/services/settings-backup لإدارة النسخ الاحتياطية
 */

// إعادة تصدير الخدمة الجديدة للتوافق مع الكود الموجود
export {
    BackupInfo, DEFAULT_SETTINGS_MANAGER_CONFIG, DEFAULT_SETTINGS_VALUES,
    ExportOptions, ImportOptions, SettingChangeInfo, SETTINGS_CONSTANTS, SETTINGS_EVENTS,
    SETTINGS_KEYS, SETTINGS_MESSAGES, SETTINGS_VALIDATION_RULES, SettingsErrorReport, SettingsErrorType,
    SettingsManagerConfig, SettingsStatistics, SettingsValidationResult
} from './settings-config';

export { SettingsBackupManager } from './settings-backup';
export { SettingsService as SettingsManager } from './settings-service';
export { SettingsValidator } from './settings-validator';

