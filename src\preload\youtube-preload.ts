/**
 * سكريبت Preload لـ YouTube
 * YouTube preload script
 * 
 * هذا الملف يحتوي على منطق التحكم في YouTube قبل تحميل الصفحة
 * This file contains YouTube control logic before page load
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 * 
 * @requires @presentation/controllers/youtube-controller للتحكم الرئيسي
 */

import { YouTubeController } from '@presentation/controllers/youtube-controller';
import { TIMEOUTS, APPLICATION_INFO } from '@shared/constants';

/**
 * تحكم YouTube العام
 * Global YouTube controller
 */
let youtubeController: YouTubeController | null = null;

/**
 * تهيئة التطبيق
 * Initializes the application
 * 
 * @returns Promise<void>
 */
async function initializeApplication(): Promise<void> {
    try {
        console.log(`تشغيل ${APPLICATION_INFO.NAME} - Preload Script`);

        // إنشاء التحكم
        youtubeController = new YouTubeController();

        // تهيئة التحكم
        const initResult = await youtubeController.initialize();
        
        if (!initResult.isValid) {
            console.error('فشل في تهيئة التحكم:', initResult.errors);
            return;
        }

        console.log('تم تهيئة التطبيق بنجاح في Preload');

        // إعداد معالجات النافذة
        setupWindowHandlers();

        // إعداد معالجات الأخطاء
        setupErrorHandlers();

    } catch (error) {
        console.error('خطأ في تهيئة التطبيق:', error);
    }
}

/**
 * إعداد معالجات النافذة
 * Sets up window handlers
 * 
 * @returns void
 */
function setupWindowHandlers(): void {
    // معالج إغلاق النافذة
    window.addEventListener('beforeunload', async () => {
        if (youtubeController) {
            await youtubeController.cleanup();
        }
    });

    // معالج تغيير حجم النافذة
    window.addEventListener('resize', () => {
        console.log('تم تغيير حجم النافذة');
    });

    // معالج فقدان التركيز
    window.addEventListener('blur', () => {
        console.log('فقدت النافذة التركيز');
    });

    // معالج استعادة التركيز
    window.addEventListener('focus', () => {
        console.log('استعادت النافذة التركيز');
    });
}

/**
 * إعداد معالجات الأخطاء
 * Sets up error handlers
 * 
 * @returns void
 */
function setupErrorHandlers(): void {
    // معالج الأخطاء العامة
    window.addEventListener('error', (event) => {
        console.error('خطأ في النافذة:', {
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            error: event.error
        });
    });

    // معالج الأخطاء غير المعالجة
    window.addEventListener('unhandledrejection', (event) => {
        console.error('Promise غير معالج:', event.reason);
        event.preventDefault();
    });

    // معالج أخطاء الشبكة
    window.addEventListener('offline', () => {
        console.warn('فقدان الاتصال بالإنترنت');
    });

    window.addEventListener('online', () => {
        console.log('تم استعادة الاتصال بالإنترنت');
    });
}

/**
 * تنظيف الموارد عند الإغلاق
 * Cleans up resources on close
 * 
 * @returns Promise<void>
 */
async function cleanup(): Promise<void> {
    try {
        if (youtubeController) {
            await youtubeController.cleanup();
            youtubeController = null;
        }
        console.log('تم تنظيف موارد Preload');
    } catch (error) {
        console.error('خطأ في تنظيف موارد Preload:', error);
    }
}

/**
 * انتظار تحميل DOM
 * Waits for DOM to load
 * 
 * @returns Promise<void>
 */
function waitForDOMLoad(): Promise<void> {
    return new Promise((resolve) => {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => resolve());
        } else {
            resolve();
        }
    });
}

/**
 * انتظار تحميل النافذة
 * Waits for window to load
 * 
 * @returns Promise<void>
 */
function waitForWindowLoad(): Promise<void> {
    return new Promise((resolve) => {
        if (document.readyState === 'complete') {
            resolve();
        } else {
            window.addEventListener('load', () => resolve());
        }
    });
}

/**
 * تشغيل التطبيق
 * Runs the application
 * 
 * @returns Promise<void>
 */
async function runApplication(): Promise<void> {
    try {
        // انتظار تحميل DOM
        await waitForDOMLoad();
        console.log('تم تحميل DOM');

        // تأخير قصير للسماح لـ YouTube بالتحميل
        await new Promise(resolve => setTimeout(resolve, TIMEOUTS.INITIAL_DELAY));

        // تهيئة التطبيق
        await initializeApplication();

        // انتظار تحميل النافذة الكامل
        await waitForWindowLoad();
        console.log('تم تحميل النافذة بالكامل');

    } catch (error) {
        console.error('خطأ في تشغيل التطبيق:', error);
    }
}

// تشغيل التطبيق
runApplication();

// تصدير للاستخدام الخارجي
declare global {
    interface Window {
        youtubeController: YouTubeController | null;
        cleanup: () => Promise<void>;
    }
}

window.youtubeController = youtubeController;
window.cleanup = cleanup;
