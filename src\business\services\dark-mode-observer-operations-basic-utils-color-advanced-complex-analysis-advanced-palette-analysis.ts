/**
 * تحليل لوحة الألوان الشامل المتقدم المعقد المتقدم
 * Advanced complex advanced comprehensive palette analysis
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic } from './dark-mode-observer-operations-basic-utils-color-advanced-complex-analysis-basic';
import { DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisAdvancedHarmony } from './dark-mode-observer-operations-basic-utils-color-advanced-complex-analysis-advanced-harmony';

/**
 * فئة تحليل لوحة الألوان الشامل المتقدم المعقد المتقدم
 * Advanced complex advanced comprehensive palette analysis class
 */
export class DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisAdvancedPaletteAnalysis {

    /** تحليل لوحة الألوان الشاملة / Comprehensive palette analysis */
    public static analyzePalette(colors: string[]): {
        overall: {
            score: number;
            accessibility: 'excellent' | 'good' | 'fair' | 'poor';
            harmony: string;
            balance: string;
        };
        individual: Array<{
            color: string;
            analysis: ReturnType<typeof DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor>;
            quality: ReturnType<typeof DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.evaluateColorQuality>;
        }>;
        recommendations: string[];
    } {
        const individual = colors.map(color => ({
            color,
            analysis: DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(color),
            quality: DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.evaluateColorQuality(color)
        }));

        const harmony = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisAdvancedHarmony.analyzeColorHarmony(colors);
        const improvements = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisAdvancedHarmony.suggestColorImprovements(colors);

        // حساب النتيجة الإجمالية
        const averageQuality = individual.reduce((sum, item) => sum + item.quality.score, 0) / individual.length;
        const overallScore = (averageQuality + harmony.score) / 2;

        let accessibility: 'excellent' | 'good' | 'fair' | 'poor';
        if (overallScore >= 90) accessibility = 'excellent';
        else if (overallScore >= 75) accessibility = 'good';
        else if (overallScore >= 60) accessibility = 'fair';
        else accessibility = 'poor';

        // تحليل التوازن
        const temperatures = individual.map(item => item.analysis.temperature);
        const warmCount = temperatures.filter(t => t === 'warm').length;
        const coolCount = temperatures.filter(t => t === 'cool').length;
        const neutralCount = temperatures.filter(t => t === 'neutral').length;

        let balance: string;
        if (Math.abs(warmCount - coolCount) <= 1) {
            balance = 'متوازن بين الألوان الدافئة والباردة';
        } else if (warmCount > coolCount + 1) {
            balance = 'يميل للألوان الدافئة';
        } else if (coolCount > warmCount + 1) {
            balance = 'يميل للألوان الباردة';
        } else {
            balance = 'توازن جيد';
        }

        const recommendations = [
            ...improvements.suggestions,
            `التناسق: ${harmony.recommendation}`,
            `التوازن: ${balance}`
        ];

        return {
            overall: {
                score: Math.round(overallScore),
                accessibility,
                harmony: harmony.harmonyType,
                balance
            },
            individual,
            recommendations
        };
    }

    /** تقييم التنوع في لوحة الألوان / Evaluate palette diversity */
    public static evaluatePaletteDiversity(colors: string[]): {
        diversityScore: number;
        typeDistribution: Record<string, number>;
        temperatureDistribution: Record<string, number>;
        intensityDistribution: Record<string, number>;
        recommendations: string[];
    } {
        if (colors.length === 0) {
            return {
                diversityScore: 0,
                typeDistribution: {},
                temperatureDistribution: {},
                intensityDistribution: {},
                recommendations: ['لا توجد ألوان للتقييم']
            };
        }

        const colorTypes = colors.map(color => 
            DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.determineColorType(color)
        );

        // توزيع الأنواع
        const typeDistribution: Record<string, number> = {};
        const temperatureDistribution: Record<string, number> = {};
        const intensityDistribution: Record<string, number> = {};

        colorTypes.forEach(type => {
            typeDistribution[type.type] = (typeDistribution[type.type] || 0) + 1;
            temperatureDistribution[type.category] = (temperatureDistribution[type.category] || 0) + 1;
            intensityDistribution[type.intensity] = (intensityDistribution[type.intensity] || 0) + 1;
        });

        // حساب نقاط التنوع
        let diversityScore = 0;

        // تنوع الأنواع (40% من النقاط)
        const uniqueTypes = Object.keys(typeDistribution).length;
        const maxTypes = Math.min(5, colors.length); // أقصى 5 أنواع
        diversityScore += (uniqueTypes / maxTypes) * 40;

        // تنوع درجة الحرارة (30% من النقاط)
        const uniqueTemperatures = Object.keys(temperatureDistribution).length;
        const maxTemperatures = Math.min(3, colors.length); // أقصى 3 درجات حرارة
        diversityScore += (uniqueTemperatures / maxTemperatures) * 30;

        // تنوع الكثافة (30% من النقاط)
        const uniqueIntensities = Object.keys(intensityDistribution).length;
        const maxIntensities = Math.min(3, colors.length); // أقصى 3 كثافات
        diversityScore += (uniqueIntensities / maxIntensities) * 30;

        // إنشاء التوصيات
        const recommendations: string[] = [];

        if (uniqueTypes === 1 && colors.length > 2) {
            recommendations.push('أضف تنوع في أنواع الألوان (أساسي، ثانوي، ثلاثي)');
        }

        if (uniqueTemperatures === 1 && colors.length > 2) {
            recommendations.push('أضف تنوع في درجة حرارة الألوان (دافئ، بارد، محايد)');
        }

        if (uniqueIntensities === 1 && colors.length > 2) {
            recommendations.push('أضف تنوع في كثافة الألوان (فاتح، متوسط، غامق)');
        }

        if (diversityScore >= 80) {
            recommendations.push('تنوع ممتاز في لوحة الألوان');
        } else if (diversityScore >= 60) {
            recommendations.push('تنوع جيد مع إمكانية للتحسين');
        } else {
            recommendations.push('يحتاج المزيد من التنوع في الألوان');
        }

        return {
            diversityScore: Math.round(diversityScore),
            typeDistribution,
            temperatureDistribution,
            intensityDistribution,
            recommendations
        };
    }
}
