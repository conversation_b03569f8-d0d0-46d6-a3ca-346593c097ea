/**
 * مولدات وهمية لاختبارات تكامل التطبيق
 * Application integration test mocks
 * 
 * هذا الملف يحتوي على المولدات الوهمية لاختبارات التكامل
 * This file contains mock generators for integration tests
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * مولد وهمي لـ Electron
 * Mock Electron generator
 */
export const mockElectron = {
    app: {
        whenReady: jest.fn(() => Promise.resolve()),
        on: jest.fn(),
        quit: jest.fn(),
        getPath: jest.fn((name: string) => {
            switch (name) {
                case 'userData': return '/mock/userData';
                case 'appData': return '/mock/appData';
                case 'temp': return '/mock/temp';
                default: return '/mock/default';
            }
        }),
        getVersion: jest.fn(() => '1.0.0'),
        getName: jest.fn(() => 'YouTube Dark Cyber X'),
        isReady: jest.fn(() => true),
        exit: jest.fn(),
        relaunch: jest.fn()
    },
    BrowserWindow: jest.fn().mockImplementation(() => ({
        loadURL: jest.fn(() => Promise.resolve()),
        loadFile: jest.fn(() => Promise.resolve()),
        on: jest.fn(),
        once: jest.fn(),
        removeAllListeners: jest.fn(),
        webContents: {
            on: jest.fn(),
            once: jest.fn(),
            send: jest.fn(),
            executeJavaScript: jest.fn(() => Promise.resolve()),
            insertCSS: jest.fn(() => Promise.resolve()),
            removeInsertedCSS: jest.fn(() => Promise.resolve()),
            setUserAgent: jest.fn(),
            session: {
                webRequest: {
                    onBeforeRequest: jest.fn(),
                    onHeadersReceived: jest.fn()
                },
                clearCache: jest.fn(() => Promise.resolve()),
                clearStorageData: jest.fn(() => Promise.resolve())
            }
        },
        show: jest.fn(),
        hide: jest.fn(),
        close: jest.fn(),
        destroy: jest.fn(),
        isDestroyed: jest.fn(() => false),
        isVisible: jest.fn(() => true),
        isFocused: jest.fn(() => true),
        isMinimized: jest.fn(() => false),
        isMaximized: jest.fn(() => false),
        isFullScreen: jest.fn(() => false),
        getBounds: jest.fn(() => ({ x: 0, y: 0, width: 1200, height: 800 })),
        setBounds: jest.fn(),
        getSize: jest.fn(() => [1200, 800]),
        setSize: jest.fn(),
        getPosition: jest.fn(() => [100, 100]),
        setPosition: jest.fn(),
        center: jest.fn(),
        minimize: jest.fn(),
        maximize: jest.fn(),
        unmaximize: jest.fn(),
        restore: jest.fn(),
        setFullScreen: jest.fn(),
        setMenuBarVisibility: jest.fn(),
        setAutoHideMenuBar: jest.fn(),
        setTitle: jest.fn(),
        getTitle: jest.fn(() => 'YouTube Dark Cyber X'),
        setIcon: jest.fn(),
        setResizable: jest.fn(),
        isResizable: jest.fn(() => true),
        setMovable: jest.fn(),
        isMovable: jest.fn(() => true),
        setMinimizable: jest.fn(),
        isMinimizable: jest.fn(() => true),
        setMaximizable: jest.fn(),
        isMaximizable: jest.fn(() => true),
        setClosable: jest.fn(),
        isClosable: jest.fn(() => true),
        setAlwaysOnTop: jest.fn(),
        isAlwaysOnTop: jest.fn(() => false),
        setSkipTaskbar: jest.fn(),
        setKiosk: jest.fn(),
        isKiosk: jest.fn(() => false)
    })),
    ipcMain: {
        handle: jest.fn(),
        on: jest.fn(),
        once: jest.fn(),
        removeHandler: jest.fn(),
        removeAllListeners: jest.fn(),
        emit: jest.fn()
    },
    ipcRenderer: {
        invoke: jest.fn(() => Promise.resolve()),
        send: jest.fn(),
        on: jest.fn(),
        once: jest.fn(),
        removeListener: jest.fn(),
        removeAllListeners: jest.fn()
    },
    Menu: {
        buildFromTemplate: jest.fn(() => ({
            popup: jest.fn(),
            closePopup: jest.fn(),
            append: jest.fn(),
            insert: jest.fn(),
            getMenuItemById: jest.fn()
        })),
        setApplicationMenu: jest.fn(),
        getApplicationMenu: jest.fn(),
        sendActionToFirstResponder: jest.fn()
    },
    MenuItem: jest.fn().mockImplementation((options) => ({
        ...options,
        click: options.click || jest.fn(),
        enabled: options.enabled !== false,
        visible: options.visible !== false,
        checked: options.checked || false
    })),
    dialog: {
        showOpenDialog: jest.fn(() => Promise.resolve({ canceled: false, filePaths: ['/mock/file.txt'] })),
        showSaveDialog: jest.fn(() => Promise.resolve({ canceled: false, filePath: '/mock/save.txt' })),
        showMessageBox: jest.fn(() => Promise.resolve({ response: 0, checkboxChecked: false })),
        showErrorBox: jest.fn(),
        showCertificateTrustDialog: jest.fn(() => Promise.resolve())
    },
    shell: {
        openExternal: jest.fn(() => Promise.resolve()),
        openPath: jest.fn(() => Promise.resolve('')),
        showItemInFolder: jest.fn(),
        moveItemToTrash: jest.fn(() => true),
        beep: jest.fn(),
        writeShortcutLink: jest.fn(() => true),
        readShortcutLink: jest.fn(() => ({}))
    },
    nativeTheme: {
        shouldUseDarkColors: false,
        themeSource: 'system',
        on: jest.fn(),
        once: jest.fn(),
        removeListener: jest.fn(),
        removeAllListeners: jest.fn()
    },
    screen: {
        getPrimaryDisplay: jest.fn(() => ({
            bounds: { x: 0, y: 0, width: 1920, height: 1080 },
            workArea: { x: 0, y: 0, width: 1920, height: 1040 },
            scaleFactor: 1,
            rotation: 0
        })),
        getAllDisplays: jest.fn(() => []),
        getDisplayNearestPoint: jest.fn(),
        getDisplayMatching: jest.fn(),
        getCursorScreenPoint: jest.fn(() => ({ x: 0, y: 0 })),
        on: jest.fn(),
        removeListener: jest.fn()
    },
    globalShortcut: {
        register: jest.fn(() => true),
        registerAll: jest.fn(),
        isRegistered: jest.fn(() => false),
        unregister: jest.fn(),
        unregisterAll: jest.fn()
    },
    clipboard: {
        readText: jest.fn(() => ''),
        writeText: jest.fn(),
        readHTML: jest.fn(() => ''),
        writeHTML: jest.fn(),
        readImage: jest.fn(),
        writeImage: jest.fn(),
        readRTF: jest.fn(() => ''),
        writeRTF: jest.fn(),
        readBookmark: jest.fn(() => ({ title: '', url: '' })),
        writeBookmark: jest.fn(),
        readFindText: jest.fn(() => ''),
        writeFindText: jest.fn(),
        clear: jest.fn(),
        availableFormats: jest.fn(() => []),
        has: jest.fn(() => false),
        read: jest.fn(() => ''),
        write: jest.fn()
    }
};

/**
 * مولد وهمي لـ Node.js modules
 * Mock Node.js modules generator
 */
export const mockNodeModules = {
    fs: {
        promises: {
            readFile: jest.fn(() => Promise.resolve('{"test": true}')),
            writeFile: jest.fn(() => Promise.resolve()),
            mkdir: jest.fn(() => Promise.resolve()),
            access: jest.fn(() => Promise.resolve()),
            stat: jest.fn(() => Promise.resolve({ isDirectory: () => false, isFile: () => true })),
            readdir: jest.fn(() => Promise.resolve(['file1.txt', 'file2.txt'])),
            unlink: jest.fn(() => Promise.resolve()),
            rmdir: jest.fn(() => Promise.resolve())
        },
        existsSync: jest.fn(() => true),
        readFileSync: jest.fn(() => '{"test": true}'),
        writeFileSync: jest.fn(),
        mkdirSync: jest.fn(),
        statSync: jest.fn(() => ({ isDirectory: () => false, isFile: () => true })),
        readdirSync: jest.fn(() => ['file1.txt', 'file2.txt']),
        unlinkSync: jest.fn(),
        rmdirSync: jest.fn()
    },
    path: {
        join: jest.fn((...args) => args.join('/')),
        resolve: jest.fn((...args) => '/' + args.join('/')),
        dirname: jest.fn((path) => path.split('/').slice(0, -1).join('/')),
        basename: jest.fn((path) => path.split('/').pop()),
        extname: jest.fn((path) => {
            const parts = path.split('.');
            return parts.length > 1 ? '.' + parts.pop() : '';
        }),
        normalize: jest.fn((path) => path),
        isAbsolute: jest.fn((path) => path.startsWith('/'))
    },
    os: {
        platform: jest.fn(() => 'win32'),
        arch: jest.fn(() => 'x64'),
        release: jest.fn(() => '10.0.19041'),
        type: jest.fn(() => 'Windows_NT'),
        homedir: jest.fn(() => '/home/<USER>'),
        tmpdir: jest.fn(() => '/tmp'),
        hostname: jest.fn(() => 'test-machine'),
        userInfo: jest.fn(() => ({ username: 'testuser', uid: 1000, gid: 1000, shell: '/bin/bash', homedir: '/home/<USER>' }))
    }
};

/**
 * مولد وهمي للـ DOM
 * Mock DOM generator
 */
export const mockDOM = {
    document: {
        createElement: jest.fn((tagName) => ({
            tagName: tagName.toUpperCase(),
            setAttribute: jest.fn(),
            getAttribute: jest.fn(),
            appendChild: jest.fn(),
            removeChild: jest.fn(),
            addEventListener: jest.fn(),
            removeEventListener: jest.fn(),
            click: jest.fn(),
            focus: jest.fn(),
            blur: jest.fn(),
            style: {},
            classList: {
                add: jest.fn(),
                remove: jest.fn(),
                contains: jest.fn(() => false),
                toggle: jest.fn()
            },
            textContent: '',
            innerHTML: '',
            id: '',
            className: ''
        })),
        querySelector: jest.fn(),
        querySelectorAll: jest.fn(() => []),
        getElementById: jest.fn(),
        getElementsByClassName: jest.fn(() => []),
        getElementsByTagName: jest.fn(() => []),
        head: {
            appendChild: jest.fn(),
            removeChild: jest.fn()
        },
        body: {
            appendChild: jest.fn(),
            removeChild: jest.fn()
        },
        addEventListener: jest.fn(),
        removeEventListener: jest.fn()
    },
    window: {
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        setTimeout: jest.fn((fn, delay) => setTimeout(fn, delay)),
        clearTimeout: jest.fn((id) => clearTimeout(id)),
        setInterval: jest.fn((fn, delay) => setInterval(fn, delay)),
        clearInterval: jest.fn((id) => clearInterval(id)),
        location: {
            href: 'https://www.youtube.com',
            hostname: 'www.youtube.com',
            pathname: '/watch',
            search: '?v=dQw4w9WgXcQ',
            hash: '',
            reload: jest.fn()
        },
        navigator: {
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            platform: 'Win32',
            language: 'en-US',
            languages: ['en-US', 'en'],
            onLine: true
        },
        console: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            info: jest.fn(),
            debug: jest.fn()
        }
    }
};
