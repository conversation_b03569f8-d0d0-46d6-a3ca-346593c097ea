/**
 * أنواع إعدادات التطبيق
 * Application settings types
 * 
 * هذا الملف يحتوي على تعريفات أنواع إعدادات التطبيق الأساسية
 * This file contains core application settings type definitions
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQuality } from './video-types';

/**
 * إعدادات التطبيق الأساسية
 * Core application settings
 */
export interface ApplicationSettings {
    /** جودة الفيديو / Video quality */
    videoQuality: VideoQuality;
    /** الوضع المظلم / Dark mode */
    darkMode: boolean;
    /** مانع الإعلانات / Ad blocker */
    adBlocker: boolean;
    /** التشغيل التلقائي / Auto play */
    autoPlay?: boolean;
    /** كتم الصوت التلقائي / Auto mute */
    autoMute?: boolean;
    /** الحجم الافتراضي / Default volume */
    defaultVolume?: number;
    /** سرعة التشغيل / Playback speed */
    playbackSpeed?: number;
    /** اللغة / Language */
    language?: string;
    /** المنطقة الزمنية / Timezone */
    timezone?: string;
}

/**
 * إعدادات الأداء
 * Performance settings
 */
export interface PerformanceSettings {
    /** تسريع الأجهزة / Hardware acceleration */
    hardwareAcceleration: boolean;
    /** حد ذاكرة التخزين المؤقت / Cache memory limit */
    cacheLimit: number;
    /** تحسين للأجهزة الضعيفة / Optimize for low-end devices */
    lowEndOptimization: boolean;
    /** تحديد معدل الإطارات / Frame rate limit */
    frameRateLimit?: number;
    /** جودة الرسوميات / Graphics quality */
    graphicsQuality: 'low' | 'medium' | 'high' | 'ultra';
}

/**
 * إعدادات الشبكة
 * Network settings
 */
export interface NetworkSettings {
    /** مهلة الاتصال / Connection timeout */
    connectionTimeout: number;
    /** عدد محاولات إعادة الاتصال / Retry attempts */
    retryAttempts: number;
    /** استخدام البروكسي / Use proxy */
    useProxy: boolean;
    /** عنوان البروكسي / Proxy address */
    proxyAddress?: string;
    /** منفذ البروكسي / Proxy port */
    proxyPort?: number;
    /** نوع البروكسي / Proxy type */
    proxyType?: 'http' | 'https' | 'socks4' | 'socks5';
}

/**
 * إعدادات الخصوصية
 * Privacy settings
 */
export interface PrivacySettings {
    /** حجب التتبع / Block tracking */
    blockTracking: boolean;
    /** مسح الكوكيز عند الخروج / Clear cookies on exit */
    clearCookiesOnExit: boolean;
    /** تعطيل التاريخ / Disable history */
    disableHistory: boolean;
    /** الوضع المجهول / Anonymous mode */
    anonymousMode: boolean;
    /** حجب التحليلات / Block analytics */
    blockAnalytics: boolean;
    /** حجب البصمة الرقمية / Block fingerprinting */
    blockFingerprinting: boolean;
}

/**
 * إعدادات الأمان
 * Security settings
 */
export interface SecuritySettings {
    /** تشفير البيانات / Encrypt data */
    encryptData: boolean;
    /** التحقق من الشهادات / Verify certificates */
    verifyCertificates: boolean;
    /** حجب المحتوى الضار / Block malicious content */
    blockMaliciousContent: boolean;
    /** مستوى الأمان / Security level */
    securityLevel: 'low' | 'medium' | 'high' | 'paranoid';
    /** تمكين الحماية من XSS / Enable XSS protection */
    xssProtection: boolean;
    /** تمكين HTTPS فقط / HTTPS only */
    httpsOnly: boolean;
}

/**
 * إعدادات التحديث
 * Update settings
 */
export interface UpdateSettings {
    /** التحديث التلقائي / Auto update */
    autoUpdate: boolean;
    /** التحقق من التحديثات / Check for updates */
    checkForUpdates: boolean;
    /** تكرار التحقق / Check frequency */
    checkFrequency: 'daily' | 'weekly' | 'monthly' | 'never';
    /** تحديث تجريبي / Beta updates */
    betaUpdates: boolean;
    /** إشعارات التحديث / Update notifications */
    updateNotifications: boolean;
}

/**
 * إعدادات التطبيق الشاملة
 * Comprehensive application settings
 */
export interface ComprehensiveSettings {
    /** الإعدادات الأساسية / Core settings */
    application: ApplicationSettings;
    /** إعدادات الأداء / Performance settings */
    performance: PerformanceSettings;
    /** إعدادات الشبكة / Network settings */
    network: NetworkSettings;
    /** إعدادات الخصوصية / Privacy settings */
    privacy: PrivacySettings;
    /** إعدادات الأمان / Security settings */
    security: SecuritySettings;
    /** إعدادات التحديث / Update settings */
    update: UpdateSettings;
}

/**
 * نتيجة تطبيق الإعدادات
 * Settings application result
 */
export interface SettingsApplicationResult {
    /** نجح التطبيق / Application successful */
    success: boolean;
    /** رسالة النتيجة / Result message */
    message: string;
    /** الإعدادات المطبقة / Applied settings */
    appliedSettings: string[];
    /** الإعدادات الفاشلة / Failed settings */
    failedSettings: string[];
    /** تحتاج إعادة تشغيل / Requires restart */
    requiresRestart: boolean;
}

/**
 * حدث تغيير الإعدادات
 * Settings change event
 */
export interface SettingsChangeEvent {
    /** نوع الحدث / Event type */
    type: 'settings-changed';
    /** الإعداد المتغير / Changed setting */
    setting: string;
    /** القيمة القديمة / Old value */
    oldValue: unknown;
    /** القيمة الجديدة / New value */
    newValue: unknown;
    /** الطابع الزمني / Timestamp */
    timestamp: Date;
}

/**
 * مدقق صحة الإعدادات
 * Settings validator
 */
export interface SettingsValidator {
    /** التحقق من صحة الإعدادات / Validate settings */
    validate(settings: Partial<ComprehensiveSettings>): SettingsValidationResult;
    /** التحقق من إعداد واحد / Validate single setting */
    validateSetting(key: string, value: unknown): boolean;
    /** الحصول على القيم الافتراضية / Get default values */
    getDefaults(): ComprehensiveSettings;
}

/**
 * نتيجة التحقق من صحة الإعدادات
 * Settings validation result
 */
export interface SettingsValidationResult {
    /** صحيح / Valid */
    valid: boolean;
    /** الأخطاء / Errors */
    errors: string[];
    /** التحذيرات / Warnings */
    warnings: string[];
    /** الإعدادات المصححة / Corrected settings */
    correctedSettings?: Partial<ComprehensiveSettings>;
}
