/**
 * مستكشف الملفات الجوهري الأساسي
 * Core basic file walker
 * 
 * هذا الملف يحتوي على مستكشف الملفات الجوهري الأساسي
 * This file contains core basic file walker
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import * as fs from 'fs';
import * as path from 'path';
import { SimpleVerificationConfig } from './simple-verification-types';

/**
 * فئة مستكشف الملفات الجوهري الأساسي
 * Core basic file walker class
 */
export class SimpleVerificationCoreUtilsFileOperationsBasicCoreWalker {

    /**
     * الحصول على جميع الملفات
     * Get all files
     */
    public static async getAllFiles(
        projectRoot: string,
        config: SimpleVerificationConfig
    ): Promise<string[]> {
        const files: string[] = [];

        const walkDir = (dir: string): void => {
            const items = fs.readdirSync(dir);
            
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const relativePath = path.relative(projectRoot, fullPath);

                // تحقق من الاستثناءات
                if (config.excludePatterns.some(pattern => 
                    relativePath.match(pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*'))
                )) {
                    continue;
                }

                const stats = fs.statSync(fullPath);
                
                if (stats.isDirectory()) {
                    walkDir(fullPath);
                } else if (stats.isFile()) {
                    // تحقق من الأنماط المضمنة
                    if (config.includePatterns.some(pattern => 
                        relativePath.match(pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*'))
                    )) {
                        files.push(fullPath);
                    }
                }
            }
        };

        walkDir(projectRoot);
        return files;
    }

    /**
     * استكشاف مجلد واحد
     * Walk single directory
     */
    public static walkSingleDirectory(
        dirPath: string,
        includeSubdirectories: boolean = true
    ): string[] {
        const files: string[] = [];

        try {
            if (!fs.existsSync(dirPath) || !fs.statSync(dirPath).isDirectory()) {
                return files;
            }

            const items = fs.readdirSync(dirPath);

            for (const item of items) {
                const fullPath = path.join(dirPath, item);
                const stats = fs.statSync(fullPath);

                if (stats.isFile()) {
                    files.push(fullPath);
                } else if (stats.isDirectory() && includeSubdirectories) {
                    const subFiles = this.walkSingleDirectory(fullPath, true);
                    files.push(...subFiles);
                }
            }

        } catch (error) {
            // تجاهل الأخطاء وإرجاع قائمة فارغة
        }

        return files;
    }

    /**
     * فلترة الملفات حسب النمط
     * Filter files by pattern
     */
    public static filterFilesByPattern(
        files: string[],
        includePatterns: string[],
        excludePatterns: string[],
        projectRoot: string
    ): string[] {
        return files.filter(file => {
            const relativePath = path.relative(projectRoot, file);

            // تحقق من الاستثناءات
            if (excludePatterns.some(pattern => 
                relativePath.match(pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*'))
            )) {
                return false;
            }

            // تحقق من الأنماط المضمنة
            return includePatterns.some(pattern => 
                relativePath.match(pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*'))
            );
        });
    }

    /**
     * فلترة الملفات حسب الامتداد
     * Filter files by extension
     */
    public static filterFilesByExtension(
        files: string[],
        allowedExtensions: string[]
    ): string[] {
        return files.filter(file => {
            const extension = path.extname(file).toLowerCase();
            return allowedExtensions.includes(extension);
        });
    }

    /**
     * فلترة الملفات حسب الحجم
     * Filter files by size
     */
    public static filterFilesBySize(
        files: string[],
        minSize: number = 0,
        maxSize: number = Number.MAX_SAFE_INTEGER
    ): string[] {
        return files.filter(file => {
            try {
                const stats = fs.statSync(file);
                return stats.size >= minSize && stats.size <= maxSize;
            } catch (error) {
                return false;
            }
        });
    }

    /**
     * ترتيب الملفات حسب الحجم
     * Sort files by size
     */
    public static sortFilesBySize(files: string[], ascending: boolean = true): string[] {
        return files.sort((a, b) => {
            try {
                const sizeA = fs.statSync(a).size;
                const sizeB = fs.statSync(b).size;
                return ascending ? sizeA - sizeB : sizeB - sizeA;
            } catch (error) {
                return 0;
            }
        });
    }

    /**
     * ترتيب الملفات حسب تاريخ التعديل
     * Sort files by modification date
     */
    public static sortFilesByModificationDate(files: string[], ascending: boolean = true): string[] {
        return files.sort((a, b) => {
            try {
                const timeA = fs.statSync(a).mtime.getTime();
                const timeB = fs.statSync(b).mtime.getTime();
                return ascending ? timeA - timeB : timeB - timeA;
            } catch (error) {
                return 0;
            }
        });
    }
}
