/**
 * أنواع اختبارات متحكم YouTube
 * YouTube controller test types
 * 
 * هذا الملف يحتوي على تعريفات الأنواع المستخدمة في اختبارات متحكم YouTube
 * This file contains type definitions used in YouTube controller tests
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { YouTubeController } from '@presentation/controllers/youtube-controller';
import { ResourceManager } from '@shared/utils/resource-manager';
import { SecurityLayer } from '@infrastructure/security/security-layer';

/**
 * واجهة إعداد اختبار متحكم YouTube
 * YouTube controller test setup interface
 */
export interface YouTubeControllerTestSetup {
    readonly youtubeController: YouTubeController;
    readonly mockResourceManager: ResourceManager;
    readonly mockSecurityLayer: jest.Mocked<SecurityLayer>;
}

/**
 * واجهة بيانات اختبار متحكم YouTube
 * YouTube controller test data interface
 */
export interface YouTubeControllerTestData {
    readonly validUrl: string;
    readonly invalidUrl: string;
    readonly testVideoId: string;
    readonly testQuality: string;
    readonly testSettings: Record<string, any>;
}

/**
 * واجهة نتائج اختبار متحكم YouTube
 * YouTube controller test results interface
 */
export interface YouTubeControllerTestResult {
    readonly success: boolean;
    readonly message: string;
    readonly data?: any;
    readonly error?: Error;
}

/**
 * واجهة توقعات اختبار متحكم YouTube
 * YouTube controller test expectations interface
 */
export interface YouTubeControllerTestExpectations {
    readonly shouldInitialize: boolean;
    readonly shouldLoadVideo: boolean;
    readonly shouldApplySettings: boolean;
    readonly shouldHandleErrors: boolean;
    readonly shouldCleanupResources: boolean;
}

/**
 * واجهة سيناريو اختبار متحكم YouTube
 * YouTube controller test scenario interface
 */
export interface YouTubeControllerTestScenario {
    readonly name: string;
    readonly description: string;
    readonly setup: Partial<YouTubeControllerTestSetup>;
    readonly input: YouTubeControllerTestData;
    readonly expectations: YouTubeControllerTestExpectations;
    readonly expectedResult: YouTubeControllerTestResult;
}

/**
 * واجهة مجموعة اختبارات متحكم YouTube
 * YouTube controller test suite interface
 */
export interface YouTubeControllerTestSuite {
    readonly name: string;
    readonly description: string;
    readonly scenarios: YouTubeControllerTestScenario[];
    readonly setup?: () => Promise<void>;
    readonly teardown?: () => Promise<void>;
}

/**
 * واجهة إحصائيات اختبار متحكم YouTube
 * YouTube controller test statistics interface
 */
export interface YouTubeControllerTestStatistics {
    readonly totalTests: number;
    readonly passedTests: number;
    readonly failedTests: number;
    readonly skippedTests: number;
    readonly executionTime: number;
    readonly coverage: number;
}

/**
 * واجهة تقرير اختبار متحكم YouTube
 * YouTube controller test report interface
 */
export interface YouTubeControllerTestReport {
    readonly suites: YouTubeControllerTestSuite[];
    readonly statistics: YouTubeControllerTestStatistics;
    readonly timestamp: Date;
    readonly environment: string;
    readonly version: string;
}

/**
 * نوع دالة إعداد اختبار متحكم YouTube
 * YouTube controller test setup function type
 */
export type YouTubeControllerTestSetupFunction = () => Promise<YouTubeControllerTestSetup>;

/**
 * نوع دالة تنظيف اختبار متحكم YouTube
 * YouTube controller test cleanup function type
 */
export type YouTubeControllerTestCleanupFunction = (setup: YouTubeControllerTestSetup) => Promise<void>;

/**
 * نوع دالة تحقق اختبار متحكم YouTube
 * YouTube controller test assertion function type
 */
export type YouTubeControllerTestAssertionFunction = (
    result: YouTubeControllerTestResult,
    expected: YouTubeControllerTestResult
) => boolean;

/**
 * نوع دالة محاكاة اختبار متحكم YouTube
 * YouTube controller test mock function type
 */
export type YouTubeControllerTestMockFunction = (setup: YouTubeControllerTestSetup) => void;

/**
 * ثوابت اختبار متحكم YouTube
 * YouTube controller test constants
 */
export const YOUTUBE_CONTROLLER_TEST_CONSTANTS = {
    /** مهلة الاختبار الافتراضية / Default test timeout */
    DEFAULT_TIMEOUT: 5000,
    
    /** عدد المحاولات الافتراضي / Default retry count */
    DEFAULT_RETRY_COUNT: 3,
    
    /** تأخير الاختبار الافتراضي / Default test delay */
    DEFAULT_DELAY: 100,
    
    /** حد الذاكرة الافتراضي / Default memory limit */
    DEFAULT_MEMORY_LIMIT: 100 * 1024 * 1024, // 100MB
    
    /** حد وقت التنفيذ الافتراضي / Default execution time limit */
    DEFAULT_EXECUTION_TIME_LIMIT: 10000, // 10 seconds
} as const;

/**
 * رسائل اختبار متحكم YouTube
 * YouTube controller test messages
 */
export const YOUTUBE_CONTROLLER_TEST_MESSAGES = {
    /** رسائل النجاح / Success messages */
    SUCCESS: {
        INITIALIZATION: 'تم تهيئة متحكم YouTube بنجاح',
        VIDEO_LOAD: 'تم تحميل الفيديو بنجاح',
        SETTINGS_APPLIED: 'تم تطبيق الإعدادات بنجاح',
        CLEANUP: 'تم تنظيف الموارد بنجاح'
    },
    
    /** رسائل الخطأ / Error messages */
    ERROR: {
        INITIALIZATION_FAILED: 'فشل في تهيئة متحكم YouTube',
        VIDEO_LOAD_FAILED: 'فشل في تحميل الفيديو',
        SETTINGS_APPLY_FAILED: 'فشل في تطبيق الإعدادات',
        CLEANUP_FAILED: 'فشل في تنظيف الموارد'
    },
    
    /** رسائل التحذير / Warning messages */
    WARNING: {
        SLOW_PERFORMANCE: 'أداء بطيء في متحكم YouTube',
        MEMORY_USAGE_HIGH: 'استخدام ذاكرة عالي',
        TIMEOUT_APPROACHING: 'اقتراب انتهاء المهلة الزمنية'
    }
} as const;

/**
 * بيانات اختبار متحكم YouTube الافتراضية
 * Default YouTube controller test data
 */
export const DEFAULT_YOUTUBE_CONTROLLER_TEST_DATA: YouTubeControllerTestData = {
    validUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    invalidUrl: 'invalid-url',
    testVideoId: 'dQw4w9WgXcQ',
    testQuality: '720p',
    testSettings: {
        autoplay: false,
        volume: 0.5,
        quality: '720p',
        darkMode: true
    }
};

/**
 * توقعات اختبار متحكم YouTube الافتراضية
 * Default YouTube controller test expectations
 */
export const DEFAULT_YOUTUBE_CONTROLLER_TEST_EXPECTATIONS: YouTubeControllerTestExpectations = {
    shouldInitialize: true,
    shouldLoadVideo: true,
    shouldApplySettings: true,
    shouldHandleErrors: true,
    shouldCleanupResources: true
};
