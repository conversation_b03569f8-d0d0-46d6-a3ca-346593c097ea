/**
 * أنماط CSS للمشغل والتحسينات
 * Player and enhancement CSS styles
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * أنماط CSS للتحكم في المشغل
 * Player control CSS styles
 */
export const DARK_MODE_PLAYER_STYLES = `
    /* شريط التحكم / Control bar */
    .ytp-chrome-bottom {
        background: linear-gradient(to top, rgba(0,0,0,0.8), transparent) !important;
    }

    /* أزرار التحكم / Control buttons */
    .ytp-play-button,
    .ytp-pause-button,
    .ytp-mute-button,
    .ytp-volume-slider,
    .ytp-time-display,
    .ytp-fullscreen-button {
        color: #ffffff !important;
    }

    /* شريط الصوت / Volume bar */
    .ytp-volume-slider-handle {
        background-color: #ffffff !important;
    }

    /* إعدادات الجودة / Quality settings */
    .ytp-settings-menu {
        background-color: #212121 !important;
        color: #ffffff !important;
    }

    .ytp-menuitem {
        color: #ffffff !important;
    }

    .ytp-menuitem:hover {
        background-color: #3f3f3f !important;
    }

    /* شاشة التحميل / Loading screen */
    .ytp-spinner {
        border-color: #ffffff transparent transparent transparent !important;
    }

    /* رسائل الخطأ / Error messages */
    .ytp-error {
        background-color: #212121 !important;
        color: #ffffff !important;
    }

    /* التحكم في السرعة / Speed control */
    .ytp-speed-button {
        color: #ffffff !important;
    }

    /* التسميات التوضيحية / Captions */
    .ytp-caption-window-container {
        color: #ffffff !important;
        background-color: rgba(0, 0, 0, 0.8) !important;
    }

    /* أزرار الفصل التالي/السابق / Next/Previous chapter buttons */
    .ytp-next-button,
    .ytp-prev-button {
        color: #ffffff !important;
    }

    /* معلومات الفيديو في المشغل / Video info in player */
    .ytp-title {
        color: #ffffff !important;
    }

    .ytp-title-link {
        color: #3ea6ff !important;
    }

    /* أزرار المشاركة / Share buttons */
    .ytp-share-button {
        color: #ffffff !important;
    }

    /* قائمة الفيديوهات التالية / Up next list */
    .ytp-upnext {
        background-color: #212121 !important;
    }

    .ytp-upnext-title {
        color: #ffffff !important;
    }

    .ytp-upnext-author {
        color: #aaaaaa !important;
    }
`;

/**
 * أنماط CSS للتحسينات الإضافية
 * Additional enhancement CSS styles
 */
export const DARK_MODE_ENHANCEMENT_STYLES = `
    /* تحسين التباين / Contrast enhancement */
    .high-contrast {
        filter: contrast(1.2) !important;
    }

    /* تقليل الضوء الأزرق / Blue light reduction */
    .blue-light-filter {
        filter: sepia(0.1) hue-rotate(180deg) !important;
    }

    /* تحسين القراءة / Reading enhancement */
    .reading-mode {
        font-size: 1.1em !important;
        line-height: 1.6 !important;
    }

    /* وضع التركيز / Focus mode */
    .focus-mode #secondary,
    .focus-mode #related {
        display: none !important;
    }

    /* تحسين الأداء / Performance optimization */
    .performance-mode * {
        animation: none !important;
        transition: none !important;
    }

    /* وضع توفير البطارية / Battery saving mode */
    .battery-saving {
        filter: brightness(0.8) !important;
    }

    /* تحسين إمكانية الوصول / Accessibility enhancement */
    .accessibility-mode {
        font-weight: bold !important;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;
    }

    /* وضع عدم الإزعاج / Do not disturb mode */
    .dnd-mode .ytd-notification-renderer {
        display: none !important;
    }

    /* تحسين الألوان للمكفوفين / Color blind enhancement */
    .color-blind-friendly {
        filter: saturate(1.5) !important;
    }

    /* وضع الليل المتقدم / Advanced night mode */
    .advanced-night {
        filter: invert(1) hue-rotate(180deg) !important;
    }

    .advanced-night img,
    .advanced-night video {
        filter: invert(1) hue-rotate(180deg) !important;
    }
`;
