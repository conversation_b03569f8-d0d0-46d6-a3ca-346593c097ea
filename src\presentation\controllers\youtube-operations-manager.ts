/**
 * مدير عمليات YouTube
 * YouTube operations manager
 * 
 * هذا الملف يحتوي على منطق إدارة عمليات YouTube
 * This file contains YouTube operations management logic
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ResourceManager } from '@shared/utils/resource-manager';
import { 
    OperationInfo,
    OperationsManager,
    YOUTUBE_CONTROLLER_CONSTANTS
} from './youtube-controller-config';
import { YouTubeOperationsTracker } from './youtube-operations-tracker';
import { YouTubeOperationsExecutor } from './youtube-operations-executor';

/**
 * فئة مدير عمليات YouTube
 * YouTube operations manager class
 */
export class YouTubeOperationsManager implements OperationsManager {
    private readonly resourceManager: ResourceManager;
    private readonly tracker: YouTubeOperationsTracker;
    private readonly executor: YouTubeOperationsExecutor;

    /**
     * منشئ مدير العمليات
     * Operations manager constructor
     * 
     * @param resourceManager - مدير الموارد
     */
    constructor(resourceManager: ResourceManager) {
        this.resourceManager = resourceManager;
        this.tracker = new YouTubeOperationsTracker();
        this.executor = new YouTubeOperationsExecutor(resourceManager, this.tracker);
    }

    /**
     * بدء عملية جديدة
     * Start new operation
     * 
     * @param type - نوع العملية
     * @param priority - أولوية العملية
     * @returns string - معرف العملية
     */
    public startOperation(type: string, priority: number = YOUTUBE_CONTROLLER_CONSTANTS.OPERATION_PRIORITIES.MEDIUM): string {
        const operation = this.tracker.createOperation(type, priority);
        
        // إضافة العملية للطابور للتنفيذ
        this.executor.queueOperation(operation.id);

        return operation.id;
    }

    /**
     * إكمال عملية
     * Complete operation
     * 
     * @param operationId - معرف العملية
     * @param success - نجح أم لا
     * @param errorMessage - رسالة الخطأ (اختياري)
     */
    public completeOperation(operationId: string, success: boolean, errorMessage?: string): void {
        const status = success ? 'COMPLETED' : 'FAILED';
        const metadata = errorMessage ? { error: errorMessage } : undefined;
        
        this.tracker.updateOperation(operationId, status, 100, metadata);
    }

    /**
     * إعادة محاولة عملية
     * Retry operation
     * 
     * @param operationId - معرف العملية
     * @returns boolean - نجح في إعادة المحاولة أم لا
     */
    public retryOperation(operationId: string): boolean {
        const operation = this.tracker.getOperation(operationId);
        if (!operation || operation.status === 'RUNNING') {
            return false;
        }

        // إعادة تعيين حالة العملية
        this.tracker.updateOperation(operationId, 'RUNNING', 0);
        
        // إضافة للطابور مرة أخرى
        this.executor.queueOperation(operationId);
        
        return true;
    }

    /**
     * إلغاء عملية
     * Cancel operation
     * 
     * @param operationId - معرف العملية
     * @returns boolean - نجح في الإلغاء أم لا
     */
    public cancelOperation(operationId: string): boolean {
        return this.tracker.cancelOperation(operationId);
    }

    /**
     * الحصول على العمليات النشطة
     * Get active operations
     * 
     * @returns قائمة العمليات النشطة
     */
    public getActiveOperations(): OperationInfo[] {
        return this.tracker.getActiveOperations();
    }

    /**
     * الحصول على تاريخ العمليات
     * Get operation history
     * 
     * @returns قائمة العمليات المنتهية
     */
    public getOperationHistory(): OperationInfo[] {
        return this.tracker.getOperationHistory();
    }

    /**
     * الحصول على إحصائيات العمليات
     * Get operation statistics
     * 
     * @returns إحصائيات العمليات
     */
    public getOperationStatistics(): Record<string, number> {
        return this.tracker.getOperationStatistics();
    }

    /**
     * الحصول على العمليات حسب النوع
     * Get operations by type
     * 
     * @param type - نوع العملية
     * @returns قائمة العمليات من النوع المحدد
     */
    public getOperationsByType(type: string): OperationInfo[] {
        return this.tracker.getOperationsByType(type);
    }

    /**
     * فحص إذا كان هناك عمليات نشطة من نوع معين
     * Check if there are active operations of a specific type
     * 
     * @param type - نوع العملية
     * @returns true إذا كان هناك عمليات نشطة من هذا النوع
     */
    public hasActiveOperationsOfType(type: string): boolean {
        return this.tracker.hasActiveOperationsOfType(type);
    }

    /**
     * تنظيف جميع العمليات
     * Clear all operations
     */
    public clearAllOperations(): void {
        this.tracker.clearAllOperations();
        this.executor.clearQueue();
    }

    /**
     * الحصول على حجم طابور العمليات
     * Get operation queue size
     * 
     * @returns حجم الطابور
     */
    public getQueueSize(): number {
        return this.executor.getQueueSize();
    }

    /**
     * تنظيف الموارد
     * Cleanup resources
     */
    public cleanup(): void {
        this.clearAllOperations();
    }
}

// إعادة تصدير الوحدات للتوافق مع الكود الموجود
export { YouTubeOperationsTracker } from './youtube-operations-tracker';
export { YouTubeOperationsExecutor } from './youtube-operations-executor';
