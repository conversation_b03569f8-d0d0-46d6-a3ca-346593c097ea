/**
 * دوال التعديل الأساسية لخدمة الإعدادات
 * Basic setter functions for settings service
 *
 * هذا الملف يجمع جميع دوال التعديل من الملفات المتخصصة
 * This file aggregates all setter functions from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ApplicationConfig, SettingChangeInfo } from '@shared/types';
import { ResourceManager } from '@shared/utils/resource-manager';
import Store from 'electron-store';
import { SettingsManagerConfig } from './settings-config';
import { SettingsServiceBasicSettersAdvanced } from './settings-service-basic-setters-advanced';
import { SettingsServiceBasicSettersSimple } from './settings-service-basic-setters-simple';
import { SettingsValidator } from './settings-validator';

// إعادة تصدير الفئات المتخصصة / Re-export specialized classes
export { SettingsServiceBasicSettersAdvanced } from './settings-service-basic-setters-advanced';
export { SettingsServiceBasicSettersSimple } from './settings-service-basic-setters-simple';

/**
 * فئة دوال التعديل الأساسية لخدمة الإعدادات / Basic setter functions for settings service class
 */
export class SettingsServiceBasicSetters {
    private readonly simpleSetters: SettingsServiceBasicSettersSimple;
    private readonly advancedSetters: SettingsServiceBasicSettersAdvanced;

    /**
     * منشئ دوال التعديل الأساسية / Basic setters constructor
     */
    constructor(
        config: SettingsManagerConfig,
        resourceManager: ResourceManager,
        store: Store<ApplicationConfig>,
        validator: SettingsValidator
    ) {
        this.simpleSetters = new SettingsServiceBasicSettersSimple(config, resourceManager, store, validator);
        this.advancedSetters = new SettingsServiceBasicSettersAdvanced(config, resourceManager, store, validator);
    }

    // دوال التعديل البسيطة / Simple setter functions

    /**
     * تعيين إعداد محدد / Set specific setting
     */
    public async setSetting<K extends keyof ApplicationConfig>(
        key: K,
        value: ApplicationConfig[K]
    ): Promise<boolean> {
        return await this.simpleSetters.setSetting(key, value);
    }

    /**
     * حذف إعداد محدد / Delete specific setting
     */
    public deleteSetting<K extends keyof ApplicationConfig>(key: K): boolean {
        return this.simpleSetters.deleteSetting(key);
    }

    /**
     * إضافة مستمع للتغييرات / Add change listener
     */
    public addChangeListener(listener: (change: SettingChangeInfo) => void): void {
        this.simpleSetters.addChangeListener(listener);
    }

    /**
     * إزالة مستمع للتغييرات / Remove change listener
     */
    public removeChangeListener(listener: (change: SettingChangeInfo) => void): void {
        this.simpleSetters.removeChangeListener(listener);
    }

    // دوال التعديل المتقدمة / Advanced setter functions

    /**
     * إعادة تعيين إعداد إلى القيمة الافتراضية / Reset setting to default
     */
    public async resetSettingToDefault<K extends keyof ApplicationConfig>(key: K): Promise<boolean> {
        return await this.advancedSetters.resetSettingToDefault(key);
    }

    /**
     * تبديل قيمة إعداد منطقي / Toggle boolean setting
     */
    public async toggleBooleanSetting<K extends keyof ApplicationConfig>(key: K): Promise<boolean> {
        return await this.advancedSetters.toggleBooleanSetting(key);
    }

    /**
     * تنظيف الموارد / Cleanup resources
     */
    public cleanup(): void {
        this.simpleSetters.cleanup();
        this.advancedSetters.cleanup();
    }
}
