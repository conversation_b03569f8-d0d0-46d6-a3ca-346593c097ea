/**
 * مراقبة الوضع المظلم - أحداث أساسية
 * Dark mode monitoring - Core events
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import {
    DARK_MODE_MESSAGES,
    DarkModeConfig
} from './dark-mode-config';
import { DarkModeObserverOperations } from './dark-mode-observer-operations';

/**
 * فئة الأحداث الأساسية لمراقبة الوضع المظلم
 * Core events for dark mode monitoring
 */
export class DarkModeObserverMonitoringEventsCore {
    private config: DarkModeConfig;
    private operations: DarkModeObserverOperations;
    private isListening: boolean = false;

    constructor(config: DarkModeConfig) {
        this.config = config;
        this.operations = new DarkModeObserverOperations(config);
    }

    /**
     * إعداد مستمع تحميل المحتوى
     * Setup DOM content loaded listener
     */
    public setupDOMContentLoadedListener(): void {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                console.log(DARK_MODE_MESSAGES.DOM_READY);
                this.operations.applyDarkMode();
            });
        } else {
            // المحتوى محمل بالفعل
            console.log(DARK_MODE_MESSAGES.DOM_READY);
            this.operations.applyDarkMode();
        }
    }

    /**
     * إعداد مستمع تغيير الرؤية
     * Setup visibility change listener
     */
    public setupVisibilityChangeListener(): void {
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                console.log('Page became visible, reapplying dark mode');
                setTimeout(() => {
                    this.operations.applyDarkMode();
                }, 100);
            }
        });
    }

    /**
     * إعداد مستمع تغيير الحجم
     * Setup resize listener
     */
    public setupResizeListener(): void {
        let resizeTimeout: NodeJS.Timeout;

        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                console.log('Window resized, reapplying dark mode');
                this.operations.applyDarkMode();
            }, 250);
        });
    }

    /**
     * إعداد مستمعي التركيز
     * Setup focus listeners
     */
    public setupFocusListeners(): void {
        window.addEventListener('focus', () => {
            console.log('Window focused, reapplying dark mode');
            setTimeout(() => {
                this.operations.applyDarkMode();
            }, 100);
        });

        window.addEventListener('blur', () => {
            console.log('Window blurred');
        });
    }

    /**
     * إعداد مستمعي الأحداث المخصصة
     * Setup custom event listeners
     */
    public setupCustomEventListeners(): void {
        // مستمع لأحداث YouTube المخصصة
        window.addEventListener('yt-navigate-start', () => {
            console.log('YouTube navigation started');
        });

        window.addEventListener('yt-navigate-finish', () => {
            console.log('YouTube navigation finished, reapplying dark mode');
            setTimeout(() => {
                this.operations.applyDarkMode();
            }, 500);
        });

        // مستمع لأحداث تغيير الفيديو
        window.addEventListener('yt-page-data-updated', () => {
            console.log('YouTube page data updated, reapplying dark mode');
            setTimeout(() => {
                this.operations.applyDarkMode();
            }, 300);
        });
    }

    /**
     * إعداد مستمعي أحداث المشغل - تفويض للأدوات
     * Setup player event listeners - Delegate to utils
     */
    public setupPlayerEventListeners(): void {
        DarkModeObserverMonitoringEventsCoreUtils.setupPlayerEventListeners(this.operations);
    }



    /**
     * إعداد مستمعي أحداث الشبكة - تفويض للأدوات
     * Setup network event listeners - Delegate to utils
     */
    public setupNetworkEventListeners(): void {
        DarkModeObserverMonitoringEventsCoreUtils.setupNetworkEventListeners(this.operations);
    }

    /**
     * إعداد مستمعي أحداث التاريخ - تفويض للأدوات
     * Setup history event listeners - Delegate to utils
     */
    public setupHistoryEventListeners(): void {
        DarkModeObserverMonitoringEventsCoreUtils.setupHistoryEventListeners(this.operations);
    }

    /**
     * فحص حالة الاستماع
     * Check listening state
     */
    public getListeningState(): boolean {
        return this.isListening;
    }

    /**
     * تعيين حالة الاستماع
     * Set listening state
     */
    public setListeningState(state: boolean): void {
        this.isListening = state;
    }

    /**
     * تنظيف مستمعي الأحداث
     * Cleanup event listeners
     */
    public cleanup(): void {
        console.log('Cleaning up event listeners');
        this.isListening = false;
        // تنظيف إضافي حسب الحاجة
    }
}
