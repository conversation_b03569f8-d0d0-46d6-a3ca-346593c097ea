/**
 * ثوابت تكوين الإعدادات
 * Settings configuration constants
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQuality, WindowBounds } from '@shared/types';

/**
 * مفاتيح الإعدادات / Settings keys
 */
export const SETTINGS_KEYS = {
    // إعدادات النافذة / Window settings
    WINDOW_BOUNDS: 'windowBounds',
    WINDOW_STATE: 'windowState',
    WINDOW_POSITION: 'windowPosition',
    
    // إعدادات الفيديو / Video settings
    VIDEO_QUALITY: 'videoQuality',
    AUTO_PLAY: 'autoPlay',
    PLAYBACK_SPEED: 'playbackSpeed',
    VOLUME: 'volume',
    MUTED: 'muted',
    
    // إعدادات المظهر / Appearance settings
    DARK_MODE: 'darkMode',
    THEME: 'theme',
    LANGUAGE: 'language',
    FONT_SIZE: 'fontSize',
    
    // إعدادات الأمان / Security settings
    AD_BLOCKER: 'adBlocker',
    SECURITY_ENABLED: 'securityEnabled',
    PRIVACY_MODE: 'privacyMode',
    
    // إعدادات التطبيق / Application settings
    AUTO_UPDATE: 'autoUpdate',
    STARTUP_BEHAVIOR: 'startupBehavior',
    MINIMIZE_TO_TRAY: 'minimizeToTray',
    CLOSE_TO_TRAY: 'closeToTray'
} as const;

/**
 * رسائل الإعدادات / Settings messages
 */
export const SETTINGS_MESSAGES = {
    // رسائل النجاح / Success messages
    SETTINGS_LOADED: 'تم تحميل الإعدادات بنجاح',
    SETTINGS_SAVED: 'تم حفظ الإعدادات بنجاح',
    SETTING_UPDATED: 'تم تحديث الإعداد بنجاح',
    SETTING_DELETED: 'تم حذف الإعداد بنجاح',
    SETTINGS_RESET: 'تم إعادة تعيين الإعدادات بنجاح',
    BACKUP_CREATED: 'تم إنشاء نسخة احتياطية بنجاح',
    BACKUP_RESTORED: 'تم استعادة النسخة الاحتياطية بنجاح',
    
    // رسائل الخطأ / Error messages
    ERROR_LOAD_SETTINGS: 'خطأ في تحميل الإعدادات',
    ERROR_SAVE_SETTINGS: 'خطأ في حفظ الإعدادات',
    ERROR_SET_SETTING: 'خطأ في تعيين الإعداد',
    ERROR_DELETE_SETTING: 'خطأ في حذف الإعداد',
    ERROR_VALIDATE_SETTINGS: 'خطأ في التحقق من الإعدادات',
    ERROR_CREATE_BACKUP: 'خطأ في إنشاء النسخة الاحتياطية',
    ERROR_RESTORE_BACKUP: 'خطأ في استعادة النسخة الاحتياطية',
    
    // رسائل التحذير / Warning messages
    SETTING_NOT_FOUND: 'الإعداد غير موجود',
    INVALID_SETTING_VALUE: 'قيمة الإعداد غير صالحة',
    VALIDATION_ERROR: 'خطأ في التحقق',
    BACKUP_NOT_FOUND: 'النسخة الاحتياطية غير موجودة',
    SETTINGS_CORRUPTED: 'الإعدادات تالفة',
    
    // رسائل المعلومات / Info messages
    SETTINGS_MIGRATED: 'تم ترحيل الإعدادات',
    SETTINGS_OPTIMIZED: 'تم تحسين الإعدادات',
    AUTO_BACKUP_CREATED: 'تم إنشاء نسخة احتياطية تلقائية',
    SETTINGS_SYNCHRONIZED: 'تم مزامنة الإعدادات'
} as const;

/**
 * أنواع أخطاء الإعدادات / Settings error types
 */
export enum SettingsErrorType {
    LOAD_ERROR = 'LOAD_ERROR',
    SAVE_ERROR = 'SAVE_ERROR',
    VALIDATION_ERROR = 'VALIDATION_ERROR',
    BACKUP_ERROR = 'BACKUP_ERROR',
    RESTORE_ERROR = 'RESTORE_ERROR',
    MIGRATION_ERROR = 'MIGRATION_ERROR',
    CORRUPTION_ERROR = 'CORRUPTION_ERROR',
    PERMISSION_ERROR = 'PERMISSION_ERROR',
    NETWORK_ERROR = 'NETWORK_ERROR',
    UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * حالات الإعدادات / Settings states
 */
export enum SettingsState {
    LOADING = 'LOADING',
    LOADED = 'LOADED',
    SAVING = 'SAVING',
    SAVED = 'SAVED',
    ERROR = 'ERROR',
    VALIDATING = 'VALIDATING',
    MIGRATING = 'MIGRATING',
    BACKING_UP = 'BACKING_UP',
    RESTORING = 'RESTORING'
}

/**
 * أولويات الإعدادات / Settings priorities
 */
export enum SettingsPriority {
    LOW = 1,
    NORMAL = 2,
    HIGH = 3,
    CRITICAL = 4
}

/**
 * مستويات التسجيل / Log levels
 */
export enum LogLevel {
    DEBUG = 'DEBUG',
    INFO = 'INFO',
    WARN = 'WARN',
    ERROR = 'ERROR',
    FATAL = 'FATAL'
}

/**
 * أنماط النسخ الاحتياطي / Backup patterns
 */
export const BACKUP_PATTERNS = {
    DAILY: 'daily',
    WEEKLY: 'weekly',
    MONTHLY: 'monthly',
    ON_CHANGE: 'on_change',
    MANUAL: 'manual'
} as const;

/**
 * حدود الإعدادات / Settings limits
 */
export const SETTINGS_LIMITS = {
    MAX_BACKUP_COUNT: 10,
    MAX_SETTING_NAME_LENGTH: 100,
    MAX_SETTING_VALUE_SIZE: 1024 * 1024, // 1MB
    MAX_SETTINGS_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    MIN_BACKUP_INTERVAL: 60000, // 1 minute
    MAX_BACKUP_INTERVAL: 86400000, // 24 hours
    MAX_VALIDATION_ERRORS: 50,
    MAX_RETRY_ATTEMPTS: 3,
    SAVE_DEBOUNCE_TIME: 1000, // 1 second
    VALIDATION_TIMEOUT: 5000 // 5 seconds
} as const;

/**
 * مسارات الإعدادات / Settings paths
 */
export const SETTINGS_PATHS = {
    CONFIG_DIR: 'config',
    BACKUP_DIR: 'backups',
    TEMP_DIR: 'temp',
    LOGS_DIR: 'logs',
    CACHE_DIR: 'cache',
    SETTINGS_FILE: 'settings.json',
    BACKUP_FILE_PREFIX: 'backup_',
    TEMP_FILE_PREFIX: 'temp_',
    LOG_FILE: 'settings.log'
} as const;

/**
 * تنسيقات الملفات / File formats
 */
export const FILE_FORMATS = {
    JSON: 'json',
    YAML: 'yaml',
    XML: 'xml',
    INI: 'ini',
    TOML: 'toml'
} as const;

/**
 * أنماط التشفير / Encryption modes
 */
export const ENCRYPTION_MODES = {
    NONE: 'none',
    AES_256: 'aes-256',
    AES_128: 'aes-128',
    BLOWFISH: 'blowfish'
} as const;

/**
 * أنماط الضغط / Compression modes
 */
export const COMPRESSION_MODES = {
    NONE: 'none',
    GZIP: 'gzip',
    DEFLATE: 'deflate',
    BROTLI: 'brotli'
} as const;

/**
 * أنماط المزامنة / Sync modes
 */
export const SYNC_MODES = {
    NONE: 'none',
    LOCAL: 'local',
    CLOUD: 'cloud',
    PEER_TO_PEER: 'p2p'
} as const;

/**
 * حالات التحقق / Validation states
 */
export const VALIDATION_STATES = {
    PENDING: 'pending',
    VALIDATING: 'validating',
    VALID: 'valid',
    INVALID: 'invalid',
    ERROR: 'error'
} as const;

/**
 * أنواع الأحداث / Event types
 */
export const EVENT_TYPES = {
    SETTING_CHANGED: 'setting_changed',
    SETTINGS_LOADED: 'settings_loaded',
    SETTINGS_SAVED: 'settings_saved',
    BACKUP_CREATED: 'backup_created',
    BACKUP_RESTORED: 'backup_restored',
    VALIDATION_COMPLETED: 'validation_completed',
    ERROR_OCCURRED: 'error_occurred'
} as const;

/**
 * أولويات الأحداث / Event priorities
 */
export const EVENT_PRIORITIES = {
    LOW: 1,
    NORMAL: 2,
    HIGH: 3,
    CRITICAL: 4
} as const;
