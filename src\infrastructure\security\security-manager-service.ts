/**
 * خدمة مدير الأمان الرئيسية
 * Main security manager service
 * 
 * هذا الملف يحتوي على الخدمة الرئيسية لإدارة الأمان
 * This file contains the main security management service
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ValidationResult, ThreatLevel } from '@shared/types';
import { SecurityThreatManager } from './security-threat-manager';
import { 
    SecurityThreatType, 
    SecurityThreatReport, 
    SecurityStats,
    SecurityManagerConfig,
    DEFAULT_SECURITY_MANAGER_CONFIG,
    SECURITY_MESSAGES
} from './security-manager-config';

/**
 * فئة مدير الأمان الشامل
 * Comprehensive security manager class
 */
export class SecurityManager {
    private readonly threatManager: SecurityThreatManager;
    private readonly config: SecurityManagerConfig;
    private isEnabled: boolean = false;

    /**
     * منشئ مدير الأمان
     * Security manager constructor
     * 
     * @param config - تكوين مدير الأمان (اختياري)
     */
    constructor(config?: Partial<SecurityManagerConfig>) {
        this.config = { ...DEFAULT_SECURITY_MANAGER_CONFIG, ...config };
        this.threatManager = new SecurityThreatManager(this.config);
    }

    /**
     * تهيئة نظام الأمان
     * Initialize security system
     * 
     * @returns نتيجة التهيئة
     */
    public async initialize(): Promise<ValidationResult> {
        try {
            this.isEnabled = true;
            
            if (this.config.logSecurityEvents) {
                console.log(SECURITY_MESSAGES.SECURITY_INITIALIZED);
            }

            return {
                isValid: true,
                errors: [],
                message: SECURITY_MESSAGES.SECURITY_INITIALIZED
            };
        } catch (error) {
            return {
                isValid: false,
                errors: [error instanceof Error ? error.message : String(error)],
                message: 'فشل في تهيئة نظام الأمان / Failed to initialize security system'
            };
        }
    }

    /**
     * إيقاف نظام الأمان
     * Disable security system
     * 
     * @returns نتيجة الإيقاف
     */
    public async disable(): Promise<ValidationResult> {
        try {
            this.isEnabled = false;
            
            if (this.config.logSecurityEvents) {
                console.log(SECURITY_MESSAGES.SECURITY_DISABLED);
            }

            return {
                isValid: true,
                errors: [],
                message: SECURITY_MESSAGES.SECURITY_DISABLED
            };
        } catch (error) {
            return {
                isValid: false,
                errors: [error instanceof Error ? error.message : String(error)],
                message: 'فشل في إيقاف نظام الأمان / Failed to disable security system'
            };
        }
    }

    /**
     * التحقق من صحة النص
     * Validate text input
     * 
     * @param input - النص المراد التحقق منه
     * @param source - مصدر النص
     * @returns نتيجة التحقق
     */
    public validateInput(input: string, source: string = 'unknown'): ValidationResult {
        if (!this.isEnabled) {
            return { isValid: true, errors: [] };
        }

        try {
            const threats: SecurityThreatReport[] = [];

            // فحص تهديدات XSS
            const xssThreat = this.threatManager.analyzeXSSThreats(input, source);
            if (xssThreat) {
                threats.push(xssThreat);
            }

            // فحص تهديدات SQL Injection
            const sqlThreat = this.threatManager.analyzeSQLInjectionThreats(input, source);
            if (sqlThreat) {
                threats.push(sqlThreat);
            }

            // فحص تهديدات السكريبت
            const scriptThreat = this.threatManager.analyzeScriptThreats(input, source);
            if (scriptThreat) {
                threats.push(scriptThreat);
            }

            const blockedThreats = threats.filter(threat => threat.blocked);
            
            return {
                isValid: blockedThreats.length === 0,
                errors: blockedThreats.map(threat => ({
                    field: 'input',
                    message: threat.description,
                    code: threat.threatType
                })),
                message: blockedThreats.length > 0 ? SECURITY_MESSAGES.THREAT_BLOCKED : SECURITY_MESSAGES.THREAT_ALLOWED
            };
        } catch (error) {
            return {
                isValid: false,
                errors: [{
                    field: 'validation',
                    message: `خطأ في التحقق من الأمان: ${error}`,
                    code: 'SECURITY_VALIDATION_ERROR'
                }]
            };
        }
    }

    /**
     * التحقق من صحة الرابط
     * Validate URL
     * 
     * @param url - الرابط المراد التحقق منه
     * @param source - مصدر الرابط
     * @returns نتيجة التحقق
     */
    public validateURL(url: string, source: string = 'unknown'): ValidationResult {
        if (!this.isEnabled) {
            return { isValid: true, errors: [] };
        }

        try {
            const urlThreat = this.threatManager.analyzeURLThreats(url, source);
            
            if (urlThreat && urlThreat.blocked) {
                return {
                    isValid: false,
                    errors: [{
                        field: 'url',
                        message: urlThreat.description,
                        code: urlThreat.threatType
                    }],
                    message: SECURITY_MESSAGES.THREAT_BLOCKED
                };
            }

            return {
                isValid: true,
                errors: [],
                message: SECURITY_MESSAGES.THREAT_ALLOWED
            };
        } catch (error) {
            return {
                isValid: false,
                errors: [{
                    field: 'url',
                    message: `خطأ في التحقق من الرابط: ${error}`,
                    code: 'URL_VALIDATION_ERROR'
                }]
            };
        }
    }

    /**
     * تنظيف المحتوى من التهديدات
     * Sanitize content from threats
     * 
     * @param content - المحتوى المراد تنظيفه
     * @returns المحتوى المنظف
     */
    public sanitizeContent(content: string): string {
        if (!this.isEnabled) {
            return content;
        }

        try {
            let sanitized = content;

            // إزالة علامات script
            sanitized = sanitized.replace(/<script[^>]*>.*?<\/script>/gi, '');
            
            // إزالة معالجات الأحداث
            sanitized = sanitized.replace(/on\w+\s*=\s*["'][^"']*["']/gi, '');
            
            // إزالة javascript: URLs
            sanitized = sanitized.replace(/javascript:/gi, '');
            
            // إزالة iframe و object و embed
            sanitized = sanitized.replace(/<(iframe|object|embed)[^>]*>.*?<\/\1>/gi, '');
            
            // إزالة eval و Function
            sanitized = sanitized.replace(/eval\s*\(/gi, '');
            sanitized = sanitized.replace(/Function\s*\(/gi, '');

            return sanitized;
        } catch (error) {
            console.warn('خطأ في تنظيف المحتوى:', error);
            return content;
        }
    }

    /**
     * الحصول على تقارير التهديدات
     * Get threat reports
     * 
     * @returns قائمة تقارير التهديدات
     */
    public getThreatReports(): readonly SecurityThreatReport[] {
        return this.threatManager.getThreatReports();
    }

    /**
     * الحصول على الإحصائيات
     * Get statistics
     * 
     * @returns إحصائيات الأمان
     */
    public getStats(): SecurityStats {
        return this.threatManager.getStats();
    }

    /**
     * الحصول على حالة التفعيل
     * Get enabled status
     * 
     * @returns true إذا كان مفعل
     */
    public isActive(): boolean {
        return this.isEnabled;
    }

    /**
     * تسجيل تهديد مخصص
     * Log custom threat
     * 
     * @param threatType - نوع التهديد
     * @param threatLevel - مستوى التهديد
     * @param description - وصف التهديد
     * @param source - مصدر التهديد
     * @param blocked - هل تم حظره
     */
    public logThreat(
        threatType: SecurityThreatType,
        threatLevel: ThreatLevel,
        description: string,
        source: string,
        blocked: boolean = false
    ): void {
        const report: SecurityThreatReport = {
            threatType,
            threatLevel,
            description,
            timestamp: new Date(),
            source,
            blocked
        };

        this.threatManager.addThreatReport(report);
    }
}
