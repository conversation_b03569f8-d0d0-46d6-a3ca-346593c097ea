/**
 * دوال القراءة البسيطة لخدمة الإعدادات
 * Simple getter functions for settings service
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DEFAULT_APPLICATION_CONFIG } from '@shared/constants';
import { ApplicationConfig } from '@shared/types';
import { ResourceManager } from '@shared/utils/resource-manager';
import Store from 'electron-store';
import { SETTINGS_MESSAGES, SettingsManagerConfig } from './settings-config';
import { SettingsValidator } from './settings-validator';

/**
 * فئة دوال القراءة البسيطة لخدمة الإعدادات
 */
export class SettingsServiceBasicGettersSimple {
    private readonly store: Store<ApplicationConfig>;
    private readonly config: SettingsManagerConfig;
    private readonly validator: SettingsValidator;
    private readonly resourceManager: ResourceManager;

    constructor(
        config: SettingsManagerConfig,
        resourceManager: ResourceManager,
        store: Store<ApplicationConfig>,
        validator: SettingsValidator
    ) {
        this.config = config;
        this.resourceManager = resourceManager;
        this.store = store;
        this.validator = validator;
    }

    /**
     * الحصول على جميع الإعدادات / Get all settings
     */
    public getAllSettings(): ApplicationConfig {
        try {
            const settings = this.store.store;
            const mergedSettings = { ...DEFAULT_APPLICATION_CONFIG, ...settings };
            
            const validation = this.validator.validateSettings(mergedSettings);
            if (!validation.isValid) {
                console.warn(SETTINGS_MESSAGES.VALIDATION_WARNING, validation.errors);
                return this.getDefaultSettings();
            }

            return mergedSettings;
        } catch (error) {
            console.error(SETTINGS_MESSAGES.ERROR_GET_SETTINGS, error);
            return this.getDefaultSettings();
        }
    }

    /**
     * الحصول على إعداد محدد / Get specific setting
     */
    public getSetting<K extends keyof ApplicationConfig>(key: K): ApplicationConfig[K] {
        try {
            const value = this.store.get(key);
            if (value !== undefined) {
                return value;
            }
            return DEFAULT_APPLICATION_CONFIG[key];
        } catch (error) {
            console.error(`${SETTINGS_MESSAGES.ERROR_GET_SETTING}: ${String(key)}`, error);
            return DEFAULT_APPLICATION_CONFIG[key];
        }
    }

    /**
     * الحصول على الإعدادات الافتراضية / Get default settings
     */
    public getDefaultSettings(): ApplicationConfig {
        return { ...DEFAULT_APPLICATION_CONFIG };
    }

    /**
     * التحقق من وجود إعداد / Check if setting exists
     */
    public hasSetting<K extends keyof ApplicationConfig>(key: K): boolean {
        return this.store.has(key);
    }

    /**
     * الحصول على حجم الإعدادات / Get settings size
     */
    public getSettingsSize(): number {
        return this.store.size;
    }

    /**
     * الحصول على مسار ملف الإعدادات / Get settings file path
     */
    public getSettingsPath(): string {
        return this.store.path;
    }

    /**
     * الحصول على قائمة بجميع مفاتيح الإعدادات / Get list of all setting keys
     */
    public getSettingKeys(): Array<keyof ApplicationConfig> {
        try {
            const settings = this.getAllSettings();
            return Object.keys(settings) as Array<keyof ApplicationConfig>;
        } catch (error) {
            console.error('خطأ في الحصول على مفاتيح الإعدادات:', error);
            return [];
        }
    }

    /**
     * التحقق من صحة إعداد محدد / Validate specific setting
     */
    public isValidSetting<K extends keyof ApplicationConfig>(
        key: K, 
        value: ApplicationConfig[K]
    ): boolean {
        try {
            const validation = this.validator.validateSingleSetting(key, value);
            return validation.isValid;
        } catch (error) {
            console.error(`خطأ في التحقق من صحة الإعداد ${String(key)}:`, error);
            return false;
        }
    }

    /**
     * تنظيف الموارد / Cleanup resources
     */
    public cleanup(): void {
        this.resourceManager.cleanup();
    }
}
