/**
 * أنماط CSS للوضع المظلم
 * Dark mode CSS styles
 *
 * هذا الملف يحتوي على أنماط CSS المستخدمة في الوضع المظلم
 * This file contains CSS styles used in dark mode
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// إعادة تصدير المكونات المتخصصة
// Re-export specialized components
export * from './dark-mode-css-effects';


/**
 * قوالب أنماط CSS للوضع المظلم
 * Dark mode CSS style templates
 */
export const DARK_MODE_STYLE_TEMPLATES = {
    // القالب الأساسي
    // Base template
    BASE: `
        /* الخلفية الرئيسية */
        html, body {
            background-color: {{backgroundColor}} !important;
            color: {{textColor}} !important;
        }
        
        /* محتوى الصفحة */
        #content, #primary, #secondary {
            background-color: {{backgroundColor}} !important;
        }
        
        /* عناصر YouTube */
        ytd-app, ytd-page-manager, ytd-browse, ytd-watch-flexy {
            background-color: {{backgroundColor}} !important;
            color: {{textColor}} !important;
        }
    `,

    // قالب النصوص
    // Text template
    TEXT: `
        /* النصوص */
        h1, h2, h3, h4, h5, h6, p, span, a {
            color: {{textColor}} !important;
        }
        
        /* الروابط */
        a {
            color: {{linkColor}} !important;
        }
        
        /* العناوين */
        #video-title, .title {
            color: {{textColor}} !important;
        }
        
        /* الوصف */
        #description-text, .description {
            color: {{textSecondaryColor}} !important;
        }
    `,

    // قالب الأزرار
    // Button template
    BUTTONS: `
        /* الأزرار */
        button, .ytp-button, .ytd-button-renderer {
            background-color: {{buttonColor}} !important;
            color: {{textColor}} !important;
            border: 1px solid {{borderColor}} !important;
        }
        
        /* أزرار التفاعل */
        #subscribe-button, #notification-preference-button {
            background-color: {{accentColor}} !important;
            color: {{textColor}} !important;
        }
        
        /* أزرار المشغل */
        .ytp-button {
            filter: invert(1) !important;
        }
    `,

    // قالب الإدخال
    // Input template
    INPUTS: `
        /* حقول الإدخال */
        input, textarea, select {
            background-color: {{inputColor}} !important;
            color: {{textColor}} !important;
            border: 1px solid {{borderColor}} !important;
        }
        
        /* مربع البحث */
        .ytd-searchbox, #search-input {
            background-color: {{inputColor}} !important;
            color: {{textColor}} !important;
        }
    `,

    // قالب التنقل
    // Navigation template
    NAVIGATION: `
        /* الهيدر */
        #masthead, .ytd-masthead {
            background-color: {{headerColor}} !important;
        }
        
        /* الشريط الجانبي */
        #guide, .ytd-guide-renderer, .ytd-mini-guide-renderer {
            background-color: {{sidebarColor}} !important;
        }
        
        /* عناصر التنقل */
        #header, #sidebar {
            background-color: {{headerColor}} !important;
        }
    `,

    // قالب الانتقالات
    // Transition template
    TRANSITIONS: `
        /* انتقالات سلسة */
        * {
            transition: background-color {{transitionDuration}}ms ease, 
                       color {{transitionDuration}}ms ease, 
                       border-color {{transitionDuration}}ms ease !important;
        }
    `,

    // قالب الأداء العالي
    // High performance template
    HIGH_PERFORMANCE: `
        /* تحسينات الأداء */
        * {
            will-change: auto !important;
            transform: translateZ(0) !important;
            backface-visibility: hidden !important;
        }
        
        /* تقليل الانتقالات */
        *, *::before, *::after {
            transition: none !important;
            animation: none !important;
        }
    `,

    // قالب إمكانية الوصول
    // Accessibility template
    ACCESSIBILITY: `
        /* تحسين إمكانية الوصول */
        :focus {
            outline: 2px solid {{accentColor}} !important;
            outline-offset: 2px !important;
        }
        
        /* تباين عالي */
        @media (prefers-contrast: high) {
            * {
                border-width: 2px !important;
                font-weight: bold !important;
            }
        }
        
        /* تقليل الحركة */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                transition: none !important;
                animation: none !important;
            }
        }
    `
} as const;
