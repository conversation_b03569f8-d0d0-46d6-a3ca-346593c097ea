/**
 * معالجة عناصر النماذج في الوضع المظلم
 * Form element processing for dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// استيراد الفئات المتخصصة / Import specialized classes
import { DarkModeObserverOperationsAdvancedElementsGenericFormsCore } from './dark-mode-observer-operations-advanced-elements-generic-forms-core';
import { DarkModeObserverOperationsAdvancedElementsGenericFormsButtons } from './dark-mode-observer-operations-advanced-elements-generic-forms-buttons';
import { DarkModeConfig } from './dark-mode-config';

/**
 * فئة معالجة عناصر النماذج
 * Form element processing class
 */
export class DarkModeObserverOperationsAdvancedElementsGenericForms {

    /** معالجة عنصر النموذج / Process form element */
    public static processFormElement(element: HTMLFormElement, config: DarkModeConfig): void {
        try {
            // المعالجة الأساسية
            DarkModeObserverOperationsAdvancedElementsGenericFormsCore.processFormElement(element, config);
            
            // معالجة عناصر الإدخال
            DarkModeObserverOperationsAdvancedElementsGenericFormsCore.processFormInputs(element, config);
            
            // معالجة الأزرار
            DarkModeObserverOperationsAdvancedElementsGenericFormsButtons.processFormButtons(element, config);
            
            // معالجة التسميات
            DarkModeObserverOperationsAdvancedElementsGenericFormsCore.processFormLabels(element, config);
            
        } catch (error) {
            console.error('خطأ في معالجة عنصر النموذج:', error);
        }
    }

    /** تطبيق أنماط النموذج / Apply form styles */
    public static applyFormStyles(element: HTMLFormElement): void {
        DarkModeObserverOperationsAdvancedElementsGenericFormsCore.applyFormStyles(element);
    }

    /** معالجة عناصر الإدخال / Process form inputs */
    public static processFormInputs(element: HTMLFormElement, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsGenericFormsCore.processFormInputs(element, config);
    }

    /** تنسيق عنصر الإدخال / Style form input */
    public static styleFormInput(input: HTMLInputElement, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsGenericFormsCore.styleFormInput(input, config);
    }

    /** معالجة أزرار النموذج / Process form buttons */
    public static processFormButtons(element: HTMLFormElement, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsGenericFormsButtons.processFormButtons(element, config);
    }

    /** تنسيق زر النموذج / Style form button */
    public static styleFormButton(button: HTMLButtonElement): void {
        DarkModeObserverOperationsAdvancedElementsGenericFormsButtons.styleFormButton(button);
    }

    /** معالجة تسميات النموذج / Process form labels */
    public static processFormLabels(element: HTMLFormElement, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsGenericFormsCore.processFormLabels(element, config);
    }

    /** معالجة رسائل الخطأ / Process error messages */
    public static processErrorMessages(form: HTMLFormElement): void {
        DarkModeObserverOperationsAdvancedElementsGenericFormsCore.processErrorMessages(form);
    }

    /** معالجة رسائل النجاح / Process success messages */
    public static processSuccessMessages(form: HTMLFormElement): void {
        DarkModeObserverOperationsAdvancedElementsGenericFormsCore.processSuccessMessages(form);
    }

    /** إضافة تأثيرات تحميل للأزرار / Add loading effects to buttons */
    public static addLoadingEffect(button: HTMLButtonElement): () => void {
        return DarkModeObserverOperationsAdvancedElementsGenericFormsButtons.addLoadingEffect(button);
    }

    /** إضافة تأثيرات نجاح للأزرار / Add success effects to buttons */
    public static addSuccessEffect(button: HTMLButtonElement, duration?: number): void {
        DarkModeObserverOperationsAdvancedElementsGenericFormsButtons.addSuccessEffect(button, duration);
    }

    /** إضافة تأثيرات خطأ للأزرار / Add error effects to buttons */
    public static addErrorEffect(button: HTMLButtonElement, duration?: number): void {
        DarkModeObserverOperationsAdvancedElementsGenericFormsButtons.addErrorEffect(button, duration);
    }
}
