/**
 * عمليات خدمة جودة الفيديو
 * Video quality service operations
 * 
 * هذا الملف يحتوي على العمليات الأساسية لخدمة جودة الفيديو
 * This file contains core operations for video quality service
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQuality, ValidationResult } from '@shared/types';
import { ResourceManager } from '@shared/utils/resource-manager';
import { 
    VideoQualityConfig,
    VIDEO_QUALITY_CONSTANTS,
    VIDEO_QUALITY_MESSAGES,
    VideoQualityState
} from './video-quality-config';
import { VideoQualityDetector } from './video-quality-detector';
import { VideoQualityApplicator } from './video-quality-applicator';

/**
 * فئة عمليات خدمة جودة الفيديو
 * Video quality service operations class
 */
export class VideoQualityServiceOperations {
    private readonly resourceManager: ResourceManager;
    private readonly config: VideoQualityConfig;
    private readonly detector: VideoQualityDetector;
    private readonly applicator: VideoQualityApplicator;
    
    private currentQuality: VideoQuality = 'auto';
    private isHidden: boolean = false;
    private state: VideoQualityState = VideoQualityState.IDLE;

    /**
     * منشئ عمليات خدمة جودة الفيديو
     * Video quality service operations constructor
     * 
     * @param resourceManager - مدير الموارد
     * @param config - تكوين جودة الفيديو
     * @param detector - كاشف جودة الفيديو
     * @param applicator - مطبق جودة الفيديو
     */
    constructor(
        resourceManager: ResourceManager,
        config: VideoQualityConfig,
        detector: VideoQualityDetector,
        applicator: VideoQualityApplicator
    ) {
        this.resourceManager = resourceManager;
        this.config = config;
        this.detector = detector;
        this.applicator = applicator;
    }

    /**
     * تطبيق جودة الفيديو
     * Apply video quality
     * 
     * @param quality - جودة الفيديو المطلوبة
     * @returns نتيجة التحقق من صحة العملية
     */
    public async applyQuality(quality: VideoQuality): Promise<ValidationResult> {
        try {
            // التحقق من صحة المدخلات
            // Validate inputs
            const validationResult = this.validateQualityInput(quality);
            if (!validationResult.isValid) {
                return validationResult;
            }

            // تحديث الحالة
            // Update state
            this.setState(VideoQualityState.APPLYING);
            this.currentQuality = quality;

            // تطبيق الجودة
            // Apply quality
            const applicationResult = await this.applicator.applyQuality(quality);
            
            if (applicationResult.isValid) {
                this.setState(VideoQualityState.APPLIED);
                return {
                    isValid: true,
                    errors: []
                };
            } else {
                this.setState(VideoQualityState.ERROR);
                return applicationResult;
            }

        } catch (error) {
            this.setState(VideoQualityState.ERROR);
            return {
                isValid: false,
                errors: [{
                    field: 'quality',
                    message: `Failed to apply quality: ${error instanceof Error ? error.message : 'Unknown error'}`,
                    code: 'QUALITY_APPLICATION_ERROR'
                }]
            };
        }
    }

    /**
     * كشف الجودة الحالية
     * Detect current quality
     * 
     * @returns الجودة المكتشفة أو null
     */
    public async detectCurrentQuality(): Promise<VideoQuality | null> {
        try {
            this.setState(VideoQualityState.DETECTING);
            const detectedQuality = await this.detector.detectCurrentQuality();
            this.setState(VideoQualityState.IDLE);
            return detectedQuality;
        } catch (error) {
            this.setState(VideoQualityState.ERROR);
            console.error('Failed to detect quality:', error);
            return null;
        }
    }

    /**
     * إخفاء عناصر التحكم في الجودة
     * Hide quality controls
     * 
     * @returns نتيجة التحقق من صحة العملية
     */
    public async hideQualityControls(): Promise<ValidationResult> {
        try {
            if (this.isHidden) {
                return {
                    isValid: true,
                    errors: []
                };
            }

            const hideResult = await this.applicator.hideQualityControls();
            if (hideResult.isValid) {
                this.isHidden = true;
            }
            
            return hideResult;
        } catch (error) {
            return {
                isValid: false,
                errors: [{
                    field: 'controls',
                    message: `Failed to hide controls: ${error instanceof Error ? error.message : 'Unknown error'}`,
                    code: 'HIDE_CONTROLS_ERROR'
                }]
            };
        }
    }

    /**
     * إظهار عناصر التحكم في الجودة
     * Show quality controls
     * 
     * @returns نتيجة التحقق من صحة العملية
     */
    public async showQualityControls(): Promise<ValidationResult> {
        try {
            if (!this.isHidden) {
                return {
                    isValid: true,
                    errors: []
                };
            }

            const showResult = await this.applicator.showQualityControls();
            if (showResult.isValid) {
                this.isHidden = false;
            }
            
            return showResult;
        } catch (error) {
            return {
                isValid: false,
                errors: [{
                    field: 'controls',
                    message: `Failed to show controls: ${error instanceof Error ? error.message : 'Unknown error'}`,
                    code: 'SHOW_CONTROLS_ERROR'
                }]
            };
        }
    }

    /**
     * التحقق من صحة مدخلات الجودة
     * Validate quality input
     * 
     * @param quality - جودة الفيديو
     * @returns نتيجة التحقق من الصحة
     */
    private validateQualityInput(quality: VideoQuality): ValidationResult {
        if (!quality) {
            return {
                isValid: false,
                errors: [{
                    field: 'quality',
                    message: VIDEO_QUALITY_MESSAGES.INVALID_QUALITY,
                    code: 'INVALID_QUALITY'
                }]
            };
        }

        const validQualities = Object.values(VIDEO_QUALITY_CONSTANTS.QUALITIES);
        if (!validQualities.includes(quality)) {
            return {
                isValid: false,
                errors: [{
                    field: 'quality',
                    message: `${VIDEO_QUALITY_MESSAGES.INVALID_QUALITY}: ${quality}`,
                    code: 'UNSUPPORTED_QUALITY'
                }]
            };
        }

        return {
            isValid: true,
            errors: []
        };
    }

    /**
     * تحديث حالة الخدمة
     * Update service state
     * 
     * @param newState - الحالة الجديدة
     */
    private setState(newState: VideoQualityState): void {
        this.state = newState;
        console.log(`Video quality service state changed to: ${newState}`);
    }

    /**
     * الحصول على الحالة الحالية
     * Get current state
     * 
     * @returns الحالة الحالية
     */
    public getState(): VideoQualityState {
        return this.state;
    }

    /**
     * الحصول على الجودة الحالية
     * Get current quality
     * 
     * @returns الجودة الحالية
     */
    public getCurrentQuality(): VideoQuality {
        return this.currentQuality;
    }

    /**
     * التحقق من حالة الإخفاء
     * Check if controls are hidden
     * 
     * @returns true إذا كانت العناصر مخفية
     */
    public areControlsHidden(): boolean {
        return this.isHidden;
    }

    /**
     * إعادة تعيين الحالة
     * Reset state
     */
    public resetState(): void {
        this.currentQuality = 'auto';
        this.isHidden = false;
        this.setState(VideoQualityState.IDLE);
    }

    /**
     * تنظيف الموارد
     * Cleanup resources
     */
    public cleanup(): void {
        this.resetState();
        // Additional cleanup if needed
    }
}
