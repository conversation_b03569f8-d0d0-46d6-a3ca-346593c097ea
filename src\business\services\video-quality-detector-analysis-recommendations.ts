/**
 * توصيات تحليل كشف جودة الفيديو
 * Video quality detection analysis recommendations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQuality } from '@shared/types';
import { VideoQualityErrorReport } from './video-quality-config';

/**
 * فئة توصيات تحليل الكشف
 * Detection analysis recommendations class
 */
export class VideoQualityDetectorAnalysisRecommendations {
    
    /** توليد التوصيات / Generate recommendations */
    public generateRecommendations(analysis: {
        currentQuality: VideoQuality | null;
        method: string;
        confidence: number;
        alternatives: VideoQuality[];
    }, errors: VideoQualityErrorReport[]): string[] {
        const recommendations: string[] = [];

        if (!analysis.currentQuality) {
            recommendations.push(...this.getNoQualityRecommendations());
        } else if (analysis.confidence < 50) {
            recommendations.push(...this.getLowConfidenceRecommendations());
        } else if (analysis.confidence < 80) {
            recommendations.push(...this.getMediumConfidenceRecommendations(analysis.alternatives));
        } else {
            recommendations.push(...this.getHighConfidenceRecommendations());
        }

        if (errors.length > 0) {
            recommendations.push(...this.getErrorRecommendations(errors));
        }

        return recommendations;
    }

    /** توصيات عدم وجود جودة / No quality recommendations */
    private getNoQualityRecommendations(): string[] {
        return [
            'لم يتم العثور على جودة الفيديو',
            'تحقق من تحميل الفيديو بالكامل',
            'جرب إعادة تحميل الصفحة',
            'تأكد من اتصال الإنترنت',
            'جرب فيديو آخر للتأكد من عمل النظام'
        ];
    }

    /** توصيات الثقة المنخفضة / Low confidence recommendations */
    private getLowConfidenceRecommendations(): string[] {
        return [
            'مستوى الثقة في الكشف منخفض',
            'قد تحتاج إلى التحقق يدوياً',
            'جرب إعادة تشغيل الفيديو',
            'تحقق من إعدادات المتصفح',
            'قد يكون هناك تداخل مع إضافات أخرى'
        ];
    }

    /** توصيات الثقة المتوسطة / Medium confidence recommendations */
    private getMediumConfidenceRecommendations(alternatives: VideoQuality[]): string[] {
        const recommendations = [
            'مستوى الثقة في الكشف متوسط',
            'النتيجة قد تكون صحيحة لكن تحتاج تأكيد'
        ];

        if (alternatives.length > 0) {
            recommendations.push(`جودات بديلة محتملة: ${alternatives.join(', ')}`);
            recommendations.push('قارن مع الجودة المعروضة في واجهة YouTube');
        }

        return recommendations;
    }

    /** توصيات الثقة العالية / High confidence recommendations */
    private getHighConfidenceRecommendations(): string[] {
        return [
            'تم كشف الجودة بثقة عالية',
            'النتيجة موثوقة ويمكن الاعتماد عليها'
        ];
    }

    /** توصيات الأخطاء / Error recommendations */
    private getErrorRecommendations(errors: VideoQualityErrorReport[]): string[] {
        const recommendations = [
            `تم تسجيل ${errors.length} خطأ أثناء الكشف`
        ];

        const errorTypes = new Set(errors.map(e => e.type));
        
        if (errorTypes.has('VIDEO_ELEMENT_ERROR')) {
            recommendations.push('مشكلة في الوصول لعنصر الفيديو - تحقق من تحميل الصفحة');
        }

        if (errorTypes.has('UI_DETECTION_ERROR')) {
            recommendations.push('مشكلة في واجهة المستخدم - قد تكون هناك تحديثات في YouTube');
        }

        if (errorTypes.has('METADATA_ERROR')) {
            recommendations.push('مشكلة في البيانات الوصفية - جرب إعادة تحميل الفيديو');
        }

        if (errorTypes.has('NETWORK_ERROR')) {
            recommendations.push('مشكلة في الشبكة - تحقق من اتصال الإنترنت');
        }

        if (errorTypes.has('ANALYSIS_ERROR')) {
            recommendations.push('خطأ في التحليل - قد تحتاج إلى إعادة تشغيل التطبيق');
        }

        return recommendations;
    }

    /** توصيات تحسين الأداء / Performance improvement recommendations */
    public getPerformanceRecommendations(detectionTime: number, successRate: number): string[] {
        const recommendations: string[] = [];

        if (detectionTime > 1000) {
            recommendations.push('وقت الكشف طويل - قد تحتاج إلى تحسين الأداء');
            recommendations.push('جرب إغلاق علامات تبويب أخرى');
            recommendations.push('تحقق من استخدام الذاكرة');
        }

        if (successRate < 70) {
            recommendations.push('معدل نجاح الكشف منخفض');
            recommendations.push('قد تحتاج إلى تحديث التطبيق');
            recommendations.push('تحقق من إعدادات مانع الإعلانات');
        }

        if (successRate > 90 && detectionTime < 500) {
            recommendations.push('أداء ممتاز في الكشف!');
            recommendations.push('النظام يعمل بكفاءة عالية');
        }

        return recommendations;
    }

    /** توصيات استكشاف الأخطاء / Troubleshooting recommendations */
    public getTroubleshootingRecommendations(): string[] {
        return [
            'خطوات استكشاف الأخطاء:',
            '1. إعادة تحميل الصفحة',
            '2. مسح ذاكرة التخزين المؤقت',
            '3. تعطيل الإضافات مؤقتاً',
            '4. تجربة متصفح آخر',
            '5. إعادة تشغيل التطبيق',
            '6. التحقق من تحديثات النظام'
        ];
    }

    /** توصيات التحسين / Optimization recommendations */
    public getOptimizationRecommendations(): string[] {
        return [
            'نصائح لتحسين الأداء:',
            '• استخدم أحدث إصدار من المتصفح',
            '• تأكد من وجود ذاكرة كافية',
            '• أغلق التطبيقات غير الضرورية',
            '• استخدم اتصال إنترنت مستقر',
            '• تجنب تشغيل عدة فيديوهات في نفس الوقت'
        ];
    }
}
