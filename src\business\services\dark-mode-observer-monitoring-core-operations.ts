/**
 * مراقبة الوضع المظلم - العمليات الأساسية المتخصصة
 * Dark mode monitoring - Specialized core operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import {
    DarkModeConfig
} from './dark-mode-config';
import { DarkModeObserverOperations } from './dark-mode-observer-operations';

/**
 * فئة العمليات الأساسية المتخصصة لمراقبة الوضع المظلم
 * Specialized core operations for dark mode monitoring
 */
export class DarkModeObserverMonitoringCoreOperations {
    private readonly config: DarkModeConfig;
    private readonly operations: DarkModeObserverOperations;

    /**
     * منشئ فئة العمليات الأساسية المتخصصة
     * Specialized core operations constructor
     */
    constructor(config: DarkModeConfig) {
        this.config = config;
        this.operations = new DarkModeObserverOperations(config);
    }

    /**
     * معالجة التغييرات
     * Handle mutations
     */
    public handleMutations(mutations: MutationRecord[]): void {
        let shouldApplyDarkMode = false;

        for (const mutation of mutations) {
            if (DarkModeObserverMonitoringCoreOperationsUtils.isMutationRelevant(mutation)) {
                shouldApplyDarkMode = true;
                break;
            }
        }

        if (shouldApplyDarkMode) {
            this.operations.applyDarkMode();
        }
    }

    /**
     * فحص ما إذا كان التغيير ذا صلة - تفويض للأدوات
     * Check if mutation is relevant - Delegate to utils
     */
    public isMutationRelevant(mutation: MutationRecord): boolean {
        return DarkModeObserverMonitoringCoreOperationsUtils.isMutationRelevant(mutation);
    }

    /**
     * فحص ما إذا كان العنصر ذا صلة - تفويض للأدوات
     * Check if element is relevant - Delegate to utils
     */
    public isElementRelevant(element: Element): boolean {
        return DarkModeObserverMonitoringCoreOperationsUtils.isElementRelevant(element);
    }

    /**
     * فحص ما إذا كان تغيير الخاصية ذا صلة - تفويض للأدوات
     * Check if attribute change is relevant - Delegate to utils
     */
    public isAttributeChangeRelevant(element: Element, attributeName: string | null): boolean {
        return DarkModeObserverMonitoringCoreOperationsUtils.isAttributeChangeRelevant(element, attributeName);
    }

    /**
     * تنفيذ الفحص الدوري - تفويض للأدوات
     * Perform periodic check - Delegate to utils
     */
    public performPeriodicCheck(): void {
        try {
            // فحص وجود عناصر جديدة تحتاج للوضع المظلم
            const newElements = DarkModeObserverMonitoringCoreOperationsUtils.findNewElements();
            if (newElements.length > 0) {
                this.operations.applyDarkMode();
            }

            // فحص حالة الوضع المظلم الحالية
            const state = DarkModeObserverMonitoringCoreOperationsUtils.validateCurrentDarkModeState(this.config);
            if (state.needsReapplication) {
                console.log('Dark mode expected but not applied, reapplying...');
                this.operations.applyDarkMode();
            } else if (state.needsRemoval) {
                console.log('Dark mode not expected but applied, removing...');
                this.operations.removeDarkMode();
            }
        } catch (error) {
            console.error('Error during periodic check:', error);
        }
    }

    /**
     * البحث عن عناصر جديدة - تفويض للأدوات
     * Find new elements - Delegate to utils
     */
    public findNewElements(): Element[] {
        return DarkModeObserverMonitoringCoreOperationsUtils.findNewElements();
    }

    /**
     * التحقق من حالة الوضع المظلم الحالية - تفويض للأدوات
     * Validate current dark mode state - Delegate to utils
     */
    public validateCurrentDarkModeState(): void {
        const state = DarkModeObserverMonitoringCoreOperationsUtils.validateCurrentDarkModeState(this.config);
        if (state.needsReapplication) {
            console.log('Dark mode expected but not applied, reapplying...');
            this.operations.applyDarkMode();
        } else if (state.needsRemoval) {
            console.log('Dark mode not expected but applied, removing...');
            this.operations.removeDarkMode();
        }
    }

    /**
     * إنشاء مراقب التغييرات مع الإعدادات المحسنة - تفويض للأدوات
     * Create mutation observer with optimized settings - Delegate to utils
     */
    public createOptimizedMutationObserver(callback: (mutations: MutationRecord[]) => void): MutationObserver {
        return DarkModeObserverMonitoringCoreOperationsUtils.createOptimizedMutationObserver(callback);
    }

    /**
     * الحصول على إعدادات المراقب المحسنة - تفويض للأدوات
     * Get optimized observer configuration - Delegate to utils
     */
    public getOptimizedObserverConfig(): MutationObserverInit {
        return DarkModeObserverMonitoringCoreOperationsUtils.getOptimizedObserverConfig();
    }

    /**
     * فحص صحة العنصر المستهدف - تفويض للأدوات
     * Validate target element - Delegate to utils
     */
    public validateTargetElement(element: Element): boolean {
        return DarkModeObserverMonitoringCoreOperationsUtils.validateTargetElement(element);
    }

    /**
     * تنظيف الموارد - تفويض للأدوات
     * Cleanup resources - Delegate to utils
     */
    public cleanup(): void {
        DarkModeObserverMonitoringCoreOperationsUtils.cleanup();
    }
}
