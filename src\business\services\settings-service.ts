/**
 * خدمة إعدادات التطبيق الرئيسية
 * Main application settings service
 *
 * هذا الملف يجمع جميع عمليات إدارة الإعدادات من الملفات المتخصصة
 * This file aggregates all settings management operations from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// استيراد العمليات الأساسية
// Import core operations
export { SettingsServiceCore } from './settings-service-core';

// استيراد العمليات المتقدمة
// Import advanced operations
export { SettingsServiceAdvanced } from './settings-service-advanced';

import { ApplicationConfig, ValidationResult } from '@shared/types';
import { ResourceManager } from '@shared/utils/resource-manager';
import Store from 'electron-store';
import { SettingsBackupManager } from './settings-backup';
import {
    DEFAULT_SETTINGS_MANAGER_CONFIG,
    ExportOptions,
    ImportOptions,
    SettingsManagerConfig,
    SettingsStatistics
} from './settings-config';
import { SettingsServiceAdvanced } from './settings-service-advanced';
import { SettingsServiceCore } from './settings-service-core';
import { SettingsValidator } from './settings-validator';

/**
 * فئة خدمة الإعدادات الرئيسية / Main settings service class
 */
export class SettingsService {
    private readonly coreService: SettingsServiceCore;
    private readonly advancedService: SettingsServiceAdvanced;

    /**
     * منشئ خدمة الإعدادات / Settings service constructor
     */
    constructor(
        config: SettingsManagerConfig = DEFAULT_SETTINGS_MANAGER_CONFIG,
        resourceManager: ResourceManager
    ) {
        const store = new Store<ApplicationConfig>({
            name: config.storeFileName,
            defaults: { ...DEFAULT_APPLICATION_CONFIG },
            ...(config.encryptionKey && { encryptionKey: config.encryptionKey })
        });

        const validator = new SettingsValidator();
        const backupManager = new SettingsBackupManager(config);

        this.coreService = new SettingsServiceCore(config, resourceManager, store, validator);
        this.advancedService = new SettingsServiceAdvanced(this.coreService, config, backupManager, resourceManager);

        // تحميل وتحقق من الإعدادات عند البدء
        if (config.validateOnLoad) {
            this.validateCurrentSettings();
        }

        // تفعيل مراقبة التغييرات
        this.advancedService.startSettingsMonitoring();
    }

    // العمليات الأساسية / Core operations

    /**
     * الحصول على جميع الإعدادات / Get all settings
     */
    public getAllSettings(): ApplicationConfig {
        return this.coreService.getAllSettings();
    }

    /**
     * الحصول على إعداد محدد / Get specific setting
     */
    public getSetting<K extends keyof ApplicationConfig>(key: K): ApplicationConfig[K] {
        return this.coreService.getSetting(key);
    }

    /**
     * تعيين إعداد محدد / Set specific setting
     */
    public async setSetting<K extends keyof ApplicationConfig>(
        key: K,
        value: ApplicationConfig[K]
    ): Promise<boolean> {
        return await this.coreService.setSetting(key, value);
    }

    /**
     * تعيين إعدادات متعددة / Set multiple settings
     */
    public async setMultipleSettings(settings: Partial<ApplicationConfig>): Promise<boolean> {
        return await this.coreService.setMultipleSettings(settings);
    }

    /**
     * حذف إعداد محدد / Delete specific setting
     */
    public deleteSetting<K extends keyof ApplicationConfig>(key: K): boolean {
        return this.coreService.deleteSetting(key);
    }

    /**
     * إعادة تعيين الإعدادات للقيم الافتراضية / Reset settings to defaults
     */
    public resetToDefaults(): boolean {
        return this.coreService.resetToDefaults();
    }

    /**
     * الحصول على الإعدادات الافتراضية / Get default settings
     */
    public getDefaultSettings(): ApplicationConfig {
        return this.coreService.getDefaultSettings();
    }

    /**
     * التحقق من وجود إعداد / Check if setting exists
     */
    public hasSetting<K extends keyof ApplicationConfig>(key: K): boolean {
        return this.coreService.hasSetting(key);
    }

    // العمليات المتقدمة / Advanced operations

    /**
     * تصدير الإعدادات / Export settings
     */
    public async exportSettings(filePath: string, options?: ExportOptions): Promise<boolean> {
        return await this.advancedService.exportSettings(filePath, options);
    }

    /**
     * استيراد الإعدادات / Import settings
     */
    public async importSettings(filePath: string, options?: ImportOptions): Promise<boolean> {
        return await this.advancedService.importSettings(filePath, options);
    }

    /**
     * إنشاء نسخة احتياطية تلقائية / Create automatic backup
     */
    public async createAutomaticBackup(): Promise<boolean> {
        return await this.advancedService.createAutomaticBackup();
    }

    /**
     * استعادة من نسخة احتياطية / Restore from backup
     */
    public async restoreFromBackup(backupPath: string): Promise<boolean> {
        return await this.advancedService.restoreFromBackup(backupPath);
    }

    /**
     * الحصول على إحصائيات الإعدادات / Get settings statistics
     */
    public async getSettingsStatistics(): Promise<SettingsStatistics> {
        return await this.advancedService.getSettingsStatistics();
    }

    /**
     * تحسين الإعدادات / Optimize settings
     */
    public async optimizeSettings(): Promise<boolean> {
        return await this.advancedService.optimizeSettings();
    }

    /**
     * مزامنة الإعدادات / Sync settings
     */
    public async syncSettings(): Promise<boolean> {
        return await this.advancedService.syncSettings();
    }

    /**
     * إعادة تحميل الإعدادات / Reload settings
     */
    public async reloadSettings(): Promise<boolean> {
        return await this.advancedService.reloadSettings();
    }

    // دوال التحقق والتفويض / Validation and delegation functions

    /**
     * التحقق من صحة الإعدادات الحالية / Validate current settings
     */
    public validateCurrentSettings(): ValidationResult {
        return this.coreService.validateCurrentSettings();
    }

    /**
     * إصلاح الإعدادات التالفة / Fix corrupted settings
     */
    public fixCorruptedSettings(): boolean {
        return this.coreService.fixCorruptedSettings();
    }

    /**
     * الحصول على حجم الإعدادات / Get settings size
     */
    public getSettingsSize(): number {
        return this.coreService.getSettingsSize();
    }

    /**
     * الحصول على مسار ملف الإعدادات / Get settings file path
     */
    public getSettingsPath(): string {
        return this.coreService.getSettingsPath();
    }

    /**
     * إضافة مستمع للتغييرات / Add change listener
     */
    public addChangeListener(listener: (change: any) => void): void {
        this.coreService.addChangeListener(listener);
    }

    /**
     * إزالة مستمع للتغييرات / Remove change listener
     */
    public removeChangeListener(listener: (change: any) => void): void {
        this.coreService.removeChangeListener(listener);
    }

    /**
     * تنظيف الموارد / Cleanup resources
     */
    public cleanup(): void {
        this.coreService.cleanup();
        this.advancedService.cleanup();
    }
}
