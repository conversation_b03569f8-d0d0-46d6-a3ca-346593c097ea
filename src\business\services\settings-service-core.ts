/**
 * العمليات الأساسية لخدمة الإعدادات
 * Core settings service operations
 *
 * هذا الملف يجمع جميع العمليات الأساسية من الملفات المتخصصة
 * This file aggregates all core operations from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// استيراد العمليات الأساسية البسيطة
// Import basic simple operations
export { SettingsServiceBasicOperations } from './settings-service-basic-operations';

// استيراد العمليات المعقدة
// Import complex operations
export { SettingsServiceComplexOperations } from './settings-service-complex-operations';

import { ApplicationConfig, ValidationResult } from '@shared/types';
import { ResourceManager } from '@shared/utils/resource-manager';
import Store from 'electron-store';
import {
    SettingsManagerConfig
} from './settings-config';
import { SettingsServiceBasicOperations } from './settings-service-basic-operations';
import { SettingsServiceComplexOperations } from './settings-service-complex-operations';
import { SettingsValidator } from './settings-validator';

/**
 * فئة العمليات الأساسية لخدمة الإعدادات / Core settings service operations class
 */
export class SettingsServiceCore {
    private readonly basicOperations: SettingsServiceBasicOperations;
    private readonly complexOperations: SettingsServiceComplexOperations;

    /**
     * منشئ العمليات الأساسية / Core operations constructor
     */
    constructor(
        config: SettingsManagerConfig,
        resourceManager: ResourceManager,
        store: Store<ApplicationConfig>,
        validator: SettingsValidator
    ) {
        this.basicOperations = new SettingsServiceBasicOperations(config, resourceManager, store, validator);
        this.complexOperations = new SettingsServiceComplexOperations(this.basicOperations, validator, resourceManager);
    }

    // العمليات الأساسية البسيطة / Basic simple operations

    /**
     * الحصول على جميع الإعدادات / Get all settings
     */
    public getAllSettings(): ApplicationConfig {
        return this.basicOperations.getAllSettings();
    }

    /**
     * الحصول على إعداد محدد / Get specific setting
     */
    public getSetting<K extends keyof ApplicationConfig>(key: K): ApplicationConfig[K] {
        return this.basicOperations.getSetting(key);
    }

    /**
     * تعيين إعداد محدد / Set specific setting
     */
    public async setSetting<K extends keyof ApplicationConfig>(
        key: K,
        value: ApplicationConfig[K]
    ): Promise<boolean> {
        return await this.basicOperations.setSetting(key, value);
    }

    /**
     * حذف إعداد محدد / Delete specific setting
     */
    public deleteSetting<K extends keyof ApplicationConfig>(key: K): boolean {
        return this.basicOperations.deleteSetting(key);
    }

    /**
     * الحصول على الإعدادات الافتراضية / Get default settings
     */
    public getDefaultSettings(): ApplicationConfig {
        return this.basicOperations.getDefaultSettings();
    }

    /**
     * التحقق من وجود إعداد / Check if setting exists
     */
    public hasSetting<K extends keyof ApplicationConfig>(key: K): boolean {
        return this.basicOperations.hasSetting(key);
    }

    /**
     * الحصول على حجم الإعدادات / Get settings size
     */
    public getSettingsSize(): number {
        return this.basicOperations.getSettingsSize();
    }

    /**
     * الحصول على مسار ملف الإعدادات / Get settings file path
     */
    public getSettingsPath(): string {
        return this.basicOperations.getSettingsPath();
    }

    // العمليات المعقدة / Complex operations

    /**
     * تعيين إعدادات متعددة / Set multiple settings
     */
    public async setMultipleSettings(settings: Partial<ApplicationConfig>): Promise<boolean> {
        return await this.complexOperations.setMultipleSettings(settings);
    }

    /**
     * إعادة تعيين الإعدادات للقيم الافتراضية / Reset settings to defaults
     */
    public resetToDefaults(): boolean {
        return this.complexOperations.resetToDefaults();
    }

    /**
     * التحقق من صحة الإعدادات / Validate settings
     */
    public validateCurrentSettings(): ValidationResult {
        return this.complexOperations.validateCurrentSettings();
    }

    /**
     * إصلاح الإعدادات التالفة / Fix corrupted settings
     */
    public fixCorruptedSettings(): boolean {
        return this.complexOperations.fixCorruptedSettings();
    }

    /**
     * إضافة مستمع للتغييرات / Add change listener
     */
    public addChangeListener(listener: (change: any) => void): void {
        this.basicOperations.addChangeListener(listener);
    }

    /**
     * إزالة مستمع للتغييرات / Remove change listener
     */
    public removeChangeListener(listener: (change: any) => void): void {
        this.basicOperations.removeChangeListener(listener);
    }

    /**
     * تنظيف الموارد / Cleanup resources
     */
    public cleanup(): void {
        this.basicOperations.cleanup();
        this.complexOperations.cleanup();
    }
}
