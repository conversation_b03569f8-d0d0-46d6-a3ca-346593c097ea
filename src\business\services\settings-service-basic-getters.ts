/**
 * دوال القراءة الأساسية لخدمة الإعدادات
 * Basic getter functions for settings service
 *
 * هذا الملف يجمع جميع دوال القراءة من الملفات المتخصصة
 * This file aggregates all getter functions from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ApplicationConfig } from '@shared/types';
import { ResourceManager } from '@shared/utils/resource-manager';
import Store from 'electron-store';
import { SettingsManagerConfig } from './settings-config';
import { SettingsServiceBasicGettersAdvanced } from './settings-service-basic-getters-advanced';
import { SettingsServiceBasicGettersSimple } from './settings-service-basic-getters-simple';
import { SettingsValidator } from './settings-validator';

// إعادة تصدير الفئات المتخصصة / Re-export specialized classes
export { SettingsServiceBasicGettersAdvanced } from './settings-service-basic-getters-advanced';
export { SettingsServiceBasicGettersSimple } from './settings-service-basic-getters-simple';

/**
 * فئة دوال القراءة الأساسية لخدمة الإعدادات / Basic getter functions for settings service class
 */
export class SettingsServiceBasicGetters {
    private readonly simpleGetters: SettingsServiceBasicGettersSimple;
    private readonly advancedGetters: SettingsServiceBasicGettersAdvanced;

    /**
     * منشئ دوال القراءة الأساسية / Basic getters constructor
     */
    constructor(
        config: SettingsManagerConfig,
        resourceManager: ResourceManager,
        store: Store<ApplicationConfig>,
        validator: SettingsValidator
    ) {
        this.simpleGetters = new SettingsServiceBasicGettersSimple(config, resourceManager, store, validator);
        this.advancedGetters = new SettingsServiceBasicGettersAdvanced(config, resourceManager, store, validator);
    }

    // دوال القراءة البسيطة / Simple getter functions

    /**
     * الحصول على جميع الإعدادات / Get all settings
     */
    public getAllSettings(): ApplicationConfig {
        return this.simpleGetters.getAllSettings();
    }

    /**
     * الحصول على إعداد محدد / Get specific setting
     */
    public getSetting<K extends keyof ApplicationConfig>(key: K): ApplicationConfig[K] {
        return this.simpleGetters.getSetting(key);
    }

    /**
     * الحصول على الإعدادات الافتراضية / Get default settings
     */
    public getDefaultSettings(): ApplicationConfig {
        return this.simpleGetters.getDefaultSettings();
    }

    /**
     * التحقق من وجود إعداد / Check if setting exists
     */
    public hasSetting<K extends keyof ApplicationConfig>(key: K): boolean {
        return this.simpleGetters.hasSetting(key);
    }

    /**
     * الحصول على حجم الإعدادات / Get settings size
     */
    public getSettingsSize(): number {
        return this.simpleGetters.getSettingsSize();
    }

    /**
     * الحصول على مسار ملف الإعدادات / Get settings file path
     */
    public getSettingsPath(): string {
        return this.simpleGetters.getSettingsPath();
    }

    // دوال القراءة المتقدمة / Advanced getter functions

    /**
     * الحصول على قيم متعددة / Get multiple values
     */
    public getMultipleSettings<K extends keyof ApplicationConfig>(
        keys: K[]
    ): Pick<ApplicationConfig, K> {
        return this.advancedGetters.getMultipleSettings(keys);
    }

    /**
     * البحث في الإعدادات / Search in settings
     */
    public searchSettings(searchTerm: string): Array<{
        key: string;
        value: any;
        matchType: 'key' | 'value';
    }> {
        return this.advancedGetters.searchSettings(searchTerm);
    }

    /**
     * تنظيف الموارد / Cleanup resources
     */
    public cleanup(): void {
        this.simpleGetters.cleanup();
        this.advancedGetters.cleanup();
    }
}
