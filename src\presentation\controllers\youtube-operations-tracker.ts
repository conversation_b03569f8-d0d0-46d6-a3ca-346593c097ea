/**
 * متتبع عمليات YouTube
 * YouTube operations tracker
 * 
 * هذا الملف يحتوي على منطق تتبع عمليات YouTube
 * This file contains YouTube operations tracking logic
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { 
    OperationInfo,
    YOUTUBE_CONTROLLER_CONSTANTS
} from './youtube-controller-config';

/**
 * فئة متتبع عمليات YouTube
 * YouTube operations tracker class
 */
export class YouTubeOperationsTracker {
    private activeOperations: Map<string, OperationInfo> = new Map();
    private operationHistory: OperationInfo[] = [];
    private operationCounter: number = 0;

    /**
     * منشئ متتبع العمليات
     * Operations tracker constructor
     */
    constructor() {
        // تنظيف دوري للعمليات المنتهية
        this.startPeriodicCleanup();
    }

    /**
     * إنشاء عملية جديدة
     * Create new operation
     * 
     * @param type - نوع العملية
     * @param priority - أولوية العملية
     * @returns معلومات العملية
     */
    public createOperation(type: string, priority: number = YOUTUBE_CONTROLLER_CONSTANTS.OPERATION_PRIORITIES.MEDIUM): OperationInfo {
        const operationId = `op_${++this.operationCounter}_${Date.now()}`;
        
        const operation: OperationInfo = {
            id: operationId,
            type,
            priority,
            status: 'RUNNING',
            startTime: new Date(),
            progress: 0,
            metadata: {}
        };

        this.activeOperations.set(operationId, operation);
        return operation;
    }

    /**
     * تحديث حالة العملية
     * Update operation status
     * 
     * @param operationId - معرف العملية
     * @param status - الحالة الجديدة
     * @param progress - التقدم (0-100)
     * @param metadata - بيانات إضافية
     */
    public updateOperation(
        operationId: string, 
        status: 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED',
        progress?: number,
        metadata?: Record<string, any>
    ): boolean {
        const operation = this.activeOperations.get(operationId);
        if (!operation) {
            return false;
        }

        operation.status = status;
        if (progress !== undefined) {
            operation.progress = Math.max(0, Math.min(100, progress));
        }
        if (metadata) {
            operation.metadata = { ...operation.metadata, ...metadata };
        }

        // إذا انتهت العملية، نقلها للتاريخ
        if (status === 'COMPLETED' || status === 'FAILED' || status === 'CANCELLED') {
            operation.endTime = new Date();
            operation.duration = operation.endTime.getTime() - operation.startTime.getTime();
            
            this.operationHistory.push(operation);
            this.activeOperations.delete(operationId);
            
            // الحفاظ على حد أقصى للتاريخ
            if (this.operationHistory.length > YOUTUBE_CONTROLLER_CONSTANTS.MAX_OPERATION_HISTORY) {
                this.operationHistory = this.operationHistory.slice(-YOUTUBE_CONTROLLER_CONSTANTS.MAX_OPERATION_HISTORY);
            }
        }

        return true;
    }

    /**
     * الحصول على العمليات النشطة
     * Get active operations
     * 
     * @returns قائمة العمليات النشطة
     */
    public getActiveOperations(): OperationInfo[] {
        return Array.from(this.activeOperations.values());
    }

    /**
     * الحصول على تاريخ العمليات
     * Get operation history
     * 
     * @returns قائمة العمليات المنتهية
     */
    public getOperationHistory(): OperationInfo[] {
        return [...this.operationHistory];
    }

    /**
     * الحصول على عملية بالمعرف
     * Get operation by ID
     * 
     * @param operationId - معرف العملية
     * @returns معلومات العملية أو null
     */
    public getOperation(operationId: string): OperationInfo | null {
        return this.activeOperations.get(operationId) || 
               this.operationHistory.find(op => op.id === operationId) || 
               null;
    }

    /**
     * إلغاء عملية
     * Cancel operation
     * 
     * @param operationId - معرف العملية
     * @returns true إذا تم الإلغاء بنجاح
     */
    public cancelOperation(operationId: string): boolean {
        const operation = this.activeOperations.get(operationId);
        if (!operation) {
            return false;
        }

        return this.updateOperation(operationId, 'CANCELLED');
    }

    /**
     * الحصول على إحصائيات العمليات
     * Get operation statistics
     * 
     * @returns إحصائيات العمليات
     */
    public getOperationStatistics(): Record<string, number> {
        const stats: Record<string, number> = {
            activeOperations: this.activeOperations.size,
            totalOperations: this.operationHistory.length + this.activeOperations.size,
            completedOperations: 0,
            failedOperations: 0,
            cancelledOperations: 0
        };

        for (const operation of this.operationHistory) {
            switch (operation.status) {
                case 'COMPLETED':
                    stats.completedOperations++;
                    break;
                case 'FAILED':
                    stats.failedOperations++;
                    break;
                case 'CANCELLED':
                    stats.cancelledOperations++;
                    break;
            }
        }

        return stats;
    }

    /**
     * الحصول على العمليات حسب النوع
     * Get operations by type
     * 
     * @param type - نوع العملية
     * @returns قائمة العمليات من النوع المحدد
     */
    public getOperationsByType(type: string): OperationInfo[] {
        const activeOps = Array.from(this.activeOperations.values()).filter(op => op.type === type);
        const historyOps = this.operationHistory.filter(op => op.type === type);
        return [...activeOps, ...historyOps];
    }

    /**
     * فحص إذا كان هناك عمليات نشطة من نوع معين
     * Check if there are active operations of a specific type
     * 
     * @param type - نوع العملية
     * @returns true إذا كان هناك عمليات نشطة من هذا النوع
     */
    public hasActiveOperationsOfType(type: string): boolean {
        return Array.from(this.activeOperations.values()).some(op => op.type === type);
    }

    /**
     * بدء التنظيف الدوري
     * Start periodic cleanup
     */
    private startPeriodicCleanup(): void {
        const cleanupInterval = YOUTUBE_CONTROLLER_CONSTANTS.OPERATION_CLEANUP_INTERVAL || 300000; // 5 دقائق
        
        setInterval(() => {
            this.cleanupStaleOperations();
        }, cleanupInterval);
    }

    /**
     * تنظيف العمليات المعلقة
     * Cleanup stale operations
     */
    private cleanupStaleOperations(): void {
        const now = new Date();
        const maxOperationTime = YOUTUBE_CONTROLLER_CONSTANTS.MAX_OPERATION_TIME || 600000; // 10 دقائق
        
        for (const [operationId, operation] of this.activeOperations.entries()) {
            const operationAge = now.getTime() - operation.startTime.getTime();
            
            if (operationAge > maxOperationTime) {
                console.warn(`تنظيف عملية معلقة / Cleaning up stale operation: ${operationId}`);
                this.updateOperation(operationId, 'FAILED', undefined, { 
                    reason: 'Operation timeout',
                    cleanedUp: true 
                });
            }
        }
    }

    /**
     * تنظيف جميع العمليات
     * Clear all operations
     */
    public clearAllOperations(): void {
        this.activeOperations.clear();
        this.operationHistory = [];
        this.operationCounter = 0;
    }
}
