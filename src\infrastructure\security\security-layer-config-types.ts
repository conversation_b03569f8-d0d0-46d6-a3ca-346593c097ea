/**
 * أنواع تكوين طبقة الأمان
 * Security layer configuration types
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ThreatLevel } from '@shared/types';
import { SecurityThreatType, SecurityLayerState, SecurityLogLevel, SecurityActionType } from './security-layer-config-constants';

/**
 * تكوين طبقة الأمان / Security layer configuration
 */
export interface SecurityLayerConfig {
    readonly enableAdBlocking: boolean;
    readonly enableRequestValidation: boolean;
    readonly enableContentSanitization: boolean;
    readonly enableThreatReporting: boolean;
    readonly strictMode: boolean;
    readonly maxConcurrentScans: number;
    readonly scanTimeout: number;
    readonly reportingInterval: number;
    readonly cacheSize: number;
    readonly logLevel: SecurityLogLevel;
}

/**
 * تقرير الأمان الشامل / Comprehensive security report
 */
export interface SecurityReport {
    readonly timestamp: Date;
    readonly totalThreats: number;
    readonly blockedRequests: number;
    readonly sanitizedContent: number;
    readonly securityLevel: ThreatLevel;
    readonly recommendations: string[];
    readonly performanceMetrics: SecurityPerformanceMetrics;
    readonly threatBreakdown: ThreatBreakdown;
}

/**
 * مقاييس أداء الأمان / Security performance metrics
 */
export interface SecurityPerformanceMetrics {
    readonly averageScanTime: number;
    readonly totalScanTime: number;
    readonly scansPerSecond: number;
    readonly memoryUsage: number;
    readonly cpuUsage: number;
    readonly cacheHitRate: number;
    readonly errorRate: number;
}

/**
 * تفصيل التهديدات / Threat breakdown
 */
export interface ThreatBreakdown {
    readonly malware: number;
    readonly phishing: number;
    readonly spam: number;
    readonly tracking: number;
    readonly advertisements: number;
    readonly suspiciousScripts: number;
    readonly unsafeRedirects: number;
    readonly dataLeaks: number;
    readonly injectionAttacks: number;
    readonly unknown: number;
}

/**
 * معلومات التهديد / Threat information
 */
export interface ThreatInfo {
    readonly id: string;
    readonly type: SecurityThreatType;
    readonly level: ThreatLevel;
    readonly url: string;
    readonly description: string;
    readonly timestamp: Date;
    readonly source: string;
    readonly blocked: boolean;
    readonly metadata?: Record<string, unknown>;
}

/**
 * إجراء الأمان / Security action
 */
export interface SecurityAction {
    readonly id: string;
    readonly type: SecurityActionType;
    readonly threatId: string;
    readonly timestamp: Date;
    readonly success: boolean;
    readonly details: string;
    readonly metadata?: Record<string, unknown>;
}

/**
 * قاعدة الأمان / Security rule
 */
export interface SecurityRule {
    readonly id: string;
    readonly name: string;
    readonly description: string;
    readonly pattern: string | RegExp;
    readonly action: SecurityActionType;
    readonly priority: number;
    readonly enabled: boolean;
    readonly metadata?: Record<string, unknown>;
}

/**
 * سياق الأمان / Security context
 */
export interface SecurityContext {
    readonly url: string;
    readonly method: string;
    readonly headers: Record<string, string>;
    readonly body?: string;
    readonly timestamp: Date;
    readonly userAgent?: string;
    readonly referrer?: string;
    readonly origin?: string;
}

/**
 * نتيجة فحص الأمان / Security scan result
 */
export interface SecurityScanResult {
    readonly id: string;
    readonly context: SecurityContext;
    readonly threats: ThreatInfo[];
    readonly actions: SecurityAction[];
    readonly scanTime: number;
    readonly timestamp: Date;
    readonly success: boolean;
    readonly errors: string[];
}

/**
 * إحصائيات الأمان / Security statistics
 */
export interface SecurityStatistics {
    readonly totalScans: number;
    readonly totalThreats: number;
    readonly totalBlocked: number;
    readonly totalAllowed: number;
    readonly averageScanTime: number;
    readonly threatsByType: Map<SecurityThreatType, number>;
    readonly actionsByType: Map<SecurityActionType, number>;
    readonly errorCount: number;
    readonly warningCount: number;
    readonly lastScanTime: Date | null;
    readonly uptime: number;
}

/**
 * تكوين مانع الإعلانات / Ad blocker configuration
 */
export interface AdBlockerConfig {
    readonly enabled: boolean;
    readonly strictMode: boolean;
    readonly blockTracking: boolean;
    readonly blockAnalytics: boolean;
    readonly blockSocialMedia: boolean;
    readonly customRules: string[];
    readonly whitelist: string[];
    readonly blacklist: string[];
}

/**
 * تكوين كاشف التهديدات / Threat detector configuration
 */
export interface ThreatDetectorConfig {
    readonly enabled: boolean;
    readonly realTimeScanning: boolean;
    readonly deepScanning: boolean;
    readonly heuristicAnalysis: boolean;
    readonly behaviorAnalysis: boolean;
    readonly signatureDatabase: string[];
    readonly customPatterns: RegExp[];
    readonly sensitivity: 'low' | 'medium' | 'high' | 'maximum';
}

/**
 * تكوين منظف المحتوى / Content sanitizer configuration
 */
export interface ContentSanitizerConfig {
    readonly enabled: boolean;
    readonly removeScripts: boolean;
    readonly removeIframes: boolean;
    readonly removeObjects: boolean;
    readonly removeEmbeds: boolean;
    readonly removeEventHandlers: boolean;
    readonly sanitizeUrls: boolean;
    readonly whitelist: string[];
    readonly customRules: string[];
}

/**
 * حدث الأمان / Security event
 */
export interface SecurityEvent {
    readonly id: string;
    readonly type: 'threat_detected' | 'threat_blocked' | 'content_sanitized' | 'scan_completed' | 'error_occurred';
    readonly timestamp: Date;
    readonly data: Record<string, unknown>;
    readonly severity: 'low' | 'medium' | 'high' | 'critical';
    readonly source: string;
}

/**
 * مستمع أحداث الأمان / Security event listener
 */
export interface SecurityEventListener {
    readonly id: string;
    readonly eventTypes: string[];
    readonly callback: (event: SecurityEvent) => void;
    readonly priority: number;
    readonly enabled: boolean;
}

/**
 * مدير أحداث الأمان / Security event manager
 */
export interface SecurityEventManager {
    addEventListener(listener: SecurityEventListener): void;
    removeEventListener(id: string): void;
    dispatchEvent(event: SecurityEvent): void;
    getListeners(): SecurityEventListener[];
    clearListeners(): void;
}

/**
 * حالة طبقة الأمان / Security layer status
 */
export interface SecurityLayerStatus {
    readonly state: SecurityLayerState;
    readonly initialized: boolean;
    readonly running: boolean;
    readonly lastError: string | null;
    readonly statistics: SecurityStatistics;
    readonly performance: SecurityPerformanceMetrics;
    readonly uptime: number;
    readonly version: string;
}

/**
 * خيارات التهيئة / Initialization options
 */
export interface SecurityInitializationOptions {
    readonly config: SecurityLayerConfig;
    readonly adBlockerConfig?: AdBlockerConfig;
    readonly threatDetectorConfig?: ThreatDetectorConfig;
    readonly contentSanitizerConfig?: ContentSanitizerConfig;
    readonly eventManager?: SecurityEventManager;
    readonly customRules?: SecurityRule[];
    readonly enableLogging?: boolean;
    readonly logLevel?: SecurityLogLevel;
}

/**
 * خيارات الفحص / Scan options
 */
export interface SecurityScanOptions {
    readonly deepScan?: boolean;
    readonly timeout?: number;
    readonly includeMetadata?: boolean;
    readonly customRules?: SecurityRule[];
    readonly skipCache?: boolean;
    readonly priority?: number;
}

/**
 * نتيجة التحقق / Validation result
 */
export interface SecurityValidationResult {
    readonly isValid: boolean;
    readonly errors: Array<{
        readonly field: string;
        readonly message: string;
        readonly code: string;
        readonly value?: unknown;
    }>;
    readonly warnings: Array<{
        readonly field: string;
        readonly message: string;
        readonly value?: unknown;
    }>;
}

/**
 * تقرير خطأ الأمان / Security error report
 */
export interface SecurityErrorReport {
    readonly id: string;
    readonly timestamp: Date;
    readonly error: Error;
    readonly context: SecurityContext;
    readonly severity: 'low' | 'medium' | 'high' | 'critical';
    readonly handled: boolean;
    readonly metadata?: Record<string, unknown>;
}
