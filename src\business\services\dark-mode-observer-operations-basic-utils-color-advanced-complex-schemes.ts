/**
 * مخططات الألوان المعقدة المتقدمة
 * Complex advanced color schemes
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeObserverOperationsBasicUtilsColorCore } from './dark-mode-observer-operations-basic-utils-color-core';
import { DarkModeObserverOperationsBasicUtilsColorAdvancedBasic } from './dark-mode-observer-operations-basic-utils-color-advanced-basic';

/**
 * فئة مخططات الألوان المعقدة المتقدمة
 * Complex advanced color schemes class
 */
export class DarkModeObserverOperationsBasicUtilsColorAdvancedComplexSchemes {

    /** الحصول على ألوان متناسقة / Get harmonious colors */
    public static getHarmoniousColors(baseColor: string, count: number = 5): string[] {
        const rgb = DarkModeObserverOperationsBasicUtilsColorCore.parseColorToRgb(baseColor);
        if (!rgb) {
            return [baseColor];
        }

        const hsl = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.rgbToHsl(rgb.r, rgb.g, rgb.b);
        const colors: string[] = [];
        const hueStep = 360 / count;

        for (let i = 0; i < count; i++) {
            const newHue = (hsl.h + (hueStep * i)) % 360;
            const newRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(newHue, hsl.s, hsl.l);
            colors.push(DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(newRgb.r, newRgb.g, newRgb.b));
        }

        return colors;
    }

    /** الحصول على ألوان متدرجة / Get gradient colors */
    public static getGradientColors(startColor: string, endColor: string, steps: number = 10): string[] {
        const colors: string[] = [];
        
        for (let i = 0; i < steps; i++) {
            const ratio = i / (steps - 1);
            const blendedColor = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.blendColors(startColor, endColor, ratio);
            colors.push(blendedColor);
        }

        return colors;
    }

    /** الحصول على ألوان مكملة / Get complementary color scheme */
    public static getComplementaryScheme(baseColor: string): { primary: string; secondary: string; accent: string } {
        const rgb = DarkModeObserverOperationsBasicUtilsColorCore.parseColorToRgb(baseColor);
        if (!rgb) {
            return { primary: baseColor, secondary: baseColor, accent: baseColor };
        }

        const hsl = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.rgbToHsl(rgb.r, rgb.g, rgb.b);
        
        // اللون المكمل (180 درجة)
        const complementaryHue = (hsl.h + 180) % 360;
        const complementaryRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(complementaryHue, hsl.s, hsl.l);
        const secondary = DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(complementaryRgb.r, complementaryRgb.g, complementaryRgb.b);

        // لون مكمل ثلاثي (120 درجة)
        const triadicHue = (hsl.h + 120) % 360;
        const triadicRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(triadicHue, hsl.s, hsl.l);
        const accent = DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(triadicRgb.r, triadicRgb.g, triadicRgb.b);

        return {
            primary: baseColor,
            secondary: secondary,
            accent: accent
        };
    }

    /** الحصول على ألوان متشابهة / Get analogous colors */
    public static getAnalogousColors(baseColor: string, count: number = 3): string[] {
        const rgb = DarkModeObserverOperationsBasicUtilsColorCore.parseColorToRgb(baseColor);
        if (!rgb) {
            return [baseColor];
        }

        const hsl = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.rgbToHsl(rgb.r, rgb.g, rgb.b);
        const colors: string[] = [baseColor];
        const hueStep = 30; // 30 درجة لكل لون متشابه

        for (let i = 1; i < count; i++) {
            const newHue = (hsl.h + (hueStep * i)) % 360;
            const newRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(newHue, hsl.s, hsl.l);
            colors.push(DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(newRgb.r, newRgb.g, newRgb.b));
        }

        return colors;
    }

    /** إنشاء لوحة ألوان متوازنة / Create balanced color palette */
    public static createBalancedPalette(baseColor: string): {
        primary: string;
        secondary: string;
        accent: string;
        neutral: string;
        light: string;
        dark: string;
    } {
        const rgb = DarkModeObserverOperationsBasicUtilsColorCore.parseColorToRgb(baseColor);
        
        if (!rgb) {
            return {
                primary: baseColor,
                secondary: baseColor,
                accent: baseColor,
                neutral: '#808080',
                light: '#f5f5f5',
                dark: '#333333'
            };
        }

        const hsl = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.rgbToHsl(rgb.r, rgb.g, rgb.b);

        // اللون الثانوي (مكمل)
        const secondaryHue = (hsl.h + 180) % 360;
        const secondaryRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(
            secondaryHue, 
            hsl.s * 0.8, 
            hsl.l
        );
        const secondary = DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(secondaryRgb.r, secondaryRgb.g, secondaryRgb.b);

        // لون التمييز (ثلاثي)
        const accentHue = (hsl.h + 120) % 360;
        const accentRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(
            accentHue, 
            Math.min(100, hsl.s * 1.2), 
            Math.min(80, hsl.l * 1.1)
        );
        const accent = DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(accentRgb.r, accentRgb.g, accentRgb.b);

        // اللون المحايد
        const neutralRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(
            hsl.h, 
            hsl.s * 0.1, 
            50
        );
        const neutral = DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(neutralRgb.r, neutralRgb.g, neutralRgb.b);

        // الألوان الفاتحة والداكنة
        const light = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.lightenColor(baseColor, 0.8);
        const dark = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.darkenColor(baseColor, 0.6);

        return {
            primary: baseColor,
            secondary,
            accent,
            neutral,
            light,
            dark
        };
    }
}
