/**
 * العمليات الأساسية لفاحص الامتثال
 * Core operations for constitution checker
 * 
 * هذا الملف يحتوي على العمليات الأساسية لفحص الامتثال
 * This file contains core operations for compliance checking
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import * as fs from 'fs';
import * as path from 'path';
import { 
    ComplianceIssue, 
    ComplianceReport, 
    FileCheckResult, 
    CheckOptions,
    ComplianceRules,
    FileInfo
} from './constitution-checker-types';

/**
 * العمليات الأساسية لفاحص الامتثال
 * Core constitution checker operations
 */
export class ConstitutionCheckerCore {
    
    /**
     * فحص ملف واحد
     * Check single file
     * 
     * @param filePath - مسار الملف
     * @param rules - قواعد الامتثال
     * @returns نتيجة فحص الملف
     */
    public static async checkFile(filePath: string, rules: ComplianceRules): Promise<FileCheckResult> {
        const issues: ComplianceIssue[] = [];
        
        try {
            // التحقق من وجود الملف
            if (!fs.existsSync(filePath)) {
                issues.push({
                    file: filePath,
                    rule: 'FILE_EXISTS',
                    severity: 'ERROR',
                    message: 'File does not exist'
                });
                
                return {
                    filePath,
                    issues,
                    lineCount: 0,
                    isCompliant: false
                };
            }

            // قراءة محتوى الملف
            const content = fs.readFileSync(filePath, 'utf-8');
            const lines = content.split('\n');
            const lineCount = lines.length;

            // فحص حجم الملف
            if (lineCount > rules.maxFileSize) {
                issues.push({
                    file: filePath,
                    rule: 'MAX_FILE_SIZE',
                    severity: 'ERROR',
                    message: `File exceeds maximum size: ${lineCount} lines (limit: ${rules.maxFileSize})`,
                    suggestion: 'Split the file into smaller modules'
                });
            }

            // فحص التسمية
            const namingIssues = this.checkNaming(filePath, rules);
            issues.push(...namingIssues);

            // فحص التوثيق
            if (rules.requiredDocumentation) {
                const docIssues = this.checkDocumentation(content, filePath);
                issues.push(...docIssues);
            }

            // فحص جودة الكود
            const qualityIssues = this.checkCodeQuality(content, filePath, rules);
            issues.push(...qualityIssues);

            return {
                filePath,
                issues,
                lineCount,
                isCompliant: issues.filter(i => i.severity === 'ERROR').length === 0
            };

        } catch (error) {
            issues.push({
                file: filePath,
                rule: 'FILE_PROCESSING',
                severity: 'ERROR',
                message: `Error processing file: ${error.message}`
            });

            return {
                filePath,
                issues,
                lineCount: 0,
                isCompliant: false
            };
        }
    }

    /**
     * فحص التسمية
     * Check naming conventions
     */
    private static checkNaming(filePath: string, rules: ComplianceRules): ComplianceIssue[] {
        const issues: ComplianceIssue[] = [];
        const fileName = path.basename(filePath, path.extname(filePath));
        
        // فحص تسمية الملف
        if (rules.namingConventions.fileNaming === 'kebab-case') {
            if (!this.isKebabCase(fileName)) {
                issues.push({
                    file: filePath,
                    rule: 'FILE_NAMING',
                    severity: 'ERROR',
                    message: `File name should be in kebab-case: ${fileName}`,
                    suggestion: `Rename to: ${this.toKebabCase(fileName)}`
                });
            }
        }

        return issues;
    }

    /**
     * فحص التوثيق
     * Check documentation
     */
    private static checkDocumentation(content: string, filePath: string): ComplianceIssue[] {
        const issues: ComplianceIssue[] = [];
        
        // فحص وجود JSDoc في بداية الملف
        if (!content.trim().startsWith('/**')) {
            issues.push({
                file: filePath,
                line: 1,
                rule: 'FILE_HEADER_DOC',
                severity: 'ERROR',
                message: 'File must start with JSDoc comment',
                suggestion: 'Add JSDoc header with file description'
            });
        }

        // فحص وجود <AUTHOR> (!content.includes('@author')) {
            issues.push({
                file: filePath,
                rule: 'AUTHOR_TAG',
                severity: 'WARNING',
                message: 'File should include <AUTHOR>
                suggestion: 'Add <AUTHOR> in JSDoc header'
            });
        }

        // فحص وجود @version
        if (!content.includes('@version')) {
            issues.push({
                file: filePath,
                rule: 'VERSION_TAG',
                severity: 'WARNING',
                message: 'File should include @version tag',
                suggestion: 'Add @version tag in JSDoc header'
            });
        }

        return issues;
    }

    /**
     * فحص جودة الكود
     * Check code quality
     */
    private static checkCodeQuality(content: string, filePath: string, rules: ComplianceRules): ComplianceIssue[] {
        const issues: ComplianceIssue[] = [];
        
        // فحص استخدام any type
        if (rules.codeQualityRules.noAnyType) {
            const anyMatches = content.match(/:\s*any\b/g);
            if (anyMatches) {
                issues.push({
                    file: filePath,
                    rule: 'NO_ANY_TYPE',
                    severity: 'ERROR',
                    message: `Found ${anyMatches.length} usage(s) of 'any' type`,
                    suggestion: 'Replace any type with specific type definitions'
                });
            }
        }

        // فحص الأرقام السحرية
        if (rules.codeQualityRules.noMagicNumbers) {
            const magicNumbers = this.findMagicNumbers(content);
            if (magicNumbers.length > 0) {
                issues.push({
                    file: filePath,
                    rule: 'NO_MAGIC_NUMBERS',
                    severity: 'WARNING',
                    message: `Found ${magicNumbers.length} magic number(s)`,
                    suggestion: 'Replace magic numbers with named constants'
                });
            }
        }

        // فحص معالجة الأخطاء
        if (rules.codeQualityRules.requireErrorHandling) {
            const hasErrorHandling = this.checkErrorHandling(content);
            if (!hasErrorHandling) {
                issues.push({
                    file: filePath,
                    rule: 'ERROR_HANDLING',
                    severity: 'WARNING',
                    message: 'File should include error handling',
                    suggestion: 'Add try-catch blocks for error handling'
                });
            }
        }

        return issues;
    }

    /**
     * التحقق من kebab-case
     * Check if string is kebab-case
     */
    private static isKebabCase(str: string): boolean {
        return /^[a-z0-9]+(-[a-z0-9]+)*$/.test(str);
    }

    /**
     * تحويل إلى kebab-case
     * Convert to kebab-case
     */
    private static toKebabCase(str: string): string {
        return str
            .replace(/([a-z])([A-Z])/g, '$1-$2')
            .replace(/[\s_]+/g, '-')
            .toLowerCase();
    }

    /**
     * البحث عن الأرقام السحرية
     * Find magic numbers
     */
    private static findMagicNumbers(content: string): number[] {
        const numbers: number[] = [];
        const regex = /\b(\d+)\b/g;
        let match;
        
        while ((match = regex.exec(content)) !== null) {
            const num = parseInt(match[1], 10);
            // تجاهل الأرقام الشائعة
            if (num !== 0 && num !== 1 && num !== -1 && num !== 100) {
                numbers.push(num);
            }
        }
        
        return numbers;
    }

    /**
     * فحص معالجة الأخطاء
     * Check error handling
     */
    private static checkErrorHandling(content: string): boolean {
        return content.includes('try') && content.includes('catch') ||
               content.includes('throw') ||
               content.includes('.catch(') ||
               content.includes('Promise.reject');
    }

    /**
     * جمع معلومات الملف
     * Collect file information
     */
    public static getFileInfo(filePath: string): FileInfo {
        const stats = fs.statSync(filePath);
        const content = fs.readFileSync(filePath, 'utf-8');
        const lines = content.split('\n');
        const extension = path.extname(filePath);
        
        return {
            path: filePath,
            size: stats.size,
            lineCount: lines.length,
            extension,
            isTypeScript: extension === '.ts',
            isJavaScript: extension === '.js',
            isTestFile: filePath.includes('.test.') || filePath.includes('.spec.')
        };
    }

    /**
     * تصفية الملفات
     * Filter files
     */
    public static filterFiles(files: string[], options: CheckOptions): string[] {
        return files.filter(file => {
            // تطبيق أنماط الاستبعاد
            for (const pattern of options.excludePatterns) {
                if (file.includes(pattern)) {
                    return false;
                }
            }
            
            // تطبيق أنماط التضمين
            if (options.includePatterns.length > 0) {
                let included = false;
                for (const pattern of options.includePatterns) {
                    if (file.includes(pattern)) {
                        included = true;
                        break;
                    }
                }
                if (!included) {
                    return false;
                }
            }
            
            return true;
        });
    }
}
