/**
 * ثوابت تحكم YouTube
 * YouTube controller constants
 * 
 * هذا الملف يحتوي على ثوابت تحكم YouTube
 * This file contains YouTube controller constants
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { YouTubeControllerConfig } from './youtube-controller-types';

/**
 * التكوين الافتراضي لتحكم YouTube
 * Default YouTube controller configuration
 */
export const DEFAULT_YOUTUBE_CONTROLLER_CONFIG: YouTubeControllerConfig = {
    enableAutoInitialization: true,
    initializationTimeout: 10000,
    retryAttempts: 3,
    retryDelay: 2000,
    enablePerformanceMonitoring: true,
    enableErrorReporting: true,
    enableDebugLogging: false,
    autoApplySettings: true,
    settingsApplyDelay: 1000,
    enableSettingsValidation: true
};

/**
 * ثوابت تحكم YouTube
 * YouTube controller constants
 */
export const YOUTUBE_CONTROLLER_CONSTANTS = {
    // ثوابت التهيئة / Initialization constants
    INITIALIZATION_TIMEOUT: 10000,
    INITIALIZATION_RETRY_ATTEMPTS: 3,
    INITIALIZATION_RETRY_DELAY: 2000,
    
    // ثوابت العمليات / Operation constants
    MAX_RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 2000,
    OPERATION_TIMEOUT: 30000,
    MAX_OPERATION_TIME: 600000,
    OPERATION_CLEANUP_INTERVAL: 300000,
    MAX_OPERATION_HISTORY: 100,
    
    // أولويات العمليات / Operation priorities
    OPERATION_PRIORITIES: {
        LOW: 1,
        MEDIUM: 5,
        HIGH: 10,
        CRITICAL: 20
    },
    
    // ثوابت مراقبة الأداء / Performance monitoring constants
    PERFORMANCE_MONITORING_INTERVAL: 30000,
    PERFORMANCE_CHECK_INTERVAL: 5000,
    MAX_PERFORMANCE_HISTORY: 100,
    PERFORMANCE_CLEANUP_INTERVAL: 3600000,
    
    // عتبات الأداء / Performance thresholds
    PERFORMANCE_THRESHOLDS: {
        MEMORY_USAGE: 500, // MB
        CPU_USAGE: 80, // %
        RESPONSE_TIME: 1000, // ms
        NETWORK_LATENCY: 200, // ms
        ERROR_RATE: 5 // %
    },
    
    // ثوابت تبليغ الأخطاء / Error reporting constants
    MAX_ERROR_HISTORY: 100,
    ERROR_CLEANUP_INTERVAL: 3600000,
    CRITICAL_ERROR_THRESHOLD: 10,
    
    // ثوابت تطبيق الإعدادات / Settings application constants
    SETTINGS_APPLY_DELAY: 1000,
    SETTINGS_VALIDATION_TIMEOUT: 5000,
    MAX_SETTINGS_RETRY: 3,
    
    // ثوابت جودة الفيديو / Video quality constants
    VIDEO_QUALITY_DETECTION_TIMEOUT: 5000,
    VIDEO_QUALITY_APPLICATION_TIMEOUT: 10000,
    VIDEO_QUALITY_RETRY_ATTEMPTS: 3,
    VIDEO_QUALITY_RETRY_DELAY: 2000,
    
    // ثوابت الوضع المظلم / Dark mode constants
    DARK_MODE_APPLICATION_TIMEOUT: 3000,
    DARK_MODE_RETRY_ATTEMPTS: 2,
    DARK_MODE_RETRY_DELAY: 1000,
    
    // ثوابت مانع الإعلانات / Ad blocker constants
    AD_BLOCKER_APPLICATION_TIMEOUT: 5000,
    AD_BLOCKER_RETRY_ATTEMPTS: 3,
    AD_BLOCKER_RETRY_DELAY: 1500,
    
    // ثوابت الشبكة / Network constants
    NETWORK_TIMEOUT: 10000,
    NETWORK_RETRY_ATTEMPTS: 3,
    NETWORK_RETRY_DELAY: 2000,
    
    // ثوابت التنظيف / Cleanup constants
    CLEANUP_INTERVAL: 300000, // 5 دقائق
    MEMORY_CLEANUP_THRESHOLD: 100, // MB
    CACHE_CLEANUP_INTERVAL: 600000, // 10 دقائق
    
    // ثوابت التسجيل / Logging constants
    LOG_LEVEL: 'INFO',
    MAX_LOG_ENTRIES: 1000,
    LOG_CLEANUP_INTERVAL: 3600000,
    
    // ثوابت التحقق / Validation constants
    VALIDATION_TIMEOUT: 5000,
    MAX_VALIDATION_ERRORS: 10,
    VALIDATION_RETRY_ATTEMPTS: 2
} as const;

/**
 * رسائل تحكم YouTube
 * YouTube controller messages
 */
export const YOUTUBE_CONTROLLER_MESSAGES = {
    // رسائل التهيئة / Initialization messages
    INITIALIZATION_STARTED: 'بدء تهيئة تحكم YouTube / YouTube controller initialization started',
    INITIALIZATION_COMPLETED: 'تم إكمال تهيئة تحكم YouTube / YouTube controller initialization completed',
    INITIALIZATION_FAILED: 'فشل في تهيئة تحكم YouTube / YouTube controller initialization failed',
    INITIALIZATION_TIMEOUT: 'انتهت مهلة تهيئة تحكم YouTube / YouTube controller initialization timeout',
    
    // رسائل العمليات / Operation messages
    OPERATION_STARTED: 'بدء العملية / Operation started',
    OPERATION_COMPLETED: 'تم إكمال العملية / Operation completed',
    OPERATION_FAILED: 'فشل في العملية / Operation failed',
    OPERATION_CANCELLED: 'تم إلغاء العملية / Operation cancelled',
    OPERATION_RETRY: 'إعادة محاولة العملية / Operation retry',
    
    // رسائل مراقبة الأداء / Performance monitoring messages
    PERFORMANCE_MONITORING_STARTED: 'بدء مراقبة الأداء / Performance monitoring started',
    PERFORMANCE_MONITORING_STOPPED: 'تم إيقاف مراقبة الأداء / Performance monitoring stopped',
    PERFORMANCE_THRESHOLD_EXCEEDED: 'تم تجاوز عتبة الأداء / Performance threshold exceeded',
    PERFORMANCE_REPORT_GENERATED: 'تم إنتاج تقرير الأداء / Performance report generated',
    
    // رسائل الأخطاء / Error messages
    ERROR_REPORTED: 'تم تبليغ الخطأ / Error reported',
    CRITICAL_ERROR_DETECTED: 'تم اكتشاف خطأ حرج / Critical error detected',
    ERROR_RECOVERY_ATTEMPTED: 'تم محاولة استرداد الخطأ / Error recovery attempted',
    ERROR_RECOVERY_SUCCESSFUL: 'نجح استرداد الخطأ / Error recovery successful',
    ERROR_RECOVERY_FAILED: 'فشل استرداد الخطأ / Error recovery failed',
    
    // رسائل الإعدادات / Settings messages
    SETTINGS_APPLIED: 'تم تطبيق الإعدادات / Settings applied',
    SETTINGS_APPLICATION_FAILED: 'فشل في تطبيق الإعدادات / Settings application failed',
    SETTINGS_VALIDATED: 'تم التحقق من الإعدادات / Settings validated',
    SETTINGS_VALIDATION_FAILED: 'فشل في التحقق من الإعدادات / Settings validation failed',
    
    // رسائل جودة الفيديو / Video quality messages
    VIDEO_QUALITY_CHANGED: 'تم تغيير جودة الفيديو / Video quality changed',
    VIDEO_QUALITY_CHANGE_FAILED: 'فشل في تغيير جودة الفيديو / Video quality change failed',
    VIDEO_QUALITY_DETECTED: 'تم اكتشاف جودة الفيديو / Video quality detected',
    VIDEO_QUALITY_DETECTION_FAILED: 'فشل في اكتشاف جودة الفيديو / Video quality detection failed',
    
    // رسائل الوضع المظلم / Dark mode messages
    DARK_MODE_ENABLED: 'تم تفعيل الوضع المظلم / Dark mode enabled',
    DARK_MODE_DISABLED: 'تم إلغاء الوضع المظلم / Dark mode disabled',
    DARK_MODE_TOGGLE_FAILED: 'فشل في تبديل الوضع المظلم / Dark mode toggle failed',
    
    // رسائل مانع الإعلانات / Ad blocker messages
    AD_BLOCKER_ENABLED: 'تم تفعيل مانع الإعلانات / Ad blocker enabled',
    AD_BLOCKER_DISABLED: 'تم إلغاء مانع الإعلانات / Ad blocker disabled',
    AD_BLOCKER_TOGGLE_FAILED: 'فشل في تبديل مانع الإعلانات / Ad blocker toggle failed',
    
    // رسائل التنظيف / Cleanup messages
    CLEANUP_STARTED: 'بدء التنظيف / Cleanup started',
    CLEANUP_COMPLETED: 'تم إكمال التنظيف / Cleanup completed',
    CLEANUP_FAILED: 'فشل في التنظيف / Cleanup failed',
    
    // رسائل عامة / General messages
    READY: 'جاهز / Ready',
    NOT_READY: 'غير جاهز / Not ready',
    LOADING: 'جاري التحميل / Loading',
    ERROR: 'خطأ / Error',
    SUCCESS: 'نجح / Success',
    WARNING: 'تحذير / Warning',
    INFO: 'معلومات / Info'
} as const;

/**
 * أحداث تحكم YouTube
 * YouTube controller events
 */
export const YOUTUBE_CONTROLLER_EVENTS = {
    // أحداث التهيئة / Initialization events
    INITIALIZATION_STARTED: 'youtube-controller:initialization-started',
    INITIALIZATION_COMPLETED: 'youtube-controller:initialization-completed',
    INITIALIZATION_FAILED: 'youtube-controller:initialization-failed',
    
    // أحداث العمليات / Operation events
    OPERATION_STARTED: 'youtube-controller:operation-started',
    OPERATION_COMPLETED: 'youtube-controller:operation-completed',
    OPERATION_FAILED: 'youtube-controller:operation-failed',
    OPERATION_CANCELLED: 'youtube-controller:operation-cancelled',
    
    // أحداث الأداء / Performance events
    PERFORMANCE_REPORT: 'youtube-controller:performance-report',
    PERFORMANCE_THRESHOLD_EXCEEDED: 'youtube-controller:performance-threshold-exceeded',
    
    // أحداث الأخطاء / Error events
    ERROR_REPORTED: 'youtube-controller:error-reported',
    CRITICAL_ERROR: 'youtube-controller:critical-error',
    
    // أحداث الإعدادات / Settings events
    SETTINGS_CHANGED: 'youtube-controller:settings-changed',
    SETTINGS_APPLIED: 'youtube-controller:settings-applied',
    
    // أحداث جودة الفيديو / Video quality events
    VIDEO_QUALITY_CHANGED: 'youtube-controller:video-quality-changed',
    VIDEO_QUALITY_DETECTED: 'youtube-controller:video-quality-detected',
    
    // أحداث الوضع المظلم / Dark mode events
    DARK_MODE_TOGGLED: 'youtube-controller:dark-mode-toggled',
    
    // أحداث مانع الإعلانات / Ad blocker events
    AD_BLOCKER_TOGGLED: 'youtube-controller:ad-blocker-toggled',
    
    // أحداث التنظيف / Cleanup events
    CLEANUP_COMPLETED: 'youtube-controller:cleanup-completed'
} as const;
