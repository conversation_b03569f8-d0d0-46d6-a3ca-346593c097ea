/**
 * العمليات الأساسية للمراقبة المتقدمة لخدمة جودة الفيديو
 * Video quality service advanced monitoring core operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQuality, ValidationResult } from '@shared/types';
import { ResourceManager } from '@shared/utils/resource-manager';
import { 
    VideoQualityConfig,
    VIDEO_QUALITY_CONSTANTS,
    VIDEO_QUALITY_MESSAGES,
    VideoQualityState
} from './video-quality-config';

/**
 * فئة العمليات الأساسية للمراقبة المتقدمة
 * Advanced monitoring core operations class
 */
export class VideoQualityServiceMonitoringAdvancedCore {
    private readonly resourceManager: ResourceManager;
    private readonly config: VideoQualityConfig;
    
    private performanceObserver: PerformanceObserver | null = null;
    private intervalId: NodeJS.Timeout | null = null;

    constructor(resourceManager: ResourceManager, config: VideoQualityConfig) {
        this.resourceManager = resourceManager;
        this.config = config;
    }

    /**
     * بدء مراقبة الأداء / Start performance monitoring
     */
    public async startPerformanceMonitoring(): Promise<ValidationResult> {
        try {
            if ('PerformanceObserver' in window) {
                this.performanceObserver = new PerformanceObserver((list) => {
                    this.handlePerformanceEntries(list.getEntries());
                });

                this.performanceObserver.observe({
                    entryTypes: ['measure', 'navigation', 'resource']
                });

                console.debug('تم بدء مراقبة الأداء');
            } else {
                console.warn('PerformanceObserver غير مدعوم في هذا المتصفح');
            }

            return {
                isValid: true,
                errors: []
            };

        } catch (error) {
            return {
                isValid: false,
                errors: [`خطأ في بدء مراقبة الأداء: ${error}`]
            };
        }
    }

    /**
     * إيقاف مراقبة الأداء / Stop performance monitoring
     */
    public async stopPerformanceMonitoring(): Promise<ValidationResult> {
        try {
            if (this.performanceObserver) {
                this.performanceObserver.disconnect();
                this.performanceObserver = null;
                console.debug('تم إيقاف مراقبة الأداء');
            }

            return {
                isValid: true,
                errors: []
            };

        } catch (error) {
            return {
                isValid: false,
                errors: [`خطأ في إيقاف مراقبة الأداء: ${error}`]
            };
        }
    }

    /**
     * بدء المراقبة الدورية / Start periodic monitoring
     */
    public async startPeriodicMonitoring(): Promise<ValidationResult> {
        try {
            if (this.intervalId) {
                clearInterval(this.intervalId);
            }

            this.intervalId = setInterval(() => {
                this.performPeriodicCheck();
            }, VIDEO_QUALITY_CONSTANTS.MONITORING_INTERVAL);

            console.debug('تم بدء المراقبة الدورية');

            return {
                isValid: true,
                errors: []
            };

        } catch (error) {
            return {
                isValid: false,
                errors: [`خطأ في بدء المراقبة الدورية: ${error}`]
            };
        }
    }

    /**
     * إيقاف المراقبة الدورية / Stop periodic monitoring
     */
    public async stopPeriodicMonitoring(): Promise<ValidationResult> {
        try {
            if (this.intervalId) {
                clearInterval(this.intervalId);
                this.intervalId = null;
                console.debug('تم إيقاف المراقبة الدورية');
            }

            return {
                isValid: true,
                errors: []
            };

        } catch (error) {
            return {
                isValid: false,
                errors: [`خطأ في إيقاف المراقبة الدورية: ${error}`]
            };
        }
    }

    /**
     * معالجة إدخالات الأداء / Handle performance entries
     */
    private handlePerformanceEntries(entries: PerformanceEntry[]): void {
        try {
            entries.forEach(entry => {
                if (entry.entryType === 'measure') {
                    console.debug(`قياس الأداء: ${entry.name} - ${entry.duration}ms`);
                } else if (entry.entryType === 'navigation') {
                    const navEntry = entry as PerformanceNavigationTiming;
                    console.debug(`وقت التحميل: ${navEntry.loadEventEnd - navEntry.navigationStart}ms`);
                } else if (entry.entryType === 'resource') {
                    const resourceEntry = entry as PerformanceResourceTiming;
                    if (resourceEntry.name.includes('video') || resourceEntry.name.includes('youtube')) {
                        console.debug(`تحميل المورد: ${resourceEntry.name} - ${resourceEntry.duration}ms`);
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في معالجة إدخالات الأداء:', error);
        }
    }

    /**
     * إجراء فحص دوري / Perform periodic check
     */
    private performPeriodicCheck(): void {
        try {
            // فحص جودة الفيديو الحالية
            const currentQuality = this.getCurrentVideoQuality();
            if (currentQuality) {
                console.debug(`الجودة الحالية: ${currentQuality}`);
            }

            // فحص صحة النظام
            this.performHealthCheck();

        } catch (error) {
            console.error('خطأ في الفحص الدوري:', error);
        }
    }

    /**
     * الحصول على جودة الفيديو الحالية / Get current video quality
     */
    public getCurrentVideoQuality(): VideoQuality | null {
        try {
            const video = document.querySelector('video') as HTMLVideoElement;
            if (!video) return null;

            const height = video.videoHeight;
            if (!height) return null;

            if (height >= 2160) return '2160p';
            if (height >= 1440) return '1440p';
            if (height >= 1080) return '1080p';
            if (height >= 720) return '720p';
            if (height >= 480) return '480p';
            if (height >= 360) return '360p';
            if (height >= 240) return '240p';
            return '144p';

        } catch (error) {
            console.error('خطأ في الحصول على جودة الفيديو الحالية:', error);
            return null;
        }
    }

    /**
     * إجراء فحص صحة النظام / Perform health check
     */
    public performHealthCheck(): {
        videoElement: boolean;
        videoError: boolean;
        networkConnection: string | null;
        memoryUsage: number | null;
    } {
        try {
            const video = document.querySelector('video') as HTMLVideoElement;
            const videoElement = !!video;
            const videoError = video ? !!video.error : false;

            // فحص الشبكة
            let networkConnection: string | null = null;
            if ('connection' in navigator) {
                const connection = (navigator as any).connection;
                if (connection && connection.effectiveType) {
                    networkConnection = connection.effectiveType;
                }
            }

            // فحص استخدام الذاكرة
            let memoryUsage: number | null = null;
            if ('memory' in performance) {
                const memory = (performance as any).memory;
                if (memory && memory.usedJSHeapSize) {
                    memoryUsage = memory.usedJSHeapSize;
                }
            }

            const healthStatus = {
                videoElement,
                videoError,
                networkConnection,
                memoryUsage
            };

            // تسجيل التحذيرات إذا لزم الأمر
            if (!videoElement) {
                console.warn('عنصر الفيديو غير موجود');
            }
            if (videoError) {
                console.error('خطأ في الفيديو:', video?.error);
            }

            return healthStatus;

        } catch (error) {
            console.error('خطأ في فحص صحة النظام:', error);
            return {
                videoElement: false,
                videoError: true,
                networkConnection: null,
                memoryUsage: null
            };
        }
    }

    /**
     * فحص أداء الفيديو / Check video performance
     */
    public checkVideoPerformance(): {
        buffered: number;
        currentTime: number;
        duration: number;
        playbackRate: number;
        readyState: number;
        networkState: number;
    } | null {
        try {
            const video = document.querySelector('video') as HTMLVideoElement;
            if (!video) return null;

            // حساب نسبة التخزين المؤقت
            let buffered = 0;
            if (video.buffered.length > 0) {
                buffered = video.buffered.end(video.buffered.length - 1) / video.duration;
            }

            return {
                buffered,
                currentTime: video.currentTime,
                duration: video.duration,
                playbackRate: video.playbackRate,
                readyState: video.readyState,
                networkState: video.networkState
            };

        } catch (error) {
            console.error('خطأ في فحص أداء الفيديو:', error);
            return null;
        }
    }

    /**
     * تنظيف الموارد / Cleanup resources
     */
    public async cleanup(): Promise<void> {
        try {
            await this.stopPerformanceMonitoring();
            await this.stopPeriodicMonitoring();
        } catch (error) {
            console.error('خطأ في تنظيف الموارد:', error);
        }
    }
}
