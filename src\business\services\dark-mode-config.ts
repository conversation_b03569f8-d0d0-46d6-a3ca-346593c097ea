/**
 * تكوين الوضع المظلم
 * Dark mode configuration
 *
 * هذا الملف يحتوي على تكوينات وثوابت الوضع المظلم
 * This file contains dark mode configurations and constants
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// إعادة تصدير المكونات المتخصصة
// Re-export specialized components
export * from './dark-mode-constants';
export * from './dark-mode-types';

import {
    DarkModeConfig
} from './dark-mode-types';

/**
 * الإعدادات الافتراضية للوضع المظلم
 * Default dark mode settings
 */
export const DEFAULT_DARK_MODE_CONFIG: DarkModeConfig = {
    enableAutoDetection: true,
    enableCustomStyles: true,
    enableTransitions: true,
    transitionDuration: 300,
    observeChanges: true,
    applyToIframes: true,
    preserveImages: false,
    customThemeColors: {
        backgroundColor: '#0f0f0f',
        textColor: '#ffffff',
        linkColor: '#3ea6ff',
        borderColor: '#303030',
        buttonColor: '#272727',
        inputColor: '#1a1a1a',
        headerColor: '#212121',
        sidebarColor: '#181818',
        accentColor: '#ff0000',
        surfaceColor: '#212121'
    },
    performanceMode: false,
    debugMode: false
};
