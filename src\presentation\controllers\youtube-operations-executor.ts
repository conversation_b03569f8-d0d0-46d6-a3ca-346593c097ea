/**
 * منفذ عمليات YouTube
 * YouTube operations executor
 * 
 * هذا الملف يحتوي على منطق تنفيذ عمليات YouTube
 * This file contains YouTube operations execution logic
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ResourceManager } from '@shared/utils/resource-manager';
import { 
    OperationInfo,
    YOUTUBE_CONTROLLER_CONSTANTS,
    YOUTUBE_CONTROLLER_MESSAGES
} from './youtube-controller-config';
import { YouTubeOperationsTracker } from './youtube-operations-tracker';

/**
 * فئة منفذ عمليات YouTube
 * YouTube operations executor class
 */
export class YouTubeOperationsExecutor {
    private readonly resourceManager: ResourceManager;
    private readonly tracker: YouTubeOperationsTracker;
    private retryAttempts: Map<string, number> = new Map();
    private operationQueue: string[] = [];
    private isProcessingQueue: boolean = false;

    /**
     * منشئ منفذ العمليات
     * Operations executor constructor
     * 
     * @param resourceManager - مدير الموارد
     * @param tracker - متتبع العمليات
     */
    constructor(resourceManager: ResourceManager, tracker: YouTubeOperationsTracker) {
        this.resourceManager = resourceManager;
        this.tracker = tracker;
        this.startQueueProcessor();
    }

    /**
     * تنفيذ عملية
     * Execute operation
     * 
     * @param operationId - معرف العملية
     * @returns Promise<boolean> - نتيجة التنفيذ
     */
    public async executeOperation(operationId: string): Promise<boolean> {
        const operation = this.tracker.getOperation(operationId);
        if (!operation) {
            console.error(`عملية غير موجودة / Operation not found: ${operationId}`);
            return false;
        }

        try {
            this.tracker.updateOperation(operationId, 'RUNNING', 0);
            
            // تنفيذ العملية حسب النوع
            const success = await this.executeByType(operation);
            
            if (success) {
                this.tracker.updateOperation(operationId, 'COMPLETED', 100);
                this.retryAttempts.delete(operationId);
                return true;
            } else {
                return await this.handleOperationFailure(operationId);
            }

        } catch (error) {
            console.error(`خطأ في تنفيذ العملية / Error executing operation ${operationId}:`, error);
            return await this.handleOperationFailure(operationId);
        }
    }

    /**
     * تنفيذ العملية حسب النوع
     * Execute operation by type
     * 
     * @param operation - معلومات العملية
     * @returns Promise<boolean> - نتيجة التنفيذ
     */
    private async executeByType(operation: OperationInfo): Promise<boolean> {
        switch (operation.type) {
            case 'INITIALIZATION':
                return await this.executeInitialization(operation);
            
            case 'SETTINGS_APPLICATION':
                return await this.executeSettingsApplication(operation);
            
            case 'VIDEO_QUALITY_CHANGE':
                return await this.executeVideoQualityChange(operation);
            
            case 'DARK_MODE_TOGGLE':
                return await this.executeDarkModeToggle(operation);
            
            case 'AD_BLOCKER_TOGGLE':
                return await this.executeAdBlockerToggle(operation);
            
            case 'CLEANUP':
                return await this.executeCleanup(operation);
            
            default:
                console.warn(`نوع عملية غير مدعوم / Unsupported operation type: ${operation.type}`);
                return false;
        }
    }

    /**
     * تنفيذ عملية التهيئة
     * Execute initialization operation
     * 
     * @param operation - معلومات العملية
     * @returns Promise<boolean> - نتيجة التنفيذ
     */
    private async executeInitialization(operation: OperationInfo): Promise<boolean> {
        this.tracker.updateOperation(operation.id, 'RUNNING', 25);
        
        // محاكاة عملية التهيئة
        await this.delay(1000);
        this.tracker.updateOperation(operation.id, 'RUNNING', 50);
        
        await this.delay(1000);
        this.tracker.updateOperation(operation.id, 'RUNNING', 75);
        
        await this.delay(500);
        return true;
    }

    /**
     * تنفيذ عملية تطبيق الإعدادات
     * Execute settings application operation
     * 
     * @param operation - معلومات العملية
     * @returns Promise<boolean> - نتيجة التنفيذ
     */
    private async executeSettingsApplication(operation: OperationInfo): Promise<boolean> {
        this.tracker.updateOperation(operation.id, 'RUNNING', 20);
        
        // تطبيق الإعدادات
        const settings = operation.metadata?.settings;
        if (!settings) {
            return false;
        }

        await this.delay(500);
        this.tracker.updateOperation(operation.id, 'RUNNING', 60);
        
        await this.delay(500);
        this.tracker.updateOperation(operation.id, 'RUNNING', 90);
        
        return true;
    }

    /**
     * تنفيذ عملية تغيير جودة الفيديو
     * Execute video quality change operation
     * 
     * @param operation - معلومات العملية
     * @returns Promise<boolean> - نتيجة التنفيذ
     */
    private async executeVideoQualityChange(operation: OperationInfo): Promise<boolean> {
        this.tracker.updateOperation(operation.id, 'RUNNING', 30);
        
        const quality = operation.metadata?.quality;
        if (!quality) {
            return false;
        }

        await this.delay(800);
        this.tracker.updateOperation(operation.id, 'RUNNING', 80);
        
        return true;
    }

    /**
     * تنفيذ عملية تبديل الوضع المظلم
     * Execute dark mode toggle operation
     * 
     * @param operation - معلومات العملية
     * @returns Promise<boolean> - نتيجة التنفيذ
     */
    private async executeDarkModeToggle(operation: OperationInfo): Promise<boolean> {
        this.tracker.updateOperation(operation.id, 'RUNNING', 40);
        
        await this.delay(600);
        this.tracker.updateOperation(operation.id, 'RUNNING', 85);
        
        return true;
    }

    /**
     * تنفيذ عملية تبديل مانع الإعلانات
     * Execute ad blocker toggle operation
     * 
     * @param operation - معلومات العملية
     * @returns Promise<boolean> - نتيجة التنفيذ
     */
    private async executeAdBlockerToggle(operation: OperationInfo): Promise<boolean> {
        this.tracker.updateOperation(operation.id, 'RUNNING', 35);
        
        await this.delay(700);
        this.tracker.updateOperation(operation.id, 'RUNNING', 90);
        
        return true;
    }

    /**
     * تنفيذ عملية التنظيف
     * Execute cleanup operation
     * 
     * @param operation - معلومات العملية
     * @returns Promise<boolean> - نتيجة التنفيذ
     */
    private async executeCleanup(operation: OperationInfo): Promise<boolean> {
        this.tracker.updateOperation(operation.id, 'RUNNING', 50);
        
        // تنظيف الموارد
        this.resourceManager.cleanup();
        
        await this.delay(300);
        return true;
    }

    /**
     * معالجة فشل العملية
     * Handle operation failure
     * 
     * @param operationId - معرف العملية
     * @returns Promise<boolean> - نتيجة المعالجة
     */
    private async handleOperationFailure(operationId: string): Promise<boolean> {
        const currentAttempts = this.retryAttempts.get(operationId) || 0;
        const maxRetries = YOUTUBE_CONTROLLER_CONSTANTS.MAX_RETRY_ATTEMPTS || 3;

        if (currentAttempts < maxRetries) {
            this.retryAttempts.set(operationId, currentAttempts + 1);
            
            // انتظار قبل إعادة المحاولة
            const retryDelay = YOUTUBE_CONTROLLER_CONSTANTS.RETRY_DELAY || 2000;
            await this.delay(retryDelay);
            
            console.log(`إعادة محاولة العملية / Retrying operation ${operationId} (attempt ${currentAttempts + 1})`);
            return await this.executeOperation(operationId);
        } else {
            this.tracker.updateOperation(operationId, 'FAILED', undefined, {
                reason: 'Max retry attempts exceeded',
                attempts: currentAttempts
            });
            this.retryAttempts.delete(operationId);
            return false;
        }
    }

    /**
     * إضافة عملية للطابور
     * Add operation to queue
     * 
     * @param operationId - معرف العملية
     */
    public queueOperation(operationId: string): void {
        this.operationQueue.push(operationId);
        this.processQueue();
    }

    /**
     * بدء معالج الطابور
     * Start queue processor
     */
    private startQueueProcessor(): void {
        setInterval(() => {
            this.processQueue();
        }, 1000);
    }

    /**
     * معالجة الطابور
     * Process queue
     */
    private async processQueue(): Promise<void> {
        if (this.isProcessingQueue || this.operationQueue.length === 0) {
            return;
        }

        this.isProcessingQueue = true;

        try {
            const operationId = this.operationQueue.shift();
            if (operationId) {
                await this.executeOperation(operationId);
            }
        } catch (error) {
            console.error('خطأ في معالجة الطابور / Error processing queue:', error);
        } finally {
            this.isProcessingQueue = false;
        }
    }

    /**
     * تأخير
     * Delay
     * 
     * @param ms - المدة بالميلي ثانية
     * @returns Promise<void>
     */
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * الحصول على حجم الطابور
     * Get queue size
     * 
     * @returns حجم الطابور
     */
    public getQueueSize(): number {
        return this.operationQueue.length;
    }

    /**
     * تنظيف الطابور
     * Clear queue
     */
    public clearQueue(): void {
        this.operationQueue = [];
        this.retryAttempts.clear();
    }
}
