/**
 * تجميع الملفات المتقدم
 * Advanced file grouping operations
 * 
 * هذا الملف يحتوي على عمليات التجميع المتقدمة للملفات
 * This file contains advanced file grouping operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvanced } from './simple-verification-core-utils-file-operations-basic-info-advanced';

/**
 * فئة تجميع الملفات المتقدم
 * Advanced file grouping class
 */
export class SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvancedStatsGroupingAdvanced {

    /**
     * تجميع الملفات حسب تاريخ التعديل
     * Group files by modification date range
     */
    public static groupFilesByModificationDateRange(filePaths: string[]): {
        today: string[];
        thisWeek: string[];
        thisMonth: string[];
        older: string[];
    } {
        const groups = {
            today: [] as string[],
            thisWeek: [] as string[],
            thisMonth: [] as string[],
            older: [] as string[]
        };

        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

        for (const filePath of filePaths) {
            try {
                const info = SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvanced.getDetailedFileInfo(filePath);
                
                if (!info.exists || !info.modificationDate) continue;
                
                if (info.modificationDate >= today) {
                    groups.today.push(filePath);
                } else if (info.modificationDate >= thisWeek) {
                    groups.thisWeek.push(filePath);
                } else if (info.modificationDate >= thisMonth) {
                    groups.thisMonth.push(filePath);
                } else {
                    groups.older.push(filePath);
                }

            } catch (error) {
                // تجاهل الملفات التي لا يمكن قراءتها
                continue;
            }
        }

        return groups;
    }

    /**
     * تجميع الملفات حسب الصلاحيات
     * Group files by permissions
     */
    public static groupFilesByPermissions(filePaths: string[]): {
        readOnly: string[];
        writeOnly: string[];
        readWrite: string[];
        noAccess: string[];
    } {
        const groups = {
            readOnly: [] as string[],
            writeOnly: [] as string[],
            readWrite: [] as string[],
            noAccess: [] as string[]
        };

        for (const filePath of filePaths) {
            try {
                const info = SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvanced.getDetailedFileInfo(filePath);
                
                if (!info.exists) continue;
                
                if (info.isReadable && info.isWritable) {
                    groups.readWrite.push(filePath);
                } else if (info.isReadable) {
                    groups.readOnly.push(filePath);
                } else if (info.isWritable) {
                    groups.writeOnly.push(filePath);
                } else {
                    groups.noAccess.push(filePath);
                }

            } catch (error) {
                // تجاهل الملفات التي لا يمكن قراءتها
                continue;
            }
        }

        return groups;
    }
}
