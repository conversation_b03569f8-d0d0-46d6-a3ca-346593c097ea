/**
 * أنواع حالة الوضع المظلم
 * Dark mode state types
 * 
 * هذا الملف يحتوي على تعريفات أنواع الحالة والأحداث للوضع المظلم
 * This file contains state and event type definitions for dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ValidationResult } from '@shared/types';
import { DarkModeConfig, DarkModeThemeColors, ThemeType } from './dark-mode-config-types';

/**
 * حالات الوضع المظلم
 * Dark mode states
 */
export enum DarkModeState {
    DISABLED = 'disabled',
    ENABLED = 'enabled',
    ENABLING = 'enabling',
    DISABLING = 'disabling',
    AUTO_DETECTING = 'auto_detecting',
    APPLYING_STYLES = 'applying_styles',
    ERROR = 'error'
}

/**
 * واجهة إحصائيات الوضع المظلم
 * Dark mode statistics interface
 */
export interface DarkModeStats {
    readonly isEnabled: boolean;
    readonly currentTheme: ThemeType;
    readonly appliedRules: number;
    readonly observedElements: number;
    readonly performanceImpact: number;
    readonly lastToggleTime: number;
    readonly totalToggles: number;
    readonly errorCount: number;
}

/**
 * واجهة أحداث الوضع المظلم
 * Dark mode events interface
 */
export interface DarkModeEvents {
    readonly onToggle: (enabled: boolean) => void;
    readonly onThemeChange: (theme: ThemeType) => void;
    readonly onStylesApplied: (rulesCount: number) => void;
    readonly onError: (error: Error) => void;
    readonly onPerformanceWarning: (impact: number) => void;
}

/**
 * واجهة خيارات التطبيق
 * Application options interface
 */
export interface DarkModeApplicationOptions {
    readonly force: boolean;
    readonly preserveExisting: boolean;
    readonly applyToIframes: boolean;
    readonly includeImages: boolean;
    readonly enableTransitions: boolean;
    readonly customSelectors: string[];
    readonly excludeSelectors: string[];
}

/**
 * واجهة نتيجة التطبيق
 * Application result interface
 */
export interface DarkModeApplicationResult extends ValidationResult {
    readonly appliedRules: number;
    readonly affectedElements: number;
    readonly performanceImpact: number;
    readonly warnings: string[];
}

/**
 * واجهة بيانات التصدير
 * Export data interface
 */
export interface DarkModeExportData {
    readonly version: string;
    readonly timestamp: number;
    readonly config: DarkModeConfig;
    readonly customStyles: string;
    readonly stats: DarkModeStats;
    readonly compatibility: DarkModeCompatibility;
}

/**
 * واجهة معلومات التوافق
 * Compatibility information interface
 */
export interface DarkModeCompatibility {
    readonly isSupported: boolean;
    readonly supportedFeatures: string[];
    readonly missingFeatures: string[];
    readonly browserVersion: string;
    readonly recommendedFallback: ThemeType;
}

/**
 * واجهة مقاييس الأداء
 * Performance metrics interface
 */
export interface PerformanceMetrics {
    readonly styleApplicationTime: number;
    readonly memoryUsage: number;
    readonly cpuUsage: number;
    readonly renderTime: number;
    readonly mutationCount: number;
    readonly errorRate: number;
}

/**
 * واجهة مشكلة الأداء
 * Performance issue interface
 */
export interface PerformanceIssue {
    readonly type: 'memory' | 'cpu' | 'render' | 'mutation';
    readonly severity: 'low' | 'medium' | 'high' | 'critical';
    readonly description: string;
    readonly recommendation: string;
    readonly impact: number;
}

/**
 * نوع callback للأحداث
 * Event callback type
 */
export type DarkModeEventCallback<T = any> = (data: T) => void;

/**
 * نوع حالة التطبيق
 * Application state type
 */
export type ApplicationState = {
    readonly state: DarkModeState;
    readonly theme: ThemeType;
    readonly isTransitioning: boolean;
    readonly lastError?: Error;
};

/**
 * نوع معلومات الحدث
 * Event information type
 */
export type EventInfo = {
    readonly type: string;
    readonly timestamp: number;
    readonly data: any;
    readonly source: string;
};

/**
 * نوع نتيجة العملية
 * Operation result type
 */
export type OperationResult<T = any> = {
    readonly success: boolean;
    readonly data?: T;
    readonly error?: Error;
    readonly warnings: string[];
    readonly duration: number;
};

/**
 * نوع تقرير الأداء
 * Performance report type
 */
export type PerformanceReport = {
    readonly metrics: PerformanceMetrics;
    readonly issues: PerformanceIssue[];
    readonly recommendations: string[];
    readonly score: number;
};

/**
 * نوع معلومات التغيير
 * Change information type
 */
export type ChangeInfo = {
    readonly property: string;
    readonly oldValue: any;
    readonly newValue: any;
    readonly timestamp: number;
    readonly source: string;
};

/**
 * نوع حالة المراقبة
 * Monitoring state type
 */
export type MonitoringState = {
    readonly isActive: boolean;
    readonly observedElements: number;
    readonly mutationCount: number;
    readonly lastUpdate: number;
    readonly errors: Error[];
};
