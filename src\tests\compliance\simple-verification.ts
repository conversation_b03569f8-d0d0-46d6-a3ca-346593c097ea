/**
 * أداة التحقق المبسطة - ملف التفويض الرئيسي
 * Simple verification tool - Main delegation file
 *
 * هذا الملف يفوض جميع العمليات للملفات المتخصصة
 * This file delegates all operations to specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { SimpleVerificationCore } from './simple-verification-core';
import {
    DEFAULT_SIMPLE_VERIFICATION_CONFIG,
    SimpleVerificationConfig,
    SimpleVerificationResult
} from './simple-verification-types';

/**
 * أداة التحقق المبسطة - التفويض
 * Simple verification tool - Delegation
 */
class SimpleVerification {
    private readonly projectRoot: string;
    private readonly config: SimpleVerificationConfig;

    constructor(
        projectRoot: string = process.cwd(),
        config: SimpleVerificationConfig = DEFAULT_SIMPLE_VERIFICATION_CONFIG
    ) {
        this.projectRoot = projectRoot;
        this.config = { ...config, projectRoot };
    }

    /**
     * تشغيل التحقق المبسط - تفويض للوحدة المتخصصة
     * Run simple verification - Delegate to specialized module
     */
    public async runVerification(): Promise<SimpleVerificationResult> {
        console.log('🎯 بدء التحقق المبسط / Starting simple verification...\n');

        // تفويض فحص الدستور للوحدة المتخصصة
        // Delegate constitution check to specialized module
        const constitutionResult = await SimpleVerificationCore.checkConstitutionCompliance(
            this.projectRoot,
            this.config
        );

        // تفويض فحص جودة الكود للوحدة المتخصصة
        // Delegate code quality check to specialized module
        const codeQualityResult = await SimpleVerificationCore.checkCodeQuality(
            this.projectRoot,
            this.config
        );

        // تفويض فحص بنية المشروع للوحدة المتخصصة
        // Delegate project structure check to specialized module
        const structureResult = await SimpleVerificationCore.checkProjectStructure(
            this.projectRoot,
            this.config
        );

        const overallScore = (constitutionResult.score + codeQualityResult.score + structureResult.score) / 3;
        const grade = this.calculateGrade(overallScore);

        // جمع جميع المشاكل والتوصيات
        // Collect all issues and recommendations
        const allIssues = [
            ...constitutionResult.issues,
            ...codeQualityResult.issues,
            ...structureResult.issues
        ];

        const allRecommendations = [
            ...constitutionResult.recommendations,
            ...codeQualityResult.recommendations,
            ...structureResult.recommendations
        ];

        const result: SimpleVerificationResult = {
            constitutionCompliance: constitutionResult.score,
            codeQuality: codeQualityResult.score,
            projectStructure: structureResult.score,
            overallScore,
            grade,
            issues: allIssues,
            recommendations: allRecommendations
        };

        this.printResults(result);
        return result;
    }

    /**
     * حساب الدرجة
     * Calculate grade
     */
    private calculateGrade(score: number): string {
        if (score >= 90) return 'A+ (ممتاز / Excellent)';
        if (score >= 80) return 'A (جيد جداً / Very Good)';
        if (score >= 70) return 'B (جيد / Good)';
        if (score >= 60) return 'C (مقبول / Acceptable)';
        if (score >= 50) return 'D (ضعيف / Poor)';
        return 'F (راسب / Fail)';
    }

    /**
     * طباعة النتائج
     * Print results
     */
    private printResults(result: SimpleVerificationResult): void {
        console.log('📊 نتائج التحقق المبسط / Simple Verification Results:');
        console.log('==================================================');
        console.log(`📋 الامتثال للدستور / Constitution Compliance: ${result.constitutionCompliance.toFixed(1)}%`);
        console.log(`🔧 جودة الكود / Code Quality: ${result.codeQuality.toFixed(1)}%`);
        console.log(`🏗️ بنية المشروع / Project Structure: ${result.projectStructure.toFixed(1)}%`);
        console.log(`📈 النتيجة الإجمالية / Overall Score: ${result.overallScore.toFixed(1)}%`);
        console.log(`🎯 الدرجة / Grade: ${result.grade}`);

        if (result.issues.length > 0) {
            console.log('\n❌ المشاكل المكتشفة / Issues Found:');
            result.issues.forEach((issue, index) => {
                console.log(`  ${index + 1}. ${issue}`);
            });
        }

        if (result.recommendations.length > 0) {
            console.log('\n💡 التوصيات / Recommendations:');
            result.recommendations.forEach((recommendation, index) => {
                console.log(`  ${index + 1}. ${recommendation}`);
            });
        }

        console.log('\n==================================================');
    }

    /**
     * تشغيل التحقق السريع
     * Run quick verification
     */
    public async runQuickVerification(): Promise<{ score: number; grade: string }> {
        console.log('⚡ تشغيل التحقق السريع / Running quick verification...');

        const result = await this.runVerification();

        return {
            score: result.overallScore,
            grade: result.grade
        };
    }

    /**
     * فحص ملف واحد
     * Check single file
     */
    public async checkSingleFile(filePath: string): Promise<{
        isValid: boolean;
        issues: string[];
        score: number;
    }> {
        const issues: string[] = [];
        let score = 100;

        try {
            const content = require('fs').readFileSync(filePath, 'utf-8');
            const lineCount = content.split('\n').length;

            // فحص حجم الملف
            if (lineCount > this.config.maxFileSize) {
                issues.push(`File too large: ${lineCount} lines (limit: ${this.config.maxFileSize})`);
                score -= 20;
            }

            // فحص التوثيق
            if (!content.includes('/**') || !content.includes('@author')) {
                issues.push('Missing JSDoc documentation');
                score -= 15;
            }

            // فحص التسمية
            const fileName = require('path').basename(filePath);
            if (!fileName.match(/^[a-z][a-z0-9]*(-[a-z0-9]+)*\.(ts|js)$/)) {
                issues.push('Invalid naming convention');
                score -= 10;
            }

            return {
                isValid: issues.length === 0,
                issues,
                score: Math.max(0, score)
            };

        } catch (error) {
            return {
                isValid: false,
                issues: [`Failed to check file: ${error instanceof Error ? error.message : 'Unknown error'}`],
                score: 0
            };
        }
    }
}

// تصدير الفئة والدالة الرئيسية
// Export class and main function
export { SimpleVerification };

/**
 * تشغيل التحقق المبسط
 * Run simple verification
 */
export async function runSimpleVerification(projectRoot?: string): Promise<SimpleVerificationResult> {
    const verification = new SimpleVerification(projectRoot);
    return await verification.runVerification();
}

// تشغيل التحقق إذا تم استدعاء الملف مباشرة
// Run verification if file is called directly
if (require.main === module) {
    runSimpleVerification()
        .then(result => {
            process.exit(result.overallScore >= 70 ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ فشل التحقق / Verification failed:', error);
            process.exit(1);
        });
}