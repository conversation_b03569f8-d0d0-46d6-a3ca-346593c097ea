/**
 * العمليات الأساسية لخدمة الإعدادات
 * Basic settings service operations
 *
 * هذا الملف يجمع جميع العمليات الأساسية من الملفات المتخصصة
 * This file aggregates all basic operations from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ApplicationConfig, SettingChangeInfo } from '@shared/types';
import { ResourceManager } from '@shared/utils/resource-manager';
import Store from 'electron-store';
import { SettingsManagerConfig } from './settings-config';
import { SettingsServiceBasicGetters } from './settings-service-basic-getters';
import { SettingsServiceBasicSetters } from './settings-service-basic-setters';
import { SettingsValidator } from './settings-validator';

// إعادة تصدير الفئات المتخصصة / Re-export specialized classes
export { SettingsServiceBasicGetters } from './settings-service-basic-getters';
export { SettingsServiceBasicSetters } from './settings-service-basic-setters';

/**
 * فئة العمليات الأساسية لخدمة الإعدادات / Basic settings service operations class
 */
export class SettingsServiceBasicOperations {
    private readonly basicGetters: SettingsServiceBasicGetters;
    private readonly basicSetters: SettingsServiceBasicSetters;

    /**
     * منشئ العمليات الأساسية / Basic operations constructor
     */
    constructor(
        config: SettingsManagerConfig,
        resourceManager: ResourceManager,
        store: Store<ApplicationConfig>,
        validator: SettingsValidator
    ) {
        this.basicGetters = new SettingsServiceBasicGetters(config, resourceManager, store, validator);
        this.basicSetters = new SettingsServiceBasicSetters(config, resourceManager, store, validator);
    }

    // دوال القراءة الأساسية / Basic getter functions

    /**
     * الحصول على جميع الإعدادات / Get all settings
     */
    public getAllSettings(): ApplicationConfig {
        return this.basicGetters.getAllSettings();
    }

    /**
     * الحصول على إعداد محدد / Get specific setting
     */
    public getSetting<K extends keyof ApplicationConfig>(key: K): ApplicationConfig[K] {
        return this.basicGetters.getSetting(key);
    }

    /**
     * الحصول على الإعدادات الافتراضية / Get default settings
     */
    public getDefaultSettings(): ApplicationConfig {
        return this.basicGetters.getDefaultSettings();
    }

    /**
     * التحقق من وجود إعداد / Check if setting exists
     */
    public hasSetting<K extends keyof ApplicationConfig>(key: K): boolean {
        return this.basicGetters.hasSetting(key);
    }

    /**
     * الحصول على حجم الإعدادات / Get settings size
     */
    public getSettingsSize(): number {
        return this.basicGetters.getSettingsSize();
    }

    /**
     * الحصول على مسار ملف الإعدادات / Get settings file path
     */
    public getSettingsPath(): string {
        return this.basicGetters.getSettingsPath();
    }

    // دوال التعديل الأساسية / Basic setter functions

    /**
     * تعيين إعداد محدد / Set specific setting
     */
    public async setSetting<K extends keyof ApplicationConfig>(
        key: K,
        value: ApplicationConfig[K]
    ): Promise<boolean> {
        return await this.basicSetters.setSetting(key, value);
    }

    /**
     * حذف إعداد محدد / Delete specific setting
     */
    public deleteSetting<K extends keyof ApplicationConfig>(key: K): boolean {
        return this.basicSetters.deleteSetting(key);
    }

    /**
     * إضافة مستمع للتغييرات / Add change listener
     */
    public addChangeListener(listener: (change: SettingChangeInfo) => void): void {
        this.basicSetters.addChangeListener(listener);
    }

    /**
     * إزالة مستمع للتغييرات / Remove change listener
     */
    public removeChangeListener(listener: (change: SettingChangeInfo) => void): void {
        this.basicSetters.removeChangeListener(listener);
    }

    /**
     * تنظيف الموارد / Cleanup resources
     */
    public cleanup(): void {
        this.basicGetters.cleanup();
        this.basicSetters.cleanup();
    }
}
