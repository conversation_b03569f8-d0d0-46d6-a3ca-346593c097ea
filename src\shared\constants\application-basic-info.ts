/**
 * معلومات التطبيق الأساسية
 * Basic application information
 * 
 * هذا الملف يحتوي على معلومات التطبيق الأساسية
 * This file contains basic application information
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * معلومات التطبيق الأساسية
 * Basic application information
 */
export const APPLICATION_INFO = {
    NAME: 'YouTube Dark CyberX',
    VERSION: '1.0.0',
    AUTHOR: 'AI Assistant',
    DESCRIPTION: 'مشغل YouTube مخصص مع ميزات متقدمة - Custom YouTube Player with Advanced Features',
    HOMEPAGE: 'https://github.com/user/youtube-dark-cyber-x',
    LICENSE: 'MIT',
    BUILD_DATE: new Date().toISOString(),
    PLATFORM: process.platform,
    ARCHITECTURE: process.arch
} as const;

/**
 * معلومات الإصدار المفصلة
 * Detailed version information
 */
export const VERSION_INFO = {
    MAJOR: 1,
    MINOR: 0,
    PATCH: 0,
    BUILD: 1,
    FULL: '*******',
    CODENAME: 'CyberX',
    RELEASE_TYPE: 'stable' as const,
    RELEASE_DATE: '2024-01-01',
    CHANGELOG_URL: 'https://github.com/user/youtube-dark-cyber-x/releases',
    UPDATE_URL: 'https://api.github.com/repos/user/youtube-dark-cyber-x/releases/latest'
} as const;

/**
 * معلومات المطور
 * Developer information
 */
export const DEVELOPER_INFO = {
    NAME: 'AI Assistant',
    EMAIL: '<EMAIL>',
    WEBSITE: 'https://ai-assistant.example.com',
    GITHUB: 'https://github.com/ai-assistant',
    TWITTER: '@ai_assistant',
    LINKEDIN: 'https://linkedin.com/in/ai-assistant',
    SUPPORT_EMAIL: '<EMAIL>',
    BUG_REPORT_URL: 'https://github.com/user/youtube-dark-cyber-x/issues',
    FEATURE_REQUEST_URL: 'https://github.com/user/youtube-dark-cyber-x/discussions'
} as const;

/**
 * معلومات الترخيص
 * License information
 */
export const LICENSE_INFO = {
    TYPE: 'MIT',
    YEAR: '2024',
    HOLDER: 'AI Assistant',
    URL: 'https://opensource.org/licenses/MIT',
    TEXT: `MIT License

Copyright (c) 2024 AI Assistant

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.`
} as const;

/**
 * معلومات النظام
 * System information
 */
export const SYSTEM_INFO = {
    PLATFORM: process.platform,
    ARCHITECTURE: process.arch,
    NODE_VERSION: process.version,
    ELECTRON_VERSION: process.versions.electron || 'unknown',
    CHROME_VERSION: process.versions.chrome || 'unknown',
    V8_VERSION: process.versions.v8 || 'unknown',
    MINIMUM_NODE_VERSION: '16.0.0',
    MINIMUM_ELECTRON_VERSION: '20.0.0',
    SUPPORTED_PLATFORMS: ['win32', 'darwin', 'linux'] as const,
    SUPPORTED_ARCHITECTURES: ['x64', 'arm64'] as const
} as const;

/**
 * معلومات البناء
 * Build information
 */
export const BUILD_INFO = {
    DATE: new Date().toISOString(),
    TIMESTAMP: Date.now(),
    ENVIRONMENT: process.env.NODE_ENV || 'development',
    DEBUG: process.env.DEBUG === 'true',
    PRODUCTION: process.env.NODE_ENV === 'production',
    DEVELOPMENT: process.env.NODE_ENV === 'development',
    TEST: process.env.NODE_ENV === 'test',
    BUILD_NUMBER: process.env.BUILD_NUMBER || '1',
    COMMIT_HASH: process.env.COMMIT_HASH || 'unknown',
    BRANCH: process.env.BRANCH || 'main',
    CI: process.env.CI === 'true',
    BUILDER: process.env.USER || process.env.USERNAME || 'unknown'
} as const;

/**
 * معلومات الأمان
 * Security information
 */
export const SECURITY_INFO = {
    CONTENT_SECURITY_POLICY: "default-src 'self'; script-src 'self' 'unsafe-inline' https://www.youtube.com https://www.google.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; media-src 'self' https:; connect-src 'self' https:;",
    PERMISSIONS_POLICY: "camera=(), microphone=(), geolocation=(), payment=()",
    REFERRER_POLICY: "strict-origin-when-cross-origin",
    X_FRAME_OPTIONS: "DENY",
    X_CONTENT_TYPE_OPTIONS: "nosniff",
    X_XSS_PROTECTION: "1; mode=block",
    STRICT_TRANSPORT_SECURITY: "max-age=31536000; includeSubDomains",
    FEATURE_POLICY: "autoplay 'self'; camera 'none'; microphone 'none'; geolocation 'none'",
    SANDBOX_ATTRIBUTES: "allow-scripts allow-same-origin allow-forms allow-popups allow-presentation",
    TRUSTED_DOMAINS: [
        'youtube.com',
        'www.youtube.com',
        'googlevideo.com',
        'ytimg.com',
        'gstatic.com',
        'googleapis.com'
    ] as const
} as const;

/**
 * معلومات الأداء
 * Performance information
 */
export const PERFORMANCE_INFO = {
    MEMORY_LIMIT: 512 * 1024 * 1024, // 512MB
    CPU_LIMIT: 80, // 80% CPU usage
    NETWORK_TIMEOUT: 30000, // 30 seconds
    CACHE_SIZE_LIMIT: 100 * 1024 * 1024, // 100MB
    LOG_SIZE_LIMIT: 10 * 1024 * 1024, // 10MB
    MAX_LOG_FILES: 5,
    GARBAGE_COLLECTION_INTERVAL: 60000, // 1 minute
    MEMORY_CHECK_INTERVAL: 30000, // 30 seconds
    PERFORMANCE_MONITORING: true,
    METRICS_COLLECTION: true,
    PROFILING_ENABLED: false,
    HEAP_SNAPSHOT_ENABLED: false
} as const;

/**
 * معلومات التطبيق المجمعة
 * Combined application information
 */
export const COMBINED_APPLICATION_INFO = {
    ...APPLICATION_INFO,
    VERSION_DETAILS: VERSION_INFO,
    DEVELOPER: DEVELOPER_INFO,
    LICENSE: LICENSE_INFO,
    SYSTEM: SYSTEM_INFO,
    BUILD: BUILD_INFO,
    SECURITY: SECURITY_INFO,
    PERFORMANCE: PERFORMANCE_INFO
} as const;

/**
 * دالة للحصول على معلومات التطبيق الكاملة
 * Function to get complete application information
 * 
 * @returns معلومات التطبيق الكاملة
 */
export function getApplicationInfo(): typeof COMBINED_APPLICATION_INFO {
    return COMBINED_APPLICATION_INFO;
}

/**
 * دالة للحصول على معلومات الإصدار
 * Function to get version information
 * 
 * @returns معلومات الإصدار
 */
export function getVersionInfo(): typeof VERSION_INFO {
    return VERSION_INFO;
}

/**
 * دالة للحصول على معلومات النظام
 * Function to get system information
 * 
 * @returns معلومات النظام
 */
export function getSystemInfo(): typeof SYSTEM_INFO {
    return SYSTEM_INFO;
}

/**
 * دالة للحصول على معلومات البناء
 * Function to get build information
 * 
 * @returns معلومات البناء
 */
export function getBuildInfo(): typeof BUILD_INFO {
    return BUILD_INFO;
}

/**
 * دالة للتحقق من توافق النظام
 * Function to check system compatibility
 * 
 * @returns true إذا كان النظام متوافق
 */
export function isSystemCompatible(): boolean {
    const { SUPPORTED_PLATFORMS, SUPPORTED_ARCHITECTURES } = SYSTEM_INFO;
    return SUPPORTED_PLATFORMS.includes(process.platform as any) &&
           SUPPORTED_ARCHITECTURES.includes(process.arch as any);
}

/**
 * دالة للتحقق من بيئة التطوير
 * Function to check development environment
 * 
 * @returns true إذا كان في بيئة التطوير
 */
export function isDevelopment(): boolean {
    return BUILD_INFO.DEVELOPMENT;
}

/**
 * دالة للتحقق من بيئة الإنتاج
 * Function to check production environment
 * 
 * @returns true إذا كان في بيئة الإنتاج
 */
export function isProduction(): boolean {
    return BUILD_INFO.PRODUCTION;
}

/**
 * دالة للحصول على نص الترخيص
 * Function to get license text
 * 
 * @returns نص الترخيص
 */
export function getLicenseText(): string {
    return LICENSE_INFO.TEXT;
}
