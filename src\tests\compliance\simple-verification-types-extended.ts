/**
 * الأنواع الموسعة لأداة التحقق المبسطة
 * Simple verification extended types
 * 
 * هذا الملف يحتوي على تعريفات الأنواع الموسعة والمتقدمة
 * This file contains extended and advanced type definitions
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { IssueType, SeverityLevel } from './simple-verification-types-core';

/**
 * خيارات التحقق المتقدمة
 * Advanced verification options
 */
export interface AdvancedVerificationOptions {
    readonly enablePerformanceAnalysis: boolean;
    readonly enableSecurityScan: boolean;
    readonly enableComplexityAnalysis: boolean;
    readonly enableDependencyCheck: boolean;
    readonly enableTestCoverage: boolean;
    readonly customRules: CustomRule[];
    readonly reportFormat: ReportFormat;
    readonly outputPath?: string;
}

/**
 * قاعدة مخصصة
 * Custom rule
 */
export interface CustomRule {
    readonly id: string;
    readonly name: string;
    readonly description: string;
    readonly pattern: string;
    readonly severity: SeverityLevel;
    readonly enabled: boolean;
    readonly fileTypes: string[];
}

/**
 * تنسيق التقرير
 * Report format
 */
export enum ReportFormat {
    JSON = 'json',
    HTML = 'html',
    MARKDOWN = 'markdown',
    PDF = 'pdf',
    CONSOLE = 'console'
}

/**
 * نتيجة تحليل الأداء
 * Performance analysis result
 */
export interface PerformanceAnalysisResult {
    readonly score: number;
    readonly issues: PerformanceIssue[];
    readonly recommendations: string[];
    readonly metrics: PerformanceMetrics;
}

/**
 * مشكلة أداء
 * Performance issue
 */
export interface PerformanceIssue {
    readonly type: PerformanceIssueType;
    readonly severity: SeverityLevel;
    readonly message: string;
    readonly filePath: string;
    readonly lineNumber?: number;
    readonly impact: PerformanceImpact;
    readonly suggestion: string;
}

/**
 * نوع مشكلة الأداء
 * Performance issue type
 */
export enum PerformanceIssueType {
    LARGE_FUNCTION = 'large-function',
    DEEP_NESTING = 'deep-nesting',
    INEFFICIENT_LOOP = 'inefficient-loop',
    MEMORY_LEAK = 'memory-leak',
    BLOCKING_OPERATION = 'blocking-operation',
    UNNECESSARY_COMPUTATION = 'unnecessary-computation'
}

/**
 * تأثير الأداء
 * Performance impact
 */
export enum PerformanceImpact {
    LOW = 'low',
    MEDIUM = 'medium',
    HIGH = 'high',
    CRITICAL = 'critical'
}

/**
 * مقاييس الأداء
 * Performance metrics
 */
export interface PerformanceMetrics {
    readonly averageFunctionSize: number;
    readonly maxFunctionSize: number;
    readonly averageNestingDepth: number;
    readonly maxNestingDepth: number;
    readonly cyclomaticComplexity: number;
    readonly cognitiveComplexity: number;
    readonly maintainabilityIndex: number;
}

/**
 * نتيجة فحص الأمان
 * Security scan result
 */
export interface SecurityScanResult {
    readonly score: number;
    readonly vulnerabilities: SecurityVulnerability[];
    readonly recommendations: string[];
    readonly riskLevel: SecurityRiskLevel;
}

/**
 * ثغرة أمنية
 * Security vulnerability
 */
export interface SecurityVulnerability {
    readonly id: string;
    readonly type: SecurityVulnerabilityType;
    readonly severity: SeverityLevel;
    readonly title: string;
    readonly description: string;
    readonly filePath: string;
    readonly lineNumber?: number;
    readonly cweId?: string;
    readonly cvssScore?: number;
    readonly remediation: string;
}

/**
 * نوع الثغرة الأمنية
 * Security vulnerability type
 */
export enum SecurityVulnerabilityType {
    XSS = 'xss',
    SQL_INJECTION = 'sql-injection',
    CSRF = 'csrf',
    INSECURE_RANDOM = 'insecure-random',
    HARDCODED_SECRET = 'hardcoded-secret',
    WEAK_CRYPTO = 'weak-crypto',
    PATH_TRAVERSAL = 'path-traversal',
    COMMAND_INJECTION = 'command-injection'
}

/**
 * مستوى المخاطر الأمنية
 * Security risk level
 */
export enum SecurityRiskLevel {
    MINIMAL = 'minimal',
    LOW = 'low',
    MEDIUM = 'medium',
    HIGH = 'high',
    CRITICAL = 'critical'
}

/**
 * نتيجة تحليل التعقيد
 * Complexity analysis result
 */
export interface ComplexityAnalysisResult {
    readonly score: number;
    readonly issues: ComplexityIssue[];
    readonly recommendations: string[];
    readonly metrics: ComplexityMetrics;
}

/**
 * مشكلة تعقيد
 * Complexity issue
 */
export interface ComplexityIssue {
    readonly type: ComplexityIssueType;
    readonly severity: SeverityLevel;
    readonly message: string;
    readonly filePath: string;
    readonly functionName?: string;
    readonly lineNumber?: number;
    readonly complexityValue: number;
    readonly threshold: number;
    readonly suggestion: string;
}

/**
 * نوع مشكلة التعقيد
 * Complexity issue type
 */
export enum ComplexityIssueType {
    CYCLOMATIC = 'cyclomatic',
    COGNITIVE = 'cognitive',
    HALSTEAD = 'halstead',
    MAINTAINABILITY = 'maintainability',
    NESTING_DEPTH = 'nesting-depth',
    PARAMETER_COUNT = 'parameter-count'
}

/**
 * مقاييس التعقيد
 * Complexity metrics
 */
export interface ComplexityMetrics {
    readonly averageCyclomaticComplexity: number;
    readonly maxCyclomaticComplexity: number;
    readonly averageCognitiveComplexity: number;
    readonly maxCognitiveComplexity: number;
    readonly averageHalsteadComplexity: number;
    readonly averageMaintainabilityIndex: number;
    readonly averageNestingDepth: number;
    readonly maxNestingDepth: number;
}

/**
 * نتيجة فحص التبعيات
 * Dependency check result
 */
export interface DependencyCheckResult {
    readonly score: number;
    readonly issues: DependencyIssue[];
    readonly recommendations: string[];
    readonly outdatedPackages: OutdatedPackage[];
    readonly vulnerablePackages: VulnerablePackage[];
    readonly unusedPackages: string[];
}

/**
 * مشكلة تبعية
 * Dependency issue
 */
export interface DependencyIssue {
    readonly type: DependencyIssueType;
    readonly severity: SeverityLevel;
    readonly packageName: string;
    readonly currentVersion: string;
    readonly recommendedVersion?: string;
    readonly description: string;
    readonly remediation: string;
}

/**
 * نوع مشكلة التبعية
 * Dependency issue type
 */
export enum DependencyIssueType {
    OUTDATED = 'outdated',
    VULNERABLE = 'vulnerable',
    UNUSED = 'unused',
    INCOMPATIBLE = 'incompatible',
    DEPRECATED = 'deprecated'
}

/**
 * حزمة قديمة
 * Outdated package
 */
export interface OutdatedPackage {
    readonly name: string;
    readonly currentVersion: string;
    readonly latestVersion: string;
    readonly type: 'dependency' | 'devDependency';
}

/**
 * حزمة معرضة للخطر
 * Vulnerable package
 */
export interface VulnerablePackage {
    readonly name: string;
    readonly version: string;
    readonly vulnerabilities: SecurityVulnerability[];
    readonly patchedVersion?: string;
}
