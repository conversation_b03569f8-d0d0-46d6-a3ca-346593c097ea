/**
 * مسجل أخطاء YouTube
 * YouTube error logger
 * 
 * هذا الملف يحتوي على منطق تسجيل أخطاء YouTube
 * This file contains YouTube error logging logic
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ResourceManager } from '@shared/utils/resource-manager';
import { 
    YouTubeControllerErrorReport,
    YouTubeControllerErrorType,
    YOUTUBE_CONTROLLER_MESSAGES,
    YOUTUBE_CONTROLLER_CONSTANTS
} from './youtube-controller-config';

/**
 * فئة مسجل أخطاء YouTube
 * YouTube error logger class
 */
export class YouTubeErrorLogger {
    private readonly resourceManager: ResourceManager;
    private logBuffer: string[] = [];
    private lastFlushTime: Date = new Date();

    /**
     * منشئ مسجل الأخطاء
     * Error logger constructor
     * 
     * @param resourceManager - مدير الموارد
     */
    constructor(resourceManager: ResourceManager) {
        this.resourceManager = resourceManager;
        this.startPeriodicFlush();
    }

    /**
     * تسجيل خطأ
     * Log error
     * 
     * @param error - تقرير الخطأ
     */
    public logError(error: YouTubeControllerErrorReport): void {
        const logEntry = this.formatErrorLog(error);
        
        // إضافة للمخزن المؤقت
        this.logBuffer.push(logEntry);
        
        // طباعة في وحدة التحكم حسب مستوى الخطورة
        this.printToConsole(error, logEntry);
        
        // تنظيف المخزن المؤقت إذا امتلأ
        if (this.logBuffer.length >= YOUTUBE_CONTROLLER_CONSTANTS.MAX_LOG_BUFFER_SIZE) {
            this.flushLogs();
        }
    }

    /**
     * تنسيق سجل الخطأ
     * Format error log
     * 
     * @param error - تقرير الخطأ
     * @returns سجل منسق
     */
    private formatErrorLog(error: YouTubeControllerErrorReport): string {
        const timestamp = error.timestamp.toISOString();
        const errorTypeLabel = this.getErrorTypeLabel(error.errorType);
        
        let logEntry = `[${timestamp}] [${errorTypeLabel}] ${error.message}`;
        
        if (error.details && Object.keys(error.details).length > 0) {
            logEntry += ` | Details: ${JSON.stringify(error.details)}`;
        }
        
        if (error.stack) {
            logEntry += ` | Stack: ${error.stack}`;
        }
        
        return logEntry;
    }

    /**
     * الحصول على تسمية نوع الخطأ
     * Get error type label
     * 
     * @param errorType - نوع الخطأ
     * @returns تسمية نوع الخطأ
     */
    private getErrorTypeLabel(errorType: YouTubeControllerErrorType): string {
        switch (errorType) {
            case YouTubeControllerErrorType.INITIALIZATION_ERROR:
                return 'INIT';
            case YouTubeControllerErrorType.SETTINGS_APPLICATION_ERROR:
                return 'SETTINGS';
            case YouTubeControllerErrorType.VIDEO_QUALITY_ERROR:
                return 'QUALITY';
            case YouTubeControllerErrorType.DARK_MODE_ERROR:
                return 'DARK_MODE';
            case YouTubeControllerErrorType.AD_BLOCKER_ERROR:
                return 'AD_BLOCK';
            case YouTubeControllerErrorType.PERFORMANCE_ERROR:
                return 'PERF';
            case YouTubeControllerErrorType.SECURITY_ERROR:
                return 'SECURITY';
            case YouTubeControllerErrorType.CRITICAL_ERROR:
                return 'CRITICAL';
            case YouTubeControllerErrorType.UNKNOWN_ERROR:
            default:
                return 'UNKNOWN';
        }
    }

    /**
     * طباعة في وحدة التحكم
     * Print to console
     * 
     * @param error - تقرير الخطأ
     * @param logEntry - سجل الخطأ
     */
    private printToConsole(error: YouTubeControllerErrorReport, logEntry: string): void {
        switch (error.errorType) {
            case YouTubeControllerErrorType.CRITICAL_ERROR:
            case YouTubeControllerErrorType.SECURITY_ERROR:
                console.error('🚨', logEntry);
                break;
            
            case YouTubeControllerErrorType.PERFORMANCE_ERROR:
                console.warn('⚡', logEntry);
                break;
            
            case YouTubeControllerErrorType.INITIALIZATION_ERROR:
            case YouTubeControllerErrorType.SETTINGS_APPLICATION_ERROR:
                console.warn('⚠️', logEntry);
                break;
            
            default:
                console.log('ℹ️', logEntry);
                break;
        }
    }

    /**
     * بدء التنظيف الدوري
     * Start periodic flush
     */
    private startPeriodicFlush(): void {
        const flushInterval = YOUTUBE_CONTROLLER_CONSTANTS.LOG_FLUSH_INTERVAL || 30000;
        
        setInterval(() => {
            this.flushLogs();
        }, flushInterval);
    }

    /**
     * تنظيف السجلات
     * Flush logs
     */
    public flushLogs(): void {
        if (this.logBuffer.length === 0) {
            return;
        }

        try {
            // في بيئة Electron، يمكن حفظ السجلات في ملف
            const logContent = this.logBuffer.join('\n');
            
            // إرسال السجلات لعملية الرئيسية للحفظ
            if (typeof window !== 'undefined' && (window as any).electronAPI) {
                (window as any).electronAPI.saveErrorLogs(logContent);
            }
            
            // تنظيف المخزن المؤقت
            this.logBuffer = [];
            this.lastFlushTime = new Date();
            
        } catch (error) {
            console.error('فشل في تنظيف السجلات / Failed to flush logs:', error);
        }
    }

    /**
     * تسجيل رسالة معلوماتية
     * Log info message
     * 
     * @param message - الرسالة
     * @param details - تفاصيل إضافية
     */
    public logInfo(message: string, details?: Record<string, any>): void {
        const error: YouTubeControllerErrorReport = {
            errorType: YouTubeControllerErrorType.UNKNOWN_ERROR,
            message,
            timestamp: new Date(),
            details: details || {}
        };
        
        this.logError(error);
    }

    /**
     * تسجيل تحذير
     * Log warning
     * 
     * @param message - الرسالة
     * @param details - تفاصيل إضافية
     */
    public logWarning(message: string, details?: Record<string, any>): void {
        const error: YouTubeControllerErrorReport = {
            errorType: YouTubeControllerErrorType.PERFORMANCE_ERROR,
            message,
            timestamp: new Date(),
            details: details || {}
        };
        
        this.logError(error);
    }

    /**
     * تسجيل خطأ حرج
     * Log critical error
     * 
     * @param message - الرسالة
     * @param details - تفاصيل إضافية
     * @param stack - مكدس الاستدعاءات
     */
    public logCritical(message: string, details?: Record<string, any>, stack?: string): void {
        const error: YouTubeControllerErrorReport = {
            errorType: YouTubeControllerErrorType.CRITICAL_ERROR,
            message,
            timestamp: new Date(),
            details: details || {},
            stack
        };
        
        this.logError(error);
    }

    /**
     * الحصول على حجم المخزن المؤقت
     * Get buffer size
     * 
     * @returns حجم المخزن المؤقت
     */
    public getBufferSize(): number {
        return this.logBuffer.length;
    }

    /**
     * الحصول على وقت آخر تنظيف
     * Get last flush time
     * 
     * @returns وقت آخر تنظيف
     */
    public getLastFlushTime(): Date {
        return this.lastFlushTime;
    }

    /**
     * تنظيف الموارد
     * Cleanup resources
     */
    public cleanup(): void {
        this.flushLogs();
        this.logBuffer = [];
    }
}
