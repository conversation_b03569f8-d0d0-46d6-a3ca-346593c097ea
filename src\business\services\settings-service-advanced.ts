/**
 * العمليات المتقدمة لخدمة الإعدادات
 * Advanced settings service operations
 *
 * هذا الملف يجمع جميع العمليات المتقدمة من الملفات المتخصصة
 * This file aggregates all advanced operations from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// استيراد العمليات المتقدمة الأساسية
// Import advanced core operations
export { SettingsServiceAdvancedCore } from './settings-service-advanced-core';

// استيراد العمليات المتقدمة المعقدة
// Import advanced complex operations
export { SettingsServiceAdvancedComplex } from './settings-service-advanced-complex';

import { ResourceManager } from '@shared/utils/resource-manager';
import { SettingsBackupManager } from './settings-backup';
import {
    ExportOptions,
    ImportOptions,
    SettingsManagerConfig,
    SettingsStatistics
} from './settings-config';
import { SettingsServiceAdvancedComplex } from './settings-service-advanced-complex';
import { SettingsServiceAdvancedCore } from './settings-service-advanced-core';
import { SettingsServiceCore } from './settings-service-core';

/**
 * فئة العمليات المتقدمة لخدمة الإعدادات / Advanced settings service operations class
 */
export class SettingsServiceAdvanced {
    private readonly advancedCore: SettingsServiceAdvancedCore;
    private readonly advancedComplex: SettingsServiceAdvancedComplex;

    /**
     * منشئ العمليات المتقدمة / Advanced operations constructor
     */
    constructor(
        coreService: SettingsServiceCore,
        config: SettingsManagerConfig,
        backupManager: SettingsBackupManager,
        resourceManager: ResourceManager
    ) {
        this.advancedCore = new SettingsServiceAdvancedCore(coreService, config, backupManager, resourceManager);
        this.advancedComplex = new SettingsServiceAdvancedComplex(coreService, config, backupManager, resourceManager);
    }

    // العمليات المتقدمة الأساسية / Advanced core operations

    /**
     * تصدير الإعدادات / Export settings
     */
    public async exportSettings(filePath: string, options?: ExportOptions): Promise<boolean> {
        return await this.advancedCore.exportSettings(filePath, options);
    }

    /**
     * استيراد الإعدادات / Import settings
     */
    public async importSettings(filePath: string, options?: ImportOptions): Promise<boolean> {
        return await this.advancedCore.importSettings(filePath, options);
    }

    /**
     * إنشاء نسخة احتياطية تلقائية / Create automatic backup
     */
    public async createAutomaticBackup(): Promise<boolean> {
        return await this.advancedCore.createAutomaticBackup();
    }

    /**
     * استعادة من النسخة الاحتياطية / Restore from backup
     */
    public async restoreFromBackup(backupPath: string): Promise<boolean> {
        return await this.advancedCore.restoreFromBackup(backupPath);
    }

    /**
     * إعادة تحميل الإعدادات / Reload settings
     */
    public async reloadSettings(): Promise<boolean> {
        return await this.advancedCore.reloadSettings();
    }

    /**
     * تحسين الإعدادات / Optimize settings
     */
    public async optimizeSettings(): Promise<boolean> {
        return await this.advancedCore.optimizeSettings();
    }

    // العمليات المتقدمة المعقدة / Advanced complex operations

    /**
     * الحصول على إحصائيات الإعدادات / Get settings statistics
     */
    public async getSettingsStatistics(): Promise<SettingsStatistics> {
        return await this.advancedComplex.getSettingsStatistics();
    }

    /**
     * مزامنة الإعدادات / Sync settings
     */
    public async syncSettings(): Promise<boolean> {
        return await this.advancedComplex.syncSettings();
    }

    /**
     * تشغيل الحفظ التلقائي / Start auto save
     */
    public startAutoSave(intervalMs?: number): void {
        this.advancedComplex.startAutoSave(intervalMs);
    }

    /**
     * إيقاف الحفظ التلقائي / Stop auto save
     */
    public stopAutoSave(): void {
        this.advancedComplex.stopAutoSave();
    }

    /**
     * مراقبة تغييرات الإعدادات / Monitor settings changes
     */
    public startMonitoring(): void {
        this.advancedComplex.startMonitoring();
    }

    /**
     * تحليل استخدام الإعدادات / Analyze settings usage
     */
    public async analyzeUsage(): Promise<any> {
        return await this.advancedComplex.analyzeUsage();
    }

    /**
     * تطبيق التوصيات / Apply recommendations
     */
    public async applyRecommendations(): Promise<boolean> {
        return await this.advancedComplex.applyRecommendations();
    }

    /**
     * تنظيف الموارد / Cleanup resources
     */
    public cleanup(): void {
        this.advancedCore.cleanup();
        this.advancedComplex.cleanup();
    }
}
