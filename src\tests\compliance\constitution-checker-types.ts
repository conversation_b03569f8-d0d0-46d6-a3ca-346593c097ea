/**
 * أنواع فاحص الامتثال للدستور
 * Constitution checker types
 * 
 * هذا الملف يحتوي على تعريفات الأنواع لفاحص الامتثال
 * This file contains type definitions for constitution checker
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * مشكلة امتثال
 * Compliance issue
 */
export interface ComplianceIssue {
    readonly file: string;
    readonly line?: number;
    readonly rule: string;
    readonly severity: 'ERROR' | 'WARNING' | 'INFO';
    readonly message: string;
    readonly suggestion?: string;
}

/**
 * تقرير الامتثال
 * Compliance report
 */
export interface ComplianceReport {
    readonly totalFiles: number;
    readonly totalIssues: number;
    readonly errorCount: number;
    readonly warningCount: number;
    readonly infoCount: number;
    readonly issues: ComplianceIssue[];
    readonly complianceScore: number;
    readonly timestamp: Date;
}

/**
 * خيارات الفحص
 * Check options
 */
export interface CheckOptions {
    readonly includeWarnings: boolean;
    readonly includeInfo: boolean;
    readonly maxFileSize: number;
    readonly excludePatterns: string[];
    readonly includePatterns: string[];
}

/**
 * نتيجة فحص الملف
 * File check result
 */
export interface FileCheckResult {
    readonly filePath: string;
    readonly issues: ComplianceIssue[];
    readonly lineCount: number;
    readonly isCompliant: boolean;
}

/**
 * إحصائيات الفحص
 * Check statistics
 */
export interface CheckStatistics {
    readonly totalFilesChecked: number;
    readonly compliantFiles: number;
    readonly nonCompliantFiles: number;
    readonly averageFileSize: number;
    readonly largestFile: string;
    readonly smallestFile: string;
    readonly processingTime: number;
}

/**
 * قواعد الامتثال
 * Compliance rules
 */
export interface ComplianceRules {
    readonly maxFileSize: number;
    readonly maxFunctionSize: number;
    readonly maxClassSize: number;
    readonly requiredDocumentation: boolean;
    readonly namingConventions: NamingConventions;
    readonly codeQualityRules: CodeQualityRules;
}

/**
 * قواعد التسمية
 * Naming conventions
 */
export interface NamingConventions {
    readonly fileNaming: 'kebab-case' | 'camelCase' | 'PascalCase';
    readonly variableNaming: 'camelCase' | 'snake_case';
    readonly functionNaming: 'camelCase' | 'snake_case';
    readonly classNaming: 'PascalCase' | 'camelCase';
    readonly constantNaming: 'UPPER_CASE' | 'camelCase';
}

/**
 * قواعد جودة الكود
 * Code quality rules
 */
export interface CodeQualityRules {
    readonly noAnyType: boolean;
    readonly requireTypeAnnotations: boolean;
    readonly noMagicNumbers: boolean;
    readonly noHardcodedValues: boolean;
    readonly requireErrorHandling: boolean;
    readonly maxComplexity: number;
}

/**
 * سياق الفحص
 * Check context
 */
export interface CheckContext {
    readonly projectRoot: string;
    readonly currentFile: string;
    readonly rules: ComplianceRules;
    readonly options: CheckOptions;
    readonly statistics: CheckStatistics;
}

/**
 * نتيجة فحص القاعدة
 * Rule check result
 */
export interface RuleCheckResult {
    readonly ruleName: string;
    readonly passed: boolean;
    readonly issues: ComplianceIssue[];
    readonly executionTime: number;
}

/**
 * معلومات الملف
 * File information
 */
export interface FileInfo {
    readonly path: string;
    readonly size: number;
    readonly lineCount: number;
    readonly extension: string;
    readonly isTypeScript: boolean;
    readonly isJavaScript: boolean;
    readonly isTestFile: boolean;
}

/**
 * تكوين الفاحص
 * Checker configuration
 */
export interface CheckerConfiguration {
    readonly rules: ComplianceRules;
    readonly options: CheckOptions;
    readonly outputFormat: 'json' | 'text' | 'html';
    readonly outputFile?: string;
    readonly verbose: boolean;
}

/**
 * حالة الفحص
 * Check state
 */
export interface CheckState {
    readonly isRunning: boolean;
    readonly currentFile: string;
    readonly progress: number;
    readonly startTime: Date;
    readonly estimatedEndTime?: Date;
}

/**
 * مرشح الملفات
 * File filter
 */
export interface FileFilter {
    readonly extensions: string[];
    readonly excludeDirectories: string[];
    readonly includeDirectories: string[];
    readonly maxSize: number;
    readonly minSize: number;
}

/**
 * معالج النتائج
 * Result processor
 */
export interface ResultProcessor {
    processReport(report: ComplianceReport): Promise<void>;
    generateSummary(report: ComplianceReport): string;
    exportResults(report: ComplianceReport, format: string): Promise<string>;
}

/**
 * مدير القواعد
 * Rules manager
 */
export interface RulesManager {
    loadRules(configPath: string): Promise<ComplianceRules>;
    validateRules(rules: ComplianceRules): ValidationResult;
    applyRule(rule: string, context: CheckContext): Promise<RuleCheckResult>;
}

/**
 * مراقب التقدم
 * Progress monitor
 */
export interface ProgressMonitor {
    onStart(totalFiles: number): void;
    onFileProcessed(filePath: string, progress: number): void;
    onComplete(report: ComplianceReport): void;
    onError(error: Error): void;
}

/**
 * مُصدر التقارير
 * Report exporter
 */
export interface ReportExporter {
    exportToJson(report: ComplianceReport): Promise<string>;
    exportToHtml(report: ComplianceReport): Promise<string>;
    exportToText(report: ComplianceReport): Promise<string>;
    exportToCsv(report: ComplianceReport): Promise<string>;
}

/**
 * محلل الكود
 * Code analyzer
 */
export interface CodeAnalyzer {
    analyzeFile(filePath: string): Promise<FileCheckResult>;
    checkSyntax(content: string): Promise<ComplianceIssue[]>;
    checkComplexity(content: string): Promise<ComplianceIssue[]>;
    checkDocumentation(content: string): Promise<ComplianceIssue[]>;
}

/**
 * مدقق التسمية
 * Naming checker
 */
export interface NamingChecker {
    checkFileNaming(filePath: string): ComplianceIssue[];
    checkVariableNaming(content: string): ComplianceIssue[];
    checkFunctionNaming(content: string): ComplianceIssue[];
    checkClassNaming(content: string): ComplianceIssue[];
}

/**
 * مدقق الحجم
 * Size checker
 */
export interface SizeChecker {
    checkFileSize(filePath: string): ComplianceIssue[];
    checkFunctionSize(content: string): ComplianceIssue[];
    checkClassSize(content: string): ComplianceIssue[];
    checkLineLength(content: string): ComplianceIssue[];
}

/**
 * مدقق التوثيق
 * Documentation checker
 */
export interface DocumentationChecker {
    checkJSDoc(content: string): ComplianceIssue[];
    checkComments(content: string): ComplianceIssue[];
    checkReadme(projectRoot: string): ComplianceIssue[];
    checkTypeDefinitions(content: string): ComplianceIssue[];
}
