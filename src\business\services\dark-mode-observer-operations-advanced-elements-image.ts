/**
 * معالجة عناصر الصور المتقدمة لمراقب الوضع المظلم
 * Advanced image element processing for dark mode observer
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// استيراد الفئات المتخصصة / Import specialized classes
import { DarkModeObserverOperationsAdvancedElementsImageCore } from './dark-mode-observer-operations-advanced-elements-image-core';
import { DarkModeObserverOperationsAdvancedElementsImageEvents } from './dark-mode-observer-operations-advanced-elements-image-events';
import { DarkModeObserverOperationsAdvancedElementsImageFilters } from './dark-mode-observer-operations-advanced-elements-image-filters';
import { DarkModeObserverOperationsAdvancedElementsCanvas } from './dark-mode-observer-operations-advanced-elements-canvas';
import { DarkModeConfig } from './dark-mode-config';

/**
 * فئة معالجة عناصر الصور المتقدمة
 * Advanced image element processing class
 */
export class DarkModeObserverOperationsAdvancedElementsImage {

    /** معالجة عنصر الصورة / Process image element */
    public static processImageElement(element: HTMLImageElement, config: DarkModeConfig): void {
        try {
            // المعالجة الأساسية
            DarkModeObserverOperationsAdvancedElementsImageCore.processImageElement(element, config);
            
            // كشف نوع الصورة
            const imageType = DarkModeObserverOperationsAdvancedElementsImageCore.detectImageType(element);
            
            // تطبيق الفلاتر
            DarkModeObserverOperationsAdvancedElementsImageFilters.applyTypeSpecificFilters(element, imageType);
            
            // معالجة الأحداث
            DarkModeObserverOperationsAdvancedElementsImageEvents.handleImageLoading(element, config);
            DarkModeObserverOperationsAdvancedElementsImageEvents.handleImageErrors(element, config);
            DarkModeObserverOperationsAdvancedElementsImageEvents.addInteractionEffects(element, imageType);
            
        } catch (error) {
            console.error('خطأ في معالجة عنصر الصورة:', error);
        }
    }

    /** معالجة عنصر Canvas / Process canvas element */
    public static processCanvasElement(element: HTMLCanvasElement, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsCanvas.processCanvasElement(element, config);
    }

    /** تطبيق فلاتر الصورة / Apply image filters */
    public static applyImageFilters(element: HTMLImageElement, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsImageFilters.applyImageFilters(element, config);
    }

    /** كشف نوع الصورة / Detect image type */
    public static detectImageType(element: HTMLImageElement): string {
        return DarkModeObserverOperationsAdvancedElementsImageCore.detectImageType(element);
    }

    /** إضافة صورة بديلة / Add placeholder image */
    public static addPlaceholderImage(element: HTMLImageElement, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsImageCore.addPlaceholderImage(element, config);
    }
}
