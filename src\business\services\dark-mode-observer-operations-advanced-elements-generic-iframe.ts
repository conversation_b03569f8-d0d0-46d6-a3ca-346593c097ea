/**
 * معالجة عناصر iframe في الوضع المظلم
 * iframe element processing for dark mode
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// استيراد الفئات المتخصصة / Import specialized classes
import { DarkModeConfig } from './dark-mode-config';
import { DarkModeObserverOperationsAdvancedElementsGenericIframeContent } from './dark-mode-observer-operations-advanced-elements-generic-iframe-content';
import { DarkModeObserverOperationsAdvancedElementsGenericIframeCore } from './dark-mode-observer-operations-advanced-elements-generic-iframe-core';

/**
 * فئة معالجة عناصر iframe
 * iframe element processing class
 */
export class DarkModeObserverOperationsAdvancedElementsGenericIframe {

    /** معالجة عنصر iframe / Process iframe element */
    public static processIframeElement(element: HTMLIFrameElement, config: DarkModeConfig): void {
        try {
            // المعالجة الأساسية
            DarkModeObserverOperationsAdvancedElementsGenericIframeCore.processIframeElement(element, config);

            // معالجة المحتوى
            DarkModeObserverOperationsAdvancedElementsGenericIframeContent.processIframeContent(element, config);

        } catch (error) {
            console.error('خطأ في معالجة عنصر iframe:', error);
        }
    }

    /** تطبيق الأنماط الأساسية / Apply base styles */
    public static applyBaseStyles(element: HTMLIFrameElement): void {
        DarkModeObserverOperationsAdvancedElementsGenericIframeCore.applyBaseStyles(element);
    }

    /** كشف نوع iframe / Detect iframe type */
    public static detectIframeType(element: HTMLIFrameElement): string {
        return DarkModeObserverOperationsAdvancedElementsGenericIframeCore.detectIframeType(element);
    }

    /** تطبيق أنماط خاصة حسب النوع / Apply type specific styles */
    public static applyTypeSpecificStyles(element: HTMLIFrameElement, type: string, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsGenericIframeCore.applyTypeSpecificStyles(element, type, config);
    }

    /** إضافة عنصر بديل / Add placeholder */
    public static addIframePlaceholder(element: HTMLIFrameElement, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsGenericIframeCore.addIframePlaceholder(element, config);
    }

    /** معالجة محتوى iframe / Process iframe content */
    public static processIframeContent(element: HTMLIFrameElement, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsGenericIframeContent.processIframeContent(element, config);
    }

    /** التحقق من إمكانية الوصول للمحتوى / Check if content is accessible */
    public static canAccessIframeContent(element: HTMLIFrameElement): boolean {
        return DarkModeObserverOperationsAdvancedElementsGenericIframeContent.canAccessIframeContent(element);
    }

    /** تطبيق الوضع المظلم على المحتوى / Apply dark mode to content */
    public static applyDarkModeToContent(document: Document, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsGenericIframeContent.applyDarkModeToContent(document, config);
    }

    /** حقن CSS في المستند / Inject CSS into document */
    public static injectCSS(document: Document, css: string): void {
        DarkModeObserverOperationsAdvancedElementsGenericIframeContent.injectCSS(document, css);
    }

    /** مراقبة تغييرات المحتوى / Observe content changes */
    public static observeContentChanges(document: Document, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsGenericIframeContent.observeContentChanges(document, config);
    }
}