{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020", "DOM"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "baseUrl": "./src", "paths": {"@presentation/*": ["presentation/*"], "@business/*": ["business/*"], "@data/*": ["data/*"], "@shared/*": ["shared/*"], "@infrastructure/*": ["infrastructure/*"], "@preload/*": ["preload/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests/**/*.test.ts"], "ts-node": {"esm": true}}