/**
 * طبقة الأمان الموحدة
 * Unified security layer
 *
 * هذا الملف يجمع جميع مكونات طبقة الأمان من الملفات المتخصصة
 * This file aggregates all security layer components from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// استيراد تكوين وأنواع طبقة الأمان
// Import security layer configuration and types
export {
    AlertMethod, DEFAULT_ALERT_SETTINGS, DEFAULT_PROTECTION_SETTINGS, DEFAULT_REPORTING_CONFIG, DEFAULT_SCAN_SETTINGS, DEFAULT_SECURITY_LAYER_CONFIG, ProtectionSettings, SECURITY_LAYER_LIMITS, SECURITY_MESSAGES, SECURITY_PRIORITIES, SecurityAlertSettings, SecurityLayerConfig, SecurityLayerState,
    SecurityLayerStatus, SecurityPerformanceMetrics, SecurityReport, SecurityReportingConfig, SecurityScanDetails, SecurityScanResult, SecurityScanSettings, SecurityStatistics, ThreatBreakdown, ThreatInfo,
    ThreatType
} from './security-layer-config';

// استيراد عمليات طبقة الأمان
// Import security layer operations
export { SecurityLayerOperations } from './security-layer-operations';

import { SECURITY_LIMITS } from '@shared/constants';
import { SecurityConfig, ValidationResult } from '@shared/types';
import { ResourceManager } from '@shared/utils/resource-manager';
import { AdBlockerService } from './ad-blocker';
import { SecurityLayerConfig, SecurityLayerOperations } from './security-layer-config';
import { SecurityManager } from './security-manager';

/**
 * طبقة الأمان الموحدة
 * Unified security layer class
 */
export class SecurityLayer {
    private readonly operations: SecurityLayerOperations;

    /**
     * منشئ طبقة الأمان / Security layer constructor
     */
    constructor(config: SecurityLayerConfig, resourceManager?: ResourceManager) {
        const rm = resourceManager || new ResourceManager();
        const securityConfig: SecurityConfig = {
            strictDomainValidation: config.strictMode,
            enableThreatReporting: config.enableThreatReporting,
            maxThreatReports: SECURITY_LIMITS.MAX_THREAT_REPORTS,
            blockSuspiciousRequests: config.enableRequestValidation,
            sanitizeContent: config.enableContentSanitization
        };

        const securityManager = new SecurityManager(securityConfig);
        const adBlockerService = new AdBlockerService(rm);

        this.operations = new SecurityLayerOperations(
            config,
            adBlockerService,
            securityManager,
            rm
        );
    }

    /**
     * تهيئة طبقة الأمان / Initialize security layer
     */
    public async initialize(): Promise<ValidationResult> {
        const success = await this.operations.initialize();
        return {
            isValid: success,
            errors: success ? [] : [{ field: 'init', message: 'فشل التهيئة', code: 'INIT_FAILED' }]
        };
    }

    /**
     * فحص أمان الطلب / Validate request security
     */
    public async validateRequest(url: string): Promise<ValidationResult> {
        const result = await this.operations.performSecurityScan(url);
        return {
            isValid: result.success,
            errors: result.success ? [] : [{ field: 'url', message: 'طلب غير آمن', code: 'UNSAFE_REQUEST' }]
        };
    }

    /**
     * تنظيف المحتوى / Sanitize content
     */
    public async sanitizeContent(content: string): Promise<string> {
        return await this.operations.sanitizeContent(content);
    }

    /**
     * حظر التهديد / Block threat
     */
    public async blockThreat(threatInfo: any): Promise<boolean> {
        return await this.operations.blockThreat(threatInfo);
    }

    /**
     * الحصول على الحالة / Get state
     */
    public getState(): any {
        return this.operations.getState();
    }

    /**
     * الحصول على الإحصائيات / Get statistics
     */
    public getStatistics(): any {
        return this.operations.getStatistics();
    }

    /**
     * تنظيف الموارد / Cleanup resources
     */
    public cleanup(): void {
        this.operations.cleanup();
    }

}
