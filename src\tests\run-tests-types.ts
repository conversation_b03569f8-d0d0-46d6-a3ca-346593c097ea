/**
 * أنواع مشغل الاختبارات
 * Test runner types
 * 
 * هذا الملف يحتوي على تعريفات الأنواع لمشغل الاختبارات
 * This file contains type definitions for test runner
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * نتيجة اختبار واحد
 * Single test result
 */
export interface TestResult {
    readonly testSuite: string;
    readonly passed: number;
    readonly failed: number;
    readonly skipped: number;
    readonly coverage: number;
    readonly duration: number;
    readonly errors: string[];
}

/**
 * تقرير الاختبارات الشامل
 * Comprehensive test report
 */
export interface TestReport {
    readonly totalTests: number;
    readonly totalPassed: number;
    readonly totalFailed: number;
    readonly totalSkipped: number;
    readonly overallCoverage: number;
    readonly totalDuration: number;
    readonly results: TestResult[];
    readonly timestamp: Date;
}

/**
 * إعدادات مشغل الاختبارات
 * Test runner configuration
 */
export interface TestRunnerConfig {
    readonly projectRoot: string;
    readonly coverageThreshold: number;
    readonly testPattern: string;
    readonly outputDir: string;
    readonly verbose: boolean;
    readonly parallel: boolean;
    readonly maxWorkers: number;
    readonly timeout: number;
}

/**
 * خيارات تشغيل الاختبارات
 * Test execution options
 */
export interface TestExecutionOptions {
    readonly pattern?: string;
    readonly coverage?: boolean;
    readonly watch?: boolean;
    readonly verbose?: boolean;
    readonly bail?: boolean;
    readonly updateSnapshots?: boolean;
    readonly silent?: boolean;
    readonly maxWorkers?: number;
    readonly testTimeout?: number;
}

/**
 * نتيجة تشغيل مجموعة اختبارات
 * Test suite execution result
 */
export interface TestSuiteResult {
    readonly suiteName: string;
    readonly testResults: TestResult[];
    readonly summary: {
        readonly total: number;
        readonly passed: number;
        readonly failed: number;
        readonly skipped: number;
        readonly duration: number;
        readonly coverage: number;
    };
    readonly errors: string[];
    readonly warnings: string[];
}

/**
 * إحصائيات التغطية
 * Coverage statistics
 */
export interface CoverageStats {
    readonly lines: {
        readonly total: number;
        readonly covered: number;
        readonly percentage: number;
    };
    readonly functions: {
        readonly total: number;
        readonly covered: number;
        readonly percentage: number;
    };
    readonly branches: {
        readonly total: number;
        readonly covered: number;
        readonly percentage: number;
    };
    readonly statements: {
        readonly total: number;
        readonly covered: number;
        readonly percentage: number;
    };
}

/**
 * تقرير التغطية المفصل
 * Detailed coverage report
 */
export interface CoverageReport {
    readonly overall: CoverageStats;
    readonly files: Record<string, CoverageStats>;
    readonly uncoveredLines: Record<string, number[]>;
    readonly threshold: {
        readonly lines: number;
        readonly functions: number;
        readonly branches: number;
        readonly statements: number;
    };
    readonly passed: boolean;
}

/**
 * معلومات البيئة
 * Environment information
 */
export interface EnvironmentInfo {
    readonly nodeVersion: string;
    readonly npmVersion: string;
    readonly platform: string;
    readonly arch: string;
    readonly memory: {
        readonly total: number;
        readonly free: number;
        readonly used: number;
    };
    readonly cpu: {
        readonly model: string;
        readonly cores: number;
        readonly speed: number;
    };
}

/**
 * تقرير الأداء
 * Performance report
 */
export interface PerformanceReport {
    readonly testExecutionTime: number;
    readonly setupTime: number;
    readonly teardownTime: number;
    readonly memoryUsage: {
        readonly peak: number;
        readonly average: number;
        readonly final: number;
    };
    readonly cpuUsage: {
        readonly peak: number;
        readonly average: number;
    };
    readonly slowestTests: Array<{
        readonly name: string;
        readonly duration: number;
        readonly file: string;
    }>;
}

/**
 * خيارات التقرير
 * Report options
 */
export interface ReportOptions {
    readonly format: 'json' | 'html' | 'xml' | 'text';
    readonly outputFile?: string;
    readonly includeDetails: boolean;
    readonly includeCoverage: boolean;
    readonly includePerformance: boolean;
    readonly includeEnvironment: boolean;
    readonly minify: boolean;
}

/**
 * حالة مشغل الاختبارات
 * Test runner state
 */
export interface TestRunnerState {
    readonly isRunning: boolean;
    readonly currentSuite?: string;
    readonly currentTest?: string;
    readonly progress: {
        readonly completed: number;
        readonly total: number;
        readonly percentage: number;
    };
    readonly startTime: Date;
    readonly estimatedEndTime?: Date;
}

/**
 * إعدادات Jest
 * Jest configuration
 */
export interface JestConfig {
    readonly preset?: string;
    readonly testEnvironment: string;
    readonly setupFilesAfterEnv: string[];
    readonly testMatch: string[];
    readonly collectCoverageFrom: string[];
    readonly coverageDirectory: string;
    readonly coverageReporters: string[];
    readonly coverageThreshold: Record<string, {
        readonly branches: number;
        readonly functions: number;
        readonly lines: number;
        readonly statements: number;
    }>;
    readonly moduleNameMapping: Record<string, string>;
    readonly transform: Record<string, string>;
    readonly testTimeout: number;
    readonly maxWorkers: number | string;
    readonly verbose: boolean;
    readonly silent: boolean;
}

/**
 * ثوابت مشغل الاختبارات
 * Test runner constants
 */
export const TEST_RUNNER_CONSTANTS = {
    DEFAULT_COVERAGE_THRESHOLD: 80,
    DEFAULT_TEST_TIMEOUT: 30000,
    DEFAULT_MAX_WORKERS: 4,
    DEFAULT_OUTPUT_DIR: 'test-results',
    DEFAULT_TEST_PATTERN: '**/*.test.ts',
    PERFORMANCE_THRESHOLD: {
        SLOW_TEST_DURATION: 5000,
        MEMORY_USAGE_LIMIT: 100 * 1024 * 1024, // 100MB
        CPU_USAGE_LIMIT: 80 // 80%
    }
} as const;

/**
 * رسائل مشغل الاختبارات
 * Test runner messages
 */
export const TEST_RUNNER_MESSAGES = {
    SUCCESS: {
        ALL_TESTS_PASSED: 'جميع الاختبارات نجحت / All tests passed',
        COVERAGE_MET: 'تم تحقيق الحد الأدنى للتغطية / Coverage threshold met',
        REPORT_GENERATED: 'تم إنشاء التقرير بنجاح / Report generated successfully'
    },
    ERROR: {
        TESTS_FAILED: 'فشلت بعض الاختبارات / Some tests failed',
        COVERAGE_NOT_MET: 'لم يتم تحقيق الحد الأدنى للتغطية / Coverage threshold not met',
        SETUP_FAILED: 'فشل في إعداد الاختبارات / Test setup failed',
        EXECUTION_FAILED: 'فشل في تشغيل الاختبارات / Test execution failed'
    },
    INFO: {
        STARTING_TESTS: 'بدء تشغيل الاختبارات / Starting tests',
        RUNNING_SUITE: 'تشغيل مجموعة اختبارات / Running test suite',
        GENERATING_REPORT: 'إنشاء التقرير / Generating report',
        CLEANUP: 'تنظيف الموارد / Cleaning up resources'
    }
} as const;

/**
 * البيانات الافتراضية لمشغل الاختبارات
 * Default test runner data
 */
export const DEFAULT_TEST_RUNNER_CONFIG: TestRunnerConfig = {
    projectRoot: process.cwd(),
    coverageThreshold: TEST_RUNNER_CONSTANTS.DEFAULT_COVERAGE_THRESHOLD,
    testPattern: TEST_RUNNER_CONSTANTS.DEFAULT_TEST_PATTERN,
    outputDir: TEST_RUNNER_CONSTANTS.DEFAULT_OUTPUT_DIR,
    verbose: false,
    parallel: true,
    maxWorkers: TEST_RUNNER_CONSTANTS.DEFAULT_MAX_WORKERS,
    timeout: TEST_RUNNER_CONSTANTS.DEFAULT_TEST_TIMEOUT
};

/**
 * خيارات التشغيل الافتراضية
 * Default execution options
 */
export const DEFAULT_EXECUTION_OPTIONS: TestExecutionOptions = {
    coverage: true,
    verbose: false,
    bail: false,
    updateSnapshots: false,
    silent: false,
    maxWorkers: TEST_RUNNER_CONSTANTS.DEFAULT_MAX_WORKERS,
    testTimeout: TEST_RUNNER_CONSTANTS.DEFAULT_TEST_TIMEOUT
};
