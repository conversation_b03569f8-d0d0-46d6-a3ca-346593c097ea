/**
 * معلومات الملفات الأساسية الجوهرية
 * Core basic file information operations
 * 
 * هذا الملف يحتوي على عمليات الحصول على معلومات الملفات الأساسية الجوهرية
 * This file contains core basic file information operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import * as fs from 'fs';
import * as path from 'path';

/**
 * فئة معلومات الملفات الأساسية الجوهرية
 * Core basic file information class
 */
export class SimpleVerificationCoreUtilsFileOperationsBasicInfoCore {

    /**
     * الحصول على معلومات الملف الأساسية
     * Get basic file information
     */
    public static getBasicFileInfo(filePath: string): {
        exists: boolean;
        size: number;
        extension: string;
        basename: string;
        directory: string;
    } {
        try {
            if (!fs.existsSync(filePath)) {
                return {
                    exists: false,
                    size: 0,
                    extension: '',
                    basename: '',
                    directory: ''
                };
            }

            const stats = fs.statSync(filePath);

            return {
                exists: true,
                size: stats.size,
                extension: path.extname(filePath),
                basename: path.basename(filePath),
                directory: path.dirname(filePath)
            };

        } catch (error) {
            return {
                exists: false,
                size: 0,
                extension: '',
                basename: '',
                directory: ''
            };
        }
    }

    /**
     * فحص نوع الملف
     * Check file type
     */
    public static getFileType(filePath: string): {
        isFile: boolean;
        isDirectory: boolean;
        isSymbolicLink: boolean;
        type: string;
    } {
        try {
            if (!fs.existsSync(filePath)) {
                return {
                    isFile: false,
                    isDirectory: false,
                    isSymbolicLink: false,
                    type: 'not-exists'
                };
            }

            const stats = fs.statSync(filePath);

            return {
                isFile: stats.isFile(),
                isDirectory: stats.isDirectory(),
                isSymbolicLink: stats.isSymbolicLink(),
                type: stats.isFile() ? 'file' : stats.isDirectory() ? 'directory' : 'other'
            };

        } catch (error) {
            return {
                isFile: false,
                isDirectory: false,
                isSymbolicLink: false,
                type: 'error'
            };
        }
    }

    /**
     * فحص الصلاحيات الأساسية
     * Check basic permissions
     */
    public static getBasicPermissions(filePath: string): {
        isReadable: boolean;
        isWritable: boolean;
        isExecutable: boolean;
    } {
        try {
            if (!fs.existsSync(filePath)) {
                return {
                    isReadable: false,
                    isWritable: false,
                    isExecutable: false
                };
            }

            let isReadable = false;
            let isWritable = false;
            let isExecutable = false;

            try {
                fs.accessSync(filePath, fs.constants.R_OK);
                isReadable = true;
            } catch (error) {
                // الملف غير قابل للقراءة
            }

            try {
                fs.accessSync(filePath, fs.constants.W_OK);
                isWritable = true;
            } catch (error) {
                // الملف غير قابل للكتابة
            }

            try {
                fs.accessSync(filePath, fs.constants.X_OK);
                isExecutable = true;
            } catch (error) {
                // الملف غير قابل للتنفيذ
            }

            return {
                isReadable,
                isWritable,
                isExecutable
            };

        } catch (error) {
            return {
                isReadable: false,
                isWritable: false,
                isExecutable: false
            };
        }
    }

    /**
     * الحصول على التواريخ الأساسية
     * Get basic dates
     */
    public static getBasicDates(filePath: string): {
        modificationDate: Date | null;
        creationDate: Date | null;
        accessDate: Date | null;
    } {
        try {
            if (!fs.existsSync(filePath)) {
                return {
                    modificationDate: null,
                    creationDate: null,
                    accessDate: null
                };
            }

            const stats = fs.statSync(filePath);

            return {
                modificationDate: stats.mtime,
                creationDate: stats.birthtime,
                accessDate: stats.atime
            };

        } catch (error) {
            return {
                modificationDate: null,
                creationDate: null,
                accessDate: null
            };
        }
    }
}
