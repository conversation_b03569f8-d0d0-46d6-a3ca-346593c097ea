/**
 * معالج أحداث الإعدادات
 * Settings event handler
 * 
 * هذا الملف يحتوي على منطق معالجة أحداث الإعدادات
 * This file contains settings event handling logic
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ApplicationConfig } from '@shared/types';
import { SettingsUIComponents } from './settings-ui-components';

/**
 * فئة معالج أحداث الإعدادات
 * Settings event handler class
 */
export class SettingsEventHandler {
    private readonly uiComponents: SettingsUIComponents;
    private isLoading: boolean = false;

    /**
     * منشئ معالج الأحداث
     * Event handler constructor
     * 
     * @param uiComponents - مكونات واجهة المستخدم
     */
    constructor(uiComponents: SettingsUIComponents) {
        this.uiComponents = uiComponents;
    }

    /**
     * إعداد مستمعي الأحداث
     * Setup event listeners
     */
    public setupEventListeners(): void {
        this.setupSaveButtonListener();
        this.setupResetButtonListener();
        this.setupCancelButtonListener();
        this.setupFormChangeListener();
        this.setupFormSubmitListener();
    }

    /**
     * إعداد مستمع زر الحفظ
     * Setup save button listener
     */
    private setupSaveButtonListener(): void {
        this.uiComponents.addButtonEventListener('save', async (event: Event) => {
            event.preventDefault();
            await this.handleSaveSettings();
        });
    }

    /**
     * إعداد مستمع زر الإعادة
     * Setup reset button listener
     */
    private setupResetButtonListener(): void {
        this.uiComponents.addButtonEventListener('reset', async (event: Event) => {
            event.preventDefault();
            await this.handleResetSettings();
        });
    }

    /**
     * إعداد مستمع زر الإلغاء
     * Setup cancel button listener
     */
    private setupCancelButtonListener(): void {
        this.uiComponents.addButtonEventListener('cancel', (event: Event) => {
            event.preventDefault();
            this.handleCancelSettings();
        });
    }

    /**
     * إعداد مستمع تغيير النموذج
     * Setup form change listener
     */
    private setupFormChangeListener(): void {
        this.uiComponents.addFormEventListener('change', (event: Event) => {
            this.handleFormChange(event);
        });
    }

    /**
     * إعداد مستمع إرسال النموذج
     * Setup form submit listener
     */
    private setupFormSubmitListener(): void {
        this.uiComponents.addFormEventListener('submit', async (event: Event) => {
            event.preventDefault();
            await this.handleSaveSettings();
        });
    }

    /**
     * معالجة حفظ الإعدادات
     * Handle save settings
     */
    private async handleSaveSettings(): Promise<void> {
        try {
            if (this.isLoading) {
                return;
            }

            // التحقق من صحة النموذج
            // Validate form
            if (!this.uiComponents.validateForm()) {
                return;
            }

            this.setLoadingState(true);
            this.uiComponents.showStatusMessage('جاري حفظ الإعدادات...', 'info');

            // الحصول على قيم النموذج
            // Get form values
            const formValues = this.uiComponents.getFormValues();

            // تحويل القيم إلى تكوين التطبيق
            // Convert values to application config
            const config = this.convertFormValuesToConfig(formValues);

            // حفظ الإعدادات عبر IPC
            // Save settings via IPC
            const result = await this.saveSettingsViaIPC(config);

            if (result.success) {
                this.uiComponents.showStatusMessage('تم حفظ الإعدادات بنجاح', 'success');
                
                // تطبيق الإعدادات فوراً
                // Apply settings immediately
                await this.applySettingsImmediately(config);
            } else {
                this.uiComponents.showStatusMessage(`خطأ في حفظ الإعدادات: ${result.error}`, 'error');
            }

        } catch (error) {
            console.error('Error saving settings:', error);
            this.uiComponents.showStatusMessage('حدث خطأ أثناء حفظ الإعدادات', 'error');
        } finally {
            this.setLoadingState(false);
        }
    }

    /**
     * معالجة إعادة تعيين الإعدادات
     * Handle reset settings
     */
    private async handleResetSettings(): Promise<void> {
        try {
            if (this.isLoading) {
                return;
            }

            // تأكيد الإعادة
            // Confirm reset
            const confirmed = confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟');
            if (!confirmed) {
                return;
            }

            this.setLoadingState(true);
            this.uiComponents.showStatusMessage('جاري إعادة تعيين الإعدادات...', 'info');

            // إعادة تعيين الإعدادات عبر IPC
            // Reset settings via IPC
            const result = await this.resetSettingsViaIPC();

            if (result.success) {
                // تحديث النموذج بالقيم الافتراضية
                // Update form with default values
                this.uiComponents.setFormValues(result.defaultSettings);
                this.uiComponents.showStatusMessage('تم إعادة تعيين الإعدادات بنجاح', 'success');
                
                // تطبيق الإعدادات الافتراضية فوراً
                // Apply default settings immediately
                await this.applySettingsImmediately(result.defaultSettings);
            } else {
                this.uiComponents.showStatusMessage(`خطأ في إعادة تعيين الإعدادات: ${result.error}`, 'error');
            }

        } catch (error) {
            console.error('Error resetting settings:', error);
            this.uiComponents.showStatusMessage('حدث خطأ أثناء إعادة تعيين الإعدادات', 'error');
        } finally {
            this.setLoadingState(false);
        }
    }

    /**
     * معالجة إلغاء الإعدادات
     * Handle cancel settings
     */
    private handleCancelSettings(): void {
        // إغلاق نافذة الإعدادات
        // Close settings window
        if (window.electronAPI && window.electronAPI.closeWindow) {
            window.electronAPI.closeWindow();
        } else {
            window.close();
        }
    }

    /**
     * معالجة تغيير النموذج
     * Handle form change
     * 
     * @param event - حدث التغيير
     */
    private handleFormChange(event: Event): void {
        const target = event.target as HTMLInputElement;
        
        // معالجة تغييرات محددة
        // Handle specific changes
        if (target.name === 'videoQuality') {
            this.handleVideoQualityChange(target.value);
        } else if (target.name === 'darkMode') {
            this.handleDarkModeChange(target.checked);
        } else if (target.name === 'adBlocker') {
            this.handleAdBlockerChange(target.checked);
        }
    }

    /**
     * معالجة تغيير جودة الفيديو
     * Handle video quality change
     * 
     * @param quality - الجودة الجديدة
     */
    private handleVideoQualityChange(quality: string): void {
        console.log('Video quality changed to:', quality);
        // يمكن إضافة منطق إضافي هنا
        // Additional logic can be added here
    }

    /**
     * معالجة تغيير الوضع المظلم
     * Handle dark mode change
     * 
     * @param enabled - true إذا كان مفعل
     */
    private handleDarkModeChange(enabled: boolean): void {
        console.log('Dark mode changed to:', enabled);
        // يمكن إضافة منطق إضافي هنا
        // Additional logic can be added here
    }

    /**
     * معالجة تغيير مانع الإعلانات
     * Handle ad blocker change
     * 
     * @param enabled - true إذا كان مفعل
     */
    private handleAdBlockerChange(enabled: boolean): void {
        console.log('Ad blocker changed to:', enabled);
        // يمكن إضافة منطق إضافي هنا
        // Additional logic can be added here
    }

    /**
     * تحويل قيم النموذج إلى تكوين التطبيق
     * Convert form values to application config
     * 
     * @param formValues - قيم النموذج
     * @returns تكوين التطبيق
     */
    private convertFormValuesToConfig(formValues: Record<string, any>): ApplicationConfig {
        return {
            videoQuality: formValues.videoQuality || 'auto',
            darkMode: Boolean(formValues.darkMode),
            adBlocker: Boolean(formValues.adBlocker),
            autoplay: Boolean(formValues.autoplay),
            volume: Number(formValues.volume) || 50,
            language: formValues.language || 'ar',
            // إضافة المزيد من الإعدادات حسب الحاجة
            // Add more settings as needed
        } as ApplicationConfig;
    }

    /**
     * حفظ الإعدادات عبر IPC
     * Save settings via IPC
     * 
     * @param config - تكوين التطبيق
     * @returns نتيجة الحفظ
     */
    private async saveSettingsViaIPC(config: ApplicationConfig): Promise<{ success: boolean; error?: string }> {
        try {
            if (window.electronAPI && window.electronAPI.saveSettings) {
                return await window.electronAPI.saveSettings(config);
            } else {
                throw new Error('Electron API not available');
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    /**
     * إعادة تعيين الإعدادات عبر IPC
     * Reset settings via IPC
     * 
     * @returns نتيجة الإعادة
     */
    private async resetSettingsViaIPC(): Promise<{ success: boolean; defaultSettings?: any; error?: string }> {
        try {
            if (window.electronAPI && window.electronAPI.resetSettings) {
                return await window.electronAPI.resetSettings();
            } else {
                throw new Error('Electron API not available');
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    /**
     * تطبيق الإعدادات فوراً
     * Apply settings immediately
     * 
     * @param config - تكوين التطبيق
     */
    private async applySettingsImmediately(config: ApplicationConfig): Promise<void> {
        try {
            if (window.electronAPI && window.electronAPI.applySettings) {
                await window.electronAPI.applySettings(config);
            }
        } catch (error) {
            console.error('Error applying settings immediately:', error);
        }
    }

    /**
     * تعيين حالة التحميل
     * Set loading state
     * 
     * @param loading - true إذا كان في حالة تحميل
     */
    private setLoadingState(loading: boolean): void {
        this.isLoading = loading;
        this.uiComponents.setButtonsEnabled(!loading);
        this.uiComponents.setFormEnabled(!loading);
    }
}
