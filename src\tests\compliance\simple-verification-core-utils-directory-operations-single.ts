/**
 * فحص المجلد الواحد
 * Single directory checking operations
 * 
 * هذا الملف يحتوي على عمليات فحص المجلد الواحد
 * This file contains single directory checking operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import * as fs from 'fs';
import * as path from 'path';
import { SimpleVerificationConfig } from './simple-verification-types';

/**
 * فئة فحص المجلد الواحد
 * Single directory checking class
 */
export class SimpleVerificationCoreUtilsDirectoryOperationsSingle {

    /**
     * فحص مجلد واحد
     * Check single directory
     */
    public static checkSingleDirectory(dirPath: string, config: SimpleVerificationConfig): {
        isValid: boolean;
        issues: string[];
        score: number;
        fileCount: number;
        subdirectoryCount: number;
        followsStructure: boolean;
    } {
        const issues: string[] = [];
        let score = 100;
        let fileCount = 0;
        let subdirectoryCount = 0;
        let followsStructure = true;

        try {
            if (!fs.existsSync(dirPath)) {
                return {
                    isValid: false,
                    issues: ['Directory does not exist'],
                    score: 0,
                    fileCount: 0,
                    subdirectoryCount: 0,
                    followsStructure: false
                };
            }

            const items = fs.readdirSync(dirPath, { withFileTypes: true });

            // عد الملفات والمجلدات
            for (const item of items) {
                if (item.isFile()) {
                    fileCount++;
                } else if (item.isDirectory()) {
                    subdirectoryCount++;
                }
            }

            // فحص البنية
            const structureResult = this.checkDirectoryStructure(dirPath, items);
            if (structureResult.issues.length > 0) {
                issues.push(...structureResult.issues);
                followsStructure = false;
                score -= structureResult.penalty;
            }

            // فحص تسمية المجلد
            const namingResult = this.checkDirectoryNaming(dirPath);
            if (namingResult.issues.length > 0) {
                issues.push(...namingResult.issues);
                score -= namingResult.penalty;
            }

            // فحص التنظيم
            const organizationResult = this.checkDirectoryOrganization(dirPath, items);
            if (organizationResult.issues.length > 0) {
                issues.push(...organizationResult.issues);
                score -= organizationResult.penalty;
            }

            return {
                isValid: issues.length === 0,
                issues,
                score: Math.max(0, score),
                fileCount,
                subdirectoryCount,
                followsStructure
            };

        } catch (error) {
            return {
                isValid: false,
                issues: [`Error checking directory: ${error}`],
                score: 0,
                fileCount: 0,
                subdirectoryCount: 0,
                followsStructure: false
            };
        }
    }

    /**
     * فحص بنية المجلد
     * Check directory structure
     */
    private static checkDirectoryStructure(dirPath: string, items: fs.Dirent[]): { issues: string[]; penalty: number } {
        const issues: string[] = [];
        let penalty = 0;

        // فحص وجود index.ts في المجلدات المناسبة
        const hasIndexFile = items.some(item => 
            item.isFile() && (item.name === 'index.ts' || item.name === 'index.js')
        );

        const dirName = path.basename(dirPath);
        const shouldHaveIndex = ['components', 'services', 'utils', 'types', 'constants'].some(
            keyword => dirName.includes(keyword)
        );

        if (shouldHaveIndex && !hasIndexFile) {
            issues.push('Missing index.ts file for barrel exports');
            penalty += 10;
        }

        // فحص عدد الملفات في المجلد
        const fileCount = items.filter(item => item.isFile()).length;
        if (fileCount > 10) {
            issues.push(`Too many files in directory: ${fileCount} (recommended: ≤10)`);
            penalty += 15;
        }

        return { issues, penalty };
    }

    /**
     * فحص تسمية المجلد
     * Check directory naming
     */
    private static checkDirectoryNaming(dirPath: string): { issues: string[]; penalty: number } {
        const issues: string[] = [];
        let penalty = 0;

        const dirName = path.basename(dirPath);

        // فحص kebab-case
        const kebabCasePattern = /^[a-z0-9]+(-[a-z0-9]+)*$/;
        if (!kebabCasePattern.test(dirName)) {
            issues.push(`Directory name "${dirName}" does not follow kebab-case convention`);
            penalty += 10;
        }

        // فحص الأسماء المحجوزة
        const reservedNames = ['node_modules', '.git', 'dist', 'build'];
        if (reservedNames.includes(dirName)) {
            // هذه أسماء مقبولة، لا نعتبرها خطأ
        }

        return { issues, penalty };
    }

    /**
     * فحص تنظيم المجلد
     * Check directory organization
     */
    private static checkDirectoryOrganization(dirPath: string, items: fs.Dirent[]): { issues: string[]; penalty: number } {
        const issues: string[] = [];
        let penalty = 0;

        const dirName = path.basename(dirPath);

        // فحص أن الملفات في المجلد المناسب
        for (const item of items) {
            if (item.isFile()) {
                const fileName = item.name;
                
                // فحص ملفات المكونات
                if (fileName.includes('component') && !dirName.includes('component') && !dirName.includes('presentation')) {
                    issues.push(`Component file "${fileName}" should be in components or presentation directory`);
                    penalty += 5;
                }

                // فحص ملفات الخدمات
                if (fileName.includes('service') && !dirName.includes('service') && !dirName.includes('business')) {
                    issues.push(`Service file "${fileName}" should be in services or business directory`);
                    penalty += 5;
                }

                // فحص ملفات الأدوات
                if (fileName.includes('util') && !dirName.includes('util') && !dirName.includes('shared')) {
                    issues.push(`Utility file "${fileName}" should be in utils or shared directory`);
                    penalty += 5;
                }

                // فحص ملفات الأنواع
                if (fileName.includes('type') && !dirName.includes('type') && !dirName.includes('shared')) {
                    issues.push(`Type file "${fileName}" should be in types or shared directory`);
                    penalty += 5;
                }
            }
        }

        return { issues, penalty };
    }
}
