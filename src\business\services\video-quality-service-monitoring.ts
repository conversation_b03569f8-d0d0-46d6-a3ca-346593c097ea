/**
 * مراقبة خدمة جودة الفيديو
 * Video quality service monitoring
 *
 * هذا الملف يجمع جميع أنظمة مراقبة خدمة جودة الفيديو من الملفات المتخصصة
 * This file aggregates all video quality service monitoring systems from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ValidationResult, VideoQuality } from '@shared/types';
import { ResourceManager } from '@shared/utils/resource-manager';
import { VideoQualityConfig } from './video-quality-config';
import { VideoQualityServiceMonitoringAdvanced } from './video-quality-service-monitoring-advanced';
import { VideoQualityServiceMonitoringCore } from './video-quality-service-monitoring-core';

// إعادة تصدير الفئات المتخصصة / Re-export specialized classes
export { VideoQualityServiceMonitoringAdvanced } from './video-quality-service-monitoring-advanced';
export { VideoQualityServiceMonitoringCore } from './video-quality-service-monitoring-core';

/**
 * فئة مراقبة خدمة جودة الفيديو / Video quality service monitoring class
 */
export class VideoQualityServiceMonitoring {
    private readonly coreMonitoring: VideoQualityServiceMonitoringCore;
    private readonly advancedMonitoring: VideoQualityServiceMonitoringAdvanced;

    /**
     * منشئ مراقبة خدمة جودة الفيديو / Video quality service monitoring constructor
     *
     * @param resourceManager - مدير الموارد
     * @param config - تكوين جودة الفيديو
     */
    constructor(resourceManager: ResourceManager, config: VideoQualityConfig) {
        this.coreMonitoring = new VideoQualityServiceMonitoringCore(resourceManager, config);
        this.advancedMonitoring = new VideoQualityServiceMonitoringAdvanced(resourceManager, config);
    }

    // دوال المراقبة الأساسية / Core monitoring functions

    /**
     * بدء مراقبة تغييرات الجودة / Start monitoring quality changes
     */
    public async startMonitoring(): Promise<ValidationResult> {
        return await this.coreMonitoring.startMonitoring();
    }

    /**
     * إيقاف مراقبة تغييرات الجودة / Stop monitoring quality changes
     */
    public async stopMonitoring(): Promise<ValidationResult> {
        return await this.coreMonitoring.stopMonitoring();
    }

    /**
     * إضافة callback للمراقبة / Add monitoring callback
     */
    public addMonitoringCallback(id: string, callback: (quality: VideoQuality) => void): void {
        this.coreMonitoring.addMonitoringCallback(id, callback);
    }

    /**
     * إزالة callback للمراقبة / Remove monitoring callback
     */
    public removeMonitoringCallback(id: string): void {
        this.coreMonitoring.removeMonitoringCallback(id);
    }

    /**
     * التحقق من حالة المراقبة / Check monitoring status
     */
    public isCurrentlyMonitoring(): boolean {
        return this.coreMonitoring.isCurrentlyMonitoring();
    }

    // دوال المراقبة المتقدمة / Advanced monitoring functions

    /**
     * بدء المراقبة المتقدمة / Start advanced monitoring
     */
    public async startAdvancedMonitoring(): Promise<ValidationResult> {
        return await this.advancedMonitoring.startAdvancedMonitoring();
    }

    /**
     * إيقاف المراقبة المتقدمة / Stop advanced monitoring
     */
    public async stopAdvancedMonitoring(): Promise<ValidationResult> {
        return await this.advancedMonitoring.stopAdvancedMonitoring();
    }

    /**
     * الحصول على الإحصائيات / Get statistics
     */
    public getStatistics() {
        return this.advancedMonitoring.getStatistics();
    }

    /**
     * الحصول على تاريخ الجودة / Get quality history
     */
    public getQualityHistory() {
        return this.advancedMonitoring.getQualityHistory();
    }

    /**
     * إعادة تعيين الإحصائيات / Reset statistics
     */
    public resetStatistics(): void {
        this.advancedMonitoring.resetStatistics();
    }

    /**
     * تنظيف الموارد / Cleanup resources
     */
    public async cleanup(): Promise<void> {
        await Promise.all([
            this.coreMonitoring.cleanup(),
            this.advancedMonitoring.cleanup()
        ]);
    }
}
