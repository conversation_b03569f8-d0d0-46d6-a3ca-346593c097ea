/**
 * أدوات مساعدة لأداة التحقق المبسطة - ملف التفويض
 * Simple verification utility functions - Delegation file
 *
 * هذا الملف يفوض جميع العمليات للملفات المتخصصة
 * This file delegates all operations to specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { SimpleVerificationCoreUtilsDirectoryOperations } from './simple-verification-core-utils-directory-operations';
import { SimpleVerificationCoreUtilsFileOperations } from './simple-verification-core-utils-file-operations';
import { SimpleVerificationConfig } from './simple-verification-types';

/**
 * فئة الأدوات المساعدة للتحقق المبسط - التفويض
 * Simple verification utility functions class - Delegation
 */
export class SimpleVerificationCoreUtils {

    /**
     * الحصول على جميع الملفات - تفويض للوحدة المتخصصة
     * Get all files - Delegate to specialized module
     */
    public static async getAllFiles(
        projectRoot: string,
        config: SimpleVerificationConfig
    ): Promise<string[]> {
        // تفويض الحصول على الملفات للوحدة المتخصصة
        // Delegate file retrieval to specialized module
        return await SimpleVerificationCoreUtilsFileOperations.getAllFiles(projectRoot, config);
    }

    /**
     * فحص ملف واحد - تفويض للوحدة المتخصصة
     * Check single file - Delegate to specialized module
     */
    public static checkSingleFile(filePath: string, config: SimpleVerificationConfig): {
        isValid: boolean;
        issues: string[];
        score: number;
        lineCount: number;
        hasDocumentation: boolean;
        followsNamingConvention: boolean;
    } {
        // تفويض فحص الملف للوحدة المتخصصة
        // Delegate file check to specialized module
        return SimpleVerificationCoreUtilsFileOperations.checkSingleFile(filePath, config);
    }

    /**
     * فحص مجلد واحد - تفويض للوحدة المتخصصة
     * Check single directory - Delegate to specialized module
     */
    public static checkSingleDirectory(dirPath: string, config: SimpleVerificationConfig): {
        isValid: boolean;
        issues: string[];
        score: number;
        fileCount: number;
        subdirectoryCount: number;
        followsStructure: boolean;
    } {
        // تفويض فحص المجلد للوحدة المتخصصة
        // Delegate directory check to specialized module
        return SimpleVerificationCoreUtilsDirectoryOperations.checkSingleDirectory(dirPath, config);
    }

    /**
     * حساب إحصائيات المشروع - تفويض للوحدة المتخصصة
     * Calculate project statistics - Delegate to specialized module
     */
    public static calculateProjectStatistics(files: string[]): {
        totalFiles: number;
        totalLines: number;
        averageFileSize: number;
        largestFile: string;
        largestFileSize: number;
        smallestFile: string;
        smallestFileSize: number;
        filesByExtension: Record<string, number>;
        directoryCount: number;
    } {
        // تفويض حساب الإحصائيات للوحدة المتخصصة
        // Delegate statistics calculation to specialized module
        return SimpleVerificationCoreUtilsFileOperations.calculateProjectStatistics(files);
    }

    /**
     * تنظيف مسار الملف - تفويض للوحدة المتخصصة
     * Clean file path - Delegate to specialized module
     */
    public static cleanFilePath(filePath: string, projectRoot: string): string {
        // تفويض تنظيف المسار للوحدة المتخصصة
        // Delegate path cleaning to specialized module
        return SimpleVerificationCoreUtilsFileOperations.cleanFilePath(filePath, projectRoot);
    }

    /**
     * فحص صحة التكوين - تفويض للوحدة المتخصصة
     * Validate configuration - Delegate to specialized module
     */
    public static validateConfig(config: SimpleVerificationConfig): {
        isValid: boolean;
        issues: string[];
    } {
        // تفويض التحقق من التكوين للوحدة المتخصصة
        // Delegate configuration validation to specialized module
        return SimpleVerificationCoreUtilsDirectoryOperations.validateConfig(config);
    }
}
