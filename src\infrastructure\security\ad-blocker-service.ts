/**
 * خدمة منع الإعلانات الرئيسية
 * Main ad blocker service
 * 
 * هذا الملف يحتوي على الخدمة الرئيسية لمنع الإعلانات
 * This file contains the main ad blocker service
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ValidationResult } from '@shared/types';
import { ResourceManager } from '@shared/utils/resource-manager';
import { AdBlockerStatsManager } from './ad-blocker-stats';
import { AdBlockerDomainsManager } from './ad-blocker-domains';
import { AD_SELECTORS, AD_BLOCKER_DEFAULTS, AD_BLOCKER_MESSAGES } from './ad-blocker-config';

/**
 * خدمة منع الإعلانات الرئيسية
 * Main ad blocker service class
 */
export class AdBlockerService {
    private readonly resourceManager: ResourceManager;
    private readonly statsManager: AdBlockerStatsManager;
    private readonly domainsManager: AdBlockerDomainsManager;
    private isEnabled: boolean = false;
    private observer: MutationObserver | null = null;

    /**
     * منشئ خدمة منع الإعلانات
     * Ad blocker service constructor
     * 
     * @param resourceManager - مدير الموارد
     */
    constructor(resourceManager?: ResourceManager) {
        this.resourceManager = resourceManager || new ResourceManager();
        this.statsManager = new AdBlockerStatsManager();
        this.domainsManager = new AdBlockerDomainsManager();
    }

    /**
     * تفعيل مانع الإعلانات
     * Enable ad blocker
     * 
     * @returns نتيجة التفعيل
     */
    public async enable(): Promise<ValidationResult> {
        try {
            if (this.isEnabled) {
                return {
                    isValid: true,
                    errors: [],
                    message: 'مانع الإعلانات مفعل بالفعل / Ad blocker already enabled'
                };
            }

            this.isEnabled = true;
            this.startDOMObserver();
            this.startPeriodicCleanup();

            console.log(AD_BLOCKER_MESSAGES.ENABLED);

            return {
                isValid: true,
                errors: [],
                message: AD_BLOCKER_MESSAGES.ENABLED
            };
        } catch (error) {
            console.error(AD_BLOCKER_MESSAGES.ERROR_INIT, error);
            return {
                isValid: false,
                errors: [error instanceof Error ? error.message : String(error)],
                message: AD_BLOCKER_MESSAGES.ERROR_INIT
            };
        }
    }

    /**
     * إيقاف مانع الإعلانات
     * Disable ad blocker
     * 
     * @returns نتيجة الإيقاف
     */
    public async disable(): Promise<ValidationResult> {
        try {
            this.isEnabled = false;
            this.stopDOMObserver();
            this.resourceManager.cleanup();

            console.log(AD_BLOCKER_MESSAGES.DISABLED);

            return {
                isValid: true,
                errors: [],
                message: AD_BLOCKER_MESSAGES.DISABLED
            };
        } catch (error) {
            console.error('خطأ في إيقاف مانع الإعلانات:', error);
            return {
                isValid: false,
                errors: [error instanceof Error ? error.message : String(error)],
                message: 'فشل في إيقاف مانع الإعلانات'
            };
        }
    }

    /**
     * فحص ما إذا كان الطلب يجب حظره
     * Check if request should be blocked
     * 
     * @param url - رابط الطلب
     * @returns true إذا كان يجب حظر الطلب
     */
    public shouldBlockRequest(url: string): boolean {
        if (!this.isEnabled) {
            return false;
        }

        const shouldBlock = this.domainsManager.shouldBlockRequest(url);
        
        if (shouldBlock) {
            this.statsManager.incrementBlockedAds();
            console.log(AD_BLOCKER_MESSAGES.BLOCKED_REQUEST, url);
        } else {
            this.statsManager.incrementAllowedRequests();
        }

        return shouldBlock;
    }

    /**
     * الحصول على حالة التفعيل
     * Get enabled status
     * 
     * @returns true إذا كان مفعل
     */
    public isEnabledStatus(): boolean {
        return this.isEnabled;
    }

    /**
     * الحصول على الإحصائيات
     * Get statistics
     * 
     * @returns إحصائيات مانع الإعلانات
     */
    public getStats(): string {
        return this.statsManager.getDetailedReport();
    }

    /**
     * بدء مراقبة DOM
     * Start DOM observer
     */
    private startDOMObserver(): void {
        if (typeof window === 'undefined' || typeof document === 'undefined') {
            return; // لا نعمل في بيئة Node.js
        }

        this.observer = new MutationObserver((mutations) => {
            mutations.forEach(() => {
                this.cleanupAdsFromDOM();
            });
        });

        this.observer.observe(document.body, AD_BLOCKER_DEFAULTS.OBSERVER_CONFIG);
    }

    /**
     * إيقاف مراقبة DOM
     * Stop DOM observer
     */
    private stopDOMObserver(): void {
        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }
    }

    /**
     * بدء التنظيف الدوري
     * Start periodic cleanup
     */
    private startPeriodicCleanup(): void {
        this.resourceManager.createInterval(() => {
            if (this.isEnabled) {
                this.cleanupAdsFromDOM();
            }
        }, AD_BLOCKER_DEFAULTS.CLEANUP_INTERVAL);
    }

    /**
     * تنظيف الإعلانات من DOM
     * Clean ads from DOM
     */
    private cleanupAdsFromDOM(): void {
        if (typeof document === 'undefined') {
            return;
        }

        try {
            let removedCount = 0;

            AD_SELECTORS.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (element && element.parentNode) {
                        element.parentNode.removeChild(element);
                        removedCount++;
                    }
                });
            });

            if (removedCount > 0) {
                this.statsManager.incrementBlockedAds();
                console.log(`${AD_BLOCKER_MESSAGES.CLEANED_DOM}: ${removedCount} عنصر`);
            }
        } catch (error) {
            console.warn(AD_BLOCKER_MESSAGES.ERROR_CLEANUP, error);
        }
    }

    /**
     * تنظيف الموارد
     * Cleanup resources
     */
    public cleanup(): void {
        this.disable();
        this.statsManager.resetStats();
        this.domainsManager.resetToDefaults();
    }
}
