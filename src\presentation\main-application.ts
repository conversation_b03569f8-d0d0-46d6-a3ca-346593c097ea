/**
 * التطبيق الرئيسي لـ YouTube Dark CyberX
 * Main application for YouTube Dark CyberX
 * 
 * هذا الملف يحتوي على منطق تشغيل التطبيق الرئيسي
 * This file contains the main application logic
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 * 
 * @requires electron للواجهة الرسومية
 * @requires @presentation/windows/window-manager لإدارة النوافذ
 * @requires @presentation/controllers/menu-manager لإدارة القوائم
 * @requires @business/services/settings-manager لإدارة الإعدادات
 * @requires @infrastructure/security/ad-blocker لمنع الإعلانات
 */

import { SettingsManager } from '@business/services/settings-manager';
import { SecurityLayer } from '@infrastructure/security/security-layer';
import { MenuManager } from '@presentation/controllers/menu-manager';
import { WindowManager } from '@presentation/windows/window-manager';
import { APPLICATION_INFO, IPC_MESSAGES } from '@shared/constants';
import { ApplicationConfig, ApplicationState } from '@shared/types';
import { app, ipcMain } from 'electron';

/**
 * التطبيق الرئيسي
 * Main application class
 */
export class MainApplication {
    private readonly windowManager: WindowManager;
    private readonly menuManager: MenuManager;
    private readonly settingsManager: SettingsManager;
    private readonly securityLayer: SecurityLayer;
    private applicationState: ApplicationState;

    /**
     * منشئ التطبيق الرئيسي
     * Main application constructor
     */
    constructor() {
        this.windowManager = new WindowManager();
        this.menuManager = new MenuManager(this.windowManager);
        this.settingsManager = new SettingsManager();
        this.securityLayer = new SecurityLayer({
            enableAdBlocking: true,
            enableRequestValidation: true,
            enableContentSanitization: true,
            enableThreatReporting: true,
            strictMode: false
        });

        this.applicationState = {
            isReady: false,
            windows: [],
            config: this.settingsManager.getAllSettings()
        };
    }

    /**
     * تهيئة التطبيق
     * Initializes the application
     * 
     * @returns Promise<void>
     * 
     * @example
     * ```typescript
     * const app = new MainApplication();
     * await app.initialize();
     * ```
     */
    public async initialize(): Promise<void> {
        try {
            console.log(`تشغيل ${APPLICATION_INFO.NAME} الإصدار ${APPLICATION_INFO.VERSION}`);

            // إعداد معالجات الأحداث
            this.setupEventHandlers();

            // إعداد معالجات IPC
            this.setupIpcHandlers();

            // انتظار جاهزية Electron
            await app.whenReady();

            // إنشاء النافذة الرئيسية
            await this.createMainWindow();

            // تهيئة طبقة الأمان
            await this.initializeSecurityLayer();

            // إنشاء القائمة
            this.menuManager.createApplicationMenu();

            // تطبيق الإعدادات
            await this.applyUserSettings();

            this.applicationState.isReady = true;
            console.log('تم تشغيل التطبيق بنجاح');

        } catch (error) {
            console.error('خطأ في تهيئة التطبيق:', error);
            throw error;
        }
    }

    /**
     * إنشاء النافذة الرئيسية
     * Creates the main window
     * 
     * @returns Promise<void>
     */
    private async createMainWindow(): Promise<void> {
        try {
            const config = this.settingsManager.getAllSettings();
            const mainWindow = await this.windowManager.createMainWindow(config);

            // حفظ حجم النافذة عند الإغلاق
            mainWindow.on('close', () => {
                const bounds = this.windowManager.saveWindowBounds(mainWindow);
                this.settingsManager.saveWindowBounds(bounds);
            });

            // تحديث حالة التطبيق
            this.applicationState.windows = this.windowManager.getAllWindows();

        } catch (error) {
            throw new Error(`فشل في إنشاء النافذة الرئيسية: ${error}`);
        }
    }

    /**
     * تهيئة طبقة الأمان
     * Initializes security layer
     *
     * @returns Promise<void>
     */
    private async initializeSecurityLayer(): Promise<void> {
        try {
            const result = await this.securityLayer.initialize();
            if (!result.isValid) {
                console.error('فشل في تهيئة طبقة الأمان:', result.errors);
                throw new Error('Security layer initialization failed');
            }
            console.log('تم تهيئة طبقة الأمان بنجاح');
        } catch (error) {
            console.error('خطأ في تهيئة طبقة الأمان:', error);
            throw error;
        }
    }

    /**
     * تطبيق إعدادات المستخدم
     * Applies user settings
     * 
     * @returns Promise<void>
     */
    private async applyUserSettings(): Promise<void> {
        try {
            const config = this.settingsManager.getAllSettings();
            const mainWindow = this.windowManager.getMainWindow();

            if (mainWindow && config.autoApplySettings) {
                // إرسال الإعدادات للنافذة الرئيسية
                mainWindow.webContents.send(IPC_MESSAGES.APPLY_SETTINGS, config);
            }
        } catch (error) {
            console.error('خطأ في تطبيق الإعدادات:', error);
        }
    }

    /**
     * إعداد معالجات الأحداث
     * Sets up event handlers
     * 
     * @returns void
     */
    private setupEventHandlers(): void {
        // إغلاق التطبيق عند إغلاق جميع النوافذ (Windows & Linux)
        app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                app.quit();
            }
        });

        // إعادة تنشيط التطبيق (macOS)
        app.on('activate', async () => {
            if (this.windowManager.getAllWindows().length === 0) {
                await this.createMainWindow();
            }
        });

        // معالجة إغلاق التطبيق
        app.on('before-quit', async () => {
            await this.cleanup();
        });
    }

    /**
     * إعداد معالجات IPC
     * Sets up IPC handlers
     * 
     * @returns void
     */
    private setupIpcHandlers(): void {
        // الحصول على الإعدادات
        ipcMain.handle(IPC_MESSAGES.GET_SETTINGS, () => {
            return this.settingsManager.getAllSettings();
        });

        // تحديث الإعدادات
        ipcMain.handle(IPC_MESSAGES.UPDATE_SETTINGS, (_, settings: Partial<ApplicationConfig>) => {
            const result = this.settingsManager.updateSettings(settings);
            if (result.isValid) {
                this.applicationState.config = this.settingsManager.getAllSettings();
            }
            return result;
        });

        // تبديل مانع الإعلانات
        ipcMain.handle(IPC_MESSAGES.TOGGLE_AD_BLOCKER, async (_, enabled: boolean) => {
            try {
                const result = enabled ?
                    await this.securityLayer.enableAdBlocker() :
                    await this.securityLayer.disableAdBlocker();

                if (result.isValid) {
                    return this.settingsManager.updateSetting('adBlockEnabled', enabled);
                } else {
                    return result;
                }
            } catch (error) {
                console.error('خطأ في تبديل مانع الإعلانات:', error);
                return {
                    isValid: false,
                    errors: [{
                        field: 'adBlockEnabled',
                        message: `خطأ في تبديل مانع الإعلانات: ${error}`,
                        code: 'AD_BLOCKER_TOGGLE_ERROR'
                    }]
                };
            }
        });

        // فتح نافذة الإعدادات
        ipcMain.handle(IPC_MESSAGES.OPEN_SETTINGS, async () => {
            try {
                await this.windowManager.createSettingsWindow();
                return { success: true };
            } catch (error) {
                return {
                    success: false,
                    error: `فشل في فتح نافذة الإعدادات: ${error}`
                };
            }
        });

        // الحصول على تقرير الأمان
        ipcMain.handle('get-security-report', () => {
            return this.securityLayer.getSecurityReport();
        });

        // الحصول على حالة طبقة الأمان
        ipcMain.handle('get-security-status', () => {
            return this.securityLayer.getStatus();
        });
    }

    /**
     * تنظيف الموارد
     * Cleans up resources
     * 
     * @returns Promise<void>
     */
    private async cleanup(): Promise<void> {
        try {
            await this.windowManager.closeAllWindows();
            this.securityLayer.cleanup();
            console.log('تم تنظيف الموارد بنجاح');
        } catch (error) {
            console.error('خطأ في تنظيف الموارد:', error);
        }
    }

    /**
     * الحصول على حالة التطبيق
     * Gets the application state
     * 
     * @returns ApplicationState - حالة التطبيق
     */
    public getApplicationState(): ApplicationState {
        return this.applicationState;
    }
}

// تشغيل التطبيق
const application = new MainApplication();
application.initialize().catch((error) => {
    console.error('فشل في تشغيل التطبيق:', error);
    process.exit(1);
});
