/**
 * اختبارات شاملة للتطبيق
 * End-to-end application tests
 * 
 * هذا الملف يحتوي على اختبارات شاملة للتطبيق من البداية للنهاية
 * This file contains comprehensive end-to-end tests for the application
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { Application } from 'spectron';
import * as path from 'path';
import * as fs from 'fs';

describe('YouTube Dark CyberX E2E Tests', () => {
    let app: Application;
    const electronPath = path.join(__dirname, '../../../node_modules/.bin/electron');
    const appPath = path.join(__dirname, '../../../dist/presentation/main-application.js');

    beforeAll(async () => {
        // Ensure the app is built
        if (!fs.existsSync(appPath)) {
            throw new Error('Application not built. Run npm run build first.');
        }

        app = new Application({
            path: electronPath,
            args: [appPath],
            env: {
                NODE_ENV: 'test',
                ELECTRON_ENABLE_LOGGING: 'true'
            },
            startTimeout: 10000,
            waitTimeout: 5000
        });

        await app.start();
    }, 30000);

    afterAll(async () => {
        if (app && app.isRunning()) {
            await app.stop();
        }
    });

    describe('Application Startup', () => {
        it('should start the application successfully', async () => {
            // Assert
            expect(app.isRunning()).toBe(true);
            
            const windowCount = await app.client.getWindowCount();
            expect(windowCount).toBe(1);
        });

        it('should have the correct window title', async () => {
            // Act
            const title = await app.client.getTitle();

            // Assert
            expect(title).toContain('YouTube Dark CyberX');
        });

        it('should load the main window with correct dimensions', async () => {
            // Act
            const bounds = await app.browserWindow.getBounds();

            // Assert
            expect(bounds.width).toBeGreaterThan(800);
            expect(bounds.height).toBeGreaterThan(600);
        });
    });

    describe('Main Window Functionality', () => {
        it('should load YouTube in the main window', async () => {
            // Wait for page to load
            await app.client.waitUntilWindowLoaded();
            
            // Act
            const url = await app.client.getUrl();

            // Assert
            expect(url).toContain('youtube.com');
        });

        it('should have application menu', async () => {
            // Act
            const menuItems = await app.client.getMenuItems();

            // Assert
            expect(menuItems).toBeDefined();
            expect(menuItems.length).toBeGreaterThan(0);
        });

        it('should handle window resize', async () => {
            // Arrange
            const originalBounds = await app.browserWindow.getBounds();

            // Act
            await app.browserWindow.setSize(1200, 900);
            const newBounds = await app.browserWindow.getBounds();

            // Assert
            expect(newBounds.width).toBe(1200);
            expect(newBounds.height).toBe(900);
            expect(newBounds.width).not.toBe(originalBounds.width);
        });
    });

    describe('Settings Window', () => {
        it('should open settings window from menu', async () => {
            // Act
            await app.client.keys(['Control', 'comma']); // Ctrl+, shortcut
            await app.client.pause(1000);

            const windowCount = await app.client.getWindowCount();

            // Assert
            expect(windowCount).toBe(2);
        });

        it('should display settings form', async () => {
            // Arrange - Ensure settings window is open
            const windows = await app.client.windowHandles();
            if (windows.value.length < 2) {
                await app.client.keys(['Control', 'comma']);
                await app.client.pause(1000);
            }

            // Switch to settings window
            const settingsWindow = (await app.client.windowHandles()).value[1];
            await app.client.window(settingsWindow);

            // Act
            const videoQualitySelect = await app.client.$('#videoQuality');
            const darkModeCheckbox = await app.client.$('#darkModeEnabled');
            const adBlockCheckbox = await app.client.$('#adBlockEnabled');

            // Assert
            expect(await videoQualitySelect.isExisting()).toBe(true);
            expect(await darkModeCheckbox.isExisting()).toBe(true);
            expect(await adBlockCheckbox.isExisting()).toBe(true);
        });

        it('should save settings changes', async () => {
            // Arrange - Switch to settings window
            const windows = await app.client.windowHandles();
            const settingsWindow = windows.value[1];
            await app.client.window(settingsWindow);

            // Act
            await app.client.$('#videoQuality').selectByValue('720p');
            await app.client.$('#darkModeEnabled').click();
            await app.client.$('#saveSettings').click();

            await app.client.pause(500);

            // Assert - Check if settings were saved (no error messages)
            const errorElements = await app.client.$$('.error-message');
            expect(errorElements.length).toBe(0);
        });
    });

    describe('YouTube Integration', () => {
        beforeEach(async () => {
            // Switch back to main window
            const windows = await app.client.windowHandles();
            await app.client.window(windows.value[0]);
        });

        it('should inject dark mode styles', async () => {
            // Act
            await app.client.pause(2000); // Wait for page load and injection

            const darkModeStyles = await app.client.execute(() => {
                const styleElements = document.querySelectorAll('style[data-dark-mode]');
                return styleElements.length > 0;
            });

            // Assert
            expect(darkModeStyles).toBe(true);
        });

        it('should block ad requests', async () => {
            // Act
            const networkLogs = await app.client.getLogs('browser');
            
            // Look for blocked requests in logs
            const blockedRequests = networkLogs.filter(log => 
                log.message.includes('blocked') || 
                log.message.includes('ads') ||
                log.message.includes('doubleclick')
            );

            // Assert
            expect(blockedRequests.length).toBeGreaterThan(0);
        });

        it('should apply video quality settings', async () => {
            // This test would require a video to be playing
            // For now, we'll check if the quality control script is injected
            
            // Act
            const qualityControlInjected = await app.client.execute(() => {
                return window.hasOwnProperty('youtubeQualityManager');
            });

            // Assert
            expect(qualityControlInjected).toBe(true);
        });
    });

    describe('IPC Communication', () => {
        it('should handle settings updates via IPC', async () => {
            // Act
            const result = await app.client.execute(() => {
                return new Promise((resolve) => {
                    if (window.electronAPI) {
                        window.electronAPI.updateSettings({
                            videoQuality: '1080p',
                            darkModeEnabled: true
                        }).then(resolve);
                    } else {
                        resolve({ isValid: false, errors: ['electronAPI not available'] });
                    }
                });
            });

            // Assert
            expect(result.isValid).toBe(true);
        });

        it('should handle ad blocker toggle via IPC', async () => {
            // Act
            const result = await app.client.execute(() => {
                return new Promise((resolve) => {
                    if (window.electronAPI) {
                        window.electronAPI.toggleAdBlocker(false).then(resolve);
                    } else {
                        resolve({ isValid: false, errors: ['electronAPI not available'] });
                    }
                });
            });

            // Assert
            expect(result.isValid).toBe(true);
        });
    });

    describe('Error Handling', () => {
        it('should handle network errors gracefully', async () => {
            // Act - Simulate network error by navigating to invalid URL
            await app.client.execute(() => {
                window.location.href = 'https://invalid-youtube-url.com';
            });

            await app.client.pause(2000);

            // Assert - Application should still be responsive
            expect(app.isRunning()).toBe(true);
            
            const title = await app.client.getTitle();
            expect(title).toBeDefined();
        });

        it('should recover from JavaScript errors', async () => {
            // Act - Inject a JavaScript error
            await app.client.execute(() => {
                throw new Error('Test error');
            });

            await app.client.pause(1000);

            // Assert - Application should still be functional
            expect(app.isRunning()).toBe(true);
            
            const windowCount = await app.client.getWindowCount();
            expect(windowCount).toBeGreaterThanOrEqual(1);
        });
    });

    describe('Performance', () => {
        it('should start within acceptable time', async () => {
            // This is tested in beforeAll with startTimeout
            // If we reach here, startup was successful within 10 seconds
            expect(true).toBe(true);
        });

        it('should handle multiple window operations efficiently', async () => {
            // Arrange
            const startTime = Date.now();

            // Act - Perform multiple window operations
            await app.client.keys(['Control', 'comma']); // Open settings
            await app.client.pause(500);
            
            const windows = await app.client.windowHandles();
            await app.client.window(windows.value[1]); // Switch to settings
            await app.client.window(windows.value[0]); // Switch back to main
            
            if (windows.value.length > 1) {
                await app.client.window(windows.value[1]);
                await app.client.close(); // Close settings window
                await app.client.window(windows.value[0]);
            }

            const endTime = Date.now();

            // Assert
            const duration = endTime - startTime;
            expect(duration).toBeLessThan(5000); // Should complete in less than 5 seconds
        });

        it('should maintain responsive UI during heavy operations', async () => {
            // Act - Simulate heavy operation
            await app.client.execute(() => {
                // Simulate heavy DOM manipulation
                for (let i = 0; i < 1000; i++) {
                    const div = document.createElement('div');
                    div.textContent = `Test element ${i}`;
                    document.body.appendChild(div);
                }
            });

            await app.client.pause(1000);

            // Assert - UI should still be responsive
            const title = await app.client.getTitle();
            expect(title).toBeDefined();
            
            const isResponsive = await app.client.execute(() => {
                return document.readyState === 'complete';
            });
            expect(isResponsive).toBe(true);
        });
    });

    describe('Security', () => {
        it('should prevent XSS attacks', async () => {
            // Act - Try to inject malicious script
            const xssBlocked = await app.client.execute(() => {
                try {
                    const script = document.createElement('script');
                    script.innerHTML = 'window.xssTest = true; alert("XSS");';
                    document.head.appendChild(script);
                    return !window.hasOwnProperty('xssTest');
                } catch (error) {
                    return true; // Error means XSS was blocked
                }
            });

            // Assert
            expect(xssBlocked).toBe(true);
        });

        it('should sanitize user input', async () => {
            // This would be tested in settings window with malicious input
            // For now, we verify that security layer is active
            
            // Act
            const securityActive = await app.client.execute(() => {
                return window.hasOwnProperty('securityLayer') || 
                       document.querySelector('[data-security-sanitized]') !== null;
            });

            // Assert
            expect(securityActive).toBe(true);
        });
    });
});
