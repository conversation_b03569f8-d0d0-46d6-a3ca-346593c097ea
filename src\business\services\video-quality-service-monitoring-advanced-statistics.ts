/**
 * إحصائيات المراقبة المتقدمة لخدمة جودة الفيديو
 * Video quality service advanced monitoring statistics
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQuality } from '@shared/types';

/**
 * إحصائيات المراقبة / Monitoring statistics
 */
export interface MonitoringStatistics {
    totalQualityChanges: number;
    qualityChangesByType: Map<VideoQuality, number>;
    averageQualityChangeTime: number;
    lastQualityChange: Date | null;
    monitoringStartTime: Date | null;
    totalMonitoringTime: number;
}

/**
 * حدث تغيير الجودة / Quality change event
 */
export interface QualityChangeEvent {
    previousQuality: VideoQuality | null;
    currentQuality: VideoQuality;
    timestamp: Date;
    source: string;
    metadata?: Record<string, unknown>;
}

/**
 * فئة إدارة الإحصائيات للمراقبة المتقدمة
 * Advanced monitoring statistics manager class
 */
export class VideoQualityServiceMonitoringAdvancedStatistics {
    private statistics: MonitoringStatistics;
    private qualityHistory: QualityChangeEvent[] = [];

    constructor() {
        this.statistics = this.initializeStatistics();
    }

    /**
     * تهيئة الإحصائيات / Initialize statistics
     */
    private initializeStatistics(): MonitoringStatistics {
        return {
            totalQualityChanges: 0,
            qualityChangesByType: new Map(),
            averageQualityChangeTime: 0,
            lastQualityChange: null,
            monitoringStartTime: null,
            totalMonitoringTime: 0
        };
    }

    /**
     * بدء تسجيل الإحصائيات / Start statistics recording
     */
    public startStatisticsRecording(): void {
        this.statistics.monitoringStartTime = new Date();
    }

    /**
     * إيقاف تسجيل الإحصائيات / Stop statistics recording
     */
    public stopStatisticsRecording(): void {
        if (this.statistics.monitoringStartTime) {
            const endTime = new Date();
            this.statistics.totalMonitoringTime += 
                endTime.getTime() - this.statistics.monitoringStartTime.getTime();
            this.statistics.monitoringStartTime = null;
        }
    }

    /**
     * تسجيل فحص الجودة / Record quality check
     */
    public recordQualityCheck(quality: VideoQuality): void {
        try {
            const lastEvent = this.qualityHistory[this.qualityHistory.length - 1];
            
            if (!lastEvent || lastEvent.currentQuality !== quality) {
                const event: QualityChangeEvent = {
                    previousQuality: lastEvent?.currentQuality || null,
                    currentQuality: quality,
                    timestamp: new Date(),
                    source: 'periodic_check'
                };

                this.qualityHistory.push(event);
                this.updateStatistics(event);

                // الحد من حجم التاريخ
                if (this.qualityHistory.length > 100) {
                    this.qualityHistory = this.qualityHistory.slice(-50);
                }
            }

        } catch (error) {
            console.error('خطأ في تسجيل فحص الجودة:', error);
        }
    }

    /**
     * تحديث الإحصائيات / Update statistics
     */
    private updateStatistics(event: QualityChangeEvent): void {
        try {
            this.statistics.totalQualityChanges++;
            this.statistics.lastQualityChange = event.timestamp;

            // تحديث إحصائيات الجودة حسب النوع
            const currentCount = this.statistics.qualityChangesByType.get(event.currentQuality) || 0;
            this.statistics.qualityChangesByType.set(event.currentQuality, currentCount + 1);

            // حساب متوسط وقت تغيير الجودة
            if (this.qualityHistory.length > 1) {
                const totalTime = this.qualityHistory[this.qualityHistory.length - 1].timestamp.getTime() - 
                                this.qualityHistory[0].timestamp.getTime();
                this.statistics.averageQualityChangeTime = totalTime / (this.qualityHistory.length - 1);
            }

        } catch (error) {
            console.error('خطأ في تحديث الإحصائيات:', error);
        }
    }

    /**
     * تسجيل حدث تغيير جودة مخصص / Record custom quality change event
     */
    public recordQualityChangeEvent(
        previousQuality: VideoQuality | null,
        currentQuality: VideoQuality,
        source: string,
        metadata?: Record<string, unknown>
    ): void {
        try {
            const event: QualityChangeEvent = {
                previousQuality,
                currentQuality,
                timestamp: new Date(),
                source,
                metadata
            };

            this.qualityHistory.push(event);
            this.updateStatistics(event);

            // الحد من حجم التاريخ
            if (this.qualityHistory.length > 100) {
                this.qualityHistory = this.qualityHistory.slice(-50);
            }

        } catch (error) {
            console.error('خطأ في تسجيل حدث تغيير الجودة:', error);
        }
    }

    /**
     * الحصول على الإحصائيات / Get statistics
     */
    public getStatistics(): MonitoringStatistics {
        return { ...this.statistics };
    }

    /**
     * الحصول على تاريخ الجودة / Get quality history
     */
    public getQualityHistory(): QualityChangeEvent[] {
        return [...this.qualityHistory];
    }

    /**
     * الحصول على إحصائيات مفصلة / Get detailed statistics
     */
    public getDetailedStatistics(): {
        basic: MonitoringStatistics;
        qualityDistribution: Record<VideoQuality, number>;
        recentChanges: QualityChangeEvent[];
        averageTimeBetweenChanges: number;
        mostCommonQuality: VideoQuality | null;
        qualityStability: number;
    } {
        const qualityDistribution: Record<VideoQuality, number> = {} as Record<VideoQuality, number>;
        
        // تحويل Map إلى Object
        this.statistics.qualityChangesByType.forEach((count, quality) => {
            qualityDistribution[quality] = count;
        });

        // الحصول على آخر 10 تغييرات
        const recentChanges = this.qualityHistory.slice(-10);

        // حساب متوسط الوقت بين التغييرات
        let averageTimeBetweenChanges = 0;
        if (this.qualityHistory.length > 1) {
            const timeSpans: number[] = [];
            for (let i = 1; i < this.qualityHistory.length; i++) {
                const timeDiff = this.qualityHistory[i].timestamp.getTime() - 
                               this.qualityHistory[i - 1].timestamp.getTime();
                timeSpans.push(timeDiff);
            }
            averageTimeBetweenChanges = timeSpans.reduce((sum, time) => sum + time, 0) / timeSpans.length;
        }

        // العثور على الجودة الأكثر شيوعاً
        let mostCommonQuality: VideoQuality | null = null;
        let maxCount = 0;
        this.statistics.qualityChangesByType.forEach((count, quality) => {
            if (count > maxCount) {
                maxCount = count;
                mostCommonQuality = quality;
            }
        });

        // حساب استقرار الجودة (نسبة الوقت في الجودة الأكثر شيوعاً)
        const qualityStability = mostCommonQuality && this.statistics.totalQualityChanges > 0
            ? (this.statistics.qualityChangesByType.get(mostCommonQuality) || 0) / this.statistics.totalQualityChanges
            : 0;

        return {
            basic: this.getStatistics(),
            qualityDistribution,
            recentChanges,
            averageTimeBetweenChanges,
            mostCommonQuality,
            qualityStability
        };
    }

    /**
     * إعادة تعيين الإحصائيات / Reset statistics
     */
    public resetStatistics(): void {
        this.statistics = this.initializeStatistics();
        this.qualityHistory = [];
    }

    /**
     * تصدير الإحصائيات إلى JSON / Export statistics to JSON
     */
    public exportStatistics(): string {
        try {
            const exportData = {
                statistics: {
                    ...this.statistics,
                    qualityChangesByType: Object.fromEntries(this.statistics.qualityChangesByType)
                },
                qualityHistory: this.qualityHistory,
                exportTimestamp: new Date().toISOString()
            };

            return JSON.stringify(exportData, null, 2);

        } catch (error) {
            console.error('خطأ في تصدير الإحصائيات:', error);
            return '{}';
        }
    }

    /**
     * استيراد الإحصائيات من JSON / Import statistics from JSON
     */
    public importStatistics(jsonData: string): boolean {
        try {
            const importData = JSON.parse(jsonData);
            
            if (importData.statistics && importData.qualityHistory) {
                this.statistics = {
                    ...importData.statistics,
                    qualityChangesByType: new Map(Object.entries(importData.statistics.qualityChangesByType))
                };
                this.qualityHistory = importData.qualityHistory;
                return true;
            }

            return false;

        } catch (error) {
            console.error('خطأ في استيراد الإحصائيات:', error);
            return false;
        }
    }
}
