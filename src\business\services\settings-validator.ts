/**
 * مدقق إعدادات التطبيق
 * Application settings validator
 *
 * هذا الملف يجمع جميع مدققات الإعدادات من الملفات المتخصصة
 * This file aggregates all settings validators from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ApplicationConfig, ValidationResult } from '@shared/types';
import { SettingsValidationResult } from './settings-config';
import { SettingsValidatorAdvanced } from './settings-validator-advanced';
import { SettingsValidatorBasic } from './settings-validator-basic';

// إعادة تصدير الفئات المتخصصة / Re-export specialized classes
export { SettingsValidatorAdvanced } from './settings-validator-advanced';
export { SettingsValidatorBasic } from './settings-validator-basic';

/**
 * فئة مدقق الإعدادات / Settings validator class
 */
export class SettingsValidator {
    private basicValidator: SettingsValidatorBasic;
    private advancedValidator: SettingsValidatorAdvanced;

    /**
     * منشئ مدقق الإعدادات / Settings validator constructor
     */
    constructor() {
        this.basicValidator = new SettingsValidatorBasic();
        this.advancedValidator = new SettingsValidatorAdvanced();
    }

    // دوال التحقق الأساسية / Basic validation functions

    /**
     * التحقق من صحة إعداد واحد / Validate single setting
     */
    public validateSingleSetting<K extends keyof ApplicationConfig>(
        key: K,
        value: ApplicationConfig[K]
    ): ValidationResult {
        return this.basicValidator.validateSingleSetting(key, value);
    }

    // دوال التحقق المتقدمة / Advanced validation functions

    /**
     * التحقق من صحة جميع الإعدادات / Validate all settings
     */
    public validateSettings(settings: ApplicationConfig): SettingsValidationResult {
        return this.advancedValidator.validateAllSettings(settings);
    }

    /**
     * إصلاح الإعدادات التالفة / Fix corrupted settings
     */
    public fixCorruptedSettings(settings: ApplicationConfig): ApplicationConfig {
        return this.advancedValidator.fixCorruptedSettings(settings);
    }

    /**
     * تحسين الإعدادات للأداء / Optimize settings for performance
     */
    public optimizeForPerformance(settings: ApplicationConfig): ApplicationConfig {
        return this.advancedValidator.optimizeForPerformance(settings);
    }

    /**
     * تنظيف الأخطاء / Clear errors
     */
    public clearErrors(): void {
        this.basicValidator.clearErrors();
        this.advancedValidator.clearErrors();
    }
}
