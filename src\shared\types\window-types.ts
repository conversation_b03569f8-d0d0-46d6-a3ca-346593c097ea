/**
 * أنواع النوافذ
 * Window types
 * 
 * هذا الملف يحتوي على تعريفات أنواع النوافذ
 * This file contains window type definitions
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * إعدادات النافذة
 * Window configuration
 */
export interface WindowConfig {
    /** عرض النافذة / Window width */
    width: number;
    /** ارتفاع النافذة / Window height */
    height: number;
    /** الحد الأدنى للعرض / Minimum width */
    minWidth?: number;
    /** الحد الأدنى للارتفاع / Minimum height */
    minHeight?: number;
    /** الحد الأقصى للعرض / Maximum width */
    maxWidth?: number;
    /** الحد الأقصى للارتفاع / Maximum height */
    maxHeight?: number;
    /** موقع النافذة X / Window position X */
    x?: number;
    /** موقع النافذة Y / Window position Y */
    y?: number;
    /** هل النافذة قابلة لتغيير الحجم / Is window resizable */
    resizable?: boolean;
    /** هل النافذة قابلة للحركة / Is window movable */
    movable?: boolean;
    /** هل النافذة قابلة للتصغير / Is window minimizable */
    minimizable?: boolean;
    /** هل النافذة قابلة للتكبير / Is window maximizable */
    maximizable?: boolean;
    /** هل النافذة قابلة للإغلاق / Is window closable */
    closable?: boolean;
    /** هل النافذة دائماً في المقدمة / Is window always on top */
    alwaysOnTop?: boolean;
    /** هل النافذة ملء الشاشة / Is window fullscreen */
    fullscreen?: boolean;
    /** هل النافذة مخفية / Is window hidden */
    show?: boolean;
    /** عنوان النافذة / Window title */
    title?: string;
    /** أيقونة النافذة / Window icon */
    icon?: string;
    /** إطار النافذة / Window frame */
    frame?: boolean;
    /** شفافية النافذة / Window transparency */
    transparent?: boolean;
    /** مستوى الشفافية / Opacity level */
    opacity?: number;
}

/**
 * حدود النافذة
 * Window bounds
 */
export interface WindowBounds {
    /** موقع X / X position */
    x: number;
    /** موقع Y / Y position */
    y: number;
    /** العرض / Width */
    width: number;
    /** الارتفاع / Height */
    height: number;
}

/**
 * حالة النافذة
 * Window state
 */
export type WindowState = 
    | 'normal'      // عادية / Normal
    | 'minimized'   // مصغرة / Minimized
    | 'maximized'   // مكبرة / Maximized
    | 'fullscreen'  // ملء الشاشة / Fullscreen
    | 'hidden';     // مخفية / Hidden

/**
 * نوع النافذة
 * Window type
 */
export type WindowType = 
    | 'main'        // النافذة الرئيسية / Main window
    | 'settings'    // نافذة الإعدادات / Settings window
    | 'about'       // نافذة حول / About window
    | 'help'        // نافذة المساعدة / Help window
    | 'dialog'      // نافذة حوار / Dialog window
    | 'splash';     // شاشة البداية / Splash screen

/**
 * أحداث النافذة
 * Window events
 */
export interface WindowEvents {
    /** عند إنشاء النافذة / On window created */
    created?: () => void;
    /** عند إظهار النافذة / On window shown */
    shown?: () => void;
    /** عند إخفاء النافذة / On window hidden */
    hidden?: () => void;
    /** عند تصغير النافذة / On window minimized */
    minimized?: () => void;
    /** عند تكبير النافذة / On window maximized */
    maximized?: () => void;
    /** عند استعادة النافذة / On window restored */
    restored?: () => void;
    /** عند تغيير حجم النافذة / On window resized */
    resized?: (bounds: WindowBounds) => void;
    /** عند تحريك النافذة / On window moved */
    moved?: (bounds: WindowBounds) => void;
    /** عند إغلاق النافذة / On window closed */
    closed?: () => void;
    /** عند التركيز على النافذة / On window focused */
    focused?: () => void;
    /** عند فقدان التركيز / On window blurred */
    blurred?: () => void;
}

/**
 * معلومات النافذة
 * Window information
 */
export interface WindowInfo {
    /** معرف النافذة / Window ID */
    id: number;
    /** نوع النافذة / Window type */
    type: WindowType;
    /** عنوان النافذة / Window title */
    title: string;
    /** حدود النافذة / Window bounds */
    bounds: WindowBounds;
    /** حالة النافذة / Window state */
    state: WindowState;
    /** هل النافذة مرئية / Is window visible */
    visible: boolean;
    /** هل النافذة نشطة / Is window active */
    active: boolean;
    /** وقت الإنشاء / Creation time */
    createdAt: Date;
    /** آخر تحديث / Last updated */
    updatedAt: Date;
}

/**
 * خيارات إنشاء النافذة
 * Window creation options
 */
export interface CreateWindowOptions extends WindowConfig {
    /** نوع النافذة / Window type */
    type: WindowType;
    /** مسار الملف / File path */
    url?: string;
    /** أحداث النافذة / Window events */
    events?: WindowEvents;
    /** النافذة الأب / Parent window */
    parent?: number;
    /** هل النافذة مودال / Is window modal */
    modal?: boolean;
    /** إعدادات الويب / Web preferences */
    webPreferences?: {
        /** تمكين Node.js / Enable Node.js */
        nodeIntegration?: boolean;
        /** تمكين السياق المعزول / Enable context isolation */
        contextIsolation?: boolean;
        /** مسار preload / Preload script path */
        preload?: string;
        /** تمكين الويب الآمن / Enable web security */
        webSecurity?: boolean;
        /** السماح بتشغيل محتوى غير آمن / Allow running insecure content */
        allowRunningInsecureContent?: boolean;
    };
}

/**
 * إعدادات مدير النوافذ
 * Window manager settings
 */
export interface WindowManagerConfig {
    /** الحد الأقصى لعدد النوافذ / Maximum window count */
    maxWindows: number;
    /** النافذة الافتراضية / Default window */
    defaultWindow: WindowType;
    /** إعدادات النوافذ / Window configurations */
    windowConfigs: Record<WindowType, Partial<WindowConfig>>;
    /** تمكين حفظ الحالة / Enable state persistence */
    persistState: boolean;
    /** مسار ملف الحالة / State file path */
    stateFilePath?: string;
}
