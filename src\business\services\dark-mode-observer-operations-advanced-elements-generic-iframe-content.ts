/**
 * معالجة محتوى iframe في الوضع المظلم
 * iframe content processing for dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeConfig } from './dark-mode-config';

/**
 * فئة معالجة محتوى iframe
 * iframe content processing class
 */
export class DarkModeObserverOperationsAdvancedElementsGenericIframeContent {

    /** معالجة محتوى iframe / Process iframe content */
    public static processIframeContent(element: HTMLIFrameElement, config: DarkModeConfig): void {
        try {
            // التحقق من إمكانية الوصول للمحتوى
            if (this.canAccessIframeContent(element)) {
                this.processAccessibleContent(element, config);
            } else {
                this.processInaccessibleContent(element, config);
            }

        } catch (error) {
            console.error('خطأ في معالجة محتوى iframe:', error);
            this.handleContentError(element, error);
        }
    }

    /** التحقق من إمكانية الوصول للمحتوى / Check if content is accessible */
    public static canAccessIframeContent(element: HTMLIFrameElement): boolean {
        try {
            // محاولة الوصول للمحتوى
            const contentDocument = element.contentDocument || element.contentWindow?.document;
            return contentDocument !== null && contentDocument !== undefined;
        } catch (error) {
            // خطأ CORS أو أمان
            return false;
        }
    }

    /** معالجة المحتوى القابل للوصول / Process accessible content */
    public static processAccessibleContent(element: HTMLIFrameElement, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsGenericIframeContentProcessing.processAccessibleContent(element, config);
    }

    /** معالجة المحتوى غير القابل للوصول / Process inaccessible content */
    public static processInaccessibleContent(element: HTMLIFrameElement, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsGenericIframeContentProcessing.processInaccessibleContent(element, config);
    }

    /** تطبيق الوضع المظلم على المحتوى / Apply dark mode to content */
    public static applyDarkModeToContent(document: Document, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsGenericIframeContentProcessing.applyDarkModeToContent(document, config);
    }

    /** حقن CSS في المستند / Inject CSS into document */
    public static injectCSS(document: Document, css: string): void {
        DarkModeObserverOperationsAdvancedElementsGenericIframeContentProcessing.injectCSS(document, css);
    }

    /** مراقبة تغييرات المحتوى / Observe content changes */
    public static observeContentChanges(document: Document, config: DarkModeConfig): void {
        DarkModeObserverOperationsAdvancedElementsGenericIframeContentProcessing.observeContentChanges(document, config);
    }

    /** معالجة خطأ المحتوى / Handle content error */
    public static handleContentError(element: HTMLIFrameElement, error: any): void {
        console.warn('تعذر معالجة محتوى iframe:', error.message);

        // تطبيق معالجة بديلة
        element.style.filter = 'brightness(0.8) contrast(1.2)';
        element.style.border = '1px solid rgba(255, 255, 255, 0.2)';
    }
}
