/**
 * العمليات الأساسية للملفات لأداة التحقق المبسطة - ملف التفويض
 * Simple verification basic file operations - Delegation file
 *
 * هذا الملف يفوض جميع العمليات للملفات المتخصصة
 * This file delegates all operations to specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { SimpleVerificationCoreUtilsFileOperationsBasicCore } from './simple-verification-core-utils-file-operations-basic-core';
import { SimpleVerificationCoreUtilsFileOperationsBasicInfo } from './simple-verification-core-utils-file-operations-basic-info';
import { SimpleVerificationConfig } from './simple-verification-types';

/**
 * فئة العمليات الأساسية للملفات - التفويض
 * Basic file operations class - Delegation
 */
export class SimpleVerificationCoreUtilsFileOperationsBasic {

    /**
     * الحصول على جميع الملفات - تفويض للوحدة الجوهرية
     * Get all files - Delegate to core module
     */
    public static async getAllFiles(
        projectRoot: string,
        config: SimpleVerificationConfig
    ): Promise<string[]> {
        // تفويض الحصول على الملفات للوحدة الجوهرية
        // Delegate file retrieval to core module
        return await SimpleVerificationCoreUtilsFileOperationsBasicCore.getAllFiles(projectRoot, config);
    }

    /**
     * فحص وجود الملف - تفويض للوحدة الجوهرية
     * Check if file exists - Delegate to core module
     */
    public static fileExists(filePath: string): boolean {
        // تفويض فحص وجود الملف للوحدة الجوهرية
        // Delegate file existence check to core module
        return SimpleVerificationCoreUtilsFileOperationsBasicCore.fileExists(filePath);
    }

    /**
     * فحص وجود المجلد - تفويض للوحدة الجوهرية
     * Check if directory exists - Delegate to core module
     */
    public static directoryExists(dirPath: string): boolean {
        // تفويض فحص وجود المجلد للوحدة الجوهرية
        // Delegate directory existence check to core module
        return SimpleVerificationCoreUtilsFileOperationsBasicCore.directoryExists(dirPath);
    }

    /**
     * قراءة محتوى الملف - تفويض للوحدة الجوهرية
     * Read file content - Delegate to core module
     */
    public static readFileContent(filePath: string): string | null {
        // تفويض قراءة المحتوى للوحدة الجوهرية
        // Delegate content reading to core module
        return SimpleVerificationCoreUtilsFileOperationsBasicCore.readFileContent(filePath);
    }

    /**
     * تنظيف مسار الملف - تفويض للوحدة الجوهرية
     * Clean file path - Delegate to core module
     */
    public static cleanFilePath(filePath: string, projectRoot: string): string {
        // تفويض تنظيف المسار للوحدة الجوهرية
        // Delegate path cleaning to core module
        return SimpleVerificationCoreUtilsFileOperationsBasicCore.cleanFilePath(filePath, projectRoot);
    }

    /**
     * الحصول على معلومات الملف الأساسية - تفويض لوحدة المعلومات
     * Get basic file information - Delegate to info module
     */
    public static getBasicFileInfo(filePath: string): {
        exists: boolean;
        size: number;
        extension: string;
        basename: string;
        directory: string;
    } {
        // تفويض الحصول على معلومات الملف لوحدة المعلومات
        // Delegate file info retrieval to info module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfo.getBasicFileInfo(filePath);
    }

    /**
     * فحص امتداد الملف - تفويض للوحدة الجوهرية
     * Check file extension - Delegate to core module
     */
    public static hasValidExtension(filePath: string, validExtensions: string[]): boolean {
        // تفويض فحص الامتداد للوحدة الجوهرية
        // Delegate extension check to core module
        return SimpleVerificationCoreUtilsFileOperationsBasicCore.hasValidExtension(filePath, validExtensions);
    }

    /**
     * فحص حجم الملف - تفويض للوحدة الجوهرية
     * Check file size - Delegate to core module
     */
    public static getFileSize(filePath: string): number {
        // تفويض فحص الحجم للوحدة الجوهرية
        // Delegate size check to core module
        return SimpleVerificationCoreUtilsFileOperationsBasicCore.getFileSize(filePath);
    }

    /**
     * فحص تاريخ تعديل الملف - تفويض للوحدة الجوهرية
     * Check file modification date - Delegate to core module
     */
    public static getFileModificationDate(filePath: string): Date | null {
        // تفويض فحص تاريخ التعديل للوحدة الجوهرية
        // Delegate modification date check to core module
        return SimpleVerificationCoreUtilsFileOperationsBasicCore.getFileModificationDate(filePath);
    }

    /**
     * فحص صلاحيات الملف - تفويض للوحدة الجوهرية
     * Check file permissions - Delegate to core module
     */
    public static isFileReadable(filePath: string): boolean {
        // تفويض فحص صلاحيات القراءة للوحدة الجوهرية
        // Delegate read permissions check to core module
        return SimpleVerificationCoreUtilsFileOperationsBasicCore.isFileReadable(filePath);
    }

    /**
     * فحص إذا كان الملف قابل للكتابة - تفويض للوحدة الجوهرية
     * Check if file is writable - Delegate to core module
     */
    public static isFileWritable(filePath: string): boolean {
        // تفويض فحص صلاحيات الكتابة للوحدة الجوهرية
        // Delegate write permissions check to core module
        return SimpleVerificationCoreUtilsFileOperationsBasicCore.isFileWritable(filePath);
    }

    /**
     * الحصول على قائمة الملفات في مجلد - تفويض للوحدة الجوهرية
     * Get list of files in directory - Delegate to core module
     */
    public static getFilesInDirectory(dirPath: string): string[] {
        // تفويض الحصول على قائمة الملفات للوحدة الجوهرية
        // Delegate files list retrieval to core module
        return SimpleVerificationCoreUtilsFileOperationsBasicCore.getFilesInDirectory(dirPath);
    }
}
