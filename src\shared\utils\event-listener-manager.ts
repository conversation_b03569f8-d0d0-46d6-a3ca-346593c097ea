/**
 * مدير مستمعي الأحداث
 * Event listener manager
 * 
 * هذا الملف يحتوي على منطق إدارة مستمعي الأحداث
 * This file contains event listener management logic
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * معلومات مستمع الأحداث
 * Event listener information
 */
interface EventListenerInfo {
    /** نوع الحدث / Event type */
    type: string;
    /** مستمع الحدث / Event listener */
    listener: EventListener;
    /** خيارات الحدث / Event options */
    options?: boolean | AddEventListenerOptions;
}

/**
 * مدير مستمعي الأحداث
 * Event listener manager class
 */
export class EventListenerManager {
    private readonly eventListeners: Map<EventTarget, EventListenerInfo[]> = new Map();

    /**
     * إضافة مستمع حدث للإدارة
     * Adds an event listener for management
     * 
     * @param target - هدف الحدث / Event target
     * @param type - نوع الحدث / Event type
     * @param listener - مستمع الحدث / Event listener
     * @param options - خيارات الحدث / Event options
     * 
     * @example
     * ```typescript
     * eventListenerManager.addEventListener(document, 'click', () => {});
     * ```
     */
    public addEventListener(
        target: EventTarget,
        type: string,
        listener: EventListener,
        options?: boolean | AddEventListenerOptions
    ): void {
        target.addEventListener(type, listener, options);

        if (!this.eventListeners.has(target)) {
            this.eventListeners.set(target, []);
        }

        this.eventListeners.get(target)!.push({
            type,
            listener,
            options
        });
    }

    /**
     * إزالة مستمع حدث من الإدارة
     * Removes an event listener from management
     * 
     * @param target - هدف الحدث / Event target
     * @param type - نوع الحدث / Event type
     * @param listener - مستمع الحدث / Event listener
     * 
     * @example
     * ```typescript
     * eventListenerManager.removeEventListener(document, 'click', listener);
     * ```
     */
    public removeEventListener(
        target: EventTarget,
        type: string,
        listener: EventListener
    ): void {
        target.removeEventListener(type, listener);

        const listeners = this.eventListeners.get(target);
        if (listeners) {
            const index = listeners.findIndex(l => l.type === type && l.listener === listener);
            if (index !== -1) {
                listeners.splice(index, 1);
            }

            if (listeners.length === 0) {
                this.eventListeners.delete(target);
            }
        }
    }

    /**
     * تنظيف جميع مستمعي الأحداث
     * Clears all event listeners
     * 
     * @example
     * ```typescript
     * eventListenerManager.clearAll();
     * ```
     */
    public clearAll(): void {
        for (const [target, listeners] of this.eventListeners) {
            for (const { type, listener } of listeners) {
                target.removeEventListener(type, listener);
            }
        }
        this.eventListeners.clear();
    }

    /**
     * الحصول على عدد مستمعي الأحداث النشطة
     * Gets the count of active event listeners
     * 
     * @returns number - عدد مستمعي الأحداث النشطة / Active event listener count
     * 
     * @example
     * ```typescript
     * const count = eventListenerManager.getEventListenerCount();
     * ```
     */
    public getEventListenerCount(): number {
        let count = 0;
        for (const listeners of this.eventListeners.values()) {
            count += listeners.length;
        }
        return count;
    }

    /**
     * الحصول على عدد الأهداف النشطة
     * Gets the count of active targets
     * 
     * @returns number - عدد الأهداف النشطة / Active target count
     * 
     * @example
     * ```typescript
     * const count = eventListenerManager.getTargetCount();
     * ```
     */
    public getTargetCount(): number {
        return this.eventListeners.size;
    }

    /**
     * التحقق من وجود مستمع حدث
     * Checks if an event listener exists
     * 
     * @param target - هدف الحدث / Event target
     * @param type - نوع الحدث / Event type
     * @param listener - مستمع الحدث / Event listener
     * @returns boolean - هل المستمع موجود / Listener exists
     * 
     * @example
     * ```typescript
     * const exists = eventListenerManager.hasEventListener(document, 'click', listener);
     * ```
     */
    public hasEventListener(
        target: EventTarget,
        type: string,
        listener: EventListener
    ): boolean {
        const listeners = this.eventListeners.get(target);
        if (!listeners) {
            return false;
        }

        return listeners.some(l => l.type === type && l.listener === listener);
    }

    /**
     * الحصول على مستمعي الأحداث لهدف معين
     * Gets event listeners for a specific target
     * 
     * @param target - هدف الحدث / Event target
     * @returns EventListenerInfo[] - قائمة مستمعي الأحداث / Event listener list
     * 
     * @example
     * ```typescript
     * const listeners = eventListenerManager.getListenersForTarget(document);
     * ```
     */
    public getListenersForTarget(target: EventTarget): EventListenerInfo[] {
        return this.eventListeners.get(target) || [];
    }

    /**
     * إزالة جميع مستمعي الأحداث لهدف معين
     * Removes all event listeners for a specific target
     * 
     * @param target - هدف الحدث / Event target
     * 
     * @example
     * ```typescript
     * eventListenerManager.removeAllListenersForTarget(document);
     * ```
     */
    public removeAllListenersForTarget(target: EventTarget): void {
        const listeners = this.eventListeners.get(target);
        if (listeners) {
            for (const { type, listener } of listeners) {
                target.removeEventListener(type, listener);
            }
            this.eventListeners.delete(target);
        }
    }

    /**
     * إضافة مستمع حدث مؤقت
     * Adds a temporary event listener
     * 
     * @param target - هدف الحدث / Event target
     * @param type - نوع الحدث / Event type
     * @param listener - مستمع الحدث / Event listener
     * @param timeout - المهلة الزمنية بالميلي ثانية / Timeout in milliseconds
     * @param options - خيارات الحدث / Event options
     * @returns NodeJS.Timeout - معرف المؤقت / Timer ID
     * 
     * @example
     * ```typescript
     * const timerId = eventListenerManager.addTemporaryEventListener(
     *     document,
     *     'click',
     *     () => console.log('Clicked'),
     *     5000
     * );
     * ```
     */
    public addTemporaryEventListener(
        target: EventTarget,
        type: string,
        listener: EventListener,
        timeout: number,
        options?: boolean | AddEventListenerOptions
    ): NodeJS.Timeout {
        this.addEventListener(target, type, listener, options);

        return setTimeout(() => {
            this.removeEventListener(target, type, listener);
        }, timeout);
    }

    /**
     * إضافة مستمع حدث لمرة واحدة
     * Adds a one-time event listener
     * 
     * @param target - هدف الحدث / Event target
     * @param type - نوع الحدث / Event type
     * @param listener - مستمع الحدث / Event listener
     * @param options - خيارات الحدث / Event options
     * 
     * @example
     * ```typescript
     * eventListenerManager.addOneTimeEventListener(
     *     document,
     *     'click',
     *     () => console.log('Clicked once')
     * );
     * ```
     */
    public addOneTimeEventListener(
        target: EventTarget,
        type: string,
        listener: EventListener,
        options?: boolean | AddEventListenerOptions
    ): void {
        const oneTimeListener = (event: Event) => {
            listener(event);
            this.removeEventListener(target, type, oneTimeListener);
        };

        this.addEventListener(target, type, oneTimeListener, options);
    }

    /**
     * إضافة مستمع حدث مع شرط
     * Adds a conditional event listener
     * 
     * @param target - هدف الحدث / Event target
     * @param type - نوع الحدث / Event type
     * @param listener - مستمع الحدث / Event listener
     * @param condition - الشرط / Condition function
     * @param options - خيارات الحدث / Event options
     * 
     * @example
     * ```typescript
     * eventListenerManager.addConditionalEventListener(
     *     document,
     *     'click',
     *     () => console.log('Clicked with condition'),
     *     (event) => event.target instanceof HTMLButtonElement
     * );
     * ```
     */
    public addConditionalEventListener(
        target: EventTarget,
        type: string,
        listener: EventListener,
        condition: (event: Event) => boolean,
        options?: boolean | AddEventListenerOptions
    ): void {
        const conditionalListener = (event: Event) => {
            if (condition(event)) {
                listener(event);
            }
        };

        this.addEventListener(target, type, conditionalListener, options);
    }
}
