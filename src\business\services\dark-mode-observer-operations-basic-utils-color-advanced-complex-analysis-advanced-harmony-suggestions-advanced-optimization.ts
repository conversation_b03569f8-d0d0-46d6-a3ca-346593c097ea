/**
 * تحسين الألوان المتقدم
 * Advanced color optimization
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeObserverOperationsBasicUtilsColorCore } from './dark-mode-observer-operations-basic-utils-color-core';
import { DarkModeObserverOperationsBasicUtilsColorAdvancedBasic } from './dark-mode-observer-operations-basic-utils-color-advanced-basic';
import { DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic } from './dark-mode-observer-operations-basic-utils-color-advanced-complex-analysis-basic';

/**
 * فئة تحسين الألوان المتقدم
 * Advanced color optimization class
 */
export class DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisAdvancedHarmonySuggestionsAdvancedOptimization {

    /**
     * تحسين لون للوضع المظلم
     * Optimize color for dark mode
     */
    public static optimizeForDarkMode(color: string): string {
        const analysis = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(color);
        
        if (!analysis.hsl) {
            return color;
        }

        let { h, s, l } = analysis.hsl;

        // تعديل السطوع للوضع المظلم
        if (l > 70) {
            l = Math.max(20, l - 50); // تقليل السطوع للألوان الفاتحة
        } else if (l < 30) {
            l = Math.min(80, l + 30); // زيادة السطوع للألوان المظلمة جداً
        }

        // تعديل التشبع
        if (s > 80) {
            s = Math.max(60, s - 20); // تقليل التشبع العالي
        } else if (s < 20) {
            s = Math.min(40, s + 20); // زيادة التشبع المنخفض
        }

        const optimizedRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(h, s, l);
        return DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(optimizedRgb.r, optimizedRgb.g, optimizedRgb.b);
    }

    /**
     * تحسين لون للوضع الفاتح
     * Optimize color for light mode
     */
    public static optimizeForLightMode(color: string): string {
        const analysis = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(color);
        
        if (!analysis.hsl) {
            return color;
        }

        let { h, s, l } = analysis.hsl;

        // تعديل السطوع للوضع الفاتح
        if (l < 30) {
            l = Math.min(80, l + 50); // زيادة السطوع للألوان المظلمة
        } else if (l > 90) {
            l = Math.max(70, l - 20); // تقليل السطوع للألوان الفاتحة جداً
        }

        // تعديل التشبع
        if (s < 30) {
            s = Math.min(60, s + 30); // زيادة التشبع المنخفض
        } else if (s > 90) {
            s = Math.max(70, s - 20); // تقليل التشبع العالي جداً
        }

        const optimizedRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(h, s, l);
        return DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(optimizedRgb.r, optimizedRgb.g, optimizedRgb.b);
    }

    /**
     * تحسين التباين
     * Optimize contrast
     */
    public static optimizeContrast(foregroundColor: string, backgroundColor: string, targetRatio: number = 4.5): string {
        const fgAnalysis = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(foregroundColor);
        const bgAnalysis = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(backgroundColor);
        
        if (!fgAnalysis.hsl || !bgAnalysis.hsl) {
            return foregroundColor;
        }

        let { h, s, l } = fgAnalysis.hsl;
        const bgL = bgAnalysis.hsl.l;

        // تحديد اتجاه التعديل بناءً على خلفية
        const shouldLighten = bgL < 50;
        
        // تعديل السطوع تدريجياً حتى الوصول للتباين المطلوب
        for (let i = 0; i < 20; i++) {
            const testRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(h, s, l);
            const testColor = DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(testRgb.r, testRgb.g, testRgb.b);
            
            const contrast = this.calculateContrastRatio(testColor, backgroundColor);
            
            if (contrast >= targetRatio) {
                return testColor;
            }

            // تعديل السطوع
            if (shouldLighten) {
                l = Math.min(95, l + 5);
            } else {
                l = Math.max(5, l - 5);
            }
        }

        // إرجاع أفضل نتيجة حتى لو لم تصل للهدف
        const finalRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(h, s, l);
        return DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(finalRgb.r, finalRgb.g, finalRgb.b);
    }

    /**
     * حساب نسبة التباين
     * Calculate contrast ratio
     */
    private static calculateContrastRatio(color1: string, color2: string): number {
        const rgb1 = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(color1).rgb;
        const rgb2 = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(color2).rgb;
        
        if (!rgb1 || !rgb2) {
            return 1;
        }

        const l1 = this.getRelativeLuminance(rgb1.r, rgb1.g, rgb1.b);
        const l2 = this.getRelativeLuminance(rgb2.r, rgb2.g, rgb2.b);
        
        const lighter = Math.max(l1, l2);
        const darker = Math.min(l1, l2);
        
        return (lighter + 0.05) / (darker + 0.05);
    }

    /**
     * حساب السطوع النسبي
     * Calculate relative luminance
     */
    private static getRelativeLuminance(r: number, g: number, b: number): number {
        const [rs, gs, bs] = [r, g, b].map(c => {
            c = c / 255;
            return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
        });
        
        return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
    }

    /**
     * اقتراح ألوان بديلة
     * Suggest alternative colors
     */
    public static suggestAlternatives(color: string, count: number = 5): string[] {
        const analysis = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(color);
        
        if (!analysis.hsl) {
            return [color];
        }

        const { h, s, l } = analysis.hsl;
        const alternatives: string[] = [];

        // اقتراحات متنوعة
        const variations = [
            { h: h, s: Math.max(10, s - 20), l: Math.min(90, l + 15) }, // أقل تشبعاً وأفتح
            { h: h, s: Math.min(90, s + 20), l: Math.max(10, l - 15) }, // أكثر تشبعاً وأغمق
            { h: (h + 30) % 360, s: s, l: l }, // تغيير طفيف في الدرجة
            { h: (h - 30 + 360) % 360, s: s, l: l }, // تغيير طفيف في الاتجاه المعاكس
            { h: h, s: s, l: Math.abs(l - 50) + 25 } // عكس السطوع
        ];

        for (let i = 0; i < Math.min(count, variations.length); i++) {
            const variation = variations[i];
            const rgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(variation.h, variation.s, variation.l);
            alternatives.push(DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(rgb.r, rgb.g, rgb.b));
        }

        return alternatives;
    }

    /**
     * تحسين لوحة ألوان كاملة
     * Optimize complete color palette
     */
    public static optimizePalette(colors: string[], mode: 'dark' | 'light' = 'dark'): string[] {
        return colors.map(color => {
            return mode === 'dark' 
                ? this.optimizeForDarkMode(color)
                : this.optimizeForLightMode(color);
        });
    }

    /**
     * التحقق من إمكانية الوصول
     * Check accessibility
     */
    public static checkAccessibility(foregroundColor: string, backgroundColor: string): {
        ratio: number;
        level: 'AAA' | 'AA' | 'A' | 'FAIL';
        isAccessible: boolean;
    } {
        const ratio = this.calculateContrastRatio(foregroundColor, backgroundColor);
        
        let level: 'AAA' | 'AA' | 'A' | 'FAIL';
        let isAccessible: boolean;

        if (ratio >= 7) {
            level = 'AAA';
            isAccessible = true;
        } else if (ratio >= 4.5) {
            level = 'AA';
            isAccessible = true;
        } else if (ratio >= 3) {
            level = 'A';
            isAccessible = false;
        } else {
            level = 'FAIL';
            isAccessible = false;
        }

        return { ratio, level, isAccessible };
    }
}
