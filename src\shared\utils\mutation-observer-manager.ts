/**
 * مدير مراقبات التحولات
 * Mutation observer manager
 * 
 * هذا الملف يحتوي على منطق إدارة مراقبات التحولات
 * This file contains mutation observer management logic
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * مدير مراقبات التحولات
 * Mutation observer manager class
 */
export class MutationObserverManager {
    private readonly observers: Set<MutationObserver> = new Set();

    /**
     * إضافة مراقب للإدارة
     * Adds an observer for management
     * 
     * @param observer - المراقب / Observer
     * @returns MutationObserver - المراقب / Observer
     * 
     * @example
     * ```typescript
     * const observer = new MutationObserver(() => {});
     * mutationObserverManager.addObserver(observer);
     * ```
     */
    public addObserver(observer: MutationObserver): MutationObserver {
        this.observers.add(observer);
        return observer;
    }

    /**
     * إزالة مراقب من الإدارة
     * Removes an observer from management
     * 
     * @param observer - المراقب / Observer
     * 
     * @example
     * ```typescript
     * mutationObserverManager.removeObserver(observer);
     * ```
     */
    public removeObserver(observer: MutationObserver): void {
        observer.disconnect();
        this.observers.delete(observer);
    }

    /**
     * تنظيف جميع المراقبات
     * Clears all observers
     * 
     * @example
     * ```typescript
     * mutationObserverManager.clearAll();
     * ```
     */
    public clearAll(): void {
        for (const observer of this.observers) {
            observer.disconnect();
        }
        this.observers.clear();
    }

    /**
     * الحصول على عدد المراقبات النشطة
     * Gets the count of active observers
     * 
     * @returns number - عدد المراقبات النشطة / Active observer count
     * 
     * @example
     * ```typescript
     * const count = mutationObserverManager.getObserverCount();
     * ```
     */
    public getObserverCount(): number {
        return this.observers.size;
    }

    /**
     * التحقق من وجود مراقب
     * Checks if an observer exists
     * 
     * @param observer - المراقب / Observer
     * @returns boolean - هل المراقب موجود / Observer exists
     * 
     * @example
     * ```typescript
     * const exists = mutationObserverManager.hasObserver(observer);
     * ```
     */
    public hasObserver(observer: MutationObserver): boolean {
        return this.observers.has(observer);
    }

    /**
     * إنشاء مراقب مُدار
     * Creates a managed observer
     * 
     * @param callback - دالة الاستدعاء / Callback function
     * @param options - خيارات المراقب / Observer options
     * @returns MutationObserver - المراقب / Observer
     * 
     * @example
     * ```typescript
     * const observer = mutationObserverManager.createObserver((mutations) => {
     *     console.log('Mutations:', mutations);
     * });
     * ```
     */
    public createObserver(
        callback: MutationCallback,
        options?: MutationObserverInit
    ): MutationObserver {
        const observer = new MutationObserver(callback);
        this.observers.add(observer);
        return observer;
    }

    /**
     * مراقبة عنصر
     * Observe an element
     * 
     * @param observer - المراقب / Observer
     * @param target - العنصر المستهدف / Target element
     * @param options - خيارات المراقبة / Observation options
     * 
     * @example
     * ```typescript
     * const observer = mutationObserverManager.createObserver(() => {});
     * mutationObserverManager.observeElement(observer, document.body, {
     *     childList: true,
     *     subtree: true
     * });
     * ```
     */
    public observeElement(
        observer: MutationObserver,
        target: Node,
        options?: MutationObserverInit
    ): void {
        if (!this.observers.has(observer)) {
            throw new Error('Observer not managed by this manager');
        }
        
        observer.observe(target, options);
    }

    /**
     * إيقاف مراقبة عنصر
     * Stop observing an element
     * 
     * @param observer - المراقب / Observer
     * 
     * @example
     * ```typescript
     * mutationObserverManager.stopObserving(observer);
     * ```
     */
    public stopObserving(observer: MutationObserver): void {
        if (this.observers.has(observer)) {
            observer.disconnect();
        }
    }

    /**
     * الحصول على جميع المراقبات
     * Gets all observers
     * 
     * @returns MutationObserver[] - قائمة المراقبات / Observer list
     * 
     * @example
     * ```typescript
     * const observers = mutationObserverManager.getAllObservers();
     * ```
     */
    public getAllObservers(): MutationObserver[] {
        return Array.from(this.observers);
    }

    /**
     * إنشاء مراقب لتغييرات النص
     * Creates a text change observer
     * 
     * @param target - العنصر المستهدف / Target element
     * @param callback - دالة الاستدعاء / Callback function
     * @returns MutationObserver - المراقب / Observer
     * 
     * @example
     * ```typescript
     * const observer = mutationObserverManager.createTextChangeObserver(
     *     document.getElementById('content'),
     *     (mutations) => console.log('Text changed')
     * );
     * ```
     */
    public createTextChangeObserver(
        target: Node,
        callback: MutationCallback
    ): MutationObserver {
        const observer = this.createObserver(callback);
        this.observeElement(observer, target, {
            characterData: true,
            subtree: true
        });
        return observer;
    }

    /**
     * إنشاء مراقب لتغييرات العناصر الفرعية
     * Creates a child list observer
     * 
     * @param target - العنصر المستهدف / Target element
     * @param callback - دالة الاستدعاء / Callback function
     * @returns MutationObserver - المراقب / Observer
     * 
     * @example
     * ```typescript
     * const observer = mutationObserverManager.createChildListObserver(
     *     document.body,
     *     (mutations) => console.log('Children changed')
     * );
     * ```
     */
    public createChildListObserver(
        target: Node,
        callback: MutationCallback
    ): MutationObserver {
        const observer = this.createObserver(callback);
        this.observeElement(observer, target, {
            childList: true,
            subtree: true
        });
        return observer;
    }

    /**
     * إنشاء مراقب لتغييرات الخصائص
     * Creates an attribute observer
     * 
     * @param target - العنصر المستهدف / Target element
     * @param callback - دالة الاستدعاء / Callback function
     * @param attributeFilter - مرشح الخصائص / Attribute filter
     * @returns MutationObserver - المراقب / Observer
     * 
     * @example
     * ```typescript
     * const observer = mutationObserverManager.createAttributeObserver(
     *     document.body,
     *     (mutations) => console.log('Attributes changed'),
     *     ['class', 'style']
     * );
     * ```
     */
    public createAttributeObserver(
        target: Node,
        callback: MutationCallback,
        attributeFilter?: string[]
    ): MutationObserver {
        const observer = this.createObserver(callback);
        this.observeElement(observer, target, {
            attributes: true,
            attributeFilter,
            attributeOldValue: true
        });
        return observer;
    }
}
