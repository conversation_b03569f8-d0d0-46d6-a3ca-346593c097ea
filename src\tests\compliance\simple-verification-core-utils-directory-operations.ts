/**
 * عمليات المجلدات لأداة التحقق المبسطة - ملف التفويض
 * Simple verification directory operations - Delegation file
 * 
 * هذا الملف يفوض جميع العمليات للملفات المتخصصة
 * This file delegates all operations to specialized files
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { SimpleVerificationConfig } from './simple-verification-types';
import { SimpleVerificationCoreUtilsDirectoryOperationsSingle } from './simple-verification-core-utils-directory-operations-single';
import { SimpleVerificationCoreUtilsDirectoryOperationsTree } from './simple-verification-core-utils-directory-operations-tree';

/**
 * فئة عمليات المجلدات للتحقق المبسط - التفويض
 * Simple verification directory operations class - Delegation
 */
export class SimpleVerificationCoreUtilsDirectoryOperations {

    /**
     * فحص مجلد واحد - تفويض للوحدة المتخصصة
     * Check single directory - Delegate to specialized module
     */
    public static checkSingleDirectory(dirPath: string, config: SimpleVerificationConfig): {
        isValid: boolean;
        issues: string[];
        score: number;
        fileCount: number;
        subdirectoryCount: number;
        followsStructure: boolean;
    } {
        // تفويض فحص المجلد الواحد للوحدة المتخصصة
        // Delegate single directory check to specialized module
        return SimpleVerificationCoreUtilsDirectoryOperationsSingle.checkSingleDirectory(dirPath, config);
    }

    /**
     * فحص شجرة المجلدات - تفويض لوحدة الشجرة
     * Check directory tree - Delegate to tree module
     */
    public static checkDirectoryTree(rootPath: string, config: SimpleVerificationConfig): {
        totalDirectories: number;
        validDirectories: number;
        invalidDirectories: number;
        totalIssues: number;
        averageScore: number;
        results: Array<{
            dirPath: string;
            isValid: boolean;
            issues: string[];
            score: number;
            fileCount: number;
            subdirectoryCount: number;
        }>;
    } {
        // تفويض فحص شجرة المجلدات لوحدة الشجرة
        // Delegate directory tree check to tree module
        return SimpleVerificationCoreUtilsDirectoryOperationsTree.checkDirectoryTree(rootPath, config);
    }

    /**
     * الحصول على جميع المجلدات - تفويض لوحدة الشجرة
     * Get all directories - Delegate to tree module
     */
    public static getAllDirectories(rootPath: string): string[] {
        // تفويض الحصول على جميع المجلدات لوحدة الشجرة
        // Delegate get all directories to tree module
        return SimpleVerificationCoreUtilsDirectoryOperationsTree.getAllDirectories(rootPath);
    }

    /**
     * إنشاء خريطة شجرة المجلدات - تفويض لوحدة الشجرة
     * Generate directory tree map - Delegate to tree module
     */
    public static generateDirectoryTreeMap(rootPath: string): {
        tree: any;
        statistics: {
            totalDirectories: number;
            maxDepth: number;
            averageFilesPerDirectory: number;
            largestDirectory: string;
            largestDirectoryFileCount: number;
        };
    } {
        // تفويض إنشاء خريطة شجرة المجلدات لوحدة الشجرة
        // Delegate directory tree map generation to tree module
        return SimpleVerificationCoreUtilsDirectoryOperationsTree.generateDirectoryTreeMap(rootPath);
    }
}
