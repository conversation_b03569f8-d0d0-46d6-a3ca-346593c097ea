/**
 * معالجة أحداث الصور في الوضع المظلم
 * Image event handling for dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeConfig } from './dark-mode-config';
import { DarkModeObserverOperationsAdvancedElementsImageCore } from './dark-mode-observer-operations-advanced-elements-image-core';

/**
 * فئة معالجة أحداث الصور
 * Image event handling class
 */
export class DarkModeObserverOperationsAdvancedElementsImageEvents {

    /** معالجة تحميل الصورة / Handle image loading */
    public static handleImageLoading(element: HTMLImageElement, config: DarkModeConfig): void {
        // إضافة مؤشر التحميل
        this.addLoadingIndicator(element);

        // حدث اكتمال التحميل
        element.addEventListener('load', () => {
            this.onImageLoad(element, config);
        });

        // حدث بداية التحميل
        if (!element.complete) {
            this.onImageLoadStart(element, config);
        } else {
            this.onImageLoad(element, config);
        }
    }

    /** إضافة مؤشر التحميل / Add loading indicator */
    private static addLoadingIndicator(element: HTMLImageElement): void {
        const container = element.parentElement;
        if (!container) return;

        const loader = document.createElement('div');
        loader.className = 'dark-mode-image-loader';
        loader.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 32px;
            height: 32px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            z-index: 1000;
        `;

        // إضافة الأنيميشن
        if (!document.querySelector('#dark-mode-spinner-style')) {
            const style = document.createElement('style');
            style.id = 'dark-mode-spinner-style';
            style.textContent = `
                @keyframes spin {
                    0% { transform: translate(-50%, -50%) rotate(0deg); }
                    100% { transform: translate(-50%, -50%) rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }

        container.style.position = 'relative';
        container.appendChild(loader);
    }

    /** حدث بداية تحميل الصورة / Image load start event */
    private static onImageLoadStart(element: HTMLImageElement, config: DarkModeConfig): void {
        element.style.opacity = '0.5';
        element.style.filter = 'blur(2px)';
    }

    /** حدث اكتمال تحميل الصورة / Image load event */
    private static onImageLoad(element: HTMLImageElement, config: DarkModeConfig): void {
        // إزالة مؤشر التحميل
        const container = element.parentElement;
        if (container) {
            const loader = container.querySelector('.dark-mode-image-loader');
            if (loader) {
                loader.remove();
            }
        }

        // إظهار الصورة تدريجياً
        element.style.opacity = '1';
        element.style.filter = element.style.filter.replace('blur(2px)', '');
    }

    /** معالجة أخطاء الصورة / Handle image errors */
    public static handleImageErrors(element: HTMLImageElement, config: DarkModeConfig): void {
        element.addEventListener('error', () => {
            this.onImageError(element, config);
        });
    }

    /** حدث خطأ الصورة / Image error event */
    private static onImageError(element: HTMLImageElement, config: DarkModeConfig): void {
        // إزالة مؤشر التحميل
        const container = element.parentElement;
        if (container) {
            const loader = container.querySelector('.dark-mode-image-loader');
            if (loader) {
                loader.remove();
            }
        }

        // إضافة صورة بديلة
        DarkModeObserverOperationsAdvancedElementsImageCore.addPlaceholderImage(element, config);
    }

    /** إضافة تأثيرات التفاعل للصور الرمزية / Add avatar interaction effects */
    public static addAvatarInteractionEffects(element: HTMLImageElement): void {
        element.addEventListener('mouseenter', () => {
            element.style.transform = 'scale(1.05)';
            element.style.borderColor = 'rgba(255, 255, 255, 0.4)';
        });

        element.addEventListener('mouseleave', () => {
            element.style.transform = 'scale(1)';
            element.style.borderColor = 'rgba(255, 255, 255, 0.2)';
        });
    }

    /** إضافة تأثيرات التفاعل للصور المصغرة / Add thumbnail interaction effects */
    public static addThumbnailInteractionEffects(element: HTMLImageElement): void {
        element.addEventListener('mouseenter', () => {
            element.style.transform = 'translateY(-2px)';
            element.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.5)';
        });

        element.addEventListener('mouseleave', () => {
            element.style.transform = 'translateY(0)';
            element.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.4)';
        });
    }

    /** إضافة تأثيرات التفاعل حسب النوع / Add type-specific interaction effects */
    public static addInteractionEffects(element: HTMLImageElement, imageType: string): void {
        switch (imageType) {
            case 'avatar':
                this.addAvatarInteractionEffects(element);
                break;
            case 'thumbnail':
                this.addThumbnailInteractionEffects(element);
                break;
            default:
                // لا توجد تأثيرات خاصة للأنواع الأخرى
                break;
        }
    }
}
