/**
 * إدارة نطاقات مانع الإعلانات
 * Ad blocker domains manager
 * 
 * هذا الملف يحتوي على إدارة النطاقات المحظورة والمسموحة
 * This file contains blocked and allowed domains management
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { BLOCKED_AD_DOMAINS, ESSENTIAL_PATTERNS } from './ad-blocker-config';

/**
 * مدير نطاقات مانع الإعلانات
 * Ad blocker domains manager
 */
export class AdBlockerDomainsManager {
    private blockedDomains: Set<string>;
    private essentialPatterns: string[];

    /**
     * منشئ مدير النطاقات
     * Domains manager constructor
     */
    constructor() {
        this.blockedDomains = new Set(BLOCKED_AD_DOMAINS);
        this.essentialPatterns = [...ESSENTIAL_PATTERNS];
    }

    /**
     * فحص ما إذا كان الطلب يجب حظره
     * Check if request should be blocked
     * 
     * @param url - رابط الطلب
     * @returns true إذا كان يجب حظر الطلب
     */
    public shouldBlockRequest(url: string): boolean {
        try {
            // فحص الأنماط الأساسية أولاً
            if (this.isEssentialRequest(url)) {
                return false;
            }

            // فحص النطاقات المحظورة
            return this.isBlockedDomain(url);
        } catch (error) {
            console.warn('خطأ في فحص الطلب / Error checking request:', error);
            return false; // في حالة الخطأ، لا نحظر الطلب
        }
    }

    /**
     * فحص ما إذا كان الطلب أساسي
     * Check if request is essential
     * 
     * @param url - رابط الطلب
     * @returns true إذا كان الطلب أساسي
     */
    private isEssentialRequest(url: string): boolean {
        const lowerUrl = url.toLowerCase();
        
        return this.essentialPatterns.some(pattern => {
            const lowerPattern = pattern.toLowerCase();
            return lowerUrl.includes(lowerPattern);
        });
    }

    /**
     * فحص ما إذا كان النطاق محظور
     * Check if domain is blocked
     * 
     * @param url - رابط الطلب
     * @returns true إذا كان النطاق محظور
     */
    private isBlockedDomain(url: string): boolean {
        try {
            const urlObj = new URL(url);
            const hostname = urlObj.hostname.toLowerCase();
            
            // فحص النطاقات المحظورة
            for (const domain of this.blockedDomains) {
                if (hostname.includes(domain.toLowerCase())) {
                    return true;
                }
            }

            // فحص كلمات مفتاحية إضافية في الرابط
            const suspiciousKeywords = [
                'ads', 'ad', 'advertisement', 'advertising',
                'doubleclick', 'googleads', 'adsense',
                'analytics', 'tracking', 'metrics'
            ];

            const lowerUrl = url.toLowerCase();
            return suspiciousKeywords.some(keyword => 
                lowerUrl.includes(`/${keyword}/`) || 
                lowerUrl.includes(`/${keyword}?`) ||
                lowerUrl.includes(`&${keyword}=`) ||
                hostname.includes(keyword)
            );
        } catch (error) {
            console.warn('خطأ في تحليل الرابط / Error parsing URL:', error);
            return false;
        }
    }

    /**
     * إضافة نطاق إلى القائمة المحظورة
     * Add domain to blocked list
     * 
     * @param domain - النطاق المراد حظره
     */
    public addBlockedDomain(domain: string): void {
        if (domain && domain.trim()) {
            this.blockedDomains.add(domain.trim().toLowerCase());
        }
    }

    /**
     * إزالة نطاق من القائمة المحظورة
     * Remove domain from blocked list
     * 
     * @param domain - النطاق المراد إزالته
     */
    public removeBlockedDomain(domain: string): void {
        this.blockedDomains.delete(domain.toLowerCase());
    }

    /**
     * إضافة نمط أساسي
     * Add essential pattern
     * 
     * @param pattern - النمط المراد إضافته
     */
    public addEssentialPattern(pattern: string): void {
        if (pattern && pattern.trim() && !this.essentialPatterns.includes(pattern)) {
            this.essentialPatterns.push(pattern.trim());
        }
    }

    /**
     * إزالة نمط أساسي
     * Remove essential pattern
     * 
     * @param pattern - النمط المراد إزالته
     */
    public removeEssentialPattern(pattern: string): void {
        const index = this.essentialPatterns.indexOf(pattern);
        if (index > -1) {
            this.essentialPatterns.splice(index, 1);
        }
    }

    /**
     * الحصول على قائمة النطاقات المحظورة
     * Get blocked domains list
     * 
     * @returns مصفوفة النطاقات المحظورة
     */
    public getBlockedDomains(): string[] {
        return Array.from(this.blockedDomains);
    }

    /**
     * الحصول على قائمة الأنماط الأساسية
     * Get essential patterns list
     * 
     * @returns مصفوفة الأنماط الأساسية
     */
    public getEssentialPatterns(): string[] {
        return [...this.essentialPatterns];
    }

    /**
     * إعادة تعيين القوائم للقيم الافتراضية
     * Reset lists to default values
     */
    public resetToDefaults(): void {
        this.blockedDomains = new Set(BLOCKED_AD_DOMAINS);
        this.essentialPatterns = [...ESSENTIAL_PATTERNS];
    }

    /**
     * الحصول على إحصائيات النطاقات
     * Get domains statistics
     * 
     * @returns إحصائيات النطاقات
     */
    public getDomainsStats(): {
        blockedDomainsCount: number;
        essentialPatternsCount: number;
    } {
        return {
            blockedDomainsCount: this.blockedDomains.size,
            essentialPatternsCount: this.essentialPatterns.length
        };
    }
}
