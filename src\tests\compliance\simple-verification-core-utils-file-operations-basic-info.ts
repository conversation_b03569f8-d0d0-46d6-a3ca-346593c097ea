/**
 * معلومات الملفات الأساسية - ملف التفويض
 * Basic file information operations - Delegation file
 *
 * هذا الملف يفوض جميع العمليات للملفات المتخصصة
 * This file delegates all operations to specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { SimpleVerificationCoreUtilsFileOperationsBasicInfoCore } from './simple-verification-core-utils-file-operations-basic-info-core';

/**
 * فئة معلومات الملفات الأساسية - التفويض
 * Basic file information class - Delegation
 */
export class SimpleVerificationCoreUtilsFileOperationsBasicInfo {

    /**
     * الحصول على معلومات الملف الأساسية - تفويض للوحدة الجوهرية
     * Get basic file information - Delegate to core module
     */
    public static getBasicFileInfo(filePath: string): {
        exists: boolean;
        size: number;
        extension: string;
        basename: string;
        directory: string;
    } {
        // تفويض الحصول على معلومات الملف للوحدة الجوهرية
        // Delegate file info retrieval to core module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfoCore.getBasicFileInfo(filePath);
    }

    /**
     * الحصول على معلومات الملف المفصلة - تفويض للوحدة المتقدمة
     * Get detailed file information - Delegate to advanced module
     */
    public static getDetailedFileInfo(filePath: string): {
        exists: boolean;
        size: number;
        extension: string;
        basename: string;
        directory: string;
        isReadable: boolean;
        isWritable: boolean;
        modificationDate: Date | null;
        creationDate: Date | null;
    } {
        // تفويض الحصول على معلومات الملف المفصلة للوحدة المتقدمة
        // Delegate detailed file info retrieval to advanced module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvanced.getDetailedFileInfo(filePath);
    }

    /**
     * فحص نوع الملف - تفويض للوحدة الجوهرية
     * Check file type - Delegate to core module
     */
    public static getFileType(filePath: string): {
        isFile: boolean;
        isDirectory: boolean;
        isSymbolicLink: boolean;
        type: string;
    } {
        // تفويض فحص نوع الملف للوحدة الجوهرية
        // Delegate file type check to core module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfoCore.getFileType(filePath);
    }

    /**
     * مقارنة ملفين - تفويض للوحدة المتقدمة
     * Compare two files - Delegate to advanced module
     */
    public static compareFiles(filePath1: string, filePath2: string): {
        bothExist: boolean;
        sameSize: boolean;
        sameModificationDate: boolean;
        sizeDifference: number;
        timeDifference: number;
    } {
        // تفويض مقارنة الملفات للوحدة المتقدمة
        // Delegate file comparison to advanced module
        return SimpleVerificationCoreUtilsFileOperationsBasicInfoAdvanced.compareFiles(filePath1, filePath2);
    }
}
