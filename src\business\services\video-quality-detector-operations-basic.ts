/**
 * العمليات الأساسية لكشف جودة الفيديو
 * Basic video quality detection operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQuality } from '@shared/types';
import { VideoQualityConfig } from './video-quality-config';

/**
 * فئة العمليات الأساسية لكشف الجودة
 * Basic quality detection operations class
 */
export class VideoQualityDetectorOperationsBasic {
    private readonly config: VideoQualityConfig;

    constructor(config: VideoQualityConfig) {
        this.config = config;
    }

    /** كشف الجودة من عنصر الفيديو / Detect quality from video element */
    public detectFromVideoElement(): VideoQuality | null {
        try {
            const video = document.querySelector('video') as HTMLVideoElement;
            if (!video || !video.videoHeight) return null;
            return this.mapHeightToQuality(video.videoHeight);
        } catch (error) {
            console.error('خطأ في كشف الجودة من عنصر الفيديو:', error);
            return null;
        }
    }

    /** كشف الجودة من واجهة المستخدم / Detect quality from UI */
    public detectFromUI(): VideoQuality | null {
        try {
            // البحث عن زر الجودة النشط
            const activeButton = document.querySelector('.ytp-quality-menu .ytp-menuitem[aria-checked="true"]');
            if (activeButton) {
                const quality = this.extractQualityFromText(activeButton.textContent || '');
                if (quality) return quality;
            }

            // البحث في قائمة الجودة
            const qualityItems = document.querySelectorAll('.ytp-quality-menu .ytp-menuitem');
            for (const item of qualityItems) {
                const element = item as HTMLElement;
                if (element.getAttribute('aria-checked') === 'true') {
                    const quality = this.extractQualityFromText(element.textContent || '');
                    if (quality) return quality;
                }
            }

            return null;
        } catch (error) {
            console.error('خطأ في كشف الجودة من واجهة المستخدم:', error);
            return null;
        }
    }

    /** كشف الجودة من البيانات الوصفية / Detect quality from metadata */
    public detectFromMetadata(): VideoQuality | null {
        try {
            const video = document.querySelector('video') as HTMLVideoElement;
            if (!video) return null;

            const videoWidth = video.videoWidth;
            const videoHeight = video.videoHeight;

            if (videoWidth && videoHeight) {
                return this.mapHeightToQuality(videoHeight);
            }

            // البحث في عناصر البيانات
            const dataElements = document.querySelectorAll('[data-quality], [data-resolution]');
            for (const element of dataElements) {
                const htmlElement = element as HTMLElement;
                const quality = htmlElement.dataset.quality || htmlElement.dataset.resolution;
                if (quality) {
                    const mappedQuality = this.extractQualityFromText(quality);
                    if (mappedQuality) return mappedQuality;
                }
            }

            return null;
        } catch (error) {
            console.error('خطأ في كشف الجودة من البيانات الوصفية:', error);
            return null;
        }
    }

    /** كشف الجودة من الشبكة / Detect quality from network */
    public detectFromNetwork(): VideoQuality | null {
        try {
            const performanceEntries = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
            
            for (const entry of performanceEntries) {
                if (entry.name.includes('videoplayback') || entry.name.includes('googlevideo')) {
                    const url = new URL(entry.name);
                    const qualityParam = url.searchParams.get('quality') || 
                                       url.searchParams.get('itag') ||
                                       url.searchParams.get('mime');
                    
                    if (qualityParam) {
                        const quality = this.mapNetworkParamToQuality(qualityParam);
                        if (quality) return quality;
                    }
                }
            }

            return null;
        } catch (error) {
            console.error('خطأ في كشف الجودة من الشبكة:', error);
            return null;
        }
    }

    /** تحويل الارتفاع إلى جودة / Map height to quality */
    private mapHeightToQuality(height: number): VideoQuality {
        if (height >= 2160) return '2160p';
        if (height >= 1440) return '1440p';
        if (height >= 1080) return '1080p';
        if (height >= 720) return '720p';
        if (height >= 480) return '480p';
        if (height >= 360) return '360p';
        if (height >= 240) return '240p';
        return '144p';
    }

    /** استخراج الجودة من النص / Extract quality from text */
    private extractQualityFromText(text: string): VideoQuality | null {
        const cleanText = text.toLowerCase().trim();
        
        const qualityMap: Record<string, VideoQuality> = {
            '2160p': '2160p', '4k': '2160p', '2160': '2160p',
            '1440p': '1440p', '1440': '1440p',
            '1080p': '1080p', '1080': '1080p', 'hd': '1080p',
            '720p': '720p', '720': '720p',
            '480p': '480p', '480': '480p',
            '360p': '360p', '360': '360p',
            '240p': '240p', '240': '240p',
            '144p': '144p', '144': '144p'
        };

        for (const [key, quality] of Object.entries(qualityMap)) {
            if (cleanText.includes(key)) {
                return quality;
            }
        }

        return null;
    }

    /** تحويل معامل الشبكة إلى جودة / Map network parameter to quality */
    private mapNetworkParamToQuality(param: string): VideoQuality | null {
        const paramLower = param.toLowerCase();
        
        const itagMap: Record<string, VideoQuality> = {
            '313': '2160p', '271': '1440p', '137': '1080p',
            '136': '720p', '135': '480p', '134': '360p',
            '133': '240p', '160': '144p'
        };

        for (const [itag, quality] of Object.entries(itagMap)) {
            if (paramLower.includes(itag)) {
                return quality;
            }
        }

        return this.extractQualityFromText(param);
    }

    /** التحقق من صحة الجودة / Validate quality */
    public validateDetectedQuality(quality: VideoQuality): boolean {
        const validQualities: VideoQuality[] = [
            '144p', '240p', '360p', '480p', '720p', '1080p', '1440p', '2160p'
        ];
        return validQualities.includes(quality);
    }

    /** الحصول على الجودات المتاحة / Get available qualities */
    public getAvailableQualities(): VideoQuality[] {
        try {
            const availableQualities: VideoQuality[] = [];
            const qualityItems = document.querySelectorAll('.ytp-quality-menu .ytp-menuitem');

            for (const item of qualityItems) {
                const element = item as HTMLElement;
                const qualityText = this.extractQualityFromText(element.textContent || '');
                if (qualityText && !availableQualities.includes(qualityText)) {
                    availableQualities.push(qualityText);
                }
            }

            return availableQualities.sort((a, b) => {
                const aNum = parseInt(a.replace('p', ''));
                const bNum = parseInt(b.replace('p', ''));
                return bNum - aNum;
            });
        } catch (error) {
            console.error('خطأ في الحصول على الجودات المتاحة:', error);
            return [];
        }
    }
}
