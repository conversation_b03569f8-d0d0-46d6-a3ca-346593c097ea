/**
 * مدير قوائم التطبيق
 * Application menu manager
 * 
 * هذا الملف يحتوي على منطق إدارة قوائم التطبيق
 * This file contains application menu management logic
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 * 
 * @requires electron للقوائم
 * @requires @presentation/windows/window-manager لإدارة النوافذ
 */

import { Menu, MenuItemConstructorOptions, app } from 'electron';
import { WindowManager } from '@presentation/windows/window-manager';

/**
 * مدير القوائم
 * Menu manager class
 */
export class MenuManager {
    private readonly windowManager: WindowManager;

    /**
     * منشئ مدير القوائم
     * Menu manager constructor
     * 
     * @param windowManager - مدير النوافذ
     */
    constructor(windowManager: WindowManager) {
        this.windowManager = windowManager;
    }

    /**
     * إنشاء قائمة التطبيق الرئيسية
     * Creates the main application menu
     * 
     * @returns void
     * 
     * @example
     * ```typescript
     * const menuManager = new MenuManager(windowManager);
     * menuManager.createApplicationMenu();
     * ```
     */
    public createApplicationMenu(): void {
        try {
            const template = this.buildMenuTemplate();
            const menu = Menu.buildFromTemplate(template);
            Menu.setApplicationMenu(menu);
        } catch (error) {
            console.error('خطأ في إنشاء قائمة التطبيق:', error);
        }
    }

    /**
     * بناء قالب القائمة
     * Builds the menu template
     * 
     * @returns MenuItemConstructorOptions[] - قالب القائمة
     */
    private buildMenuTemplate(): MenuItemConstructorOptions[] {
        return [
            this.createFileMenu(),
            this.createViewMenu(),
            this.createToolsMenu(),
            this.createHelpMenu()
        ];
    }

    /**
     * إنشاء قائمة الملف
     * Creates the file menu
     * 
     * @returns MenuItemConstructorOptions - قائمة الملف
     */
    private createFileMenu(): MenuItemConstructorOptions {
        return {
            label: 'ملف / File',
            submenu: [
                {
                    label: 'الإعدادات / Settings',
                    accelerator: 'Ctrl+,',
                    click: async () => {
                        try {
                            await this.windowManager.createSettingsWindow();
                        } catch (error) {
                            console.error('خطأ في فتح نافذة الإعدادات:', error);
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'خروج / Exit',
                    accelerator: 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        };
    }

    /**
     * إنشاء قائمة العرض
     * Creates the view menu
     * 
     * @returns MenuItemConstructorOptions - قائمة العرض
     */
    private createViewMenu(): MenuItemConstructorOptions {
        return {
            label: 'عرض / View',
            submenu: [
                { 
                    role: 'reload', 
                    label: 'إعادة تحميل / Reload' 
                },
                { 
                    role: 'forceReload', 
                    label: 'إعادة تحميل قسري / Force Reload' 
                },
                { 
                    role: 'toggleDevTools', 
                    label: 'أدوات المطور / Developer Tools' 
                },
                { type: 'separator' },
                { 
                    role: 'resetZoom', 
                    label: 'إعادة تعيين التكبير / Reset Zoom' 
                },
                { 
                    role: 'zoomIn', 
                    label: 'تكبير / Zoom In' 
                },
                { 
                    role: 'zoomOut', 
                    label: 'تصغير / Zoom Out' 
                },
                { type: 'separator' },
                { 
                    role: 'togglefullscreen', 
                    label: 'ملء الشاشة / Fullscreen' 
                }
            ]
        };
    }

    /**
     * إنشاء قائمة الأدوات
     * Creates the tools menu
     * 
     * @returns MenuItemConstructorOptions - قائمة الأدوات
     */
    private createToolsMenu(): MenuItemConstructorOptions {
        return {
            label: 'أدوات / Tools',
            submenu: [
                {
                    label: 'تبديل مانع الإعلانات / Toggle Ad Blocker',
                    accelerator: 'Ctrl+Shift+A',
                    click: () => {
                        const mainWindow = this.windowManager.getMainWindow();
                        if (mainWindow) {
                            mainWindow.webContents.send('toggle-ad-blocker');
                        }
                    }
                },
                {
                    label: 'تبديل الوضع المظلم / Toggle Dark Mode',
                    accelerator: 'Ctrl+Shift+D',
                    click: () => {
                        const mainWindow = this.windowManager.getMainWindow();
                        if (mainWindow) {
                            mainWindow.webContents.send('toggle-dark-mode');
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'إحصائيات مانع الإعلانات / Ad Blocker Stats',
                    click: () => {
                        const mainWindow = this.windowManager.getMainWindow();
                        if (mainWindow) {
                            mainWindow.webContents.send('show-ad-blocker-stats');
                        }
                    }
                }
            ]
        };
    }

    /**
     * إنشاء قائمة المساعدة
     * Creates the help menu
     * 
     * @returns MenuItemConstructorOptions - قائمة المساعدة
     */
    private createHelpMenu(): MenuItemConstructorOptions {
        return {
            label: 'مساعدة / Help',
            submenu: [
                {
                    label: 'حول التطبيق / About',
                    click: () => {
                        // TODO: إنشاء نافذة حول التطبيق
                        console.log('عرض معلومات التطبيق');
                    }
                },
                {
                    label: 'اختصارات لوحة المفاتيح / Keyboard Shortcuts',
                    accelerator: 'Ctrl+?',
                    click: () => {
                        // TODO: إنشاء نافذة اختصارات لوحة المفاتيح
                        console.log('عرض اختصارات لوحة المفاتيح');
                    }
                },
                { type: 'separator' },
                {
                    label: 'الإبلاغ عن مشكلة / Report Issue',
                    click: () => {
                        // TODO: فتح رابط الإبلاغ عن المشاكل
                        console.log('فتح رابط الإبلاغ عن المشاكل');
                    }
                },
                {
                    label: 'التحقق من التحديثات / Check for Updates',
                    click: () => {
                        // TODO: فحص التحديثات
                        console.log('فحص التحديثات');
                    }
                }
            ]
        };
    }

    /**
     * تحديث قائمة التطبيق
     * Updates the application menu
     * 
     * @returns void
     */
    public updateMenu(): void {
        try {
            this.createApplicationMenu();
        } catch (error) {
            console.error('خطأ في تحديث القائمة:', error);
        }
    }

    /**
     * إزالة قائمة التطبيق
     * Removes the application menu
     * 
     * @returns void
     */
    public removeMenu(): void {
        try {
            Menu.setApplicationMenu(null);
        } catch (error) {
            console.error('خطأ في إزالة القائمة:', error);
        }
    }
}
