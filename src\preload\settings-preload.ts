/**
 * سكريبت التحميل المسبق لنافذة الإعدادات
 * Settings window preload script
 * 
 * هذا الملف يحتوي على الكود المحقون في نافذة الإعدادات
 * This file contains code injected into settings window
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 * 
 * @requires electron للتواصل مع العملية الرئيسية
 * @requires @shared/types لتعريفات الأنواع
 */

import { IPC_MESSAGES } from '@shared/constants';
import { ApplicationConfig } from '@shared/types';
import { contextBridge, ipcRenderer } from 'electron';

/**
 * واجهة API الإعدادات المعروضة للعارض
 * Settings API interface exposed to renderer
 */
interface SettingsAPI {
    loadSettings(): Promise<ApplicationConfig>;
    saveSettings(settings: ApplicationConfig): Promise<boolean>;
    resetSettings(): Promise<boolean>;
    collectSettingsFromUI(): ApplicationConfig;
    updateSettingsUI(settings: ApplicationConfig): void;
    showSuccessMessage(message: string): void;
    showErrorMessage(message: string): void;
    debouncedApplySetting(key: string, value: string | boolean | number): void;
}

/**
 * فئة إدارة إعدادات Preload
 * Preload settings manager class
 */
class SettingsPreloadManager {
    private debounceTimers: Map<string, NodeJS.Timeout> = new Map();
    private readonly DEBOUNCE_DELAY = 500; // 500ms

    /**
     * تحميل الإعدادات الحالية
     * Loads current settings
     * 
     * @returns Promise<ApplicationConfig> - كائن الإعدادات
     * 
     * @example
     * ```typescript
     * const settings = await manager.loadSettings();
     * ```
     */
    public async loadSettings(): Promise<ApplicationConfig> {
        try {
            const settings = await ipcRenderer.invoke(IPC_MESSAGES.GET_SETTINGS);
            console.log('تم تحميل الإعدادات:', settings);
            return settings;
        } catch (error) {
            console.error('خطأ في تحميل الإعدادات:', error);
            throw new Error(`فشل في تحميل الإعدادات: ${error}`);
        }
    }

    /**
     * حفظ الإعدادات الجديدة
     * Saves new settings
     *
     * @param newSettings - الإعدادات الجديدة
     * @returns Promise<boolean> - نتيجة الحفظ
     */
    public async saveSettings(newSettings: ApplicationConfig): Promise<boolean> {
        try {
            const result = await ipcRenderer.invoke(IPC_MESSAGES.UPDATE_SETTINGS, newSettings);
            console.log('تم حفظ الإعدادات:', newSettings);
            return result;
        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            return false;
        }
    }

    /**
     * إعادة تعيين الإعدادات للقيم الافتراضية
     * Resets settings to default values
     *
     * @returns Promise<boolean> - نتيجة إعادة التعيين
     */
    public async resetSettings(): Promise<boolean> {
        try {
            const result = await ipcRenderer.invoke('reset-settings');
            console.log('تم إعادة تعيين الإعدادات');
            return result;
        } catch (error) {
            console.error('خطأ في إعادة تعيين الإعدادات:', error);
            return false;
        }
    }

    /**
     * جمع الإعدادات من واجهة المستخدم
     * Collects settings from user interface
     *
     * @returns ApplicationConfig - كائن الإعدادات
     */
    public collectSettingsFromUI(): ApplicationConfig {
        try {
            const adBlockEnabled = (document.getElementById('adBlockEnabled') as HTMLInputElement)?.checked || false;
            const darkModeEnabled = (document.getElementById('darkModeEnabled') as HTMLInputElement)?.checked || false;
            const videoQuality = (document.getElementById('videoQuality') as HTMLSelectElement)?.value || '720p';
            const hideQualityButton = (document.getElementById('hideQualityButton') as HTMLInputElement)?.checked || false;
            const autoApplySettings = (document.getElementById('autoApplySettings') as HTMLInputElement)?.checked || true;

            const settings: ApplicationConfig = {
                adBlockEnabled,
                darkModeEnabled,
                videoQuality: videoQuality as ApplicationConfig['videoQuality'],
                hideQualityButton,
                autoApplySettings,
                monetizationEnabled: false, // قيمة افتراضية
                windowBounds: { // قيمة افتراضية
                    width: 1200,
                    height: 800,
                    x: 100,
                    y: 100
                }
            };

            console.log('تم جمع الإعدادات من الواجهة:', settings);
            return settings;
        } catch (error) {
            console.error('خطأ في جمع الإعدادات من الواجهة:', error);
            throw new Error(`فشل في جمع الإعدادات: ${error}`);
        }
    }

    /**
     * تحديث واجهة المستخدم بالإعدادات
     * Updates user interface with settings
     *
     * @param settings - كائن الإعدادات
     * @returns void
     */
    public updateSettingsUI(settings: ApplicationConfig): void {
        try {
            // تحديث مربعات الاختيار
            const adBlockCheckbox = document.getElementById('adBlockEnabled') as HTMLInputElement;
            if (adBlockCheckbox) {
                adBlockCheckbox.checked = settings.adBlockEnabled;
            }

            const darkModeCheckbox = document.getElementById('darkModeEnabled') as HTMLInputElement;
            if (darkModeCheckbox) {
                darkModeCheckbox.checked = settings.darkModeEnabled;
            }

            const hideQualityCheckbox = document.getElementById('hideQualityButton') as HTMLInputElement;
            if (hideQualityCheckbox) {
                hideQualityCheckbox.checked = settings.hideQualityButton;
            }

            const autoApplyCheckbox = document.getElementById('autoApplySettings') as HTMLInputElement;
            if (autoApplyCheckbox) {
                autoApplyCheckbox.checked = settings.autoApplySettings;
            }

            // تحديث قائمة جودة الفيديو
            const qualitySelect = document.getElementById('videoQuality') as HTMLSelectElement;
            if (qualitySelect) {
                qualitySelect.value = settings.videoQuality;
            }

            console.log('تم تحديث واجهة المستخدم بالإعدادات');
        } catch (error) {
            console.error('خطأ في تحديث واجهة المستخدم:', error);
        }
    }

    /**
     * عرض رسالة نجاح
     * Shows success message
     *
     * @param message - نص الرسالة
     * @returns void
     */
    public showSuccessMessage(message: string): void {
        this.showMessage(message, 'success');
    }

    /**
     * عرض رسالة خطأ
     * Shows error message
     *
     * @param message - نص الرسالة
     * @returns void
     */
    public showErrorMessage(message: string): void {
        this.showMessage(message, 'error');
    }

    /**
     * عرض رسالة عامة
     * Shows general message
     *
     * @param message - نص الرسالة
     * @param type - نوع الرسالة
     * @returns void
     */
    private showMessage(message: string, type: 'success' | 'error'): void {
        try {
            // إنشاء عنصر الرسالة
            const messageElement = document.createElement('div');
            messageElement.className = `message ${type}`;
            messageElement.textContent = message;
            messageElement.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                z-index: 10000;
                animation: slideIn 0.3s ease-out;
                background-color: ${type === 'success' ? '#4CAF50' : '#f44336'};
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            `;

            // إضافة الرسالة للصفحة
            document.body.appendChild(messageElement);

            // إزالة الرسالة بعد 3 ثوان
            setTimeout(() => {
                if (messageElement.parentNode) {
                    messageElement.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => {
                        messageElement.remove();
                    }, 300);
                }
            }, 3000);

        } catch (error) {
            console.error('خطأ في عرض الرسالة:', error);
        }
    }

    /**
     * تطبيق إعداد واحد مع تأخير (debounced)
     * Applies single setting with debounce
     *
     * @param key - مفتاح الإعداد
     * @param value - قيمة الإعداد
     * @returns void
     */
    public debouncedApplySetting(key: string, value: string | boolean | number): void {
        try {
            // إلغاء المؤقت السابق إن وجد
            const existingTimer = this.debounceTimers.get(key);
            if (existingTimer) {
                clearTimeout(existingTimer);
            }

            // إنشاء مؤقت جديد
            const newTimer = setTimeout(async () => {
                try {
                    await ipcRenderer.invoke('apply-single-setting', key, value);
                    console.log(`تم تطبيق الإعداد: ${key} = ${value}`);
                } catch (error) {
                    console.error(`خطأ في تطبيق الإعداد ${key}:`, error);
                }
                this.debounceTimers.delete(key);
            }, this.DEBOUNCE_DELAY);

            this.debounceTimers.set(key, newTimer);
        } catch (error) {
            console.error('خطأ في تطبيق الإعداد المؤجل:', error);
        }
    }
}

// إنشاء مثيل مدير الإعدادات
const settingsManager = new SettingsPreloadManager();

// تعريف API للعارض
const settingsAPI: SettingsAPI = {
    loadSettings: () => settingsManager.loadSettings(),
    saveSettings: (settings: ApplicationConfig) => settingsManager.saveSettings(settings),
    resetSettings: () => settingsManager.resetSettings(),
    collectSettingsFromUI: () => settingsManager.collectSettingsFromUI(),
    updateSettingsUI: (settings: ApplicationConfig) => settingsManager.updateSettingsUI(settings),
    showSuccessMessage: (message: string) => settingsManager.showSuccessMessage(message),
    showErrorMessage: (message: string) => settingsManager.showErrorMessage(message),
    debouncedApplySetting: (key: string, value: string | boolean | number) => settingsManager.debouncedApplySetting(key, value)
};

// تعريض API للعارض
contextBridge.exposeInMainWorld('settingsAPI', settingsAPI);

console.log('تم تحميل سكريبت إعدادات Preload بنجاح');
