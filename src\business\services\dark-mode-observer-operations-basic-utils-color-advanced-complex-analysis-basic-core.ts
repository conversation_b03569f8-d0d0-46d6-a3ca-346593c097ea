/**
 * تحليل الألوان الأساسي الأساسي المعقد المتقدم
 * Core basic complex advanced color analysis
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeObserverOperationsBasicUtilsColorCore } from './dark-mode-observer-operations-basic-utils-color-core';
import { DarkModeObserverOperationsBasicUtilsColorAdvancedBasic } from './dark-mode-observer-operations-basic-utils-color-advanced-basic';

/**
 * فئة تحليل الألوان الأساسي الأساسي المعقد المتقدم
 * Core basic complex advanced color analysis class
 */
export class DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasicCore {

    /** تحليل اللون وإرجاع معلومات مفصلة / Analyze color and return detailed information */
    public static analyzeColor(color: string): {
        rgb: { r: number; g: number; b: number } | null;
        hsl: { h: number; s: number; l: number } | null;
        hex: string;
        isLight: boolean;
        isDark: boolean;
        luminance: number;
        temperature: 'warm' | 'cool' | 'neutral';
    } {
        const rgb = DarkModeObserverOperationsBasicUtilsColorCore.parseColorToRgb(color);
        const isLight = DarkModeObserverOperationsBasicUtilsColorCore.isLightColor(color);
        const isDark = !isLight;
        
        let hsl: { h: number; s: number; l: number } | null = null;
        let hex = color;
        let luminance = 0;
        let temperature: 'warm' | 'cool' | 'neutral' = 'neutral';

        if (rgb) {
            hsl = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.rgbToHsl(rgb.r, rgb.g, rgb.b);
            hex = DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(rgb.r, rgb.g, rgb.b);
            luminance = this.calculateLuminance(rgb);
            
            // تحديد درجة حرارة اللون
            if (hsl.h >= 0 && hsl.h < 60) {
                temperature = 'warm'; // أحمر-أصفر
            } else if (hsl.h >= 60 && hsl.h < 180) {
                temperature = 'cool'; // أصفر-أخضر-سماوي
            } else if (hsl.h >= 180 && hsl.h < 240) {
                temperature = 'cool'; // سماوي-أزرق
            } else if (hsl.h >= 240 && hsl.h < 300) {
                temperature = 'cool'; // أزرق-بنفسجي
            } else {
                temperature = 'warm'; // بنفسجي-أحمر
            }
        }

        return {
            rgb,
            hsl,
            hex,
            isLight,
            isDark,
            luminance,
            temperature
        };
    }

    /** حساب luminance للون / Calculate color luminance */
    public static calculateLuminance(rgb: { r: number; g: number; b: number }): number {
        const rsRGB = rgb.r / 255;
        const gsRGB = rgb.g / 255;
        const bsRGB = rgb.b / 255;

        const r = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);
        const g = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);
        const b = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);

        return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    }

    /** تحليل التباين بين لونين / Analyze contrast between two colors */
    public static analyzeContrast(color1: string, color2: string): {
        ratio: number;
        level: 'AAA' | 'AA' | 'A' | 'FAIL';
        isAccessible: boolean;
        recommendation: string;
    } {
        const contrast = DarkModeObserverOperationsBasicUtilsColorCore.calculateContrast(color1, color2);
        
        let level: 'AAA' | 'AA' | 'A' | 'FAIL';
        let isAccessible: boolean;
        let recommendation: string;

        if (contrast >= 7) {
            level = 'AAA';
            isAccessible = true;
            recommendation = 'ممتاز! التباين مثالي لجميع الاستخدامات';
        } else if (contrast >= 4.5) {
            level = 'AA';
            isAccessible = true;
            recommendation = 'جيد! التباين مناسب للنصوص العادية';
        } else if (contrast >= 3) {
            level = 'A';
            isAccessible = false;
            recommendation = 'مقبول للنصوص الكبيرة فقط';
        } else {
            level = 'FAIL';
            isAccessible = false;
            recommendation = 'ضعيف! يحتاج تحسين للوصولية';
        }

        return {
            ratio: contrast,
            level,
            isAccessible,
            recommendation
        };
    }

    /** تحديد نوع اللون / Determine color type */
    public static determineColorType(color: string): {
        type: 'primary' | 'secondary' | 'tertiary' | 'neutral' | 'accent';
        category: 'warm' | 'cool' | 'neutral';
        intensity: 'light' | 'medium' | 'dark';
        saturation: 'low' | 'medium' | 'high';
    } {
        const analysis = this.analyzeColor(color);
        
        let type: 'primary' | 'secondary' | 'tertiary' | 'neutral' | 'accent' = 'neutral';
        let intensity: 'light' | 'medium' | 'dark' = 'medium';
        let saturation: 'low' | 'medium' | 'high' = 'medium';

        if (analysis.hsl) {
            // تحديد النوع بناءً على الـ hue
            if (analysis.hsl.h >= 0 && analysis.hsl.h < 120) {
                type = 'primary'; // أحمر-أصفر-أخضر
            } else if (analysis.hsl.h >= 120 && analysis.hsl.h < 240) {
                type = 'secondary'; // أخضر-سماوي-أزرق
            } else {
                type = 'tertiary'; // أزرق-بنفسجي-أحمر
            }

            // تحديد الكثافة
            if (analysis.hsl.l < 30) {
                intensity = 'dark';
            } else if (analysis.hsl.l > 70) {
                intensity = 'light';
            } else {
                intensity = 'medium';
            }

            // تحديد التشبع
            if (analysis.hsl.s < 30) {
                saturation = 'low';
            } else if (analysis.hsl.s > 70) {
                saturation = 'high';
            } else {
                saturation = 'medium';
            }

            // تحديد الألوان المحايدة
            if (analysis.hsl.s < 10) {
                type = 'neutral';
            }

            // تحديد ألوان التمييز
            if (analysis.hsl.s > 80 && (analysis.hsl.l > 40 && analysis.hsl.l < 60)) {
                type = 'accent';
            }
        }

        return {
            type,
            category: analysis.temperature,
            intensity,
            saturation
        };
    }
}
