/**
 * أدوات الألوان للعمليات الأساسية لمراقب الوضع المظلم
 * Color utilities for basic dark mode observer operations
 *
 * هذا الملف يجمع جميع أدوات الألوان من الملفات المتخصصة
 * This file aggregates all color utilities from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// استيراد الفئات المتخصصة / Import specialized classes
import { DarkModeObserverOperationsBasicUtilsColorAdvanced } from './dark-mode-observer-operations-basic-utils-color-advanced';
import { DarkModeObserverOperationsBasicUtilsColorCore } from './dark-mode-observer-operations-basic-utils-color-core';

/**
 * فئة أدوات الألوان للعمليات الأساسية
 * Basic operations color utility class
 */
export class DarkModeObserverOperationsBasicUtilsColor {

    /** حساب التباين بين لونين / Calculate contrast between two colors */
    public static calculateContrast(color1: string, color2: string): number {
        return DarkModeObserverOperationsBasicUtilsColorCore.calculateContrast(color1, color2);
    }

    /** حساب السطوع النسبي / Calculate relative luminance */
    public static calculateLuminance(rgb: { r: number; g: number; b: number }): number {
        return DarkModeObserverOperationsBasicUtilsColorCore.calculateLuminance(rgb);
    }

    /** التحقق من التباين الجيد / Check if contrast is good */
    public static hasGoodContrast(color1: string, color2: string, level: 'AA' | 'AAA' = 'AA'): boolean {
        return DarkModeObserverOperationsBasicUtilsColorCore.hasGoodContrast(color1, color2, level);
    }

    /** الحصول على لون متباين / Get contrasting color */
    public static getContrastingColor(backgroundColor: string): string {
        return DarkModeObserverOperationsBasicUtilsColorCore.getContrastingColor(backgroundColor);
    }

    /** تحليل اللون إلى قيم RGB / Parse color to RGB values */
    public static parseColorToRgb(color: string): { r: number; g: number; b: number } | null {
        return DarkModeObserverOperationsBasicUtilsColorCore.parseColorToRgb(color);
    }

    /** التحقق من كون اللون فاتح / Check if color is light */
    public static isLightColor(color: string): boolean {
        return DarkModeObserverOperationsBasicUtilsColorCore.isLightColor(color);
    }

    /** التحقق من كون اللون داكن / Check if color is dark */
    public static isDarkColor(color: string): boolean {
        return DarkModeObserverOperationsBasicUtilsColorCore.isDarkColor(color);
    }

    /** تحويل RGB إلى HEX / Convert RGB to HEX */
    public static rgbToHex(r: number, g: number, b: number): string {
        return DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(r, g, b);
    }

    /** تحويل HEX إلى RGB / Convert HEX to RGB */
    public static hexToRgb(hex: string): { r: number; g: number; b: number } | null {
        return DarkModeObserverOperationsBasicUtilsColorCore.hexToRgb(hex);
    }

    /** تفتيح اللون / Lighten color */
    public static lightenColor(color: string, amount: number): string {
        return DarkModeObserverOperationsBasicUtilsColorAdvanced.lightenColor(color, amount);
    }

    /** تغميق اللون / Darken color */
    public static darkenColor(color: string, amount: number): string {
        return DarkModeObserverOperationsBasicUtilsColorAdvanced.darkenColor(color, amount);
    }

    /** مزج لونين / Blend two colors */
    public static blendColors(color1: string, color2: string, ratio: number): string {
        return DarkModeObserverOperationsBasicUtilsColorAdvanced.blendColors(color1, color2, ratio);
    }

    /** الحصول على لون مكمل / Get complementary color */
    public static getComplementaryColor(color: string): string {
        return DarkModeObserverOperationsBasicUtilsColorAdvanced.getComplementaryColor(color);
    }


}
