/**
 * اختبارات أساسية لمدير جودة الفيديو
 * Basic video quality manager tests
 * 
 * هذا الملف يحتوي على الاختبارات الأساسية لمدير جودة الفيديو
 * This file contains basic tests for video quality manager
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQualityManager } from '@business/services/video-quality-manager';
import { ResourceManager } from '@shared/utils/resource-manager';

describe('VideoQualityManager - Basic Tests', () => {
    let videoQualityManager: VideoQualityManager;
    let mockResourceManager: ResourceManager;

    beforeEach(() => {
        mockResourceManager = new ResourceManager();
        videoQualityManager = new VideoQualityManager(mockResourceManager);
        
        // Setup DOM
        document.body.innerHTML = `
            <div id="movie_player">
                <video></video>
                <div class="ytp-settings-menu">
                    <div class="ytp-menuitem" data-value="auto">Auto</div>
                    <div class="ytp-menuitem" data-value="1080p">1080p</div>
                    <div class="ytp-menuitem" data-value="720p">720p</div>
                    <div class="ytp-menuitem" data-value="480p">480p</div>
                </div>
            </div>
        `;
    });

    afterEach(() => {
        videoQualityManager.cleanup();
        document.body.innerHTML = '';
    });

    describe('Initialization', () => {
        it('should initialize successfully', () => {
            // Act
            const result = videoQualityManager.initialize();

            // Assert
            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });

        it('should fail initialization without video player', () => {
            // Arrange
            document.body.innerHTML = '';

            // Act
            const result = videoQualityManager.initialize();

            // Assert
            expect(result.isValid).toBe(false);
            expect(result.errors).toContain('Video player not found');
        });

        it('should set default quality on initialization', () => {
            // Act
            videoQualityManager.initialize();

            // Assert
            expect(videoQualityManager.getCurrentQuality()).toBe('auto');
        });
    });

    describe('Quality Detection', () => {
        beforeEach(() => {
            videoQualityManager.initialize();
        });

        it('should detect available qualities', () => {
            // Act
            const qualities = videoQualityManager.getAvailableQualities();

            // Assert
            expect(qualities).toContain('1080p');
            expect(qualities).toContain('720p');
            expect(qualities).toContain('480p');
            expect(qualities).toContain('auto');
        });

        it('should return empty array when no qualities available', () => {
            // Arrange
            document.querySelector('.ytp-settings-menu')?.remove();

            // Act
            const qualities = videoQualityManager.getAvailableQualities();

            // Assert
            expect(qualities).toHaveLength(0);
        });

        it('should detect current quality correctly', () => {
            // Arrange
            const qualityItem = document.querySelector('[data-value="720p"]') as HTMLElement;
            qualityItem.classList.add('ytp-menuitem-selected');

            // Act
            const currentQuality = videoQualityManager.getCurrentQuality();

            // Assert
            expect(currentQuality).toBe('720p');
        });
    });

    describe('Quality Setting', () => {
        beforeEach(() => {
            videoQualityManager.initialize();
        });

        it('should set quality successfully', async () => {
            // Act
            const result = await videoQualityManager.setQuality('720p');

            // Assert
            expect(result.success).toBe(true);
            expect(result.quality).toBe('720p');
        });

        it('should fail to set invalid quality', async () => {
            // Act
            const result = await videoQualityManager.setQuality('invalid');

            // Assert
            expect(result.success).toBe(false);
            expect(result.error).toContain('Quality not available');
        });

        it('should update current quality after setting', async () => {
            // Act
            await videoQualityManager.setQuality('480p');

            // Assert
            expect(videoQualityManager.getCurrentQuality()).toBe('480p');
        });
    });

    describe('Auto Quality', () => {
        beforeEach(() => {
            videoQualityManager.initialize();
        });

        it('should enable auto quality', async () => {
            // Act
            const result = await videoQualityManager.enableAutoQuality();

            // Assert
            expect(result.success).toBe(true);
            expect(videoQualityManager.isAutoQualityEnabled()).toBe(true);
        });

        it('should disable auto quality', async () => {
            // Arrange
            await videoQualityManager.enableAutoQuality();

            // Act
            const result = await videoQualityManager.disableAutoQuality();

            // Assert
            expect(result.success).toBe(true);
            expect(videoQualityManager.isAutoQualityEnabled()).toBe(false);
        });

        it('should automatically adjust quality based on connection', async () => {
            // Arrange
            await videoQualityManager.enableAutoQuality();
            
            // Mock slow connection
            Object.defineProperty(navigator, 'connection', {
                value: { effectiveType: '2g' },
                writable: true
            });

            // Act
            await videoQualityManager.adjustQualityForConnection();

            // Assert
            expect(videoQualityManager.getCurrentQuality()).toBe('480p');
        });
    });

    describe('Quality Monitoring', () => {
        beforeEach(() => {
            videoQualityManager.initialize();
        });

        it('should start monitoring quality changes', () => {
            // Act
            videoQualityManager.startMonitoring();

            // Assert
            expect(videoQualityManager.isMonitoring()).toBe(true);
        });

        it('should stop monitoring quality changes', () => {
            // Arrange
            videoQualityManager.startMonitoring();

            // Act
            videoQualityManager.stopMonitoring();

            // Assert
            expect(videoQualityManager.isMonitoring()).toBe(false);
        });

        it('should detect quality changes during monitoring', (done) => {
            // Arrange
            videoQualityManager.startMonitoring();
            
            videoQualityManager.onQualityChange((newQuality) => {
                expect(newQuality).toBe('1080p');
                done();
            });

            // Act
            setTimeout(() => {
                videoQualityManager.setQuality('1080p');
            }, 100);
        });
    });

    describe('Error Handling', () => {
        it('should handle missing video element gracefully', () => {
            // Arrange
            document.querySelector('video')?.remove();

            // Act
            const result = videoQualityManager.initialize();

            // Assert
            expect(result.isValid).toBe(false);
            expect(result.errors).toContain('Video element not found');
        });

        it('should handle DOM manipulation errors', async () => {
            // Arrange
            videoQualityManager.initialize();
            
            // Mock DOM error
            const originalClick = HTMLElement.prototype.click;
            HTMLElement.prototype.click = () => {
                throw new Error('DOM error');
            };

            // Act
            const result = await videoQualityManager.setQuality('720p');

            // Assert
            expect(result.success).toBe(false);
            expect(result.error).toContain('DOM error');

            // Cleanup
            HTMLElement.prototype.click = originalClick;
        });
    });
});
