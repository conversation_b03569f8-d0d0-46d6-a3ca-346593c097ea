/**
 * إنشاء لوحات الألوان المتقدمة
 * Advanced color palette generation
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeObserverOperationsBasicUtilsColorCore } from './dark-mode-observer-operations-basic-utils-color-core';
import { DarkModeObserverOperationsBasicUtilsColorAdvancedBasic } from './dark-mode-observer-operations-basic-utils-color-advanced-basic';
import { DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic } from './dark-mode-observer-operations-basic-utils-color-advanced-complex-analysis-basic';

/**
 * فئة إنشاء لوحات الألوان المتقدمة
 * Advanced color palette generation class
 */
export class DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisAdvancedHarmonySuggestionsAdvancedPalette {

    /**
     * إنشاء لوحة ألوان متوافقة
     * Generate harmonious color palette
     */
    public static generateHarmoniousPalette(
        baseColor: string, 
        type: 'monochromatic' | 'analogous' | 'complementary' | 'triadic' = 'analogous'
    ): string[] {
        const analysis = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(baseColor);
        
        if (!analysis.hsl) {
            return [baseColor]; // إرجاع اللون الأساسي إذا فشل التحليل
        }

        const { h, s, l } = analysis.hsl;
        const palette: string[] = [baseColor];

        switch (type) {
            case 'monochromatic':
                return this.generateMonochromaticPalette(h, s, l, baseColor);
            case 'analogous':
                return this.generateAnalogousPalette(h, s, l, baseColor);
            case 'complementary':
                return this.generateComplementaryPalette(h, s, l, baseColor);
            case 'triadic':
                return this.generateTriadicPalette(h, s, l, baseColor);
            default:
                return palette;
        }
    }

    /**
     * إنشاء لوحة ألوان أحادية
     * Generate monochromatic palette
     */
    private static generateMonochromaticPalette(h: number, s: number, l: number, baseColor: string): string[] {
        const palette: string[] = [baseColor];
        
        // ألوان أحادية بتشبع وسطوع مختلف
        for (let i = 1; i <= 4; i++) {
            const newS = Math.max(10, Math.min(90, s + (i * 15) - 30));
            const newL = Math.max(10, Math.min(90, l + (i * 20) - 40));
            const newRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(h, newS, newL);
            palette.push(DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(newRgb.r, newRgb.g, newRgb.b));
        }
        
        return palette;
    }

    /**
     * إنشاء لوحة ألوان متشابهة
     * Generate analogous palette
     */
    private static generateAnalogousPalette(h: number, s: number, l: number, baseColor: string): string[] {
        const palette: string[] = [baseColor];
        
        // ألوان متشابهة (±30 درجة)
        for (let i = 1; i <= 4; i++) {
            const newH = (h + (i * 30)) % 360;
            const newRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(newH, s, l);
            palette.push(DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(newRgb.r, newRgb.g, newRgb.b));
        }
        
        return palette;
    }

    /**
     * إنشاء لوحة ألوان متكاملة
     * Generate complementary palette
     */
    private static generateComplementaryPalette(h: number, s: number, l: number, baseColor: string): string[] {
        const palette: string[] = [baseColor];
        
        // اللون المكمل (180 درجة)
        const complementaryH = (h + 180) % 360;
        const complementaryRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(complementaryH, s, l);
        palette.push(DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(complementaryRgb.r, complementaryRgb.g, complementaryRgb.b));
        
        // ألوان متدرجة من اللون الأساسي
        for (let i = 1; i <= 2; i++) {
            const newL = Math.max(10, Math.min(90, l + (i * 25) - 25));
            const newRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(h, s, newL);
            palette.push(DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(newRgb.r, newRgb.g, newRgb.b));
        }
        
        // ألوان متدرجة من اللون المكمل
        for (let i = 1; i <= 2; i++) {
            const newL = Math.max(10, Math.min(90, l + (i * 25) - 25));
            const newRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(complementaryH, s, newL);
            palette.push(DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(newRgb.r, newRgb.g, newRgb.b));
        }
        
        return palette;
    }

    /**
     * إنشاء لوحة ألوان ثلاثية
     * Generate triadic palette
     */
    private static generateTriadicPalette(h: number, s: number, l: number, baseColor: string): string[] {
        const palette: string[] = [baseColor];
        
        // الألوان الثلاثية (120 درجة لكل منها)
        const triadic1H = (h + 120) % 360;
        const triadic2H = (h + 240) % 360;
        
        const triadic1Rgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(triadic1H, s, l);
        const triadic2Rgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(triadic2H, s, l);
        
        palette.push(DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(triadic1Rgb.r, triadic1Rgb.g, triadic1Rgb.b));
        palette.push(DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(triadic2Rgb.r, triadic2Rgb.g, triadic2Rgb.b));
        
        // ألوان متدرجة من كل لون
        for (const baseH of [h, triadic1H, triadic2H]) {
            const newL = Math.max(10, Math.min(90, l + 20));
            const newRgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(baseH, s, newL);
            palette.push(DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(newRgb.r, newRgb.g, newRgb.b));
        }
        
        return palette;
    }

    /**
     * إنشاء لوحة ألوان متدرجة
     * Generate gradient palette
     */
    public static generateGradientPalette(startColor: string, endColor: string, steps: number = 5): string[] {
        const startAnalysis = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(startColor);
        const endAnalysis = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(endColor);
        
        if (!startAnalysis.rgb || !endAnalysis.rgb) {
            return [startColor, endColor];
        }

        const palette: string[] = [];
        const { r: r1, g: g1, b: b1 } = startAnalysis.rgb;
        const { r: r2, g: g2, b: b2 } = endAnalysis.rgb;

        for (let i = 0; i < steps; i++) {
            const ratio = i / (steps - 1);
            const r = Math.round(r1 + (r2 - r1) * ratio);
            const g = Math.round(g1 + (g2 - g1) * ratio);
            const b = Math.round(b1 + (b2 - b1) * ratio);
            
            palette.push(DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(r, g, b));
        }

        return palette;
    }

    /**
     * إنشاء لوحة ألوان للوضع المظلم
     * Generate dark mode palette
     */
    public static generateDarkModePalette(baseColor: string): string[] {
        const analysis = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(baseColor);
        
        if (!analysis.hsl) {
            return [baseColor];
        }

        const { h, s } = analysis.hsl;
        const palette: string[] = [];

        // ألوان مظلمة متدرجة
        const lightnesses = [15, 25, 35, 45, 55, 65];
        
        for (const l of lightnesses) {
            const adjustedS = Math.max(20, Math.min(80, s - 10)); // تقليل التشبع قليلاً
            const rgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(h, adjustedS, l);
            palette.push(DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(rgb.r, rgb.g, rgb.b));
        }

        return palette;
    }

    /**
     * إنشاء لوحة ألوان للوضع الفاتح
     * Generate light mode palette
     */
    public static generateLightModePalette(baseColor: string): string[] {
        const analysis = DarkModeObserverOperationsBasicUtilsColorAdvancedComplexAnalysisBasic.analyzeColor(baseColor);
        
        if (!analysis.hsl) {
            return [baseColor];
        }

        const { h, s } = analysis.hsl;
        const palette: string[] = [];

        // ألوان فاتحة متدرجة
        const lightnesses = [85, 75, 65, 55, 45, 35];
        
        for (const l of lightnesses) {
            const adjustedS = Math.max(30, Math.min(90, s + 10)); // زيادة التشبع قليلاً
            const rgb = DarkModeObserverOperationsBasicUtilsColorAdvancedBasic.hslToRgb(h, adjustedS, l);
            palette.push(DarkModeObserverOperationsBasicUtilsColorCore.rgbToHex(rgb.r, rgb.g, rgb.b));
        }

        return palette;
    }
}
