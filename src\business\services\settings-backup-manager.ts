/**
 * مدير النسخ الاحتياطية للإعدادات
 * Settings backup manager
 * 
 * هذا الملف يحتوي على إدارة النسخ الاحتياطية المتقدمة
 * This file contains advanced backup management
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ApplicationConfig, ValidationResult } from '@shared/types';
import * as fs from 'fs';
import * as path from 'path';
import {
    BackupInfo,
    SETTINGS_MESSAGES,
    SettingsErrorReport,
    SettingsErrorType,
    SettingsManagerConfig
} from './settings-config';

/**
 * مدير النسخ الاحتياطية المتقدم
 * Advanced backup manager class
 */
export class SettingsBackupManager {
    private readonly config: SettingsManagerConfig;
    private readonly backupDirectory: string;
    private backupErrors: SettingsErrorReport[] = [];

    /**
     * منشئ مدير النسخ الاحتياطية / Backup manager constructor
     */
    constructor(config: SettingsManagerConfig, backupDirectory: string) {
        this.config = config;
        this.backupDirectory = backupDirectory;
    }

    /**
     * الحصول على قائمة النسخ الاحتياطية / Get backup list
     */
    public async getBackupList(): Promise<BackupInfo[]> {
        try {
            if (!fs.existsSync(this.backupDirectory)) {
                return [];
            }

            const files = await fs.promises.readdir(this.backupDirectory);
            const backups: BackupInfo[] = [];

            for (const file of files) {
                if (file.endsWith('.json')) {
                    const filePath = path.join(this.backupDirectory, file);
                    const stats = await fs.promises.stat(filePath);
                    
                    try {
                        const content = await fs.promises.readFile(filePath, 'utf8');
                        const data = JSON.parse(content);
                        
                        backups.push({
                            id: this.extractBackupId(file),
                            filename: file,
                            filePath,
                            timestamp: new Date(data.timestamp || stats.mtime),
                            size: stats.size,
                            description: data.description || 'غير محدد / Not specified',
                            checksum: data.checksum || '',
                            isValid: this.validateBackupFile(data)
                        });
                    } catch (error) {
                        // ملف تالف، إضافة كنسخة احتياطية غير صحيحة
                        backups.push({
                            id: this.extractBackupId(file),
                            filename: file,
                            filePath,
                            timestamp: stats.mtime,
                            size: stats.size,
                            description: 'ملف تالف / Corrupted file',
                            checksum: '',
                            isValid: false
                        });
                    }
                }
            }

            // ترتيب حسب التاريخ (الأحدث أولاً)
            return backups.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
        } catch (error) {
            this.addError(SettingsErrorType.BACKUP_ERROR, `فشل في الحصول على قائمة النسخ الاحتياطية: ${error}`);
            return [];
        }
    }

    /**
     * تنظيف النسخ الاحتياطية القديمة / Cleanup old backups
     */
    public async cleanupOldBackups(maxBackups: number = 10): Promise<number> {
        try {
            const backups = await this.getBackupList();
            
            if (backups.length <= maxBackups) {
                return 0;
            }

            // ترتيب حسب التاريخ والاحتفاظ بالأحدث
            const sortedBackups = backups.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
            const backupsToDelete = sortedBackups.slice(maxBackups);
            
            let deletedCount = 0;
            for (const backup of backupsToDelete) {
                try {
                    await fs.promises.unlink(backup.filePath);
                    deletedCount++;
                } catch (error) {
                    this.addError(SettingsErrorType.BACKUP_ERROR, `فشل في حذف النسخة الاحتياطية ${backup.filename}: ${error}`);
                }
            }

            return deletedCount;
        } catch (error) {
            this.addError(SettingsErrorType.BACKUP_ERROR, `فشل في تنظيف النسخ الاحتياطية: ${error}`);
            return 0;
        }
    }

    /**
     * التحقق من سلامة النسخ الاحتياطية / Verify backup integrity
     */
    public async verifyBackupIntegrity(): Promise<ValidationResult> {
        const errors: string[] = [];
        const backups = await this.getBackupList();

        for (const backup of backups) {
            if (!backup.isValid) {
                errors.push(`النسخة الاحتياطية ${backup.filename} تالفة`);
                continue;
            }

            try {
                const content = await fs.promises.readFile(backup.filePath, 'utf8');
                const data = JSON.parse(content);
                
                // التحقق من التوقيع إذا كان متوفراً
                if (data.checksum && backup.checksum) {
                    const calculatedChecksum = this.calculateChecksum(JSON.stringify(data.settings));
                    if (calculatedChecksum !== backup.checksum) {
                        errors.push(`فشل التحقق من سلامة النسخة الاحتياطية ${backup.filename}`);
                    }
                }
            } catch (error) {
                errors.push(`خطأ في قراءة النسخة الاحتياطية ${backup.filename}: ${error}`);
            }
        }

        return {
            isValid: errors.length === 0,
            errors: errors.map(error => ({ field: 'backup', message: error, code: 'BACKUP_INTEGRITY_ERROR' }))
        };
    }

    /**
     * إنشاء نسخة احتياطية تلقائية / Create automatic backup
     */
    public async createAutomaticBackup(settings: ApplicationConfig): Promise<BackupInfo | null> {
        try {
            // التحقق من الحد الأقصى للنسخ الاحتياطية
            const backups = await this.getBackupList();
            if (backups.length >= this.config.maxBackups) {
                await this.cleanupOldBackups(this.config.maxBackups - 1);
            }

            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `auto-backup-${timestamp}.json`;
            const filePath = path.join(this.backupDirectory, filename);

            const backupData = {
                version: '1.0.0',
                timestamp: new Date().toISOString(),
                description: 'نسخة احتياطية تلقائية / Automatic backup',
                settings: settings,
                checksum: this.calculateChecksum(JSON.stringify(settings)),
                automatic: true
            };

            await fs.promises.writeFile(filePath, JSON.stringify(backupData, null, 2), 'utf8');

            return {
                id: this.generateBackupId(),
                filename,
                filePath,
                timestamp: new Date(),
                size: (await fs.promises.stat(filePath)).size,
                description: backupData.description,
                checksum: backupData.checksum,
                isValid: true
            };
        } catch (error) {
            this.addError(SettingsErrorType.BACKUP_ERROR, `فشل في إنشاء النسخة الاحتياطية التلقائية: ${error}`);
            return null;
        }
    }

    /**
     * البحث في النسخ الاحتياطية / Search backups
     */
    public async searchBackups(query: string): Promise<BackupInfo[]> {
        const allBackups = await this.getBackupList();
        const searchTerm = query.toLowerCase();

        return allBackups.filter(backup => 
            backup.filename.toLowerCase().includes(searchTerm) ||
            backup.description.toLowerCase().includes(searchTerm) ||
            backup.timestamp.toISOString().includes(searchTerm)
        );
    }

    /**
     * الحصول على إحصائيات النسخ الاحتياطية / Get backup statistics
     */
    public async getBackupStatistics(): Promise<{
        totalBackups: number;
        totalSize: number;
        validBackups: number;
        corruptedBackups: number;
        oldestBackup: Date | null;
        newestBackup: Date | null;
    }> {
        const backups = await this.getBackupList();
        
        const totalSize = backups.reduce((sum, backup) => sum + backup.size, 0);
        const validBackups = backups.filter(backup => backup.isValid).length;
        const corruptedBackups = backups.length - validBackups;
        
        const timestamps = backups.map(backup => backup.timestamp);
        const oldestBackup = timestamps.length > 0 ? new Date(Math.min(...timestamps.map(t => t.getTime()))) : null;
        const newestBackup = timestamps.length > 0 ? new Date(Math.max(...timestamps.map(t => t.getTime()))) : null;

        return {
            totalBackups: backups.length,
            totalSize,
            validBackups,
            corruptedBackups,
            oldestBackup,
            newestBackup
        };
    }

    /**
     * التحقق من صحة ملف النسخة الاحتياطية / Validate backup file
     */
    private validateBackupFile(data: any): boolean {
        return (
            data &&
            typeof data === 'object' &&
            data.version &&
            data.timestamp &&
            data.settings &&
            typeof data.settings === 'object'
        );
    }

    /**
     * استخراج معرف النسخة الاحتياطية من اسم الملف / Extract backup ID from filename
     */
    private extractBackupId(filename: string): string {
        return filename.replace('.json', '').replace(/[^a-zA-Z0-9-_]/g, '_');
    }

    /**
     * حساب التوقيع / Calculate checksum
     */
    private calculateChecksum(data: string): string {
        const crypto = require('crypto');
        return crypto.createHash('sha256').update(data).digest('hex');
    }

    /**
     * توليد معرف النسخة الاحتياطية / Generate backup ID
     */
    private generateBackupId(): string {
        return `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * إضافة خطأ / Add error
     */
    private addError(type: SettingsErrorType, message: string): void {
        this.backupErrors.push({
            type,
            message,
            timestamp: new Date(),
            context: 'SettingsBackupManager'
        });
    }

    /**
     * الحصول على الأخطاء / Get errors
     */
    public getErrors(): SettingsErrorReport[] {
        return [...this.backupErrors];
    }

    /**
     * مسح الأخطاء / Clear errors
     */
    public clearErrors(): void {
        this.backupErrors = [];
    }
}
