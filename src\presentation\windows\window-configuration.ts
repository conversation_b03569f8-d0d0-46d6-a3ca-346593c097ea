/**
 * تكوين النوافذ
 * Window configuration
 * 
 * هذا الملف يحتوي على تكوينات النوافذ المختلفة
 * This file contains configurations for different windows
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { BASE_URLS, DEFAULT_WINDOW_BOUNDS, FILE_PATHS } from '@shared/constants';
import { ApplicationConfig, WindowBounds } from '@shared/types';
import * as path from 'path';

/**
 * فئة تكوين النوافذ
 * Window configuration class
 */
export class WindowConfiguration {

    /**
     * الحصول على تكوين النافذة الرئيسية
     * Get main window configuration
     * 
     * @param config - إعدادات التطبيق
     * @returns تكوين النافذة الرئيسية
     */
    public static getMainWindowConfig(config: ApplicationConfig): Electron.BrowserWindowConstructorOptions {
        const { width, height } = config.windowBounds || DEFAULT_WINDOW_BOUNDS;

        return {
            width,
            height,
            minWidth: 800,
            minHeight: 600,
            show: false,
            icon: path.join(__dirname, '../../assets/icon.png'),
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                preload: path.join(__dirname, '../../preload/youtube-preload.js'),
                webSecurity: true,
                allowRunningInsecureContent: false,
                experimentalFeatures: false,
                sandbox: false
            },
            titleBarStyle: 'default',
            frame: true,
            resizable: true,
            maximizable: true,
            minimizable: true,
            closable: true,
            focusable: true,
            alwaysOnTop: false,
            fullscreenable: true,
            skipTaskbar: false,
            kiosk: false,
            autoHideMenuBar: false,
            useContentSize: false,
            center: true,
            movable: true,
            backgroundColor: '#1a1a1a',
            darkTheme: config.darkMode || true,
            vibrancy: undefined,
            opacity: 1.0,
            hasShadow: true,
            thickFrame: true,
            acceptFirstMouse: false,
            disableAutoHideCursor: false,
            enableLargerThanScreen: false,
            tabbingIdentifier: undefined,
            webgl: true
        };
    }

    /**
     * الحصول على تكوين نافذة الإعدادات
     * Get settings window configuration
     * 
     * @returns تكوين نافذة الإعدادات
     */
    public static getSettingsWindowConfig(): Electron.BrowserWindowConstructorOptions {
        return {
            width: 600,
            height: 500,
            minWidth: 500,
            minHeight: 400,
            maxWidth: 800,
            maxHeight: 700,
            show: false,
            modal: true,
            resizable: true,
            maximizable: false,
            minimizable: false,
            closable: true,
            focusable: true,
            alwaysOnTop: true,
            fullscreenable: false,
            skipTaskbar: true,
            autoHideMenuBar: true,
            useContentSize: false,
            center: true,
            movable: true,
            frame: true,
            titleBarStyle: 'default',
            backgroundColor: '#2d2d2d',
            darkTheme: true,
            hasShadow: true,
            thickFrame: true,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                preload: path.join(__dirname, '../../preload/settings-preload.js'),
                webSecurity: true,
                allowRunningInsecureContent: false,
                experimentalFeatures: false,
                sandbox: false,
                webgl: false
            }
        };
    }

    /**
     * الحصول على تكوين نافذة عامة
     * Get generic window configuration
     * 
     * @param options - خيارات إضافية
     * @returns تكوين النافذة العامة
     */
    public static getGenericWindowConfig(options: Partial<Electron.BrowserWindowConstructorOptions> = {}): Electron.BrowserWindowConstructorOptions {
        const defaultConfig: Electron.BrowserWindowConstructorOptions = {
            width: 800,
            height: 600,
            minWidth: 400,
            minHeight: 300,
            show: false,
            resizable: true,
            maximizable: true,
            minimizable: true,
            closable: true,
            focusable: true,
            alwaysOnTop: false,
            fullscreenable: true,
            skipTaskbar: false,
            autoHideMenuBar: false,
            useContentSize: false,
            center: true,
            movable: true,
            frame: true,
            titleBarStyle: 'default',
            backgroundColor: '#ffffff',
            darkTheme: false,
            hasShadow: true,
            thickFrame: true,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                webSecurity: true,
                allowRunningInsecureContent: false,
                experimentalFeatures: false,
                sandbox: false
            }
        };

        return { ...defaultConfig, ...options };
    }

    /**
     * الحصول على تكوين نافذة مطور
     * Get developer window configuration
     * 
     * @returns تكوين نافذة المطور
     */
    public static getDeveloperWindowConfig(): Electron.BrowserWindowConstructorOptions {
        return {
            width: 1200,
            height: 800,
            minWidth: 800,
            minHeight: 600,
            show: false,
            resizable: true,
            maximizable: true,
            minimizable: true,
            closable: true,
            focusable: true,
            alwaysOnTop: false,
            fullscreenable: true,
            skipTaskbar: false,
            autoHideMenuBar: true,
            useContentSize: false,
            center: true,
            movable: true,
            frame: true,
            titleBarStyle: 'default',
            backgroundColor: '#1e1e1e',
            darkTheme: true,
            hasShadow: true,
            thickFrame: true,
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false,
                enableRemoteModule: true,
                webSecurity: false,
                allowRunningInsecureContent: true,
                experimentalFeatures: true,
                sandbox: false,
                devTools: true
            }
        };
    }

    /**
     * الحصول على تكوين نافذة ملء الشاشة
     * Get fullscreen window configuration
     * 
     * @returns تكوين نافذة ملء الشاشة
     */
    public static getFullscreenWindowConfig(): Electron.BrowserWindowConstructorOptions {
        return {
            fullscreen: true,
            show: false,
            frame: false,
            resizable: false,
            maximizable: false,
            minimizable: false,
            closable: true,
            focusable: true,
            alwaysOnTop: true,
            fullscreenable: true,
            skipTaskbar: true,
            autoHideMenuBar: true,
            useContentSize: true,
            center: true,
            movable: false,
            titleBarStyle: 'hidden',
            backgroundColor: '#000000',
            darkTheme: true,
            hasShadow: false,
            thickFrame: false,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                webSecurity: true,
                allowRunningInsecureContent: false,
                experimentalFeatures: false,
                sandbox: false
            }
        };
    }

    /**
     * الحصول على تكوين نافذة شفافة
     * Get transparent window configuration
     * 
     * @param opacity - مستوى الشفافية (0-1)
     * @returns تكوين النافذة الشفافة
     */
    public static getTransparentWindowConfig(opacity: number = 0.9): Electron.BrowserWindowConstructorOptions {
        return {
            width: 600,
            height: 400,
            show: false,
            transparent: true,
            frame: false,
            resizable: true,
            maximizable: false,
            minimizable: true,
            closable: true,
            focusable: true,
            alwaysOnTop: true,
            fullscreenable: false,
            skipTaskbar: false,
            autoHideMenuBar: true,
            useContentSize: false,
            center: true,
            movable: true,
            titleBarStyle: 'hidden',
            backgroundColor: 'rgba(0, 0, 0, 0)',
            opacity: Math.max(0, Math.min(1, opacity)),
            hasShadow: false,
            thickFrame: false,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                webSecurity: true,
                allowRunningInsecureContent: false,
                experimentalFeatures: false,
                sandbox: false
            }
        };
    }

    /**
     * تطبيق إعدادات الأمان المتقدمة
     * Apply advanced security settings
     * 
     * @param config - التكوين الأساسي
     * @returns التكوين مع إعدادات الأمان المتقدمة
     */
    public static applyAdvancedSecurity(config: Electron.BrowserWindowConstructorOptions): Electron.BrowserWindowConstructorOptions {
        return {
            ...config,
            webPreferences: {
                ...config.webPreferences,
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                webSecurity: true,
                allowRunningInsecureContent: false,
                experimentalFeatures: false,
                sandbox: true,
                safeDialogs: true,
                safeDialogsMessage: 'This application is trying to show a dialog',
                disableDialogs: false,
                navigateOnDragDrop: false,
                autoplayPolicy: 'user-gesture-required',
                disableHtmlFullscreenWindowResize: true
            }
        };
    }

    /**
     * تطبيق إعدادات الأداء المحسنة
     * Apply performance optimizations
     * 
     * @param config - التكوين الأساسي
     * @returns التكوين مع إعدادات الأداء المحسنة
     */
    public static applyPerformanceOptimizations(config: Electron.BrowserWindowConstructorOptions): Electron.BrowserWindowConstructorOptions {
        return {
            ...config,
            webPreferences: {
                ...config.webPreferences,
                backgroundThrottling: false,
                offscreen: false,
                paintWhenInitiallyHidden: false,
                enableWebSQL: false,
                v8CacheOptions: 'code',
                disableBlinkFeatures: 'Auxclick',
                enableBlinkFeatures: 'CSSColorSchemeUARendering'
            }
        };
    }

    /**
     * تطبيق إعدادات إمكانية الوصول
     * Apply accessibility settings
     * 
     * @param config - التكوين الأساسي
     * @returns التكوين مع إعدادات إمكانية الوصول
     */
    public static applyAccessibilitySettings(config: Electron.BrowserWindowConstructorOptions): Electron.BrowserWindowConstructorOptions {
        return {
            ...config,
            accessibleTitle: config.title || 'Application Window',
            webPreferences: {
                ...config.webPreferences,
                enableRemoteModule: false,
                contextIsolation: true
            }
        };
    }
}
