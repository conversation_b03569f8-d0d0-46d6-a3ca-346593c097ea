/**
 * محلل أداء YouTube
 * YouTube performance analyzer
 * 
 * هذا الملف يحتوي على منطق تحليل أداء YouTube
 * This file contains YouTube performance analysis logic
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { 
    YouTubeControllerPerformanceReport,
    YOUTUBE_CONTROLLER_CONSTANTS
} from './youtube-controller-config';

/**
 * معلومات تحليل الأداء
 * Performance analysis info
 */
interface PerformanceAnalysis {
    trend: 'IMPROVING' | 'STABLE' | 'DEGRADING';
    score: number; // 0-100
    issues: string[];
    recommendations: string[];
    summary: string;
}

/**
 * إحصائيات الأداء
 * Performance statistics
 */
interface PerformanceStatistics {
    averageMemoryUsage: number;
    averageCpuUsage: number;
    averageResponseTime: number;
    averageNetworkLatency: number;
    totalOperations: number;
    totalErrors: number;
    totalWarnings: number;
    errorRate: number;
    warningRate: number;
    uptimeHours: number;
}

/**
 * فئة محلل أداء YouTube
 * YouTube performance analyzer class
 */
export class YouTubePerformanceAnalyzer {
    private performanceThresholds = {
        memoryUsage: YOUTUBE_CONTROLLER_CONSTANTS.PERFORMANCE_THRESHOLDS?.MEMORY_USAGE || 500, // MB
        cpuUsage: YOUTUBE_CONTROLLER_CONSTANTS.PERFORMANCE_THRESHOLDS?.CPU_USAGE || 80, // %
        responseTime: YOUTUBE_CONTROLLER_CONSTANTS.PERFORMANCE_THRESHOLDS?.RESPONSE_TIME || 1000, // ms
        networkLatency: YOUTUBE_CONTROLLER_CONSTANTS.PERFORMANCE_THRESHOLDS?.NETWORK_LATENCY || 200, // ms
        errorRate: YOUTUBE_CONTROLLER_CONSTANTS.PERFORMANCE_THRESHOLDS?.ERROR_RATE || 5 // %
    };

    /**
     * منشئ محلل الأداء
     * Performance analyzer constructor
     */
    constructor() {
        // تهيئة المحلل
    }

    /**
     * تحليل تقارير الأداء
     * Analyze performance reports
     * 
     * @param reports - قائمة تقارير الأداء
     * @returns تحليل الأداء
     */
    public analyzePerformance(reports: YouTubeControllerPerformanceReport[]): PerformanceAnalysis {
        if (reports.length === 0) {
            return {
                trend: 'STABLE',
                score: 0,
                issues: ['لا توجد بيانات أداء متاحة / No performance data available'],
                recommendations: ['بدء مراقبة الأداء / Start performance monitoring'],
                summary: 'لا توجد بيانات كافية للتحليل / Insufficient data for analysis'
            };
        }

        const statistics = this.calculateStatistics(reports);
        const trend = this.analyzeTrend(reports);
        const score = this.calculatePerformanceScore(statistics);
        const issues = this.identifyIssues(statistics);
        const recommendations = this.generateRecommendations(statistics, issues);
        const summary = this.generateSummary(statistics, trend, score);

        return {
            trend,
            score,
            issues,
            recommendations,
            summary
        };
    }

    /**
     * حساب إحصائيات الأداء
     * Calculate performance statistics
     * 
     * @param reports - قائمة تقارير الأداء
     * @returns إحصائيات الأداء
     */
    public calculateStatistics(reports: YouTubeControllerPerformanceReport[]): PerformanceStatistics {
        if (reports.length === 0) {
            return {
                averageMemoryUsage: 0,
                averageCpuUsage: 0,
                averageResponseTime: 0,
                averageNetworkLatency: 0,
                totalOperations: 0,
                totalErrors: 0,
                totalWarnings: 0,
                errorRate: 0,
                warningRate: 0,
                uptimeHours: 0
            };
        }

        const latestReport = reports[reports.length - 1];
        const totalMemory = reports.reduce((sum, r) => sum + r.memoryUsage, 0);
        const totalCpu = reports.reduce((sum, r) => sum + (r.cpuUsage || 0), 0);
        const totalResponseTime = reports.reduce((sum, r) => sum + (r.averageResponseTime || 0), 0);
        const totalNetworkLatency = reports.reduce((sum, r) => sum + (r.networkLatency || 0), 0);

        const totalOperations = latestReport.operationCount;
        const totalErrors = latestReport.errorCount;
        const totalWarnings = latestReport.warningCount;

        return {
            averageMemoryUsage: totalMemory / reports.length,
            averageCpuUsage: totalCpu / reports.length,
            averageResponseTime: totalResponseTime / reports.length,
            averageNetworkLatency: totalNetworkLatency / reports.length,
            totalOperations,
            totalErrors,
            totalWarnings,
            errorRate: totalOperations > 0 ? (totalErrors / totalOperations) * 100 : 0,
            warningRate: totalOperations > 0 ? (totalWarnings / totalOperations) * 100 : 0,
            uptimeHours: latestReport.uptime / (1000 * 60 * 60)
        };
    }

    /**
     * تحليل الاتجاه
     * Analyze trend
     * 
     * @param reports - قائمة تقارير الأداء
     * @returns اتجاه الأداء
     */
    private analyzeTrend(reports: YouTubeControllerPerformanceReport[]): 'IMPROVING' | 'STABLE' | 'DEGRADING' {
        if (reports.length < 3) {
            return 'STABLE';
        }

        const recentReports = reports.slice(-5); // آخر 5 تقارير
        const olderReports = reports.slice(-10, -5); // التقارير السابقة

        if (olderReports.length === 0) {
            return 'STABLE';
        }

        const recentAvgMemory = recentReports.reduce((sum, r) => sum + r.memoryUsage, 0) / recentReports.length;
        const olderAvgMemory = olderReports.reduce((sum, r) => sum + r.memoryUsage, 0) / olderReports.length;

        const recentAvgResponse = recentReports.reduce((sum, r) => sum + (r.averageResponseTime || 0), 0) / recentReports.length;
        const olderAvgResponse = olderReports.reduce((sum, r) => sum + (r.averageResponseTime || 0), 0) / olderReports.length;

        const memoryTrend = recentAvgMemory - olderAvgMemory;
        const responseTrend = recentAvgResponse - olderAvgResponse;

        // إذا زاد استخدام الذاكرة أو وقت الاستجابة بشكل كبير
        if (memoryTrend > 50 || responseTrend > 100) {
            return 'DEGRADING';
        }

        // إذا انخفض استخدام الذاكرة أو وقت الاستجابة بشكل كبير
        if (memoryTrend < -20 || responseTrend < -50) {
            return 'IMPROVING';
        }

        return 'STABLE';
    }

    /**
     * حساب نقاط الأداء
     * Calculate performance score
     * 
     * @param statistics - إحصائيات الأداء
     * @returns نقاط الأداء (0-100)
     */
    private calculatePerformanceScore(statistics: PerformanceStatistics): number {
        let score = 100;

        // خصم نقاط لاستخدام الذاكرة العالي
        if (statistics.averageMemoryUsage > this.performanceThresholds.memoryUsage) {
            score -= 20;
        }

        // خصم نقاط لاستخدام المعالج العالي
        if (statistics.averageCpuUsage > this.performanceThresholds.cpuUsage) {
            score -= 20;
        }

        // خصم نقاط لوقت الاستجابة البطيء
        if (statistics.averageResponseTime > this.performanceThresholds.responseTime) {
            score -= 15;
        }

        // خصم نقاط لزمن استجابة الشبكة العالي
        if (statistics.averageNetworkLatency > this.performanceThresholds.networkLatency) {
            score -= 10;
        }

        // خصم نقاط لمعدل الأخطاء العالي
        if (statistics.errorRate > this.performanceThresholds.errorRate) {
            score -= 25;
        }

        // خصم نقاط لمعدل التحذيرات العالي
        if (statistics.warningRate > 10) {
            score -= 10;
        }

        return Math.max(0, score);
    }

    /**
     * تحديد المشاكل
     * Identify issues
     * 
     * @param statistics - إحصائيات الأداء
     * @returns قائمة المشاكل
     */
    private identifyIssues(statistics: PerformanceStatistics): string[] {
        const issues: string[] = [];

        if (statistics.averageMemoryUsage > this.performanceThresholds.memoryUsage) {
            issues.push(`استخدام ذاكرة عالي: ${statistics.averageMemoryUsage.toFixed(1)} MB`);
        }

        if (statistics.averageCpuUsage > this.performanceThresholds.cpuUsage) {
            issues.push(`استخدام معالج عالي: ${statistics.averageCpuUsage.toFixed(1)}%`);
        }

        if (statistics.averageResponseTime > this.performanceThresholds.responseTime) {
            issues.push(`وقت استجابة بطيء: ${statistics.averageResponseTime.toFixed(1)} ms`);
        }

        if (statistics.averageNetworkLatency > this.performanceThresholds.networkLatency) {
            issues.push(`زمن استجابة شبكة عالي: ${statistics.averageNetworkLatency.toFixed(1)} ms`);
        }

        if (statistics.errorRate > this.performanceThresholds.errorRate) {
            issues.push(`معدل أخطاء عالي: ${statistics.errorRate.toFixed(1)}%`);
        }

        if (statistics.warningRate > 10) {
            issues.push(`معدل تحذيرات عالي: ${statistics.warningRate.toFixed(1)}%`);
        }

        return issues;
    }

    /**
     * إنتاج التوصيات
     * Generate recommendations
     * 
     * @param statistics - إحصائيات الأداء
     * @param issues - قائمة المشاكل
     * @returns قائمة التوصيات
     */
    private generateRecommendations(statistics: PerformanceStatistics, issues: string[]): string[] {
        const recommendations: string[] = [];

        if (statistics.averageMemoryUsage > this.performanceThresholds.memoryUsage) {
            recommendations.push('تحسين إدارة الذاكرة وتنظيف الموارد غير المستخدمة');
        }

        if (statistics.averageCpuUsage > this.performanceThresholds.cpuUsage) {
            recommendations.push('تحسين خوارزميات المعالجة وتقليل العمليات المكثفة');
        }

        if (statistics.averageResponseTime > this.performanceThresholds.responseTime) {
            recommendations.push('تحسين سرعة الاستجابة وتحسين قواعد البيانات');
        }

        if (statistics.errorRate > this.performanceThresholds.errorRate) {
            recommendations.push('مراجعة وإصلاح الأخطاء المتكررة');
        }

        if (recommendations.length === 0) {
            recommendations.push('الأداء جيد، استمر في المراقبة المنتظمة');
        }

        return recommendations;
    }

    /**
     * إنتاج الملخص
     * Generate summary
     * 
     * @param statistics - إحصائيات الأداء
     * @param trend - اتجاه الأداء
     * @param score - نقاط الأداء
     * @returns ملخص الأداء
     */
    private generateSummary(statistics: PerformanceStatistics, trend: string, score: number): string {
        const trendText = {
            'IMPROVING': 'يتحسن',
            'STABLE': 'مستقر',
            'DEGRADING': 'يتدهور'
        }[trend] || 'غير محدد';

        return `الأداء العام: ${score}/100 - الاتجاه: ${trendText} - العمليات: ${statistics.totalOperations} - الأخطاء: ${statistics.totalErrors}`;
    }
}
