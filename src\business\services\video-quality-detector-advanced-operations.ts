/**
 * العمليات المتقدمة لكشف جودة الفيديو
 * Advanced video quality detection operations
 *
 * هذا الملف يجمع العمليات المتقدمة من الملفات المتخصصة
 * This file aggregates advanced operations from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQuality } from '@shared/types';
import { VideoQualityConfig } from './video-quality-config';
import { VideoQualityDetectorCore } from './video-quality-detector-core';

// استيراد العمليات المتخصصة / Import specialized operations
import { VideoQualityDetectorOperationsBasic } from './video-quality-detector-operations-basic';

/**
 * فئة العمليات المتقدمة لكشف الجودة
 * Advanced quality detection operations class
 */
export class VideoQualityDetectorAdvancedOperations {
    private readonly basicOperations: VideoQualityDetectorOperationsBasic;
    private readonly coreDetector: VideoQualityDetectorCore;

    constructor(config: VideoQualityConfig, coreDetector: VideoQualityDetectorCore) {
        this.basicOperations = new VideoQualityDetectorOperationsBasic(config);
        this.coreDetector = coreDetector;
    }

    /** كشف الجودة من عنصر الفيديو / Detect quality from video element */
    public detectFromVideoElement(): VideoQuality | null {
        return this.basicOperations.detectFromVideoElement();
    }

    /** كشف الجودة من واجهة المستخدم / Detect quality from UI */
    public detectFromUI(): VideoQuality | null {
        return this.basicOperations.detectFromUI();
    }

    /** كشف الجودة من البيانات الوصفية / Detect quality from metadata */
    public detectFromMetadata(): VideoQuality | null {
        return this.basicOperations.detectFromMetadata();
    }

    /** كشف الجودة من الشبكة / Detect quality from network */
    public detectFromNetwork(): VideoQuality | null {
        return this.basicOperations.detectFromNetwork();
    }

    /** التحقق من صحة الجودة / Validate quality */
    public validateDetectedQuality(quality: VideoQuality): boolean {
        return this.basicOperations.validateDetectedQuality(quality);
    }

    /** الحصول على الجودات المتاحة / Get available qualities */
    public getAvailableQualities(): VideoQuality[] {
        return this.basicOperations.getAvailableQualities();
    }
}
