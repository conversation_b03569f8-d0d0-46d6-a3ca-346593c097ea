/**
 * مراقبة خدمة جودة الفيديو - الأساسية
 * Video quality service monitoring - Core
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQuality, ValidationResult } from '@shared/types';
import { ResourceManager } from '@shared/utils/resource-manager';
import { 
    VideoQualityConfig,
    VIDEO_QUALITY_CONSTANTS,
    VIDEO_QUALITY_MESSAGES,
    VideoQualityState
} from './video-quality-config';

/**
 * فئة المراقبة الأساسية لخدمة جودة الفيديو
 */
export class VideoQualityServiceMonitoringCore {
    private readonly resourceManager: ResourceManager;
    private readonly config: VideoQualityConfig;
    
    private observer: MutationObserver | null = null;
    private isMonitoring: boolean = false;
    private monitoringCallbacks: Map<string, (quality: VideoQuality) => void> = new Map();

    constructor(resourceManager: ResourceManager, config: VideoQualityConfig) {
        this.resourceManager = resourceManager;
        this.config = config;
    }

    /**
     * بدء مراقبة تغييرات الجودة / Start monitoring quality changes
     */
    public async startMonitoring(): Promise<ValidationResult> {
        try {
            if (this.isMonitoring) {
                return {
                    isValid: true,
                    errors: []
                };
            }

            this.observer = new MutationObserver((mutations) => {
                this.handleMutations(mutations);
            });

            const targetElement = document.querySelector('video');
            if (!targetElement) {
                return {
                    isValid: false,
                    errors: ['عنصر الفيديو غير موجود']
                };
            }

            this.observer.observe(targetElement, {
                attributes: true,
                attributeFilter: ['src', 'currentSrc'],
                childList: true,
                subtree: true
            });

            this.isMonitoring = true;

            return {
                isValid: true,
                errors: []
            };

        } catch (error) {
            return {
                isValid: false,
                errors: [`خطأ في بدء المراقبة: ${error}`]
            };
        }
    }

    /**
     * إيقاف مراقبة تغييرات الجودة / Stop monitoring quality changes
     */
    public async stopMonitoring(): Promise<ValidationResult> {
        try {
            if (!this.isMonitoring) {
                return {
                    isValid: true,
                    errors: []
                };
            }

            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }

            this.isMonitoring = false;

            return {
                isValid: true,
                errors: []
            };

        } catch (error) {
            return {
                isValid: false,
                errors: [`خطأ في إيقاف المراقبة: ${error}`]
            };
        }
    }

    /**
     * معالجة التغييرات / Handle mutations
     */
    private handleMutations(mutations: MutationRecord[]): void {
        try {
            for (const mutation of mutations) {
                if (mutation.type === 'attributes') {
                    this.handleAttributeChange(mutation);
                } else if (mutation.type === 'childList') {
                    this.handleChildListChange(mutation);
                }
            }
        } catch (error) {
            console.error('خطأ في معالجة التغييرات:', error);
        }
    }

    /**
     * معالجة تغيير الخصائص / Handle attribute change
     */
    private handleAttributeChange(mutation: MutationRecord): void {
        try {
            const target = mutation.target as HTMLVideoElement;
            if (target && target.tagName === 'VIDEO') {
                const currentQuality = this.detectCurrentQuality(target);
                if (currentQuality) {
                    this.notifyQualityChange(currentQuality);
                }
            }
        } catch (error) {
            console.error('خطأ في معالجة تغيير الخصائص:', error);
        }
    }

    /**
     * معالجة تغيير قائمة الأطفال / Handle child list change
     */
    private handleChildListChange(mutation: MutationRecord): void {
        try {
            // فحص العقد المضافة
            for (const node of Array.from(mutation.addedNodes)) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    const element = node as Element;
                    if (element.tagName === 'VIDEO') {
                        this.setupVideoMonitoring(element as HTMLVideoElement);
                    }
                }
            }
        } catch (error) {
            console.error('خطأ في معالجة تغيير قائمة الأطفال:', error);
        }
    }

    /**
     * إعداد مراقبة الفيديو / Setup video monitoring
     */
    private setupVideoMonitoring(video: HTMLVideoElement): void {
        try {
            video.addEventListener('loadstart', () => {
                const quality = this.detectCurrentQuality(video);
                if (quality) {
                    this.notifyQualityChange(quality);
                }
            });

            video.addEventListener('loadedmetadata', () => {
                const quality = this.detectCurrentQuality(video);
                if (quality) {
                    this.notifyQualityChange(quality);
                }
            });

        } catch (error) {
            console.error('خطأ في إعداد مراقبة الفيديو:', error);
        }
    }

    /**
     * اكتشاف الجودة الحالية / Detect current quality
     */
    private detectCurrentQuality(video: HTMLVideoElement): VideoQuality | null {
        try {
            // فحص دقة الفيديو
            if (video.videoHeight && video.videoWidth) {
                const height = video.videoHeight;
                
                if (height >= 2160) return '2160p';
                if (height >= 1440) return '1440p';
                if (height >= 1080) return '1080p';
                if (height >= 720) return '720p';
                if (height >= 480) return '480p';
                if (height >= 360) return '360p';
                if (height >= 240) return '240p';
                return '144p';
            }

            return null;

        } catch (error) {
            console.error('خطأ في اكتشاف الجودة الحالية:', error);
            return null;
        }
    }

    /**
     * إشعار تغيير الجودة / Notify quality change
     */
    private notifyQualityChange(quality: VideoQuality): void {
        try {
            for (const [id, callback] of this.monitoringCallbacks) {
                try {
                    callback(quality);
                } catch (error) {
                    console.error(`خطأ في استدعاء callback ${id}:`, error);
                }
            }
        } catch (error) {
            console.error('خطأ في إشعار تغيير الجودة:', error);
        }
    }

    /**
     * إضافة callback للمراقبة / Add monitoring callback
     */
    public addMonitoringCallback(id: string, callback: (quality: VideoQuality) => void): void {
        this.monitoringCallbacks.set(id, callback);
    }

    /**
     * إزالة callback للمراقبة / Remove monitoring callback
     */
    public removeMonitoringCallback(id: string): void {
        this.monitoringCallbacks.delete(id);
    }

    /**
     * التحقق من حالة المراقبة / Check monitoring status
     */
    public isCurrentlyMonitoring(): boolean {
        return this.isMonitoring;
    }

    /**
     * الحصول على عدد callbacks / Get callbacks count
     */
    public getCallbacksCount(): number {
        return this.monitoringCallbacks.size;
    }

    /**
     * تنظيف الموارد / Cleanup resources
     */
    public async cleanup(): Promise<void> {
        try {
            await this.stopMonitoring();
            this.monitoringCallbacks.clear();
        } catch (error) {
            console.error('خطأ في تنظيف الموارد:', error);
        }
    }
}
