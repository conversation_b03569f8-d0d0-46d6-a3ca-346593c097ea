/**
 * عمليات مراقب الوضع المظلم
 * Dark mode observer operations
 *
 * هذا الملف يجمع جميع عمليات المراقب من الملفات المتخصصة
 * This file aggregates all observer operations from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeConfig } from './dark-mode-config';
import { DarkModeStylesApplicator } from './dark-mode-styles';

// استيراد الفئات المتخصصة / Import specialized classes
import { DarkModeObserverOperationsAdvanced } from './dark-mode-observer-operations-advanced';
import { DarkModeObserverOperationsBasic } from './dark-mode-observer-operations-basic';

/**
 * فئة عمليات مراقب الوضع المظلم
 * Dark mode observer operations class
 */
export class DarkModeObserverOperations {
    private readonly config: DarkModeConfig;
    private readonly basicOperations: DarkModeObserverOperationsBasic;
    private readonly advancedOperations: DarkModeObserverOperationsAdvanced;

    constructor(config: DarkModeConfig, stylesApplicator: DarkModeStylesApplicator) {
        this.config = config;
        this.basicOperations = new DarkModeObserverOperationsBasic(config, stylesApplicator);
        this.advancedOperations = new DarkModeObserverOperationsAdvanced(config);
    }

    /** تطبيق الوضع المظلم على العنصر / Apply dark mode to element */
    public applyDarkModeToElement(element: Element): boolean {
        return this.basicOperations.applyDarkModeToElement(element);
    }

    /** إزالة الوضع المظلم من العنصر / Remove dark mode from element */
    public removeDarkModeFromElement(element: Element): boolean {
        return this.basicOperations.removeDarkModeFromElement(element);
    }

    /** تبديل الوضع المظلم / Toggle dark mode */
    public toggleDarkMode(element: Element): boolean {
        return this.basicOperations.toggleDarkMode(element);
    }

    /** التحقق من إمكانية تطبيق الوضع المظلم / Check if dark mode can be applied */
    public canApplyDarkMode(element: Element): boolean {
        return this.basicOperations.canApplyDarkMode(element);
    }

    /** التحقق من تطبيق الوضع المظلم / Check if dark mode is applied */
    public isDarkModeApplied(element: Element): boolean {
        return this.basicOperations.isDarkModeApplied(element);
    }

    /** معالجة العقد المضافة / Process added nodes */
    public processAddedNodes(nodes: NodeList): void {
        this.advancedOperations.processAddedNodes(nodes);
    }

    /** معالجة العقد المحذوفة / Process removed nodes */
    public processRemovedNodes(nodes: NodeList): void {
        this.advancedOperations.processRemovedNodes(nodes);
    }

    /** معالجة التغييرات في الخصائص / Process attribute changes */
    public processAttributeChanges(target: Element, attributeName: string): void {
        this.advancedOperations.processAttributeChanges(target, attributeName);
    }


}
