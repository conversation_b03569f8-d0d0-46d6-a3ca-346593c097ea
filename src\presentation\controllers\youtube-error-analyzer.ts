/**
 * محلل أخطاء YouTube
 * YouTube error analyzer
 * 
 * هذا الملف يحتوي على منطق تحليل أخطاء YouTube
 * This file contains YouTube error analysis logic
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { 
    YouTubeControllerErrorReport,
    YouTubeControllerErrorType,
    YOUTUBE_CONTROLLER_CONSTANTS
} from './youtube-controller-config';

/**
 * فئة محلل أخطاء YouTube
 * YouTube error analyzer class
 */
export class YouTubeErrorAnalyzer {
    private errorHistory: YouTubeControllerErrorReport[] = [];
    private errorCounts: Map<YouTubeControllerErrorType, number> = new Map();

    /**
     * منشئ محلل الأخطاء
     * Error analyzer constructor
     */
    constructor() {
        this.initializeErrorCounts();
    }

    /**
     * تهيئة عدادات الأخطاء
     * Initialize error counts
     */
    private initializeErrorCounts(): void {
        for (const errorType of Object.values(YouTubeControllerErrorType)) {
            this.errorCounts.set(errorType, 0);
        }
    }

    /**
     * إضافة خطأ للتحليل
     * Add error for analysis
     * 
     * @param error - تقرير الخطأ
     */
    public addError(error: YouTubeControllerErrorReport): void {
        this.errorHistory.push(error);
        
        // تحديث العدادات
        const currentCount = this.errorCounts.get(error.errorType) || 0;
        this.errorCounts.set(error.errorType, currentCount + 1);

        // الحفاظ على حد أقصى للتاريخ
        if (this.errorHistory.length > YOUTUBE_CONTROLLER_CONSTANTS.MAX_ERROR_HISTORY) {
            this.errorHistory = this.errorHistory.slice(-YOUTUBE_CONTROLLER_CONSTANTS.MAX_ERROR_HISTORY);
        }
    }

    /**
     * الحصول على تاريخ الأخطاء
     * Get error history
     * 
     * @returns قائمة الأخطاء
     */
    public getErrorHistory(): YouTubeControllerErrorReport[] {
        return [...this.errorHistory];
    }

    /**
     * الحصول على إحصائيات الأخطاء
     * Get error statistics
     * 
     * @returns إحصائيات الأخطاء
     */
    public getErrorStatistics(): Record<string, number> {
        const stats: Record<string, number> = {};
        
        for (const [errorType, count] of this.errorCounts.entries()) {
            stats[errorType] = count;
        }
        
        return stats;
    }

    /**
     * الحصول على الأخطاء حسب النوع
     * Get errors by type
     * 
     * @param errorType - نوع الخطأ
     * @returns قائمة الأخطاء من النوع المحدد
     */
    public getErrorsByType(errorType: YouTubeControllerErrorType): YouTubeControllerErrorReport[] {
        return this.errorHistory.filter(error => error.errorType === errorType);
    }

    /**
     * الحصول على الأخطاء الأخيرة
     * Get recent errors
     * 
     * @param count - عدد الأخطاء المطلوبة
     * @returns قائمة الأخطاء الأخيرة
     */
    public getRecentErrors(count: number = 10): YouTubeControllerErrorReport[] {
        return this.errorHistory.slice(-count);
    }

    /**
     * الحصول على الأخطاء الحرجة
     * Get critical errors
     * 
     * @returns قائمة الأخطاء الحرجة
     */
    public getCriticalErrors(): YouTubeControllerErrorReport[] {
        return this.errorHistory.filter(error => 
            error.errorType === YouTubeControllerErrorType.CRITICAL_ERROR ||
            error.errorType === YouTubeControllerErrorType.SECURITY_ERROR ||
            error.errorType === YouTubeControllerErrorType.PERFORMANCE_ERROR
        );
    }

    /**
     * تحليل اتجاهات الأخطاء
     * Analyze error trends
     * 
     * @param timeWindowMs - نافذة زمنية بالميلي ثانية
     * @returns تحليل الاتجاهات
     */
    public analyzeErrorTrends(timeWindowMs: number = 3600000): {
        totalErrors: number;
        errorRate: number;
        mostCommonError: string;
        criticalErrorCount: number;
    } {
        const now = new Date();
        const cutoffTime = new Date(now.getTime() - timeWindowMs);
        
        const recentErrors = this.errorHistory.filter(error => 
            error.timestamp >= cutoffTime
        );

        const errorTypeCounts: Record<string, number> = {};
        let criticalErrorCount = 0;

        for (const error of recentErrors) {
            errorTypeCounts[error.errorType] = (errorTypeCounts[error.errorType] || 0) + 1;
            
            if (error.errorType === YouTubeControllerErrorType.CRITICAL_ERROR ||
                error.errorType === YouTubeControllerErrorType.SECURITY_ERROR) {
                criticalErrorCount++;
            }
        }

        const mostCommonError = Object.entries(errorTypeCounts)
            .sort(([, a], [, b]) => b - a)[0]?.[0] || 'NONE';

        return {
            totalErrors: recentErrors.length,
            errorRate: recentErrors.length / (timeWindowMs / 1000), // أخطاء في الثانية
            mostCommonError,
            criticalErrorCount
        };
    }

    /**
     * تنظيف تاريخ الأخطاء
     * Clear error history
     */
    public clearErrorHistory(): void {
        this.errorHistory = [];
        this.initializeErrorCounts();
    }

    /**
     * تصدير تقرير الأخطاء
     * Export error report
     * 
     * @returns تقرير مفصل بصيغة JSON
     */
    public exportErrorReport(): string {
        const report = {
            timestamp: new Date().toISOString(),
            totalErrors: this.errorHistory.length,
            errorCounts: Object.fromEntries(this.errorCounts),
            recentErrors: this.getRecentErrors(20),
            criticalErrors: this.getCriticalErrors(),
            trends: this.analyzeErrorTrends()
        };

        return JSON.stringify(report, null, 2);
    }

    /**
     * فحص إذا كان هناك أخطاء حرجة حديثة
     * Check if there are recent critical errors
     * 
     * @param timeWindowMs - النافذة الزمنية للفحص
     * @returns true إذا كان هناك أخطاء حرجة حديثة
     */
    public hasRecentCriticalErrors(timeWindowMs: number = 300000): boolean {
        const now = new Date();
        const cutoffTime = new Date(now.getTime() - timeWindowMs);
        
        return this.errorHistory.some(error => 
            error.timestamp >= cutoffTime && (
                error.errorType === YouTubeControllerErrorType.CRITICAL_ERROR ||
                error.errorType === YouTubeControllerErrorType.SECURITY_ERROR
            )
        );
    }
}
