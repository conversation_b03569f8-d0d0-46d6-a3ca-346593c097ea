/**
 * أنواع التحقق النهائي
 * Final verification types
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * نتيجة التحقق الشاملة
 * Comprehensive verification result
 */
export interface VerificationResult {
    readonly constitutionCompliance: {
        readonly score: number;
        readonly issues: number;
        readonly criticalIssues: number;
    };
    readonly functionalityTests: {
        readonly score: number;
        readonly passedTests: number;
        readonly totalTests: number;
    };
    readonly unitTests: {
        readonly coverage: number;
        readonly passedTests: number;
        readonly totalTests: number;
    };
    readonly overallScore: number;
    readonly grade: 'A+' | 'A' | 'B+' | 'B' | 'C+' | 'C' | 'D' | 'F';
    readonly recommendations: string[];
    readonly criticalIssues: string[];
    readonly timestamp: Date;
}

/**
 * تكوين التحقق
 * Verification configuration
 */
export interface VerificationConfig {
    readonly enableConstitutionCheck: boolean;
    readonly enableFunctionalityTests: boolean;
    readonly enableUnitTests: boolean;
    readonly enablePerformanceTests: boolean;
    readonly enableSecurityTests: boolean;
    readonly minCoverageThreshold: number;
    readonly minScoreThreshold: number;
    readonly outputFormat: 'json' | 'html' | 'text';
    readonly outputPath: string;
    readonly verbose: boolean;
}

/**
 * نتيجة اختبار فردي
 * Individual test result
 */
export interface TestResult {
    readonly name: string;
    readonly passed: boolean;
    readonly score: number;
    readonly duration: number;
    readonly error?: string;
    readonly details?: any;
}

/**
 * تقرير التحقق
 * Verification report
 */
export interface VerificationReport {
    readonly summary: VerificationResult;
    readonly detailedResults: {
        readonly constitution: any;
        readonly functionality: TestResult[];
        readonly unitTests: TestResult[];
        readonly performance: TestResult[];
        readonly security: TestResult[];
    };
    readonly generatedAt: Date;
    readonly version: string;
}

/**
 * إعدادات التقييم
 * Grading settings
 */
export interface GradingSettings {
    readonly weights: {
        readonly constitution: number;
        readonly functionality: number;
        readonly unitTests: number;
        readonly performance: number;
        readonly security: number;
    };
    readonly thresholds: {
        readonly aPlus: number;
        readonly a: number;
        readonly bPlus: number;
        readonly b: number;
        readonly cPlus: number;
        readonly c: number;
        readonly d: number;
    };
}
