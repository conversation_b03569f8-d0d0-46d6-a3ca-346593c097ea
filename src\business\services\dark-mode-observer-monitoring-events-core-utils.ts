/**
 * مراقبة الوضع المظلم - أدوات الأحداث الأساسية
 * Dark mode monitoring - Core events utilities
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeObserverOperations } from './dark-mode-observer-operations';

/**
 * فئة أدوات الأحداث الأساسية لمراقبة الوضع المظلم
 * Core events utilities for dark mode monitoring
 */
export class DarkModeObserverMonitoringEventsCoreUtils {

    /**
     * إعداد مستمعي أحداث المشغل
     * Setup player event listeners
     */
    public static setupPlayerEventListeners(operations: DarkModeObserverOperations): void {
        // مراقبة تغييرات حالة المشغل
        const checkForPlayer = () => {
            const player = document.querySelector('#movie_player');
            if (player) {
                console.log('Player found, setting up player events');
                this.setupPlayerSpecificEvents(player, operations);
            } else {
                setTimeout(checkForPlayer, 1000);
            }
        };

        checkForPlayer();
    }

    /**
     * إعداد أحداث المشغل المحددة
     * Setup player specific events
     */
    public static setupPlayerSpecificEvents(player: Element, operations: DarkModeObserverOperations): void {
        // مراقبة تغييرات خصائص المشغل
        const observer = new MutationObserver((mutations) => {
            let shouldReapply = false;

            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' &&
                    (mutation.attributeName === 'class' || mutation.attributeName === 'style')) {
                    shouldReapply = true;
                }
            });

            if (shouldReapply) {
                console.log('Player attributes changed, reapplying dark mode');
                setTimeout(() => {
                    operations.applyDarkMode();
                }, 100);
            }
        });

        observer.observe(player, {
            attributes: true,
            attributeFilter: ['class', 'style']
        });
    }

    /**
     * إعداد مستمعي أحداث الشبكة
     * Setup network event listeners
     */
    public static setupNetworkEventListeners(operations: DarkModeObserverOperations): void {
        window.addEventListener('online', () => {
            console.log('Network online, reapplying dark mode');
            setTimeout(() => {
                operations.applyDarkMode();
            }, 200);
        });

        window.addEventListener('offline', () => {
            console.log('Network offline');
        });
    }

    /**
     * إعداد مستمعي أحداث التاريخ
     * Setup history event listeners
     */
    public static setupHistoryEventListeners(operations: DarkModeObserverOperations): void {
        window.addEventListener('popstate', () => {
            console.log('History popstate, reapplying dark mode');
            setTimeout(() => {
                operations.applyDarkMode();
            }, 300);
        });

        // مراقبة تغييرات URL
        let currentUrl = window.location.href;
        const urlCheckInterval = setInterval(() => {
            if (window.location.href !== currentUrl) {
                currentUrl = window.location.href;
                console.log('URL changed, reapplying dark mode');
                setTimeout(() => {
                    operations.applyDarkMode();
                }, 400);
            }
        }, 1000);

        // تنظيف الفاصل الزمني عند إغلاق النافذة
        window.addEventListener('beforeunload', () => {
            clearInterval(urlCheckInterval);
        });
    }

    /**
     * إنشاء مستمع أحداث محسن - تفويض للأدوات الأساسية
     * Create optimized event listener - Delegate to basic utils
     */
    public static createOptimizedEventListener(
        eventType: string,
        callback: () => void,
        delay: number = 100
    ): EventListener {
        return DarkModeObserverMonitoringEventsCoreUtilsBasic.createOptimizedEventListener(eventType, callback, delay);
    }

    /**
     * إنشاء مستمع أحداث مع تحكم في التكرار - تفويض للأدوات الأساسية
     * Create throttled event listener - Delegate to basic utils
     */
    public static createThrottledEventListener(
        callback: () => void,
        delay: number = 250
    ): EventListener {
        return DarkModeObserverMonitoringEventsCoreUtilsBasic.createThrottledEventListener(callback, delay);
    }

    /**
     * فحص دعم الأحداث - تفويض للأدوات الأساسية
     * Check event support - Delegate to basic utils
     */
    public static checkEventSupport(eventType: string): boolean {
        return DarkModeObserverMonitoringEventsCoreUtilsBasic.checkEventSupport(eventType);
    }

    /**
     * إنشاء حدث مخصص - تفويض للأدوات الأساسية
     * Create custom event - Delegate to basic utils
     */
    public static createCustomEvent(eventType: string, detail?: any): CustomEvent {
        return DarkModeObserverMonitoringEventsCoreUtilsBasic.createCustomEvent(eventType, detail);
    }

    /**
     * إرسال حدث مخصص - تفويض للأدوات الأساسية
     * Dispatch custom event - Delegate to basic utils
     */
    public static dispatchCustomEvent(eventType: string, detail?: any, target: EventTarget = window): boolean {
        return DarkModeObserverMonitoringEventsCoreUtilsBasic.dispatchCustomEvent(eventType, detail, target);
    }

    /**
     * تنظيف مستمعي الأحداث - تفويض للأدوات الأساسية
     * Cleanup event listeners - Delegate to basic utils
     */
    public static cleanupEventListeners(listeners: Map<string, EventListener[]>): void {
        DarkModeObserverMonitoringEventsCoreUtilsBasic.cleanupEventListeners(listeners);
    }

    /**
     * فحص حالة الأحداث - تفويض للأدوات الأساسية
     * Check events state - Delegate to basic utils
     */
    public static checkEventsState(): {
        windowListeners: number;
        documentListeners: number;
        customEvents: string[];
    } {
        return DarkModeObserverMonitoringEventsCoreUtilsBasic.checkEventsState();
    }

    /**
     * تسجيل معلومات الأحداث - تفويض للأدوات الأساسية
     * Log events information - Delegate to basic utils
     */
    public static logEventsInfo(): void {
        DarkModeObserverMonitoringEventsCoreUtilsBasic.logEventsInfo();
    }
}
