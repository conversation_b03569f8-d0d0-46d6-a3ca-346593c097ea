/**
 * العمليات المتقدمة الأساسية لخدمة الإعدادات
 * Advanced core settings service operations
 * 
 * هذا الملف يحتوي على العمليات المتقدمة الأساسية لإدارة الإعدادات
 * This file contains advanced core settings management operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ApplicationConfig } from '@shared/types';
import { ResourceManager } from '@shared/utils/resource-manager';
import * as fs from 'fs';
import * as path from 'path';
import { SettingsBackupManager } from './settings-backup';
import {
    ExportOptions,
    ImportOptions,
    SETTINGS_MESSAGES,
    SettingsManagerConfig
} from './settings-config';
import { SettingsServiceCore } from './settings-service-core';

/**
 * فئة العمليات المتقدمة الأساسية لخدمة الإعدادات
 * Advanced core settings service operations class
 */
export class SettingsServiceAdvancedCore {
    private readonly coreService: SettingsServiceCore;
    private readonly config: SettingsManagerConfig;
    private readonly backupManager: SettingsBackupManager;
    private readonly resourceManager: ResourceManager;

    /**
     * منشئ العمليات المتقدمة الأساسية / Advanced core operations constructor
     */
    constructor(
        coreService: SettingsServiceCore,
        config: SettingsManagerConfig,
        backupManager: SettingsBackupManager,
        resourceManager: ResourceManager
    ) {
        this.coreService = coreService;
        this.config = config;
        this.backupManager = backupManager;
        this.resourceManager = resourceManager;
    }

    /**
     * تصدير الإعدادات / Export settings
     */
    public async exportSettings(filePath: string, options?: ExportOptions): Promise<boolean> {
        try {
            const settings = this.coreService.getAllSettings();
            const exportData = {
                settings,
                metadata: {
                    exportDate: new Date().toISOString(),
                    version: this.config.version || '1.0.0',
                    format: options?.format || 'json'
                }
            };

            // إنشاء المجلد إذا لم يكن موجوداً
            const dir = path.dirname(filePath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }

            // كتابة البيانات
            const dataToWrite = options?.format === 'pretty' 
                ? JSON.stringify(exportData, null, 2)
                : JSON.stringify(exportData);

            fs.writeFileSync(filePath, dataToWrite, 'utf8');

            console.log(`${SETTINGS_MESSAGES.SETTINGS_EXPORTED}: ${filePath}`);
            return true;

        } catch (error) {
            console.error(SETTINGS_MESSAGES.ERROR_EXPORT_SETTINGS, error);
            return false;
        }
    }

    /**
     * استيراد الإعدادات / Import settings
     */
    public async importSettings(filePath: string, options?: ImportOptions): Promise<boolean> {
        try {
            if (!fs.existsSync(filePath)) {
                console.error(`ملف الإعدادات غير موجود: ${filePath}`);
                return false;
            }

            const fileContent = fs.readFileSync(filePath, 'utf8');
            const importData = JSON.parse(fileContent);

            let settingsToImport: ApplicationConfig;

            // التحقق من تنسيق البيانات
            if (importData.settings && importData.metadata) {
                settingsToImport = importData.settings;
            } else {
                settingsToImport = importData;
            }

            // إنشاء نسخة احتياطية قبل الاستيراد
            if (options?.createBackup !== false) {
                await this.createAutomaticBackup();
            }

            // تطبيق الإعدادات
            if (options?.merge === true) {
                const currentSettings = this.coreService.getAllSettings();
                const mergedSettings = { ...currentSettings, ...settingsToImport };
                await this.coreService.setMultipleSettings(mergedSettings);
            } else {
                await this.coreService.setMultipleSettings(settingsToImport);
            }

            console.log(`${SETTINGS_MESSAGES.SETTINGS_IMPORTED}: ${filePath}`);
            return true;

        } catch (error) {
            console.error(SETTINGS_MESSAGES.ERROR_IMPORT_SETTINGS, error);
            return false;
        }
    }

    /**
     * إنشاء نسخة احتياطية تلقائية / Create automatic backup
     */
    public async createAutomaticBackup(): Promise<boolean> {
        try {
            const settings = this.coreService.getAllSettings();
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const description = `نسخة احتياطية تلقائية - ${timestamp}`;

            const backupInfo = await this.backupManager.createBackup(settings, description);
            
            if (backupInfo) {
                console.log(`تم إنشاء نسخة احتياطية: ${backupInfo.path}`);
                return true;
            }

            return false;

        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية التلقائية:', error);
            return false;
        }
    }

    /**
     * استعادة من النسخة الاحتياطية / Restore from backup
     */
    public async restoreFromBackup(backupPath: string): Promise<boolean> {
        try {
            const restoredSettings = await this.backupManager.restoreFromBackup(backupPath);
            
            if (restoredSettings) {
                await this.coreService.setMultipleSettings(restoredSettings);
                console.log(`تم استعادة الإعدادات من: ${backupPath}`);
                return true;
            }

            return false;

        } catch (error) {
            console.error('خطأ في استعادة النسخة الاحتياطية:', error);
            return false;
        }
    }

    /**
     * إعادة تحميل الإعدادات / Reload settings
     */
    public async reloadSettings(): Promise<boolean> {
        try {
            // إعادة تحميل الإعدادات من الملف
            const settingsPath = this.coreService.getSettingsPath();
            
            if (fs.existsSync(settingsPath)) {
                const fileContent = fs.readFileSync(settingsPath, 'utf8');
                const settings = JSON.parse(fileContent);
                
                await this.coreService.setMultipleSettings(settings);
                console.log('تم إعادة تحميل الإعدادات بنجاح');
                return true;
            }

            console.warn('ملف الإعدادات غير موجود، سيتم استخدام القيم الافتراضية');
            this.coreService.resetToDefaults();
            return true;

        } catch (error) {
            console.error('خطأ في إعادة تحميل الإعدادات:', error);
            return false;
        }
    }

    /**
     * التحقق من تكامل الإعدادات / Verify settings integrity
     */
    public async verifyIntegrity(): Promise<boolean> {
        try {
            const validation = this.coreService.validateCurrentSettings();
            
            if (!validation.isValid) {
                console.warn('تم اكتشاف مشاكل في تكامل الإعدادات:', validation.errors);
                
                // محاولة إصلاح المشاكل
                const fixed = this.coreService.fixCorruptedSettings();
                if (fixed) {
                    console.log('تم إصلاح مشاكل تكامل الإعدادات');
                    return true;
                } else {
                    console.error('فشل في إصلاح مشاكل تكامل الإعدادات');
                    return false;
                }
            }

            console.log('تكامل الإعدادات سليم');
            return true;

        } catch (error) {
            console.error('خطأ في التحقق من تكامل الإعدادات:', error);
            return false;
        }
    }

    /**
     * تحسين الإعدادات / Optimize settings
     */
    public async optimizeSettings(): Promise<boolean> {
        try {
            const settings = this.coreService.getAllSettings();
            let optimized = false;

            // إزالة القيم المكررة مع القيم الافتراضية
            const defaultSettings = this.coreService.getDefaultSettings();
            const optimizedSettings: Partial<ApplicationConfig> = {};

            for (const [key, value] of Object.entries(settings)) {
                const typedKey = key as keyof ApplicationConfig;
                const defaultValue = defaultSettings[typedKey];
                
                // الاحتفاظ فقط بالقيم المختلفة عن الافتراضية
                if (JSON.stringify(value) !== JSON.stringify(defaultValue)) {
                    (optimizedSettings as any)[key] = value;
                }
            }

            // تطبيق الإعدادات المحسنة
            if (Object.keys(optimizedSettings).length < Object.keys(settings).length) {
                this.coreService.resetToDefaults();
                await this.coreService.setMultipleSettings(optimizedSettings);
                optimized = true;
                console.log('تم تحسين الإعدادات بنجاح');
            }

            return optimized;

        } catch (error) {
            console.error('خطأ في تحسين الإعدادات:', error);
            return false;
        }
    }

    /**
     * تنظيف الموارد / Cleanup resources
     */
    public cleanup(): void {
        this.resourceManager.cleanup();
    }
}
