/**
 * ثوابت جودة الفيديو
 * Video quality constants
 *
 * هذا الملف يحتوي على جميع الثوابت المتعلقة بجودة الفيديو
 * This file contains all video quality related constants
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQuality } from '@shared/types';

// إعادة تصدير ثوابت جودة الفيديو من الملفات المتخصصة
// Re-export video quality constants from specialized files
export * from './video-quality-selectors';

/**
 * جودات الفيديو المدعومة
 * Supported video qualities
 */
export const VIDEO_QUALITIES: readonly VideoQuality[] = [
    '144p',
    '240p',
    '360p',
    '480p',
    '720p',
    '1080p',
    '1440p',
    '2160p',
] as const;

/**
 * جودة الفيديو الافتراضية
 * Default video quality
 */
export const DEFAULT_VIDEO_QUALITY: VideoQuality = '480p';

/**
 * أوصاف جودات الفيديو
 * Video quality descriptions
 */
export const VIDEO_QUALITY_DESCRIPTIONS: Record<VideoQuality, string> = {
    '144p': 'جودة منخفضة جداً - Low Quality',
    '240p': 'جودة منخفضة - Low Quality',
    '360p': 'جودة متوسطة - Medium Quality',
    '480p': 'جودة متوسطة عالية - Medium High Quality',
    '720p': 'جودة عالية - High Quality',
    '1080p': 'جودة عالية جداً - Full HD',
    '1440p': 'جودة فائقة - Quad HD',
    '2160p': 'جودة 4K - Ultra HD'
} as const;

/**
 * دقة الفيديو بالبكسل
 * Video resolution in pixels
 */
export const VIDEO_RESOLUTIONS: Record<VideoQuality, { width: number; height: number }> = {
    '144p': { width: 256, height: 144 },
    '240p': { width: 426, height: 240 },
    '360p': { width: 640, height: 360 },
    '480p': { width: 854, height: 480 },
    '720p': { width: 1280, height: 720 },
    '1080p': { width: 1920, height: 1080 },
    '1440p': { width: 2560, height: 1440 },
    '2160p': { width: 3840, height: 2160 }
} as const;

/**
 * معدل البت التقريبي لكل جودة
 * Approximate bitrate for each quality
 */
export const VIDEO_BITRATES: Record<VideoQuality, number> = {
    '144p': 80, // kbps
    '240p': 300, // kbps
    '360p': 700, // kbps
    '480p': 1500, // kbps
    '720p': 2500, // kbps
    '1080p': 4500, // kbps
    '1440p': 9000, // kbps
    '2160p': 20000 // kbps
} as const;

/**
 * استهلاك البيانات التقريبي لكل ساعة
 * Approximate data consumption per hour
 */
export const DATA_CONSUMPTION_PER_HOUR: Record<VideoQuality, string> = {
    '144p': '36 MB',
    '240p': '135 MB',
    '360p': '315 MB',
    '480p': '675 MB',
    '720p': '1.125 GB',
    '1080p': '2.025 GB',
    '1440p': '4.05 GB',
    '2160p': '9 GB'
} as const;

/**
 * متطلبات الحد الأدنى لسرعة الإنترنت
 * Minimum internet speed requirements
 */
export const MIN_INTERNET_SPEED: Record<VideoQuality, string> = {
    '144p': '0.5 Mbps',
    '240p': '1 Mbps',
    '360p': '2 Mbps',
    '480p': '3 Mbps',
    '720p': '5 Mbps',
    '1080p': '8 Mbps',
    '1440p': '16 Mbps',
    '2160p': '35 Mbps'
} as const;

/**
 * أولوية جودات الفيديو للاختيار التلقائي
 * Video quality priority for auto-selection
 */
export const VIDEO_QUALITY_PRIORITY: Record<VideoQuality, number> = {
    '144p': 1,
    '240p': 2,
    '360p': 3,
    '480p': 4,
    '720p': 5,
    '1080p': 6,
    '1440p': 7,
    '2160p': 8
} as const;



