/**
 * مدقق الإعدادات الأساسي
 * Basic settings validator
 *
 * هذا الملف يجمع جميع مدققات الإعدادات الأساسية من الملفات المتخصصة
 * This file aggregates all basic settings validators from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ApplicationConfig, ValidationResult } from '@shared/types';
import { SettingsErrorReport } from './settings-config';
import { SettingsValidatorBasicConstraints } from './settings-validator-basic-constraints';
import { SettingsValidatorBasicCore } from './settings-validator-basic-core';

// إعادة تصدير الفئات المتخصصة / Re-export specialized classes
export { SettingsValidatorBasicConstraints } from './settings-validator-basic-constraints';
export { SettingsValidatorBasicCore } from './settings-validator-basic-core';

/**
 * فئة مدقق الإعدادات الأساسي / Basic settings validator class
 */
export class SettingsValidatorBasic {
    private coreValidator: SettingsValidatorBasicCore;
    private constraintsValidator: SettingsValidatorBasicConstraints;
    private validationErrors: SettingsErrorReport[] = [];

    constructor() {
        this.coreValidator = new SettingsValidatorBasicCore();
        this.constraintsValidator = new SettingsValidatorBasicConstraints();
    }

    // دوال التحقق الأساسية / Basic validation functions

    /**
     * التحقق من صحة إعداد واحد / Validate single setting
     */
    public validateSingleSetting<K extends keyof ApplicationConfig>(
        key: K,
        value: ApplicationConfig[K]
    ): ValidationResult {
        return this.coreValidator.validateSingleSetting(key, value);
    }

    /**
     * التحقق من صحة النوع / Validate type
     */
    public validateType<K extends keyof ApplicationConfig>(
        key: K,
        value: ApplicationConfig[K]
    ): ValidationResult {
        return this.coreValidator.validateType(key, value);
    }

    /**
     * التحقق من صحة المفتاح / Validate key
     */
    public isValidKey<K extends keyof ApplicationConfig>(key: K): boolean {
        return this.coreValidator.isValidKey(key);
    }

    /**
     * التحقق من القيود / Validate constraints
     */
    public validateConstraints<K extends keyof ApplicationConfig>(
        key: K,
        value: ApplicationConfig[K]
    ): ValidationResult {
        return this.constraintsValidator.validateConstraints(key, value);
    }

    /**
     * التحقق من قيود الأرقام / Validate numeric constraints
     */
    public validateNumericConstraints(
        value: number,
        min?: number,
        max?: number,
        fieldName?: string
    ): ValidationResult {
        return this.constraintsValidator.validateNumericConstraints(value, min, max, fieldName);
    }

    /**
     * تنظيف الأخطاء / Clear errors
     */
    public clearErrors(): void {
        this.validationErrors = [];
    }

    /**
     * الحصول على الأخطاء / Get errors
     */
    public getErrors(): SettingsErrorReport[] {
        return [...this.validationErrors];
    }
}
