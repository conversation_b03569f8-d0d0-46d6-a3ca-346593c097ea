/**
 * سكريبت بناء التطبيق
 * Application build script
 * 
 * هذا الملف يحتوي على منطق بناء التطبيق للإنتاج
 * This file contains the application build logic for production
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 * 
 * @requires child_process لتشغيل الأوامر
 * @requires fs للتعامل مع الملفات
 * @requires path للتعامل مع المسارات
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * ألوان الطرفية للرسائل
 * Terminal colors for messages
 */
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

/**
 * طباعة رسالة ملونة
 * Prints colored message
 * 
 * @param {string} message - نص الرسالة
 * @param {string} color - لون الرسالة
 * @returns {void}
 */
function printMessage(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * التحقق من وجود Node.js و npm
 * Checks for Node.js and npm availability
 * 
 * @returns {Promise<boolean>} نتيجة التحقق
 */
function checkPrerequisites() {
    return new Promise((resolve) => {
        exec('node --version && npm --version', (error, stdout) => {
            if (error) {
                printMessage('❌ خطأ: Node.js أو npm غير مثبت', 'red');
                printMessage('يرجى تثبيت Node.js من: https://nodejs.org/', 'yellow');
                resolve(false);
            } else {
                printMessage('✅ Node.js و npm متوفران', 'green');
                printMessage(`الإصدارات: ${stdout.trim()}`, 'cyan');
                resolve(true);
            }
        });
    });
}

/**
 * تثبيت التبعيات
 * Installs dependencies
 * 
 * @returns {Promise<boolean>} نتيجة التثبيت
 */
function installDependencies() {
    return new Promise((resolve) => {
        printMessage('📦 جاري تثبيت التبعيات...', 'blue');
        
        const npmInstall = spawn('npm', ['install'], {
            stdio: 'inherit',
            shell: true
        });

        npmInstall.on('close', (code) => {
            if (code === 0) {
                printMessage('✅ تم تثبيت التبعيات بنجاح', 'green');
                resolve(true);
            } else {
                printMessage('❌ فشل في تثبيت التبعيات', 'red');
                resolve(false);
            }
        });

        npmInstall.on('error', (error) => {
            printMessage(`❌ خطأ في تثبيت التبعيات: ${error.message}`, 'red');
            resolve(false);
        });
    });
}

/**
 * تشغيل اختبارات الكود
 * Runs code tests
 * 
 * @returns {Promise<boolean>} نتيجة الاختبارات
 */
function runTests() {
    return new Promise((resolve) => {
        printMessage('🧪 جاري تشغيل الاختبارات...', 'blue');
        
        // التحقق من وجود ملفات اختبار
        const testDir = path.join(__dirname, '../src/tests');
        if (!fs.existsSync(testDir)) {
            printMessage('⚠️ لا توجد اختبارات للتشغيل', 'yellow');
            resolve(true);
            return;
        }

        const npmTest = spawn('npm', ['test'], {
            stdio: 'inherit',
            shell: true
        });

        npmTest.on('close', (code) => {
            if (code === 0) {
                printMessage('✅ جميع الاختبارات نجحت', 'green');
                resolve(true);
            } else {
                printMessage('❌ بعض الاختبارات فشلت', 'red');
                resolve(false);
            }
        });

        npmTest.on('error', (error) => {
            printMessage(`❌ خطأ في تشغيل الاختبارات: ${error.message}`, 'red');
            resolve(false);
        });
    });
}

/**
 * بناء التطبيق للإنتاج
 * Builds application for production
 * 
 * @param {string} platform - المنصة المستهدفة
 * @returns {Promise<boolean>} نتيجة البناء
 */
function buildApplication(platform = 'win') {
    return new Promise((resolve) => {
        printMessage(`🔨 جاري بناء التطبيق للمنصة: ${platform}`, 'blue');
        
        const buildCommand = platform === 'portable' ? 'build-portable' : `build-${platform}`;
        
        const npmBuild = spawn('npm', ['run', buildCommand], {
            stdio: 'inherit',
            shell: true
        });

        npmBuild.on('close', (code) => {
            if (code === 0) {
                printMessage('✅ تم بناء التطبيق بنجاح', 'green');
                printMessage('📁 ملفات التطبيق متوفرة في مجلد: dist/', 'cyan');
                resolve(true);
            } else {
                printMessage('❌ فشل في بناء التطبيق', 'red');
                resolve(false);
            }
        });

        npmBuild.on('error', (error) => {
            printMessage(`❌ خطأ في بناء التطبيق: ${error.message}`, 'red');
            resolve(false);
        });
    });
}

/**
 * تنظيف ملفات البناء السابقة
 * Cleans previous build files
 * 
 * @returns {void}
 */
function cleanBuildFiles() {
    const distDir = path.join(__dirname, '../dist');
    
    if (fs.existsSync(distDir)) {
        printMessage('🧹 جاري تنظيف ملفات البناء السابقة...', 'yellow');
        fs.rmSync(distDir, { recursive: true, force: true });
        printMessage('✅ تم تنظيف ملفات البناء السابقة', 'green');
    }
}

/**
 * التحقق من الملفات المطلوبة
 * Checks for required files
 * 
 * @returns {boolean} نتيجة التحقق
 */
function checkRequiredFiles() {
    const requiredFiles = [
        'package.json',
        'src/main/main.js',
        'src/preload/youtube-preload.js',
        'src/preload/settings-preload.js',
        'src/renderer/settings.html',
        'src/renderer/settings.css',
        'src/renderer/settings.js'
    ];

    let allFilesExist = true;

    printMessage('📋 جاري التحقق من الملفات المطلوبة...', 'blue');

    for (const file of requiredFiles) {
        const filePath = path.join(__dirname, '..', file);
        if (!fs.existsSync(filePath)) {
            printMessage(`❌ ملف مفقود: ${file}`, 'red');
            allFilesExist = false;
        } else {
            printMessage(`✅ ${file}`, 'green');
        }
    }

    return allFilesExist;
}

/**
 * عرض معلومات البناء
 * Shows build information
 * 
 * @returns {void}
 */
function showBuildInfo() {
    const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '../package.json'), 'utf8'));
    
    printMessage('', 'reset');
    printMessage('🚀 YouTube Dark CyberX - سكريبت البناء', 'cyan');
    printMessage('='.repeat(50), 'cyan');
    printMessage(`📦 اسم التطبيق: ${packageJson.name}`, 'blue');
    printMessage(`🔢 الإصدار: ${packageJson.version}`, 'blue');
    printMessage(`👨‍💻 المطور: ${packageJson.author}`, 'blue');
    printMessage(`📄 الترخيص: ${packageJson.license}`, 'blue');
    printMessage('='.repeat(50), 'cyan');
    printMessage('', 'reset');
}

/**
 * الدالة الرئيسية للبناء
 * Main build function
 * 
 * @returns {Promise<void>}
 */
async function main() {
    showBuildInfo();

    // التحقق من المتطلبات الأساسية
    const prerequisitesOk = await checkPrerequisites();
    if (!prerequisitesOk) {
        process.exit(1);
    }

    // التحقق من الملفات المطلوبة
    const filesOk = checkRequiredFiles();
    if (!filesOk) {
        printMessage('❌ بعض الملفات المطلوبة مفقودة', 'red');
        process.exit(1);
    }

    // تنظيف ملفات البناء السابقة
    cleanBuildFiles();

    // تثبيت التبعيات
    const dependenciesOk = await installDependencies();
    if (!dependenciesOk) {
        process.exit(1);
    }

    // تشغيل الاختبارات
    const testsOk = await runTests();
    if (!testsOk) {
        printMessage('⚠️ تحذير: بعض الاختبارات فشلت، لكن البناء سيستمر', 'yellow');
    }

    // بناء التطبيق
    const platform = process.argv[2] || 'portable';
    const buildOk = await buildApplication(platform);
    
    if (buildOk) {
        printMessage('', 'reset');
        printMessage('🎉 تم بناء التطبيق بنجاح!', 'green');
        printMessage('📁 يمكنك العثور على الملفات في مجلد: dist/', 'cyan');
        printMessage('', 'reset');
    } else {
        printMessage('❌ فشل في بناء التطبيق', 'red');
        process.exit(1);
    }
}

// تشغيل السكريبت
if (require.main === module) {
    main().catch((error) => {
        printMessage(`❌ خطأ غير متوقع: ${error.message}`, 'red');
        process.exit(1);
    });
}

module.exports = {
    checkPrerequisites,
    installDependencies,
    runTests,
    buildApplication,
    cleanBuildFiles,
    checkRequiredFiles
};
