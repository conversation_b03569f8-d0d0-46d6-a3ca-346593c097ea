/**
 * مراقبة الوضع المظلم - أدوات الأحداث الأساسية الجوهرية
 * Dark mode monitoring - Basic core events utilities core
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */


/**
 * فئة أدوات الأحداث الأساسية الجوهرية لمراقبة الوضع المظلم
 * Basic core events utilities core for dark mode monitoring
 */
export class DarkModeObserverMonitoringEventsCoreUtilsBasicCore {

    /**
     * إنشاء مستمع أحداث محسن - تفويض للرئيسي
     * Create optimized event listener - Delegate to main
     */
    public static createOptimizedEventListener(
        eventType: string,
        callback: () => void,
        delay: number = 100
    ): EventListener {
        return DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMain.createOptimizedEventListener(eventType, callback, delay);
    }

    /**
     * إنشاء مستمع أحداث مع تحكم في التكرار - تفويض للرئيسي
     * Create throttled event listener - Delegate to main
     */
    public static createThrottledEventListener(
        callback: () => void,
        delay: number = 250
    ): EventListener {
        return DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMain.createThrottledEventListener(callback, delay);
    }

    /**
     * فحص دعم الأحداث - تفويض للرئيسي
     * Check event support - Delegate to main
     */
    public static checkEventSupport(eventType: string): boolean {
        return DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMain.checkEventSupport(eventType);
    }

    /**
     * إنشاء حدث مخصص - تفويض للرئيسي
     * Create custom event - Delegate to main
     */
    public static createCustomEvent(eventType: string, detail?: any): CustomEvent {
        return DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMain.createCustomEvent(eventType, detail);
    }

    /**
     * إرسال حدث مخصص - تفويض للرئيسي
     * Dispatch custom event - Delegate to main
     */
    public static dispatchCustomEvent(eventType: string, detail?: any, target: EventTarget = window): boolean {
        return DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMain.dispatchCustomEvent(eventType, detail, target);
    }

    /**
     * تنظيف مستمعي الأحداث - تفويض للرئيسي
     * Cleanup event listeners - Delegate to main
     */
    public static cleanupEventListeners(listeners: Map<string, EventListener[]>): void {
        DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMain.cleanupEventListeners(listeners);
    }

    /**
     * فحص حالة الأحداث - تفويض للرئيسي
     * Check events state - Delegate to main
     */
    public static checkEventsState(): {
        windowListeners: number;
        documentListeners: number;
        customEvents: string[];
    } {
        return DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMain.checkEventsState();
    }

    /**
     * تسجيل معلومات الأحداث - تفويض للرئيسي
     * Log events information - Delegate to main
     */
    public static logEventsInfo(): void {
        DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMain.logEventsInfo();
    }

    /**
     * فحص صحة مستمع الأحداث - تفويض للرئيسي
     * Validate event listener - Delegate to main
     */
    public static validateEventListener(listener: EventListener): boolean {
        return DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMain.validateEventListener(listener);
    }

    /**
     * فحص صحة نوع الحدث - تفويض للرئيسي
     * Validate event type - Delegate to main
     */
    public static validateEventType(eventType: string): boolean {
        return DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMain.validateEventType(eventType);
    }

    /**
     * إنشاء معرف فريد للحدث - تفويض للرئيسي
     * Create unique event identifier - Delegate to main
     */
    public static createEventId(eventType: string, target?: string): string {
        return DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMain.createEventId(eventType, target);
    }

    /**
     * فحص ما إذا كان الحدث مدعوم في المتصفح - تفويض للرئيسي
     * Check if event is supported in browser - Delegate to main
     */
    public static isBrowserEventSupported(eventType: string): boolean {
        return DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMain.isBrowserEventSupported(eventType);
    }

    /**
     * إنشاء مستمع أحداث آمن - تفويض للرئيسي
     * Create safe event listener - Delegate to main
     */
    public static createSafeEventListener(callback: () => void): EventListener {
        return DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMain.createSafeEventListener(callback);
    }

    /**
     * فحص ما إذا كان العنصر المستهدف صالح - تفويض للرئيسي
     * Check if target element is valid - Delegate to main
     */
    public static isValidEventTarget(target: EventTarget | null): boolean {
        return DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMain.isValidEventTarget(target);
    }

    /**
     * إنشاء تقرير حالة الأحداث - تفويض للرئيسي
     * Create events status report - Delegate to main
     */
    public static createEventsStatusReport(): {
        timestamp: number;
        supportedEvents: string[];
        customEventsCount: number;
        browserSupport: boolean;
    } {
        return DarkModeObserverMonitoringEventsCoreUtilsBasicCoreMain.createEventsStatusReport();
    }
}
