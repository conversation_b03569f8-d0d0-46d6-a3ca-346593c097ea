/**
 * عمليات أنماط الوضع المظلم
 * Dark mode styles operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DarkModeConfig } from './dark-mode-config';
import { 
    DARK_MODE_BASE_STYLES,
    DARK_MODE_ADVANCED_STYLES,
    DARK_MODE_PLAYER_STYLES,
    DARK_MODE_ENHANCEMENT_STYLES
} from './dark-mode-styles-css-constants';

/**
 * فئة عمليات أنماط الوضع المظلم
 * Dark mode styles operations class
 */
export class DarkModeStylesOperations {
    private readonly config: DarkModeConfig;

    constructor(config: DarkModeConfig) {
        this.config = config;
    }

    /** إنشاء عنصر الأنماط / Create style element */
    public createStyleElement(): HTMLStyleElement {
        const styleElement = document.createElement('style');
        styleElement.id = 'dark-mode-styles';
        styleElement.type = 'text/css';
        return styleElement;
    }

    /** إضافة عنصر الأنماط للصفحة / Add style element to page */
    public addStyleElementToPage(styleElement: HTMLStyleElement): boolean {
        try {
            const head = document.head || document.getElementsByTagName('head')[0];
            if (head) {
                head.appendChild(styleElement);
                return true;
            }
            return false;
        } catch (error) {
            console.error('خطأ في إضافة عنصر الأنماط:', error);
            return false;
        }
    }

    /** إزالة عنصر الأنماط من الصفحة / Remove style element from page */
    public removeStyleElementFromPage(): boolean {
        try {
            const existingStyle = document.getElementById('dark-mode-styles');
            if (existingStyle) {
                existingStyle.remove();
                return true;
            }
            return false;
        } catch (error) {
            console.error('خطأ في إزالة عنصر الأنماط:', error);
            return false;
        }
    }

    /** بناء CSS كامل / Build complete CSS */
    public buildCompleteCSS(): string {
        let css = '';

        // الأنماط الأساسية
        if (this.config.enableBasicStyles) {
            css += DARK_MODE_BASE_STYLES;
        }

        // الأنماط المتقدمة
        if (this.config.enableAdvancedStyles) {
            css += DARK_MODE_ADVANCED_STYLES;
        }

        // أنماط المشغل
        if (this.config.enablePlayerStyles) {
            css += DARK_MODE_PLAYER_STYLES;
        }

        // التحسينات الإضافية
        if (this.config.enableEnhancements) {
            css += DARK_MODE_ENHANCEMENT_STYLES;
        }

        // إضافة أنماط مخصصة
        if (this.config.customStyles) {
            css += this.config.customStyles;
        }

        return css;
    }

    /** تطبيق أنماط مخصصة / Apply custom styles */
    public applyCustomStyles(customCSS: string): boolean {
        try {
            const styleElement = document.getElementById('dark-mode-custom-styles') as HTMLStyleElement;
            
            if (styleElement) {
                styleElement.textContent = customCSS;
            } else {
                const newStyleElement = document.createElement('style');
                newStyleElement.id = 'dark-mode-custom-styles';
                newStyleElement.type = 'text/css';
                newStyleElement.textContent = customCSS;
                
                const head = document.head || document.getElementsByTagName('head')[0];
                if (head) {
                    head.appendChild(newStyleElement);
                }
            }
            
            return true;
        } catch (error) {
            console.error('خطأ في تطبيق الأنماط المخصصة:', error);
            return false;
        }
    }

    /** إزالة الأنماط المخصصة / Remove custom styles */
    public removeCustomStyles(): boolean {
        try {
            const customStyleElement = document.getElementById('dark-mode-custom-styles');
            if (customStyleElement) {
                customStyleElement.remove();
                return true;
            }
            return false;
        } catch (error) {
            console.error('خطأ في إزالة الأنماط المخصصة:', error);
            return false;
        }
    }

    /** تحديث الأنماط / Update styles */
    public updateStyles(newCSS: string): boolean {
        try {
            const styleElement = document.getElementById('dark-mode-styles') as HTMLStyleElement;
            if (styleElement) {
                styleElement.textContent = newCSS;
                return true;
            }
            return false;
        } catch (error) {
            console.error('خطأ في تحديث الأنماط:', error);
            return false;
        }
    }

    /** التحقق من وجود الأنماط / Check if styles exist */
    public stylesExist(): boolean {
        return document.getElementById('dark-mode-styles') !== null;
    }

    /** الحصول على الأنماط الحالية / Get current styles */
    public getCurrentStyles(): string | null {
        const styleElement = document.getElementById('dark-mode-styles') as HTMLStyleElement;
        return styleElement ? styleElement.textContent : null;
    }

    /** تطبيق فئة CSS على الجسم / Apply CSS class to body */
    public applyBodyClass(className: string): boolean {
        try {
            if (document.body) {
                document.body.classList.add(className);
                return true;
            }
            return false;
        } catch (error) {
            console.error('خطأ في تطبيق فئة CSS:', error);
            return false;
        }
    }

    /** إزالة فئة CSS من الجسم / Remove CSS class from body */
    public removeBodyClass(className: string): boolean {
        try {
            if (document.body) {
                document.body.classList.remove(className);
                return true;
            }
            return false;
        } catch (error) {
            console.error('خطأ في إزالة فئة CSS:', error);
            return false;
        }
    }

    /** تطبيق أنماط مؤقتة / Apply temporary styles */
    public applyTemporaryStyles(css: string, duration: number): void {
        const tempStyleElement = document.createElement('style');
        tempStyleElement.id = 'dark-mode-temp-styles';
        tempStyleElement.type = 'text/css';
        tempStyleElement.textContent = css;

        const head = document.head || document.getElementsByTagName('head')[0];
        if (head) {
            head.appendChild(tempStyleElement);

            // إزالة الأنماط المؤقتة بعد المدة المحددة
            setTimeout(() => {
                if (tempStyleElement.parentNode) {
                    tempStyleElement.parentNode.removeChild(tempStyleElement);
                }
            }, duration);
        }
    }

    /** تطبيق أنماط متحركة / Apply animated styles */
    public applyAnimatedStyles(fromCSS: string, toCSS: string, duration: number): void {
        const animationStyleElement = document.createElement('style');
        animationStyleElement.id = 'dark-mode-animation-styles';
        animationStyleElement.type = 'text/css';

        const animationCSS = `
            @keyframes darkModeTransition {
                from { ${fromCSS} }
                to { ${toCSS} }
            }
            
            .dark-mode-transition {
                animation: darkModeTransition ${duration}ms ease-in-out;
            }
        `;

        animationStyleElement.textContent = animationCSS;

        const head = document.head || document.getElementsByTagName('head')[0];
        if (head) {
            head.appendChild(animationStyleElement);

            // تطبيق الفئة المتحركة
            if (document.body) {
                document.body.classList.add('dark-mode-transition');

                // إزالة الفئة والأنماط بعد انتهاء الحركة
                setTimeout(() => {
                    if (document.body) {
                        document.body.classList.remove('dark-mode-transition');
                    }
                    if (animationStyleElement.parentNode) {
                        animationStyleElement.parentNode.removeChild(animationStyleElement);
                    }
                }, duration);
            }
        }
    }

    /** تنظيف جميع الأنماط / Clean up all styles */
    public cleanupAllStyles(): boolean {
        try {
            const styleIds = [
                'dark-mode-styles',
                'dark-mode-custom-styles',
                'dark-mode-temp-styles',
                'dark-mode-animation-styles'
            ];

            let success = true;
            for (const id of styleIds) {
                const element = document.getElementById(id);
                if (element) {
                    try {
                        element.remove();
                    } catch (error) {
                        console.error(`خطأ في إزالة العنصر ${id}:`, error);
                        success = false;
                    }
                }
            }

            // إزالة فئات CSS من الجسم
            const bodyClasses = [
                'dark-mode',
                'dark-mode-transition',
                'high-contrast',
                'blue-light-filter',
                'reading-mode',
                'focus-mode',
                'performance-mode',
                'battery-saving',
                'accessibility-mode',
                'dnd-mode',
                'color-blind-friendly',
                'advanced-night'
            ];

            if (document.body) {
                for (const className of bodyClasses) {
                    document.body.classList.remove(className);
                }
            }

            return success;
        } catch (error) {
            console.error('خطأ في تنظيف الأنماط:', error);
            return false;
        }
    }
}
