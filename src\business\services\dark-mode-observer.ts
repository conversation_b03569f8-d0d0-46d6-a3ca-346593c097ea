/**
 * مراقب تغييرات الوضع المظلم
 * Dark mode changes observer
 *
 * هذا الملف يحتوي على منطق مراقبة التغييرات وتطبيق الوضع المظلم تلقائياً
 * This file contains logic for observing changes and applying dark mode automatically
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// إعادة تصدير المكونات المتخصصة
// Re-export specialized components
export * from './dark-mode-observer-monitoring';
export * from './dark-mode-observer-operations';

import {
    DarkModeConfig
} from './dark-mode-config';
import { DarkModeObserverMonitoring } from './dark-mode-observer-monitoring';
import { DarkModeObserverOperations } from './dark-mode-observer-operations';
import { DarkModeStylesApplicator } from './dark-mode-styles';

/**
 * فئة مراقب تغييرات الوضع المظلم
 * Dark mode changes observer class
 */
export class DarkModeObserver {
    private readonly config: DarkModeConfig;
    private readonly stylesApplicator: DarkModeStylesApplicator;
    private readonly operations: DarkModeObserverOperations;
    private readonly monitoring: DarkModeObserverMonitoring;

    /**
     * منشئ مراقب التغييرات
     * Changes observer constructor
     *
     * @param config - تكوين الوضع المظلم
     * @param stylesApplicator - مطبق الأنماط
     */
    constructor(config: DarkModeConfig, stylesApplicator: DarkModeStylesApplicator) {
        this.config = config;
        this.stylesApplicator = stylesApplicator;

        // إنشاء المكونات المتخصصة
        // Create specialized components
        this.operations = new DarkModeObserverOperations(config, stylesApplicator);
        this.monitoring = new DarkModeObserverMonitoring(config, this.operations);
    }

    /**
     * بدء مراقبة التغييرات
     * Start observing changes
     *
     * @returns true إذا تم البدء بنجاح
     */
    public startObserving(): boolean {
        return this.monitoring.startObserving();
    }

    /**
     * إيقاف مراقبة التغييرات
     * Stop observing changes
     *
     * @returns true إذا تم الإيقاف بنجاح
     */
    public stopObserving(): boolean {
        return this.monitoring.stopObserving();
    }

    /**
     * تطبيق الوضع المظلم على العنصر
     * Apply dark mode to element
     *
     * @param element - العنصر المراد تطبيق الوضع المظلم عليه
     * @returns true إذا تم التطبيق بنجاح
     */
    public applyDarkModeToElement(element: Element): boolean {
        return this.operations.applyDarkModeToElement(element);
    }

    /**
     * إزالة الوضع المظلم من العنصر
     * Remove dark mode from element
     *
     * @param element - العنصر المراد إزالة الوضع المظلم منه
     * @returns true إذا تمت الإزالة بنجاح
     */
    public removeDarkModeFromElement(element: Element): boolean {
        return this.operations.removeDarkModeFromElement(element);
    }

    /**
     * فحص ما إذا كان العنصر يحتاج إلى تطبيق الوضع المظلم
     * Check if element needs dark mode application
     *
     * @param element - العنصر المراد فحصه
     * @returns true إذا كان العنصر يحتاج إلى تطبيق الوضع المظلم
     */
    public needsDarkModeApplication(element: Element): boolean {
        return this.operations.needsDarkModeApplication(element);
    }

    /**
     * فحص حالة المراقبة
     * Check observing status
     *
     * @returns true إذا كانت المراقبة نشطة
     */
    public isObservingActive(): boolean {
        return this.monitoring.isObservingActive();
    }

    /**
     * إعادة تشغيل المراقبة
     * Restart observation
     *
     * @returns true إذا تمت إعادة التشغيل بنجاح
     */
    public restartObserving(): boolean {
        return this.monitoring.restartObserving();
    }

    /**
     * تنظيف الموارد
     * Cleanup resources
     */
    public cleanup(): void {
        this.monitoring.cleanup();
    }
}
