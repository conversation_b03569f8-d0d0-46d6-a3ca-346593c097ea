/**
 * العمليات المتقدمة لكشف جودة الفيديو
 * Advanced video quality detection operations
 *
 * هذا الملف يجمع جميع العمليات المتقدمة من الملفات المتخصصة
 * This file aggregates all advanced operations from specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { VideoQuality } from '@shared/types';
import { VideoQualityConfig } from './video-quality-config';
import { VideoQualityDetectorCore } from './video-quality-detector-core';

// استيراد الفئات المتخصصة / Import specialized classes
import {
    AnalysisReport,
    DetectionStatistics,
    VideoQualityDetectorAdvancedAnalysis
} from './video-quality-detector-advanced-analysis';
import { VideoQualityDetectorAdvancedOperations } from './video-quality-detector-advanced-operations';

// إعادة تصدير الأنواع / Re-export types
export { AnalysisReport, DetectionStatistics };

/**
 * فئة العمليات المتقدمة لكشف جودة الفيديو
 * Advanced video quality detection operations class
 */
export class VideoQualityDetectorAdvanced {
    private readonly operations: VideoQualityDetectorAdvancedOperations;
    private readonly analysis: VideoQualityDetectorAdvancedAnalysis;

    constructor(config: VideoQualityConfig, coreDetector: VideoQualityDetectorCore) {
        this.operations = new VideoQualityDetectorAdvancedOperations(config, coreDetector);
        this.analysis = new VideoQualityDetectorAdvancedAnalysis(config);
    }

    /** اكتشاف متقدم للجودة / Advanced quality detection */
    public async detectQualityAdvanced(): Promise<VideoQuality | null> {
        const analysisReport = await this.analysis.performComprehensiveAnalysis();
        return analysisReport.currentQuality;
    }

    /** كشف الجودة من عنصر الفيديو / Detect quality from video element */
    public detectFromVideoElement(): VideoQuality | null {
        return this.operations.detectFromVideoElement();
    }

    /** كشف الجودة من واجهة المستخدم / Detect quality from UI */
    public detectFromUI(): VideoQuality | null {
        return this.operations.detectFromUI();
    }

    /** كشف الجودة من البيانات الوصفية / Detect quality from metadata */
    public detectFromMetadata(): VideoQuality | null {
        return this.operations.detectFromMetadata();
    }

    /** كشف الجودة من الشبكة / Detect quality from network */
    public detectFromNetwork(): VideoQuality | null {
        return this.operations.detectFromNetwork();
    }

    /** التحقق من صحة الجودة / Validate quality */
    public validateDetectedQuality(quality: VideoQuality): boolean {
        return this.operations.validateDetectedQuality(quality);
    }

    /** الحصول على الجودات المتاحة / Get available qualities */
    public getAvailableQualities(): VideoQuality[] {
        return this.operations.getAvailableQualities();
    }

    /** إجراء تحليل شامل / Perform comprehensive analysis */
    public async performComprehensiveAnalysis(): Promise<AnalysisReport> {
        return this.analysis.performComprehensiveAnalysis();
    }

    /** الحصول على إحصائيات الكشف / Get detection statistics */
    public getDetectionStatistics(): DetectionStatistics {
        return this.analysis.getDetectionStatistics();
    }

    /** إعادة تعيين الإحصائيات / Reset statistics */
    public resetStatistics(): void {
        this.analysis.resetStatistics();
    }
}
