/**
 * أنماط منصات iframe المختلفة في الوضع المظلم
 * Different iframe platform styles for dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * فئة أنماط منصات iframe
 * iframe platform styles class
 */
export class DarkModeObserverOperationsAdvancedElementsGenericIframeCoreStylesPlatforms {

    /** تطبيق أنماط YouTube / Apply YouTube styles */
    public static applyYouTubeStyles(element: HTMLIFrameElement): void {
        element.style.filter = 'brightness(0.85) contrast(1.2)';
        element.style.borderColor = '#ff0000';
        element.style.boxShadow = '0 4px 12px rgba(255, 0, 0, 0.2)';
    }

    /** تطبيق أنماط Vimeo / Apply Vimeo styles */
    public static applyVimeoStyles(element: HTMLIFrameElement): void {
        element.style.filter = 'brightness(0.9) contrast(1.1)';
        element.style.borderColor = '#1ab7ea';
        element.style.boxShadow = '0 4px 12px rgba(26, 183, 234, 0.2)';
    }

    /** تطبيق أنماط Facebook / Apply Facebook styles */
    public static applyFacebookStyles(element: HTMLIFrameElement): void {
        element.style.filter = 'brightness(0.8) contrast(1.3)';
        element.style.borderColor = '#1877f2';
        element.style.boxShadow = '0 4px 12px rgba(24, 119, 242, 0.2)';
    }

    /** تطبيق أنماط Twitter / Apply Twitter styles */
    public static applyTwitterStyles(element: HTMLIFrameElement): void {
        element.style.filter = 'brightness(0.85) contrast(1.2)';
        element.style.borderColor = '#1da1f2';
        element.style.boxShadow = '0 4px 12px rgba(29, 161, 242, 0.2)';
    }

    /** تطبيق أنماط Instagram / Apply Instagram styles */
    public static applyInstagramStyles(element: HTMLIFrameElement): void {
        element.style.filter = 'brightness(0.9) contrast(1.1)';
        element.style.borderColor = '#e4405f';
        element.style.boxShadow = '0 4px 12px rgba(228, 64, 95, 0.2)';
    }

    /** تطبيق أنماط الخرائط / Apply maps styles */
    public static applyMapsStyles(element: HTMLIFrameElement): void {
        element.style.filter = 'brightness(0.7) contrast(1.4) hue-rotate(180deg)';
        element.style.borderColor = '#34a853';
        element.style.boxShadow = '0 4px 12px rgba(52, 168, 83, 0.2)';
    }

    /** تطبيق أنماط الفيديو / Apply video styles */
    public static applyVideoStyles(element: HTMLIFrameElement): void {
        element.style.filter = 'brightness(0.85) contrast(1.2)';
        element.style.borderColor = '#ff6b6b';
        element.style.boxShadow = '0 4px 12px rgba(255, 107, 107, 0.2)';
    }

    /** تطبيق أنماط الإعلانات / Apply advertisement styles */
    public static applyAdvertisementStyles(element: HTMLIFrameElement): void {
        // إخفاء الإعلانات أو تطبيق فلتر قوي
        element.style.opacity = '0.3';
        element.style.filter = 'blur(2px) grayscale(1)';
        element.style.pointerEvents = 'none';
        
        // إضافة تسمية
        this.addAdvertisementLabel(element);
    }

    /** تطبيق أنماط عامة / Apply generic styles */
    public static applyGenericStyles(element: HTMLIFrameElement): void {
        element.style.filter = 'brightness(0.9) contrast(1.1)';
        element.style.borderColor = 'rgba(255, 255, 255, 0.3)';
    }

    /** إضافة تسمية للإعلان / Add advertisement label */
    public static addAdvertisementLabel(element: HTMLIFrameElement): void {
        const label = document.createElement('div');
        label.textContent = 'إعلان';
        label.style.cssText = `
            position: absolute;
            top: 4px;
            right: 4px;
            background: rgba(255, 0, 0, 0.8);
            color: white;
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 3px;
            z-index: 1000;
            pointer-events: none;
        `;
        
        // إضافة التسمية بجانب iframe
        if (element.parentElement) {
            element.parentElement.style.position = 'relative';
            element.parentElement.appendChild(label);
        }
    }

    /** كشف نوع iframe / Detect iframe type */
    public static detectIframeType(element: HTMLIFrameElement): string {
        const src = element.src || '';
        const className = element.className.toLowerCase();
        
        // فحص المصدر
        if (src.includes('youtube.com') || src.includes('youtu.be')) {
            return 'youtube';
        }
        
        if (src.includes('vimeo.com')) {
            return 'vimeo';
        }
        
        if (src.includes('facebook.com')) {
            return 'facebook';
        }
        
        if (src.includes('twitter.com') || src.includes('x.com')) {
            return 'twitter';
        }
        
        if (src.includes('instagram.com')) {
            return 'instagram';
        }
        
        if (src.includes('maps.google.com') || src.includes('google.com/maps')) {
            return 'maps';
        }
        
        // فحص الفئات
        if (className.includes('video') || className.includes('player')) {
            return 'video';
        }
        
        if (className.includes('map')) {
            return 'map';
        }
        
        if (className.includes('social')) {
            return 'social';
        }
        
        if (className.includes('ad') || className.includes('advertisement')) {
            return 'advertisement';
        }

        return 'generic';
    }

    /** الحصول على نص البديل / Get placeholder text */
    public static getPlaceholderText(type: string): string {
        const textMap: { [key: string]: string } = {
            'youtube': 'فيديو YouTube',
            'vimeo': 'فيديو Vimeo',
            'facebook': 'محتوى Facebook',
            'twitter': 'تغريدة Twitter',
            'instagram': 'منشور Instagram',
            'maps': 'خريطة Google',
            'video': 'مشغل فيديو',
            'advertisement': 'إعلان',
            'generic': 'محتوى مدمج'
        };

        return textMap[type] || textMap['generic'];
    }
}
