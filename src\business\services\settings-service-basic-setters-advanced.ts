/**
 * دوال التعديل المتقدمة لخدمة الإعدادات
 * Advanced setter functions for settings service
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { DEFAULT_APPLICATION_CONFIG } from '@shared/constants';
import { ApplicationConfig } from '@shared/types';
import { ResourceManager } from '@shared/utils/resource-manager';
import Store from 'electron-store';
import { SettingsManagerConfig } from './settings-config';
import { SettingsServiceBasicSettersSimple } from './settings-service-basic-setters-simple';
import { SettingsValidator } from './settings-validator';

/**
 * فئة دوال التعديل المتقدمة لخدمة الإعدادات
 */
export class SettingsServiceBasicSettersAdvanced {
    private readonly simpleSetters: SettingsServiceBasicSettersSimple;

    constructor(
        config: SettingsManagerConfig,
        resourceManager: ResourceManager,
        store: Store<ApplicationConfig>,
        validator: SettingsValidator
    ) {
        this.simpleSetters = new SettingsServiceBasicSettersSimple(config, resourceManager, store, validator);
    }

    /**
     * إعادة تعيين إعداد إلى القيمة الافتراضية / Reset setting to default
     */
    public async resetSettingToDefault<K extends keyof ApplicationConfig>(key: K): Promise<boolean> {
        try {
            const defaultValue = DEFAULT_APPLICATION_CONFIG[key];
            return await this.simpleSetters.setSetting(key, defaultValue);
        } catch (error) {
            console.error(`خطأ في إعادة تعيين الإعداد ${String(key)} للقيمة الافتراضية:`, error);
            return false;
        }
    }

    /**
     * تبديل قيمة إعداد منطقي / Toggle boolean setting
     */
    public async toggleBooleanSetting<K extends keyof ApplicationConfig>(key: K): Promise<boolean> {
        try {
            const currentValue = await this.getCurrentValue(key);
            if (typeof currentValue === 'boolean') {
                return await this.simpleSetters.setSetting(key, !currentValue as ApplicationConfig[K]);
            }
            console.error(`الإعداد ${String(key)} ليس من النوع المنطقي`);
            return false;
        } catch (error) {
            console.error(`خطأ في تبديل الإعداد المنطقي ${String(key)}:`, error);
            return false;
        }
    }

    /**
     * زيادة قيمة إعداد رقمي / Increment numeric setting
     */
    public async incrementNumericSetting<K extends keyof ApplicationConfig>(
        key: K, 
        increment: number = 1
    ): Promise<boolean> {
        try {
            const currentValue = await this.getCurrentValue(key);
            if (typeof currentValue === 'number') {
                const newValue = currentValue + increment;
                return await this.simpleSetters.setSetting(key, newValue as ApplicationConfig[K]);
            }
            console.error(`الإعداد ${String(key)} ليس من النوع الرقمي`);
            return false;
        } catch (error) {
            console.error(`خطأ في زيادة الإعداد الرقمي ${String(key)}:`, error);
            return false;
        }
    }

    /**
     * تقليل قيمة إعداد رقمي / Decrement numeric setting
     */
    public async decrementNumericSetting<K extends keyof ApplicationConfig>(
        key: K, 
        decrement: number = 1
    ): Promise<boolean> {
        try {
            const currentValue = await this.getCurrentValue(key);
            if (typeof currentValue === 'number') {
                const newValue = currentValue - decrement;
                return await this.simpleSetters.setSetting(key, newValue as ApplicationConfig[K]);
            }
            console.error(`الإعداد ${String(key)} ليس من النوع الرقمي`);
            return false;
        } catch (error) {
            console.error(`خطأ في تقليل الإعداد الرقمي ${String(key)}:`, error);
            return false;
        }
    }

    /**
     * إضافة عنصر إلى مصفوفة / Add to array setting
     */
    public async addToArraySetting<K extends keyof ApplicationConfig>(
        key: K, 
        item: any
    ): Promise<boolean> {
        try {
            const currentValue = await this.getCurrentValue(key);
            if (Array.isArray(currentValue)) {
                const newArray = [...currentValue, item];
                return await this.simpleSetters.setSetting(key, newArray as ApplicationConfig[K]);
            }
            console.error(`الإعداد ${String(key)} ليس من نوع المصفوفة`);
            return false;
        } catch (error) {
            console.error(`خطأ في إضافة عنصر للمصفوفة ${String(key)}:`, error);
            return false;
        }
    }

    /**
     * إزالة عنصر من مصفوفة / Remove from array setting
     */
    public async removeFromArraySetting<K extends keyof ApplicationConfig>(
        key: K, 
        item: any
    ): Promise<boolean> {
        try {
            const currentValue = await this.getCurrentValue(key);
            if (Array.isArray(currentValue)) {
                const newArray = currentValue.filter(existingItem => 
                    JSON.stringify(existingItem) !== JSON.stringify(item)
                );
                return await this.simpleSetters.setSetting(key, newArray as ApplicationConfig[K]);
            }
            console.error(`الإعداد ${String(key)} ليس من نوع المصفوفة`);
            return false;
        } catch (error) {
            console.error(`خطأ في إزالة عنصر من المصفوفة ${String(key)}:`, error);
            return false;
        }
    }

    /**
     * الحصول على القيمة الحالية / Get current value
     */
    private async getCurrentValue<K extends keyof ApplicationConfig>(key: K): Promise<ApplicationConfig[K]> {
        // هذه دالة مساعدة للحصول على القيمة الحالية
        // في التطبيق الحقيقي، ستحتاج للوصول إلى store أو استخدام getter
        return DEFAULT_APPLICATION_CONFIG[key];
    }

    /**
     * تنظيف الموارد / Cleanup resources
     */
    public cleanup(): void {
        this.simpleSetters.cleanup();
    }
}
