/**
 * مدير الموارد
 * Resource manager
 *
 * هذا الملف يحتوي على منطق إدارة الموارد ومنع تسريب الذاكرة
 * This file contains resource management logic and memory leak prevention
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// إعادة تصدير المديرين المتخصصين
// Re-export specialized managers
export * from './observer-manager';
export * from './timer-manager';

import { ObserverManager } from './observer-manager';
import { TimerManager } from './timer-manager';

/**
 * مدير الموارد الشامل
 * Comprehensive resource manager
 */
export class ResourceManager {
    private readonly timerManager: TimerManager = new TimerManager();
    private readonly observerManager: ObserverManager = new ObserverManager();

    /**
     * الحصول على مدير المؤقتات
     * Gets the timer manager
     *
     * @returns TimerManager - مدير المؤقتات / Timer manager
     */
    public getTimerManager(): TimerManager {
        return this.timerManager;
    }

    /**
     * الحصول على مدير المراقبات
     * Gets the observer manager
     *
     * @returns ObserverManager - مدير المراقبات / Observer manager
     */
    public getObserverManager(): ObserverManager {
        return this.observerManager;
    }

    /**
     * تنظيف جميع الموارد
     * Clears all resources
     *
     * @example
     * ```typescript
     * resourceManager.clearAll();
     * ```
     */
    public clearAll(): void {
        this.timerManager.clearAll();
        this.observerManager.clearAll();
    }

    /**
     * الحصول على إحصائيات الموارد
     * Gets resource statistics
     *
     * @returns object - إحصائيات الموارد / Resource statistics
     *
     * @example
     * ```typescript
     * const stats = resourceManager.getStats();
     * console.log(`Timers: ${stats.timers}, Observers: ${stats.observers}`);
     * ```
     */
    public getStats(): {
        timers: number;
        intervals: number;
        observers: number;
        eventListeners: number;
        total: number;
    } {
        const timers = this.timerManager.getTimerCount();
        const intervals = this.timerManager.getIntervalCount();
        const observers = this.observerManager.getObserverCount();
        const eventListeners = this.observerManager.getEventListenerCount();

        return {
            timers,
            intervals,
            observers,
            eventListeners,
            total: timers + intervals + observers + eventListeners
        };
    }
}


