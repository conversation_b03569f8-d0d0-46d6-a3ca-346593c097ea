/**
 * مدير الفترات الزمنية
 * Interval manager
 * 
 * هذا الملف يحتوي على منطق إدارة الفترات الزمنية
 * This file contains interval management logic
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * مدير الفترات الزمنية
 * Interval manager class
 */
export class IntervalManager {
    private readonly intervals: Set<NodeJS.Timeout> = new Set();

    /**
     * إنشاء فترة زمنية مُدارة
     * Creates a managed interval
     * 
     * @param callback - دالة الاستدعاء / Callback function
     * @param delay - التأخير بالميلي ثانية / Delay in milliseconds
     * @returns NodeJS.Timeout - معرف الفترة / Interval ID
     * 
     * @example
     * ```typescript
     * const intervalId = intervalManager.createInterval(() => {
     *     console.log('Interval executed');
     * }, 1000);
     * ```
     */
    public createInterval(callback: () => void, delay: number): NodeJS.Timeout {
        const intervalId = setInterval(callback, delay);
        this.intervals.add(intervalId);
        return intervalId;
    }

    /**
     * إلغاء فترة زمنية
     * Cancels an interval
     * 
     * @param intervalId - معرف الفترة / Interval ID
     * 
     * @example
     * ```typescript
     * intervalManager.clearInterval(intervalId);
     * ```
     */
    public clearInterval(intervalId: NodeJS.Timeout): void {
        clearInterval(intervalId);
        this.intervals.delete(intervalId);
    }

    /**
     * تنظيف جميع الفترات الزمنية
     * Clears all intervals
     * 
     * @example
     * ```typescript
     * intervalManager.clearAll();
     * ```
     */
    public clearAll(): void {
        for (const intervalId of this.intervals) {
            clearInterval(intervalId);
        }
        this.intervals.clear();
    }

    /**
     * الحصول على عدد الفترات النشطة
     * Gets the count of active intervals
     * 
     * @returns number - عدد الفترات النشطة / Active interval count
     * 
     * @example
     * ```typescript
     * const count = intervalManager.getIntervalCount();
     * ```
     */
    public getIntervalCount(): number {
        return this.intervals.size;
    }

    /**
     * التحقق من وجود فترة زمنية
     * Checks if an interval exists
     * 
     * @param intervalId - معرف الفترة / Interval ID
     * @returns boolean - هل الفترة موجودة / Interval exists
     * 
     * @example
     * ```typescript
     * const exists = intervalManager.hasInterval(intervalId);
     * ```
     */
    public hasInterval(intervalId: NodeJS.Timeout): boolean {
        return this.intervals.has(intervalId);
    }

    /**
     * إنشاء فترة زمنية محدودة
     * Creates a limited interval
     * 
     * @param callback - دالة الاستدعاء / Callback function
     * @param delay - التأخير بالميلي ثانية / Delay in milliseconds
     * @param maxExecutions - العدد الأقصى للتنفيذ / Maximum execution count
     * @returns NodeJS.Timeout - معرف الفترة / Interval ID
     * 
     * @example
     * ```typescript
     * const intervalId = intervalManager.createLimitedInterval(
     *     () => console.log('Limited execution'),
     *     1000,
     *     5
     * );
     * ```
     */
    public createLimitedInterval(
        callback: () => void,
        delay: number,
        maxExecutions: number
    ): NodeJS.Timeout {
        let executionCount = 0;
        
        const intervalId = setInterval(() => {
            if (executionCount < maxExecutions) {
                callback();
                executionCount++;
                
                if (executionCount >= maxExecutions) {
                    this.clearInterval(intervalId);
                }
            }
        }, delay);
        
        this.intervals.add(intervalId);
        return intervalId;
    }

    /**
     * إنشاء فترة زمنية مع شرط
     * Creates a conditional interval
     * 
     * @param callback - دالة الاستدعاء / Callback function
     * @param delay - التأخير بالميلي ثانية / Delay in milliseconds
     * @param condition - شرط التنفيذ / Execution condition
     * @param stopOnFalse - إيقاف عند عدم تحقق الشرط / Stop when condition is false
     * @returns NodeJS.Timeout - معرف الفترة / Interval ID
     * 
     * @example
     * ```typescript
     * const intervalId = intervalManager.createConditionalInterval(
     *     () => console.log('Conditional execution'),
     *     1000,
     *     () => document.visibilityState === 'visible',
     *     true
     * );
     * ```
     */
    public createConditionalInterval(
        callback: () => void,
        delay: number,
        condition: () => boolean,
        stopOnFalse: boolean = false
    ): NodeJS.Timeout {
        const intervalId = setInterval(() => {
            if (condition()) {
                callback();
            } else if (stopOnFalse) {
                this.clearInterval(intervalId);
            }
        }, delay);
        
        this.intervals.add(intervalId);
        return intervalId;
    }

    /**
     * إنشاء فترة زمنية متدرجة
     * Creates a progressive interval
     * 
     * @param callback - دالة الاستدعاء / Callback function
     * @param initialDelay - التأخير الأولي / Initial delay
     * @param increment - زيادة التأخير / Delay increment
     * @param maxDelay - التأخير الأقصى / Maximum delay
     * @returns NodeJS.Timeout - معرف الفترة / Interval ID
     * 
     * @example
     * ```typescript
     * const intervalId = intervalManager.createProgressiveInterval(
     *     () => console.log('Progressive execution'),
     *     1000,
     *     500,
     *     5000
     * );
     * ```
     */
    public createProgressiveInterval(
        callback: () => void,
        initialDelay: number,
        increment: number,
        maxDelay: number
    ): NodeJS.Timeout {
        let currentDelay = initialDelay;
        
        const scheduleNext = (): NodeJS.Timeout => {
            const intervalId = setTimeout(() => {
                callback();
                this.intervals.delete(intervalId);
                
                currentDelay = Math.min(currentDelay + increment, maxDelay);
                const nextIntervalId = scheduleNext();
                this.intervals.add(nextIntervalId);
            }, currentDelay);
            
            return intervalId;
        };
        
        const intervalId = scheduleNext();
        this.intervals.add(intervalId);
        return intervalId;
    }

    /**
     * إنشاء فترة زمنية مع معالجة الأخطاء
     * Creates an interval with error handling
     * 
     * @param callback - دالة الاستدعاء / Callback function
     * @param delay - التأخير بالميلي ثانية / Delay in milliseconds
     * @param errorHandler - معالج الأخطاء / Error handler
     * @param stopOnError - إيقاف عند حدوث خطأ / Stop on error
     * @returns NodeJS.Timeout - معرف الفترة / Interval ID
     * 
     * @example
     * ```typescript
     * const intervalId = intervalManager.createSafeInterval(
     *     () => riskyOperation(),
     *     1000,
     *     (error) => console.error('Interval error:', error),
     *     false
     * );
     * ```
     */
    public createSafeInterval(
        callback: () => void | Promise<void>,
        delay: number,
        errorHandler?: (error: Error) => void,
        stopOnError: boolean = false
    ): NodeJS.Timeout {
        const intervalId = setInterval(async () => {
            try {
                await callback();
            } catch (error) {
                if (errorHandler) {
                    errorHandler(error as Error);
                }
                
                if (stopOnError) {
                    this.clearInterval(intervalId);
                }
            }
        }, delay);
        
        this.intervals.add(intervalId);
        return intervalId;
    }

    /**
     * إنشاء فترة زمنية مع مهلة زمنية
     * Creates an interval with timeout
     * 
     * @param callback - دالة الاستدعاء / Callback function
     * @param delay - التأخير بالميلي ثانية / Delay in milliseconds
     * @param totalDuration - المدة الإجمالية / Total duration
     * @returns NodeJS.Timeout - معرف الفترة / Interval ID
     * 
     * @example
     * ```typescript
     * const intervalId = intervalManager.createTimedInterval(
     *     () => console.log('Timed execution'),
     *     1000,
     *     10000
     * );
     * ```
     */
    public createTimedInterval(
        callback: () => void,
        delay: number,
        totalDuration: number
    ): NodeJS.Timeout {
        const intervalId = setInterval(callback, delay);
        this.intervals.add(intervalId);
        
        setTimeout(() => {
            this.clearInterval(intervalId);
        }, totalDuration);
        
        return intervalId;
    }
}
