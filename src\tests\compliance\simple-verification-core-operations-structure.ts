/**
 * فحص بنية المشروع
 * Project structure checking operations
 * 
 * هذا الملف يحتوي على عمليات فحص بنية المشروع
 * This file contains project structure checking operations
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import * as fs from 'fs';
import * as path from 'path';
import {
    SimpleVerificationConfig,
    ProjectStructureCheckResult,
    DEFAULT_EVALUATION_CRITERIA
} from './simple-verification-types';

/**
 * فئة فحص بنية المشروع
 * Project structure checking class
 */
export class SimpleVerificationCoreOperationsStructure {

    /**
     * فحص بنية المشروع
     * Check project structure
     */
    public static async checkProjectStructure(
        projectRoot: string,
        config: SimpleVerificationConfig
    ): Promise<ProjectStructureCheckResult> {
        const issues: string[] = [];
        const recommendations: string[] = [];
        const missingDirectories: string[] = [];
        const extraDirectories: string[] = [];
        const organizationIssues: string[] = [];

        try {
            // فحص المجلدات المطلوبة
            const requiredDirectories = [
                'src',
                'src/presentation',
                'src/business',
                'src/data',
                'src/shared',
                'src/infrastructure',
                'tests',
                'docs',
                'config'
            ];

            for (const dir of requiredDirectories) {
                const dirPath = path.join(projectRoot, dir);
                if (!fs.existsSync(dirPath)) {
                    missingDirectories.push(dir);
                    issues.push(`Missing required directory: ${dir}`);
                }
            }

            // فحص المجلدات الإضافية غير المرغوب فيها
            const allowedDirectories = [
                'src', 'tests', 'docs', 'config', 'scripts', 'public',
                'node_modules', '.git', 'dist', 'build', '.vscode'
            ];

            const actualDirectories = fs.readdirSync(projectRoot, { withFileTypes: true })
                .filter(dirent => dirent.isDirectory())
                .map(dirent => dirent.name);

            for (const dir of actualDirectories) {
                if (!allowedDirectories.includes(dir) && !dir.startsWith('.')) {
                    extraDirectories.push(dir);
                    issues.push(`Unexpected directory: ${dir}`);
                }
            }

            // فحص تنظيم الملفات
            const organizationResult = this.checkFileOrganization(projectRoot);
            if (organizationResult.issues.length > 0) {
                organizationIssues.push(...organizationResult.issues);
                issues.push(...organizationResult.issues);
            }

            // إضافة التوصيات
            if (missingDirectories.length > 0) {
                recommendations.push('Create missing required directories according to layered architecture');
                recommendations.push('Follow the prescribed directory structure for better organization');
            }

            if (extraDirectories.length > 0) {
                recommendations.push('Remove or reorganize unexpected directories');
                recommendations.push('Keep only necessary directories in project root');
            }

            if (organizationIssues.length > 0) {
                recommendations.push('Reorganize files according to their functionality');
                recommendations.push('Use proper file naming and directory structure');
            }

            const score = this.calculateStructureScore(
                requiredDirectories.length,
                missingDirectories.length,
                extraDirectories.length,
                organizationIssues.length
            );

            return {
                score,
                issues,
                recommendations,
                missingDirectories,
                extraDirectories,
                organizationIssues,
                requiredDirectories: requiredDirectories.length,
                actualDirectories: actualDirectories.length
            };

        } catch (error) {
            issues.push(`Error during project structure check: ${error}`);
            return {
                score: 0,
                issues,
                recommendations: ['Fix errors and retry project structure check'],
                missingDirectories,
                extraDirectories,
                organizationIssues,
                requiredDirectories: 0,
                actualDirectories: 0
            };
        }
    }

    /**
     * فحص تنظيم الملفات
     * Check file organization
     */
    private static checkFileOrganization(projectRoot: string): { issues: string[] } {
        const issues: string[] = [];

        try {
            // فحص ملفات src
            const srcPath = path.join(projectRoot, 'src');
            if (fs.existsSync(srcPath)) {
                const srcFiles = this.getAllFiles(srcPath);
                
                for (const file of srcFiles) {
                    const relativePath = path.relative(srcPath, file);
                    
                    // فحص أن الملفات في المجلدات الصحيحة
                    if (relativePath.includes('component') && !relativePath.includes('presentation')) {
                        issues.push(`Component file in wrong directory: ${relativePath}`);
                    }
                    
                    if (relativePath.includes('service') && !relativePath.includes('business')) {
                        issues.push(`Service file in wrong directory: ${relativePath}`);
                    }
                    
                    if (relativePath.includes('util') && !relativePath.includes('shared')) {
                        issues.push(`Utility file in wrong directory: ${relativePath}`);
                    }
                }
            }

            // فحص ملفات التكوين
            const configFiles = ['package.json', 'tsconfig.json', 'jest.config.js'];
            for (const configFile of configFiles) {
                const configPath = path.join(projectRoot, configFile);
                if (!fs.existsSync(configPath)) {
                    issues.push(`Missing configuration file: ${configFile}`);
                }
            }

            // فحص ملفات التوثيق
            const docFiles = ['README.md'];
            for (const docFile of docFiles) {
                const docPath = path.join(projectRoot, docFile);
                if (!fs.existsSync(docPath)) {
                    issues.push(`Missing documentation file: ${docFile}`);
                }
            }

        } catch (error) {
            issues.push(`Error checking file organization: ${error}`);
        }

        return { issues };
    }

    /**
     * الحصول على جميع الملفات في مجلد
     * Get all files in directory
     */
    private static getAllFiles(dirPath: string): string[] {
        const files: string[] = [];

        try {
            const items = fs.readdirSync(dirPath, { withFileTypes: true });
            
            for (const item of items) {
                const fullPath = path.join(dirPath, item.name);
                
                if (item.isDirectory()) {
                    files.push(...this.getAllFiles(fullPath));
                } else if (item.isFile()) {
                    files.push(fullPath);
                }
            }
        } catch (error) {
            // تجاهل الأخطاء في قراءة المجلدات
        }

        return files;
    }

    /**
     * حساب نقاط بنية المشروع
     * Calculate project structure score
     */
    private static calculateStructureScore(
        requiredDirectories: number,
        missingDirectories: number,
        extraDirectories: number,
        organizationIssues: number
    ): number {
        if (requiredDirectories === 0) return 0;

        const maxPossibleScore = requiredDirectories * 2; // 2 points per required directory
        const penalties = missingDirectories * 2 + extraDirectories + organizationIssues;
        const actualScore = maxPossibleScore - penalties;

        return Math.max(0, Math.round((actualScore / maxPossibleScore) * 100));
    }
}
