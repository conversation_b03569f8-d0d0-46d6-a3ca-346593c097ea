/**
 * اختبارات وحدة مدير الأمان
 * Security manager unit tests
 * 
 * هذا الملف يحتوي على اختبارات شاملة لمدير الأمان
 * This file contains comprehensive tests for security manager
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { SecurityManager } from '@infrastructure/security/security-manager';
import { SecurityConfig, ThreatLevel } from '@shared/types';

describe('SecurityManager', () => {
    let securityManager: SecurityManager;
    let mockConfig: SecurityConfig;

    beforeEach(() => {
        mockConfig = {
            strictDomainValidation: true,
            enableThreatReporting: true,
            maxThreatReports: 100,
            blockSuspiciousRequests: true,
            sanitizeContent: true
        };
        securityManager = new SecurityManager(mockConfig);
    });

    describe('Request Security Validation', () => {
        it('should allow safe URLs', () => {
            // Arrange
            const safeUrls = [
                'https://www.youtube.com/watch?v=123',
                'https://googlevideo.com/videoplayback',
                'https://fonts.gstatic.com/font.woff2',
                'https://www.gstatic.com/youtube/img/logo.png'
            ];

            // Act & Assert
            safeUrls.forEach(url => {
                const result = securityManager.checkRequestSecurity(url, 'GET');
                expect(result.isValid).toBe(true);
            });
        });

        it('should block suspicious URLs', () => {
            // Arrange
            const suspiciousUrls = [
                'https://malicious-ads.com/track',
                'https://suspicious-domain.net/malware',
                'http://insecure-site.com/data',
                'https://phishing-site.org/login'
            ];

            // Act & Assert
            suspiciousUrls.forEach(url => {
                const result = securityManager.checkRequestSecurity(url, 'GET');
                expect(result.isValid).toBe(false);
                expect(result.errors).toHaveLength(1);
            });
        });

        it('should validate HTTP methods', () => {
            // Arrange
            const url = 'https://www.youtube.com/api/data';

            // Act
            const getResult = securityManager.checkRequestSecurity(url, 'GET');
            const postResult = securityManager.checkRequestSecurity(url, 'POST');
            const deleteResult = securityManager.checkRequestSecurity(url, 'DELETE');

            // Assert
            expect(getResult.isValid).toBe(true);
            expect(postResult.isValid).toBe(true);
            expect(deleteResult.isValid).toBe(false); // DELETE should be blocked
        });

        it('should handle malformed URLs', () => {
            // Arrange
            const malformedUrls = [
                'not-a-url',
                'ftp://invalid-protocol.com',
                'javascript:alert("xss")',
                'data:text/html,<script>alert("xss")</script>'
            ];

            // Act & Assert
            malformedUrls.forEach(url => {
                const result = securityManager.checkRequestSecurity(url, 'GET');
                expect(result.isValid).toBe(false);
            });
        });
    });

    describe('Content Sanitization', () => {
        it('should remove script tags', () => {
            // Arrange
            const maliciousContent = '<div>Safe content</div><script>alert("xss")</script>';

            // Act
            const sanitized = securityManager.sanitizeContent(maliciousContent);

            // Assert
            expect(sanitized).not.toContain('<script>');
            expect(sanitized).not.toContain('alert("xss")');
            expect(sanitized).toContain('Safe content');
        });

        it('should remove event handlers', () => {
            // Arrange
            const maliciousContent = '<div onclick="alert(\'xss\')" onload="malicious()">Content</div>';

            // Act
            const sanitized = securityManager.sanitizeContent(maliciousContent);

            // Assert
            expect(sanitized).not.toContain('onclick');
            expect(sanitized).not.toContain('onload');
            expect(sanitized).toContain('Content');
        });

        it('should remove javascript: URLs', () => {
            // Arrange
            const maliciousContent = '<a href="javascript:alert(\'xss\')">Click me</a>';

            // Act
            const sanitized = securityManager.sanitizeContent(maliciousContent);

            // Assert
            expect(sanitized).not.toContain('javascript:');
            expect(sanitized).toContain('Click me');
        });

        it('should preserve safe content', () => {
            // Arrange
            const safeContent = '<div class="safe"><p>This is <strong>safe</strong> content</p></div>';

            // Act
            const sanitized = securityManager.sanitizeContent(safeContent);

            // Assert
            expect(sanitized).toBe(safeContent);
        });

        it('should handle empty or null content', () => {
            // Act
            const emptyResult = securityManager.sanitizeContent('');
            const nullResult = securityManager.sanitizeContent(null as any);
            const undefinedResult = securityManager.sanitizeContent(undefined as any);

            // Assert
            expect(emptyResult).toBe('');
            expect(nullResult).toBe('');
            expect(undefinedResult).toBe('');
        });
    });

    describe('Threat Reporting', () => {
        it('should report threats when enabled', () => {
            // Arrange
            const threatUrl = 'https://malicious-site.com/malware';

            // Act
            securityManager.checkRequestSecurity(threatUrl, 'GET');
            const stats = securityManager.getSecurityStats();

            // Assert
            expect(stats.totalThreats).toBe(1);
            expect(stats.threatReports).toHaveLength(1);
            expect(stats.threatReports[0].url).toBe(threatUrl);
            expect(stats.threatReports[0].threatLevel).toBe(ThreatLevel.HIGH);
        });

        it('should not exceed max threat reports', () => {
            // Arrange
            const config: SecurityConfig = {
                ...mockConfig,
                maxThreatReports: 2
            };
            const limitedSecurityManager = new SecurityManager(config);

            // Act
            for (let i = 0; i < 5; i++) {
                limitedSecurityManager.checkRequestSecurity(`https://threat-${i}.com`, 'GET');
            }

            const stats = limitedSecurityManager.getSecurityStats();

            // Assert
            expect(stats.threatReports).toHaveLength(2);
            expect(stats.totalThreats).toBe(5);
        });

        it('should categorize threat levels correctly', () => {
            // Arrange
            const threats = [
                { url: 'https://ads.example.com', expectedLevel: ThreatLevel.LOW },
                { url: 'https://tracking.example.com', expectedLevel: ThreatLevel.MEDIUM },
                { url: 'https://malware.example.com', expectedLevel: ThreatLevel.HIGH },
                { url: 'javascript:alert("xss")', expectedLevel: ThreatLevel.CRITICAL }
            ];

            // Act & Assert
            threats.forEach(threat => {
                securityManager.checkRequestSecurity(threat.url, 'GET');
                const stats = securityManager.getSecurityStats();
                const latestReport = stats.threatReports[stats.threatReports.length - 1];
                expect(latestReport.threatLevel).toBe(threat.expectedLevel);
            });
        });

        it('should clear threat reports', () => {
            // Arrange
            securityManager.checkRequestSecurity('https://malicious.com', 'GET');
            expect(securityManager.getSecurityStats().threatReports).toHaveLength(1);

            // Act
            securityManager.clearThreatReports();

            // Assert
            const stats = securityManager.getSecurityStats();
            expect(stats.threatReports).toHaveLength(0);
            expect(stats.totalThreats).toBe(0);
        });
    });

    describe('Configuration Handling', () => {
        it('should respect disabled threat reporting', () => {
            // Arrange
            const config: SecurityConfig = {
                ...mockConfig,
                enableThreatReporting: false
            };
            const noReportingManager = new SecurityManager(config);

            // Act
            noReportingManager.checkRequestSecurity('https://malicious.com', 'GET');
            const stats = noReportingManager.getSecurityStats();

            // Assert
            expect(stats.threatReports).toHaveLength(0);
            expect(stats.totalThreats).toBe(0);
        });

        it('should respect disabled content sanitization', () => {
            // Arrange
            const config: SecurityConfig = {
                ...mockConfig,
                sanitizeContent: false
            };
            const noSanitizationManager = new SecurityManager(config);
            const maliciousContent = '<script>alert("xss")</script>';

            // Act
            const result = noSanitizationManager.sanitizeContent(maliciousContent);

            // Assert
            expect(result).toBe(maliciousContent);
        });

        it('should respect disabled request blocking', () => {
            // Arrange
            const config: SecurityConfig = {
                ...mockConfig,
                blockSuspiciousRequests: false
            };
            const noBlockingManager = new SecurityManager(config);

            // Act
            const result = noBlockingManager.checkRequestSecurity('https://malicious.com', 'GET');

            // Assert
            expect(result.isValid).toBe(true);
        });
    });

    describe('Performance and Edge Cases', () => {
        it('should handle high volume of requests efficiently', () => {
            // Arrange
            const startTime = Date.now();
            const requestCount = 1000;

            // Act
            for (let i = 0; i < requestCount; i++) {
                securityManager.checkRequestSecurity(`https://example-${i}.com`, 'GET');
            }
            const endTime = Date.now();

            // Assert
            const duration = endTime - startTime;
            expect(duration).toBeLessThan(1000); // Should complete in less than 1 second
        });

        it('should handle concurrent requests safely', async () => {
            // Arrange
            const promises = [];
            for (let i = 0; i < 100; i++) {
                promises.push(
                    Promise.resolve(securityManager.checkRequestSecurity(`https://test-${i}.com`, 'GET'))
                );
            }

            // Act
            const results = await Promise.all(promises);

            // Assert
            expect(results).toHaveLength(100);
            results.forEach(result => {
                expect(result).toHaveProperty('isValid');
                expect(result).toHaveProperty('errors');
            });
        });

        it('should handle extremely long URLs', () => {
            // Arrange
            const longUrl = 'https://example.com/' + 'a'.repeat(10000);

            // Act
            const result = securityManager.checkRequestSecurity(longUrl, 'GET');

            // Assert
            expect(result.isValid).toBe(false);
            expect(result.errors[0].message).toContain('URL too long');
        });

        it('should handle special characters in URLs', () => {
            // Arrange
            const specialUrls = [
                'https://example.com/path?param=value%20with%20spaces',
                'https://example.com/path#fragment',
                'https://example.com:8080/path',
                'https://subdomain.example.com/path'
            ];

            // Act & Assert
            specialUrls.forEach(url => {
                const result = securityManager.checkRequestSecurity(url, 'GET');
                expect(result).toHaveProperty('isValid');
            });
        });
    });
});
