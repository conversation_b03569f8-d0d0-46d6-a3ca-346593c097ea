/**
 * دوال التعديل البسيطة لخدمة الإعدادات
 * Simple setter functions for settings service
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import { ApplicationConfig, SettingChangeInfo } from '@shared/types';
import { ResourceManager } from '@shared/utils/resource-manager';
import Store from 'electron-store';
import { SETTINGS_MESSAGES, SettingsManagerConfig } from './settings-config';
import { SettingsValidator } from './settings-validator';

/**
 * فئة دوال التعديل البسيطة لخدمة الإعدادات
 */
export class SettingsServiceBasicSettersSimple {
    private readonly store: Store<ApplicationConfig>;
    private readonly config: SettingsManagerConfig;
    private readonly validator: SettingsValidator;
    private readonly resourceManager: ResourceManager;
    private changeListeners: Array<(change: SettingChangeInfo) => void> = [];

    constructor(
        config: SettingsManagerConfig,
        resourceManager: ResourceManager,
        store: Store<ApplicationConfig>,
        validator: SettingsValidator
    ) {
        this.config = config;
        this.resourceManager = resourceManager;
        this.store = store;
        this.validator = validator;
    }

    /**
     * تعيين إعداد محدد / Set specific setting
     */
    public async setSetting<K extends keyof ApplicationConfig>(
        key: K,
        value: ApplicationConfig[K]
    ): Promise<boolean> {
        try {
            const validation = this.validator.validateSingleSetting(key, value);
            if (!validation.isValid) {
                console.error(`${SETTINGS_MESSAGES.VALIDATION_ERROR}: ${key}`, validation.errors);
                return false;
            }

            const oldValue = this.store.get(key);
            this.store.set(key, value);

            this.notifyChange({
                key: String(key),
                oldValue,
                newValue: value,
                timestamp: new Date()
            });

            console.log(`${SETTINGS_MESSAGES.SETTING_UPDATED}: ${String(key)}`);
            return true;

        } catch (error) {
            console.error(`${SETTINGS_MESSAGES.ERROR_SET_SETTING}: ${String(key)}`, error);
            return false;
        }
    }

    /**
     * حذف إعداد محدد / Delete specific setting
     */
    public deleteSetting<K extends keyof ApplicationConfig>(key: K): boolean {
        try {
            const oldValue = this.store.get(key);
            this.store.delete(key);

            this.notifyChange({
                key: String(key),
                oldValue,
                newValue: undefined,
                timestamp: new Date()
            });

            console.log(`${SETTINGS_MESSAGES.SETTING_DELETED}: ${String(key)}`);
            return true;

        } catch (error) {
            console.error(`${SETTINGS_MESSAGES.ERROR_DELETE_SETTING}: ${String(key)}`, error);
            return false;
        }
    }

    /**
     * إضافة مستمع للتغييرات / Add change listener
     */
    public addChangeListener(listener: (change: SettingChangeInfo) => void): void {
        this.changeListeners.push(listener);
    }

    /**
     * إزالة مستمع للتغييرات / Remove change listener
     */
    public removeChangeListener(listener: (change: SettingChangeInfo) => void): void {
        const index = this.changeListeners.indexOf(listener);
        if (index > -1) {
            this.changeListeners.splice(index, 1);
        }
    }

    /**
     * إشعار المستمعين بالتغييرات / Notify listeners of changes
     */
    private notifyChange(change: SettingChangeInfo): void {
        this.changeListeners.forEach(listener => {
            try {
                listener(change);
            } catch (error) {
                console.error('خطأ في إشعار مستمع التغييرات:', error);
            }
        });
    }

    /**
     * تنظيف الموارد / Cleanup resources
     */
    public cleanup(): void {
        this.changeListeners = [];
        this.resourceManager.cleanup();
    }
}
