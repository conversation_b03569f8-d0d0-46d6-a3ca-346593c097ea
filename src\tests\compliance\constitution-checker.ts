/**
 * فاحص الامتثال للدستور الحاكم - ملف التفويض الرئيسي
 * Constitution compliance checker - Main delegation file
 *
 * هذا الملف يفوض جميع عمليات فحص الامتثال للملفات المتخصصة
 * This file delegates all compliance checking operations to specialized files
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

import * as fs from 'fs';
import * as path from 'path';
import { ConstitutionCheckerCore } from './constitution-checker-core';
import {
    ComplianceIssue,
    ComplianceReport
} from './constitution-checker-types';

/**
 * فاحص الامتثال للدستور الحاكم
 * Constitution compliance checker
 */
export class ConstitutionChecker {
    private readonly projectRoot: string;
    private readonly issues: ComplianceIssue[] = [];

    constructor(projectRoot: string = process.cwd()) {
        this.projectRoot = projectRoot;
    }

    /**
     * فحص الامتثال الشامل
     * Comprehensive compliance check
     */
    public async checkCompliance(): Promise<ComplianceReport> {
        console.log('🔍 بدء فحص الامتثال للدستور الحاكم / Starting constitution compliance check...\n');

        this.issues.length = 0; // Clear previous issues

        const srcPath = path.join(this.projectRoot, 'src');
        const files = this.getAllTypeScriptFiles(srcPath);

        console.log(`📁 فحص ${files.length} ملف / Checking ${files.length} files...\n`);

        // تفويض فحص الملفات للوحدة الأساسية
        // Delegate file checking to core module
        const rules = this.getDefaultRules();

        for (const file of files) {
            const result = await ConstitutionCheckerCore.checkFile(file, rules);
            this.issues.push(...result.issues);
        }

        return this.generateReport(files.length);
    }

    /**
     * الحصول على القواعد الافتراضية
     * Get default rules
     */
    private getDefaultRules() {
        return {
            maxFileSize: 200,
            maxFunctionSize: 20,
            maxClassSize: 200,
            requiredDocumentation: true,
            namingConventions: {
                fileNaming: 'kebab-case' as const,
                variableNaming: 'camelCase' as const,
                functionNaming: 'camelCase' as const,
                classNaming: 'PascalCase' as const,
                constantNaming: 'UPPER_CASE' as const
            },
            codeQualityRules: {
                noAnyType: true,
                requireTypeAnnotations: true,
                noMagicNumbers: true,
                noHardcodedValues: true,
                requireErrorHandling: true,
                maxComplexity: 10
            }
        };
    }

    /**
     * الحصول على جميع ملفات TypeScript
     * Get all TypeScript files
     */
    private getAllTypeScriptFiles(dir: string): string[] {
        const files: string[] = [];

        try {
            const items = fs.readdirSync(dir);
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);

                if (stat.isDirectory()) {
                    files.push(...this.getAllTypeScriptFiles(fullPath));
                } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
                    files.push(fullPath);
                }
            }
        } catch (error) {
            console.error(`Error reading directory ${dir}:`, error.message);
        }

        return files;
    }

    /**
     * إنشاء التقرير
     * Generate report
     */
    private generateReport(totalFiles: number): ComplianceReport {
        const errorCount = this.issues.filter(i => i.severity === 'ERROR').length;
        const warningCount = this.issues.filter(i => i.severity === 'WARNING').length;
        const infoCount = this.issues.filter(i => i.severity === 'INFO').length;

        // حساب نقاط الامتثال (100 - نسبة الأخطاء)
        const complianceScore = Math.max(0, 100 - (errorCount * 10) - (warningCount * 2) - (infoCount * 0.5));

        return {
            totalFiles,
            totalIssues: this.issues.length,
            errorCount,
            warningCount,
            infoCount,
            issues: [...this.issues],
            complianceScore: Math.round(complianceScore * 100) / 100,
            timestamp: new Date()
        };
    }
}
