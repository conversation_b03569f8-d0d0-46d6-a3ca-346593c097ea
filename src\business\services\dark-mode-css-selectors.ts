/**
 * محددات CSS للوضع المظلم
 * Dark mode CSS selectors
 * 
 * هذا الملف يحتوي على محددات CSS المستخدمة في الوضع المظلم
 * This file contains CSS selectors used in dark mode
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

/**
 * ثوابت محددات CSS للوضع المظلم
 * Dark mode CSS selector constants
 */
export const DARK_MODE_CSS_SELECTORS = {
    // محددات العناصر الأساسية
    // Basic element selectors
    BASIC: {
        BODY: 'body',
        HTML: 'html',
        VIDEO_PLAYER: '#movie_player',
        YOUTUBE_CONTAINER: '#container',
        HEADER: '#masthead',
        SIDEBAR: '#guide',
        CONTENT: '#content',
        COMMENTS: '#comments',
        DESCRIPTION: '#description',
        TITLE: '#title',
        METADATA: '#metadata',
        RELATED_VIDEOS: '#related',
        PLAYLIST: '#playlist',
        CHAT: '#chat',
        LIVE_CHAT: '#live-chat-iframe'
    },

    // محددات YouTube المتقدمة
    // Advanced YouTube selectors
    YOUTUBE: {
        APP: 'ytd-app',
        PAGE_MANAGER: 'ytd-page-manager',
        BROWSE: 'ytd-browse',
        WATCH_FLEXY: 'ytd-watch-flexy',
        VIDEO_PRIMARY_INFO: '.ytd-video-primary-info-renderer',
        VIDEO_SECONDARY_INFO: '.ytd-video-secondary-info-renderer',
        MINI_GUIDE: '.ytd-mini-guide-renderer',
        GUIDE_RENDERER: '.ytd-guide-renderer',
        MASTHEAD: '.ytd-masthead',
        SEARCHBOX: '.ytd-searchbox',
        BUTTON_RENDERER: '.ytd-button-renderer',
        TOGGLE_BUTTON: '.ytd-toggle-button-renderer'
    },

    // محددات المشغل
    // Player selectors
    PLAYER: {
        CONTAINER: '.html5-video-container',
        VIDEO: '.html5-main-video',
        CONTROLS: '.ytp-chrome-controls',
        PROGRESS_BAR: '.ytp-progress-bar',
        VOLUME_SLIDER: '.ytp-volume-slider',
        SETTINGS_BUTTON: '.ytp-settings-button',
        FULLSCREEN_BUTTON: '.ytp-fullscreen-button',
        PLAY_BUTTON: '.ytp-play-button',
        PAUSE_BUTTON: '.ytp-pause-button'
    },

    // محددات التعليقات
    // Comments selectors
    COMMENTS: {
        SECTION: '#comments',
        HEADER: '#comments-header',
        THREAD: '.ytd-comment-thread-renderer',
        COMMENT: '.ytd-comment-renderer',
        REPLIES: '.ytd-comment-replies-renderer',
        INPUT: '#placeholder-area',
        BUTTON: '#submit-button'
    },

    // محددات الشريط الجانبي
    // Sidebar selectors
    SIDEBAR: {
        CONTAINER: '#secondary',
        RELATED: '#related',
        PLAYLIST: '#playlist',
        CHAT: '#chat',
        LIVE_CHAT: '#live-chat-iframe',
        RECOMMENDATIONS: '.ytd-compact-video-renderer',
        PLAYLIST_PANEL: '.ytd-playlist-panel-renderer'
    },

    // محددات النماذج والإدخال
    // Forms and input selectors
    FORMS: {
        SEARCH_INPUT: '#search-input',
        VOICE_SEARCH: '#voice-search-button',
        SUBSCRIBE_BUTTON: '#subscribe-button',
        NOTIFICATION_BUTTON: '#notification-preference-button',
        LIKE_BUTTON: '#top-level-buttons-computed > ytd-toggle-button-renderer:first-child',
        DISLIKE_BUTTON: '#top-level-buttons-computed > ytd-toggle-button-renderer:nth-child(2)',
        SHARE_BUTTON: '#top-level-buttons-computed > ytd-button-renderer'
    }
} as const;

/**
 * فئات CSS المخصصة للوضع المظلم
 * Custom CSS classes for dark mode
 */
export const DARK_MODE_CSS_CLASSES = {
    // فئات الحالة
    // State classes
    STATE: {
        DARK_MODE: 'dark-mode-enabled',
        LIGHT_MODE: 'light-mode-enabled',
        TRANSITION: 'dark-mode-transition',
        LOADING: 'dark-mode-loading',
        ERROR: 'dark-mode-error'
    },

    // فئات الثيم
    // Theme classes
    THEME: {
        CUSTOM_THEME: 'custom-dark-theme',
        HIGH_CONTRAST: 'high-contrast-theme',
        AUTO_THEME: 'auto-theme',
        SYSTEM_THEME: 'system-theme'
    },

    // فئات الأداء
    // Performance classes
    PERFORMANCE: {
        OPTIMIZED: 'performance-optimized',
        REDUCED_MOTION: 'reduced-motion',
        LOW_PERFORMANCE: 'low-performance-mode',
        HIGH_PERFORMANCE: 'high-performance-mode'
    },

    // فئات المحتوى
    // Content classes
    CONTENT: {
        PRESERVE_IMAGES: 'preserve-images',
        IFRAME_DARK: 'iframe-dark-mode',
        VIDEO_OVERLAY: 'video-overlay-dark',
        TEXT_ENHANCED: 'text-enhanced',
        CONTRAST_ENHANCED: 'contrast-enhanced'
    },

    // فئات التفاعل
    // Interaction classes
    INTERACTION: {
        HOVER_ENHANCED: 'hover-enhanced',
        FOCUS_ENHANCED: 'focus-enhanced',
        ACTIVE_ENHANCED: 'active-enhanced',
        DISABLED_ENHANCED: 'disabled-enhanced'
    }
} as const;

/**
 * خصائص CSS للوضع المظلم
 * CSS properties for dark mode
 */
export const DARK_MODE_CSS_PROPERTIES = {
    // خصائص الألوان
    // Color properties
    COLORS: {
        BACKGROUND_COLOR: 'background-color',
        COLOR: 'color',
        BORDER_COLOR: 'border-color',
        OUTLINE_COLOR: 'outline-color',
        TEXT_SHADOW: 'text-shadow',
        BOX_SHADOW: 'box-shadow'
    },

    // خصائص الانتقال
    // Transition properties
    TRANSITIONS: {
        TRANSITION: 'transition',
        TRANSITION_DURATION: 'transition-duration',
        TRANSITION_TIMING: 'transition-timing-function',
        TRANSITION_DELAY: 'transition-delay',
        TRANSITION_PROPERTY: 'transition-property'
    },

    // خصائص التأثيرات
    // Effect properties
    EFFECTS: {
        FILTER: 'filter',
        OPACITY: 'opacity',
        TRANSFORM: 'transform'
    },

    // خصائص التخطيط
    // Layout properties
    LAYOUT: {
        DISPLAY: 'display',
        VISIBILITY: 'visibility'
    }
} as const;
