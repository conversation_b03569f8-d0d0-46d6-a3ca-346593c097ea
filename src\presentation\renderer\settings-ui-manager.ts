/**
 * مدير واجهة المستخدم للإعدادات
 * Settings UI manager
 *
 * هذا الملف يحتوي على منطق إدارة واجهة المستخدم للإعدادات
 * This file contains settings UI management logic
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-01-01
 */

// إعادة تصدير المكونات المتخصصة
// Re-export specialized components
export * from './settings-event-handler';
export * from './settings-ui-components';

import { ApplicationConfig } from '@shared/types';
import { SettingsEventHandler } from './settings-event-handler';
import { SettingsUIComponents } from './settings-ui-components';

/**
 * فئة مدير واجهة المستخدم للإعدادات
 * Settings UI manager class
 */
export class SettingsUIManager {
    private readonly uiComponents: SettingsUIComponents;
    private readonly eventHandler: SettingsEventHandler;

    /**
     * منشئ مدير واجهة المستخدم
     * UI manager constructor
     */
    constructor() {
        // إنشاء المكونات المتخصصة
        // Create specialized components
        this.uiComponents = new SettingsUIComponents();
        this.eventHandler = new SettingsEventHandler(this.uiComponents);
    }

    /**
     * تهيئة واجهة المستخدم
     * Initialize UI
     */
    public initializeUI(): void {
        this.eventHandler.setupEventListeners();
        this.loadInitialSettings();
    }

    /**
     * جمع الإعدادات من واجهة المستخدم
     * Collect settings from UI
     *
     * @returns إعدادات التطبيق
     */
    public collectSettingsFromUI(): ApplicationConfig {
        return this.uiComponents.getFormValues() as ApplicationConfig;
    }

    /**
     * تحديث واجهة المستخدم بالإعدادات
     * Update UI with settings
     *
     * @param settings - إعدادات التطبيق
     */
    public updateSettingsUI(settings: ApplicationConfig): void {
        this.uiComponents.setFormValues(settings);
    }

    /**
     * تحميل الإعدادات الأولية
     * Load initial settings
     */
    private async loadInitialSettings(): Promise<void> {
        try {
            const settings = await window.settingsAPI.loadSettings();
            this.updateSettingsUI(settings);
        } catch (error) {
            console.error('خطأ في تحميل الإعدادات / Error loading settings:', error);
            this.uiComponents.showStatusMessage('فشل في تحميل الإعدادات / Failed to load settings', 'error');
        }
    }

    /**
     * عرض رسالة الحالة
     * Show status message
     *
     * @param message - الرسالة
     * @param type - نوع الرسالة
     */
    public showStatusMessage(message: string, type: 'success' | 'error' | 'info' = 'info'): void {
        this.uiComponents.showStatusMessage(message, type);
    }

    /**
     * تفعيل/إلغاء تفعيل الأزرار
     * Enable/disable buttons
     *
     * @param enabled - حالة التفعيل
     */
    public setButtonsEnabled(enabled: boolean): void {
        this.uiComponents.setButtonsEnabled(enabled);
    }

    /**
     * إعادة تعيين النموذج
     * Reset form
     */
    public resetForm(): void {
        this.uiComponents.resetForm();
    }

    /**
     * التحقق من صحة النموذج
     * Validate form
     *
     * @returns true إذا كان النموذج صحيح
     */
    public validateForm(): boolean {
        return this.uiComponents.validateForm();
    }
}
